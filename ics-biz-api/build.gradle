apply plugin: 'org.springframework.boot'
apply plugin: 'java'
apply plugin: 'org.liquibase.gradle'

ext {
    springfoxVersion = '2.9.2'
}

springBoot{
    mainClassName = 'com.dcjet.cs.Application'
}

dependencies {
    compile "io.springfox:springfox-swagger2:$springfoxVersion"
    compile "io.springfox:springfox-swagger-ui:$springfoxVersion"
    compile 'org.apache.commons:commons-io:1.3.2'
    testImplementation "org.springframework.boot:spring-boot-starter-test"
    liquibaseRuntime 'com.oracle:ojdbc6:11.2.0.1.0'  //liquibase
    liquibaseRuntime 'org.liquibase:liquibase-core:3.7.0'  //liquibase
    liquibaseRuntime 'org.liquibase:liquibase-groovy-dsl:2.0.3' //liquibase
    liquibaseRuntime 'ch.qos.logback:logback-core:1.2.3'  //liquibase
    liquibaseRuntime 'ch.qos.logback:logback-classic:1.2.3' //liquibase
    liquibaseRuntime 'org.postgresql:postgresql:42.2.10'  //liquibase
    compile 'org.apache.logging.log4j:log4j-core:2.17.1'
    compile 'org.apache.logging.log4j:log4j-api:2.17.1'
    compile 'org.apache.logging.log4j:log4j-to-slf4j:2.17.1'
    compile 'ch.qos.logback:logback-core:1.2.10'
    compile 'ch.qos.logback:logback-classic:1.2.10'
}

//生成对比记录文件的位置
project.ext.diffChangelogFile = '../db/liquibase/changelog/' + new Date().format('yyyyMMddHHmmss') + '_changelog.xml'
//生成sql文件的位置
project.ext.generateSql = '../db/liquibase/changelog/' + new Date().format('yyyyMMddHHmmss') + '_update.sql'

liquibase {
    activities {
        dev1 {
            driver 'oracle.jdbc.OracleDriver'
            url '*******************************************'
            username 'GWSTD'
            password 'chinaport2018'
            changeLogFile '../db/liquibase/oracle_master.xml'
            defaultSchemaName ''
            logLevel 'debug'
            outputFile project.ext.generateSql
        }
        dev2 {
            driver 'org.postgresql.Driver'
            url '*****************************************************'
            username 'gwstd'
            password 'dcjet'
            changeLogFile '../db/liquibase/postgresql_master.xml'
            defaultSchemaName 'gwstd'
            logLevel 'debug'
            outputFile project.ext.generateSql
        }
        fat {
            driver 'oracle.jdbc.OracleDriver'
            url '******************************************'
            username 'hello'
            password 'hello'
            changeLogFile '../db/liquibase/master.xml'
            defaultSchemaName ''
            logLevel 'debug'
            outputFile project.ext.generateSql
        }
        prod {
            //driver 'com.microsoft.sqlserver.jdbc.SQLServerDriver'  //driver
            url '***************************************************'
            username 'hello'
            password 'hellos'
            changeLogFile '../db/liquibase/master.xml'
            defaultSchemaName ''
            logLevel 'debug'
            outputFile project.ext.generateSql
        }
        diffLog {
            driver 'oracle.jdbc.OracleDriver'
            url '******************************************'
            username 'hello'
            password 'goodday'
            changeLogFile project.ext.diffChangelogFile
            defaultSchemaName ''
            logLevel 'debug'
        }
        runList = 'dev2'
        //runList = project.ext.runList //'dev' // project.ext.runList  gradle update -PrunList=dev // 这里代表选择哪一个配置 可用参数代替
    }
}
