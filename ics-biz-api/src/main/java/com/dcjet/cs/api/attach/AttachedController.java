package com.dcjet.cs.api.attach;

import com.dcjet.cs.attach.model.Attached;
import com.dcjet.cs.attach.service.AttachedService;
import com.dcjet.cs.common.service.FastdFsService;
import com.dcjet.cs.dto.attach.AttachedDto;
import com.dcjet.cs.dto.attach.AttachedParam;
import com.dcjet.cs.util.ConstantsMessage;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.file.XdoFileHandler;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.*;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.lang.invoke.MethodHandles;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("v1/attached")
@Api(tags = "随附单据-随附单据")
public class AttachedController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    @Resource(name = "eternalXdoFileHandler")
    public XdoFileHandler fileHandler;
    @Resource
    private AttachedService attachedService;
    @Resource
    private FastdFsService fastdFsService;


    @ApiOperation("新增随附单据")
    @PostMapping("/insert")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = " 附件", dataType = "String"),
            @ApiImplicitParam(name = "businessType", value = "业务单证类型", dataType = "String"),
            @ApiImplicitParam(name = "businessSid", value = "业务单证SID", dataType = "String"),
            @ApiImplicitParam(name = "acmpNo", value = "随附单据编号", dataType = "String"),
            @ApiImplicitParam(name = "acmpType", value = "随附单据类型", dataType = "String")
    })
    public ResultObject<AttachedDto> insert(@RequestParam(value = "file", required = false) MultipartFile[] file,
                                            @RequestParam("businessType") String businessType,
                                            @RequestParam("businessSid") String businessSid,
                                            @RequestParam("acmpNo") String acmpNo,
                                            @RequestParam("acmpType") String acmpType,
                                            UserInfoToken userInfo) {
        ResultObject<AttachedDto> resultObject = add(file, businessType, businessSid, acmpNo, acmpType, null, null, userInfo);
        return resultObject;
    }

    @ApiOperation("新增随附单据")
    @PostMapping("insertAll")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = " 附件", dataType = "String"),
            @ApiImplicitParam(name = "businessType", value = "业务单证类型", dataType = "String"),
            @ApiImplicitParam(name = "businessSid", value = "业务单证SID", dataType = "String"),
            @ApiImplicitParam(name = "acmpNo", value = "随附单据编号", dataType = "String"),
            @ApiImplicitParam(name = "acmpType", value = "随附单据类型", dataType = "String"),
            @ApiImplicitParam(name = "note", value = "备注", dataType = "note")
    })
    public ResultObject<AttachedDto> insertAll(@RequestParam(value = "file", required = false) MultipartFile[] file,
                                               @RequestParam("businessType") String businessType,
                                               @RequestParam("businessSid") String businessSid,
                                               @RequestParam("acmpNo") String acmpNo,
                                               @RequestParam("acmpType") String acmpType,
                                               @RequestParam("note") String note,
                                               UserInfoToken userInfo) {
        ResultObject<AttachedDto> resultObject = add(file, businessType, businessSid, acmpNo, acmpType, null, note, userInfo);
        return resultObject;
    }

    @ApiOperation("文档管理上传附件")
    @PostMapping("insertDoc")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = " 附件", dataType = "String"),
            @ApiImplicitParam(name = "businessType", value = "业务单证类型", dataType = "String"),
            @ApiImplicitParam(name = "businessSid", value = "业务单证SID", dataType = "String"),
            @ApiImplicitParam(name = "acmpNo", value = "随附单据编号", dataType = "String"),
            @ApiImplicitParam(name = "acmpType", value = "随附单据类型", dataType = "String"),
            @ApiImplicitParam(name = "fileName", value = "随附单据名称", dataType = "String"),
            @ApiImplicitParam(name = "note", value = "备注", dataType = "note")
    })
    public ResultObject<AttachedDto> insertDoc(@RequestParam(value = "file", required = false) MultipartFile[] file,
                                               @RequestParam("businessType") String businessType,
                                               @RequestParam("businessSid") String businessSid,
                                               @RequestParam("acmpNo") String acmpNo,
                                               @RequestParam("acmpType") String acmpType,
                                               @RequestParam("fileName") String fileName,
                                               @RequestParam("note") String note,
                                               UserInfoToken userInfo) {
        ResultObject<AttachedDto> resultObject = add(file, businessType, businessSid, acmpNo, acmpType, fileName, note, userInfo);
        return resultObject;
    }

    public ResultObject<AttachedDto> add(MultipartFile[] files, String businessType, String businessSid, String acmpNo, String acmpType, String InfileName, String note, UserInfoToken userInfo) {
        Attached model = new Attached();
        String sid = java.util.UUID.randomUUID().toString();
        model.setSid(sid);
        ResultObject<AttachedDto> resultObject = ResultObject.createInstance(true, ConstantsMessage.INSERT_SUCCESS);
        if (StringUtils.isBlank(businessSid)) {
            throw new ErrorException(400, "业务单证主键不能为空");
        }
        if (files != null && files.length > 0) {
            for (MultipartFile file : files) {
                //保存文件
                if (!file.isEmpty()) {
                    String originName = file.getOriginalFilename();
                    String extName = originName.substring(originName.lastIndexOf("."));
                    if (StringUtils.isNotBlank(InfileName)) {
                        originName = InfileName;
                    }
                    if (StringUtils.isNotBlank(extName) && ".exe,.com,.pif,.bat,.scr".contains(extName.toLowerCase())) {
                        throw new ErrorException(400, "文件扩展名不支持！");
                    }
                    if (originName != null && originName.length() > 200) {
                        throw new ErrorException(400, "文件名长度不能超过200位！");
                    }
                    if (acmpType != null && acmpType.length() > 20) {
                        throw new ErrorException(400, "随附单据类型长度不能超过20位！");
                    }
                    if (file.getSize() > 10 * 1024 * 1024) {
                        throw new ErrorException(400, "附件大小超过10M");
                    }
                    String fileName = "";
                    int repeatCount = 0;
                    while (repeatCount < 3) {
                        try {
                            fileName = fileHandler.uploadFile(file.getInputStream());
                            break;
                        } catch (Exception e) {
                            logger.error(e.getMessage());
                            fileName = "";
                            repeatCount++;
                        }
                    }
                    if (!StringUtils.isBlank(fileName)) {
                        model.setFileName(fileName);
                    } else {
                        throw new ErrorException(400, "文件上传失败！");
                    }

                    model.setOriginFileName(originName);
                    model.setBusinessSid(businessSid);
                    model.setBusinessType(businessType);

                    model.setAcmpNo(acmpNo);
                    model.setAcmpType(acmpType);
                    model.setNote(note);
                    model.setFileSize(new BigDecimal(file.getSize()).divide(BigDecimal.valueOf(1024), 5,
                            BigDecimal.ROUND_HALF_UP));

                    AttachedDto attachedDto = attachedService.insert(model, userInfo);

                    if (attachedDto != null) {
                        resultObject.setData(attachedDto);
                    } else {
                        throw new ErrorException(400, ResultObject.INSERT_FAIL);
                    }
                }
            }
        }
        return resultObject;
    }


    @ApiOperation("新增随附单据")
    @PostMapping("insert3M")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = " 附件", dataType = "String"),
            @ApiImplicitParam(name = "businessType", value = "业务单证类型", dataType = "String"),
            @ApiImplicitParam(name = "businessSid", value = "业务单证SID", dataType = "String"),
            @ApiImplicitParam(name = "acmpNo", value = "随附单据编号", dataType = "String"),
            @ApiImplicitParam(name = "acmpType", value = "随附单据类型", dataType = "String")
    })
    public ResultObject<AttachedDto> insert3M(@RequestParam(value = "file", required = false) MultipartFile[] file,
                                              @RequestParam("businessType") String businessType,
                                              @RequestParam("businessSid") String businessSid,
                                              @RequestParam("acmpNo") String acmpNo,
                                              @RequestParam("acmpType") String acmpType,
                                              UserInfoToken userInfo) {
        ResultObject<AttachedDto> resultObject = add3M(file, businessType, businessSid, acmpNo, acmpType, null, null, userInfo);
        return resultObject;
    }

    public ResultObject<AttachedDto> add3M(MultipartFile[] files, String businessType, String businessSid, String acmpNo, String acmpType, String InfileName, String note, UserInfoToken userInfo) {
        Attached model = new Attached();
        String sid = java.util.UUID.randomUUID().toString();
        model.setSid(sid);
        ResultObject<AttachedDto> resultObject = ResultObject.createInstance(true, ConstantsMessage.INSERT_SUCCESS);
        if (StringUtils.isBlank(businessSid)) {
            throw new ErrorException(400, "业务单证主键不能为空");
        }
        if (files != null && files.length > 0) {
            for (MultipartFile file : files) {
                //保存文件
                if (!file.isEmpty()) {
                    String originName = file.getOriginalFilename();
                    String extName = originName.substring(originName.lastIndexOf("."));
                    if (StringUtils.isNotBlank(InfileName)) {
                        originName = InfileName;
                    }
                    if (StringUtils.isNotBlank(extName) && !".pdf".contains(extName.toLowerCase())) {
                        throw new ErrorException(400, "只能上传pdf文件！");
                    }
                    if (originName != null && originName.length() > 200) {
                        throw new ErrorException(400, "文件名长度不能超过200位！");
                    }
                    if (acmpType != null && acmpType.length() > 20) {
                        throw new ErrorException(400, "随附单据类型长度不能超过20位！");
                    }
                    if (file.getSize() > 3.5 * 1024 * 1024) {
                        throw new ErrorException(400, "附件大小超过3.5M");
                    }
                    String fileName = "";
                    int repeatCount = 0;
                    while (repeatCount < 3) {
                        try {
                            fileName = fileHandler.uploadFile(file.getInputStream());
                            break;
                        } catch (Exception e) {
                            logger.error(e.getMessage());
                            fileName = "";
                            repeatCount++;
                        }
                    }
                    if (!StringUtils.isBlank(fileName)) {
                        model.setFileName(fileName);
                    } else {
                        throw new ErrorException(400, "文件上传失败！");
                    }

                    model.setOriginFileName(originName);
                    model.setBusinessSid(businessSid);
                    model.setBusinessType(businessType);

                    model.setAcmpNo(acmpNo);
                    model.setAcmpType(acmpType);
                    model.setNote(note);
                    model.setFileSize(new BigDecimal(file.getSize()).divide(BigDecimal.valueOf(1024), 5,
                            BigDecimal.ROUND_HALF_UP));

                    AttachedDto attachedDto = attachedService.insert(model, userInfo);


                    if (attachedDto != null) {
                        resultObject.setData(attachedDto);
                    } else {
                        throw new ErrorException(400, ResultObject.INSERT_FAIL);
                    }
                }
            }
        }
        return resultObject;
    }


    /**
     * @param sid
     * @param attachedParam
     * @param userInfo
     * @return
     */
    @ApiOperation("随附单据修改")
    @PutMapping("{sid}")
    public ResultObject<AttachedDto> update(@PathVariable String sid, @Valid @RequestBody AttachedParam attachedParam, UserInfoToken userInfo) throws Exception {
        attachedParam.setSid(sid);
        attachedService.update(attachedParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ConstantsMessage.UPDATE_SUCCESS);
        return resultObject;
    }

    /**
     * @param sids
     * @return
     */
    @ApiOperation("随附单据删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) throws Exception {
        ResultObject resultObject = ResultObject.createInstance(true, ConstantsMessage.DELETE_SUCCESS);
        // 检查是否存在表体数据
        attachedService.delete(sids, userInfo);

        return resultObject;
    }


    @ApiOperation("获取随附单据文件")
    @GetMapping("{sid}")
    public ResponseEntity getAcmpFile(@PathVariable String sid) throws Exception {
        var model = attachedService.selectByPrimarykey(sid);
        //modify by jlzhang 支持华为云环境上fastFds文件下载
        return fastdFsService.getFileFromFastDFS(model.getFileName(), model.getOriginFileName());
    }



    /**
     * 获取附件列表
     *
     * @param sid
     * @param userInfo
     * @return
     */
    @ApiOperation("获取业务sid获取附件列表")
    @PostMapping(value = "/getAttechedList/{sid}")
    public ResultObject<List<AttachedDto>> getAttechedList(@PathVariable("sid") String sid, UserInfoToken userInfo) {
        return attachedService.getAttechedList(sid, userInfo);
    }


    /**
     * 获取附件列表
     *
     * @param sid
     * @param userInfo
     * @return
     */
    @ApiOperation("获取业务sid获取附件列表")
    @PostMapping(value = "/getAttechedListByType/{businessType}/{sid}")
    public ResultObject<List<AttachedDto>> getAttechedList(@PathVariable("businessType")String businessType,@PathVariable("sid") String sid, UserInfoToken userInfo) {
        return attachedService.getAttechedList(businessType,sid, userInfo);
    }




    @ApiOperation("根据附件id下载附件")
    @RequestMapping(value = "/getAttachFile/{sid}", method = RequestMethod.GET, headers = {"Accept=application/json;charset=UTF-8"})
    public ResponseEntity getFile(@PathVariable("sid") String sid) throws Exception {
        Attached model = attachedService.selectByPrimaryKey(sid);

        //modify by jlzhang 支持华为云环境上fastFds文件下载
        return fastdFsService.getFileFromFastDFSByIOS(model.getFileName(), model.getOriginFileName());
    }


}
