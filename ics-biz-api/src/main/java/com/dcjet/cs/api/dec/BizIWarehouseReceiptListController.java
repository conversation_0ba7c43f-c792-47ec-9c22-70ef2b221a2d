package com.dcjet.cs.api.dec;

import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dec.service.BizIWarehouseReceiptHeadService;
import com.dcjet.cs.dec.service.BizIWarehouseReceiptListService;
import com.dcjet.cs.dto.dec.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
* BizIOrderHeadController层
*
* <AUTHOR>
* @date 2025-03-07 15:37:18
*/
@RestController
@RequestMapping("v1/bizIWarehouseReceiptList")
@Api(tags = "进口管理-入库回单表体接口")
public class BizIWarehouseReceiptListController {

    @Resource
    private BizIWarehouseReceiptListService service;

    @Resource
    private PCodeHolder pCodeHolder;

    @Resource
    private ExcelService excelService;
 
    /**
     * 分页获取进口管理-订单信息表头数据
     * @param param
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页获取进口管理-表头数据")
    @PostMapping("list")
    public ResultObject<List<BizIWarehouseReceiptListDto>> getListPaged(@RequestBody BizIWarehouseReceiptListParam param, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizIWarehouseReceiptListDto>> paged = service.getListPaged(param, pageParam,userInfo);
        return paged;
    }



    /**
     * @param sid
     * @param param
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizIWarehouseReceiptListDto> update(@PathVariable String sid, @Valid @RequestBody BizIWarehouseReceiptListParam param, UserInfoToken userInfo) {
        param.setSid(sid);
        BizIWarehouseReceiptListDto dto = service.update(param, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (dto != null) {
            resultObject.setData(dto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }


    @PostMapping("/getWarehouseReceiptListBySid/{sid}")
    public ResultObject<BizIWarehouseReceiptListDto> getWarehouseReceiptListBySid(@PathVariable String sid,UserInfoToken userInfo) {
        return service.getWarehouseReceiptListBySid(sid,userInfo);
    }

    /**
     * 获取汇总数据 数量、总值、折扣金额、货款金额
     * @param param headId 表头sid
     * @param userInfo 用户信息
     * @return 汇总数据
     */
    @ApiOperation("获取汇总数据")
    @PostMapping("/getSumData")
    public ResultObject getSumData(@RequestBody BizIWarehouseReceiptListParam param, UserInfoToken userInfo) {
        return service.getSumData(param, userInfo);
    }


    /**
     * 提取税金
     */
    @ApiOperation("提取税金")
    @PostMapping("extractTaxes")
    public ResultObject extractTaxes(@RequestBody BizIWarehouseReceiptListParam param, UserInfoToken userInfo) {
        return service.extractTaxes(param.getParentId(), userInfo);
    }
}
