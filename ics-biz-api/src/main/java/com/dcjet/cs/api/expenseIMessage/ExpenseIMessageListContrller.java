package com.dcjet.cs.api.expenseIMessage;

import com.dcjet.cs.bi.service.ExpenseIHeadServise;
import com.dcjet.cs.bi.service.ExpenseIListServise;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.bi.*;
import com.dcjet.cs.dto.dec.BizISellListParam;
import com.dcjet.cs.params.dao.CostTypeMapper;
import com.dcjet.cs.params.model.CostType;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

@RestController
@RequestMapping("v1/expenseIList")
@Api(tags = "费用头接口")
public class ExpenseIMessageListContrller extends BaseController {
    @Resource
    private ExpenseIListServise expenseIListServise;
    @Resource
    private ExcelService excelService;
    @Resource
    private CostTypeMapper costTypeMapper;
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<ExpenseIListDto>> getListPaged(@RequestBody ExpenseIListParam expenseIListParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<ExpenseIListDto>> paged = expenseIListServise.getListPaged(expenseIListParam, pageParam,userInfo);
        return paged;
    }
    
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<ExpenseIListDto> update(@PathVariable String sid, @Valid @RequestBody ExpenseIListParam expenseIListParam, UserInfoToken userInfo) {
        expenseIListParam.setSid(sid);
        ExpenseIListDto expenseIHeadDto = expenseIListServise.update(expenseIListParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (expenseIHeadDto != null) {
            resultObject.setData(expenseIHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }

    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids,UserInfoToken userInfo) throws Exception {
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("删除成功"));
        // 检查是否存在表体数据
        expenseIListServise.delete(sids,userInfo);
        return resultObject;
    }
    @ApiOperation("Excel数据导出接口")
    @PostMapping("export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<ExpenseIListParam> exportParam, UserInfoToken userInfo) throws Exception{
        List<ExpenseIListDto> expenseIListDtos = expenseIListServise.selectAll(exportParam.getExportColumns(), userInfo);
        expenseIListDtos = convertForPrint(expenseIListDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), expenseIListDtos);
    }

    private List<ExpenseIListDto> convertForPrint(List<ExpenseIListDto> expenseIListDtos) {
        for (ExpenseIListDto expenseIListDto : expenseIListDtos) {
            //费用类型
            if(StringUtils.isNotBlank(expenseIListDto.getExpenseType())){
                //如果expenseType存在","则将其转为数组对数组每个对象进行转换，转换后将数组内信息进行","汇总
                String[] expenseTypes = expenseIListDto.getExpenseType().split(",");
                StringBuilder expenseTypeBuilder = new StringBuilder();
                for (String expenseType : expenseTypes) {
                    List<CostType> select = costTypeMapper.select(new CostType() {{
                        setParamCode(expenseType);
                    }});
                    if(select!= null && select.size() > 0){
                        expenseTypeBuilder.append(select.get(0).getCostName()).append(",");
                    }else{
                        expenseTypeBuilder.append(expenseType).append(",");
                    }
                }
                expenseIListDto.setExpenseType(expenseTypeBuilder.toString().substring(0, expenseTypeBuilder.length() - 1));
            }
        }

        return expenseIListDtos;
    }

    @ApiOperation("合同分页查询接口")
    @PostMapping("listContract")
    public ResultObject<List<CostIContractParam>> listContract(@RequestBody CostIContractParam costIContractParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<CostIContractParam>> paged = expenseIListServise.listContract(costIContractParam, pageParam,userInfo);
        return paged;
    }

    @ApiOperation("合同分页查询接口")
    @PostMapping("listContractPayment")
    public ResultObject<List<CostIContractParam>> listContractPayment(@RequestBody CostIContractParam costIContractParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<CostIContractParam>> paged = expenseIListServise.listContractPayment(costIContractParam, pageParam,userInfo);
        return paged;
    }

    @ApiOperation("合同新增明细接口")
    @PostMapping("insertContractDetail")
    public ResultObject insertContractDetail(@RequestBody CostIContractParam costIContractParam,UserInfoToken userInfo) throws Exception {
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("新增成功！"));
        costIContractParam.setTradeCode(userInfo.getCompany());
        expenseIListServise.insertContractDetail(costIContractParam,userInfo);
        return resultObject;
    }

    @ApiOperation("出货单分页查询接口")
    @PostMapping("listShippingOrder")
    public ResultObject<List<CostIShippingOrderParam>> listShippingOrder(@RequestBody CostIShippingOrderParam costIShippingOrderParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<CostIShippingOrderParam>> paged = expenseIListServise.listShippingOrder(costIShippingOrderParam, pageParam,userInfo);
        return paged;
    }

    @ApiOperation("出货单分页查询接口")
    @PostMapping("listShippingOrderPayment")
    public ResultObject<List<CostIShippingOrderParam>> listShippingOrderPayment(@RequestBody CostIShippingOrderParam costIShippingOrderParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<CostIShippingOrderParam>> paged = expenseIListServise.listShippingOrderPayment(costIShippingOrderParam, pageParam,userInfo);
        return paged;
    }

    @ApiOperation("出货单新增明细接口")
    @PostMapping("insertShippingOrder")
    public ResultObject insertShippingOrder(@RequestBody CostIShippingOrderParam costIShippingOrderParam,UserInfoToken userInfo) throws Exception {
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("新增成功！"));
        costIShippingOrderParam.setTradeCode(userInfo.getCompany());
        expenseIListServise.insertShippingOrder(costIShippingOrderParam,userInfo);
        return resultObject;
    }

    @PostMapping("/getSumDataByInvoiceSummary")
    public ResultObject getSumDataByInvoiceSummary(@RequestBody CostIShippingOrderParam costIShippingOrderParam, UserInfoToken userInfo) {
        return expenseIListServise.getSumDataByInvoiceSellSummary(costIShippingOrderParam, userInfo);
    }
    //获取费用类型对应的下拉参数
    @PostMapping("/selectCostType")
    public ResultObject selectCostType(@RequestBody CostIShippingOrderParam costIShippingOrderParam, UserInfoToken userInfo) {
        return expenseIListServise.selectCostType(costIShippingOrderParam, userInfo);
    }


    /**
     * 读取税金
     * @param file
     * @param sids
     * @param headId
     * @return
     */
    @PostMapping("upload")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = " 附件", dataType = "String"),
            @ApiImplicitParam(name = "sids", value = "业务单证类型", dataType = "String"),
            @ApiImplicitParam(name = "headId", value = "进口费用表头id", dataType = "String"),
    })
    public ResultObject uploadTaxPdf(@RequestParam(value = "file", required = false) MultipartFile[] file,
                                     @RequestParam("sids") List<String> sids,
                                     @RequestParam("headId") String headId,
                                     UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("读取成功！"));
        expenseIListServise.uploadTaxPdf(file, sids, headId, userInfo);
        return resultObject;
    }
}
