package com.dcjet.cs.api.dec;

import com.dcjet.cs.dto.dec.*;
import com.dcjet.cs.dec.service.BizIPurchaseListService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
import com.xdo.domain.ResultObject;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.dcjet.cs.common.service.ExcelService;
import com.xdo.pcode.service.PCodeHolder;
import xdoi18n.XdoI18nUtil;

/**
* BizIPurchaseListController层
*
* <AUTHOR>
* @date 2025-03-07 15:38:12
*/
@RestController
@RequestMapping("v1/bizIPurchaseList")
@Api(tags = "进口管理-进货信息表体接口")
public class BizIPurchaseListController{

    @Resource
    private BizIPurchaseListService bizIPurchaseListService;

    @Resource
    private PCodeHolder pCodeHolder;

    @Resource
    private ExcelService excelService;

    /**
     * 分页获取进口管理-进货信息表体数据
     * @param bizIPurchaseListParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页获取进口管理-进货信息表体数据")
    @PostMapping("list")
    public ResultObject<List<BizIPurchaseListDto>> getListPaged(@RequestBody BizIPurchaseListParam bizIPurchaseListParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizIPurchaseListDto>> paged = bizIPurchaseListService.getListPaged(bizIPurchaseListParam, pageParam,userInfo);
        return paged;
    }


    /**
     * @param bizIPurchaseListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizIPurchaseListDto> insert(@Valid @RequestBody BizIPurchaseListParam bizIPurchaseListParam, UserInfoToken userInfo) {
        ResultObject<BizIPurchaseListDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizIPurchaseListDto bizIPurchaseListDto = bizIPurchaseListService.insert(bizIPurchaseListParam, userInfo);
        if (bizIPurchaseListDto != null) {
            resultObject.setData(bizIPurchaseListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sid
     * @param bizIPurchaseListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizIPurchaseListDto> update(@PathVariable String sid, @Valid @RequestBody BizIPurchaseListParam bizIPurchaseListParam, UserInfoToken userInfo) {
        bizIPurchaseListParam.setSid(sid);
        BizIPurchaseListDto bizIPurchaseListDto = bizIPurchaseListService.update(bizIPurchaseListParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizIPurchaseListDto != null) {
            resultObject.setData(bizIPurchaseListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		bizIPurchaseListService.delete(sids, userInfo);
        return resultObject;
    }


    /**
     * 导出数据
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizIPurchaseListExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizIPurchaseListDto> bizIPurchaseListDtos = bizIPurchaseListService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizIPurchaseListDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizIPurchaseListDtos);
    }


    /**
     * 进货信息表体-行内编辑更新字段
     * @param param 进口管理-进货信息表体参数
     * @param userInfo 用户信息
     * @return 是否成功
     */
    @ApiOperation("进货信息表体-行内编辑更新字段")
    @PostMapping("/innerUpdatePurchaseList")
    public ResultObject innerUpdatePurchaseList(@RequestBody @Valid BizIPurchaseListParam param, UserInfoToken userInfo) {
        return bizIPurchaseListService.innerUpdatePurchaseList(param, userInfo);
    }


    @PostMapping("/innerDecTotalUpdatePurchaseList")
    public ResultObject innerDecTotalUpdatePurchaseList(@RequestBody @Valid BizIPurchaseListParam param, UserInfoToken userInfo) {
        return bizIPurchaseListService.innerDecTotalUpdatePurchaseList(param, userInfo);
    }


    @PostMapping("/getPurchaseListBySid/{sid}")
    public ResultObject getPurchaseListBySid(@PathVariable String sid, UserInfoToken userInfo) {
        return bizIPurchaseListService.getPurchaseListBySid(sid, userInfo);
    }



    /**
     * 进货信息表体-行内编辑更新字段(跟新进口发票号)
     * @param param 进口管理-进货信息表体参数
     * @param userInfo 用户信息
     * @return 是否成功
     */
    @ApiOperation("进货信息表体-行内编辑更新字段(跟新进口发票号)")
    @PostMapping("/innerUpdatePurchaseListInvoiceNo")
    public ResultObject innerUpdatePurchaseListInvoiceNo(@RequestBody @Valid BizIPurchaseListParam param, UserInfoToken userInfo) {
        return bizIPurchaseListService.innerUpdatePurchaseListInvoiceNo(param, userInfo);
    }


    /**
     * 获取汇总数据 数量、总值、折扣金额、货款金额
     * @param param headId 表头sid
     * @param userInfo 用户信息
     * @return 汇总数据
     */
    @ApiOperation("获取汇总数据 数量、总值、折扣金额、货款金额")
    @PostMapping("/getSumData")
    public ResultObject getSumData(@RequestBody BizIPurchaseListParam param, UserInfoToken userInfo) {
        return bizIPurchaseListService.getSumData(param, userInfo);
    }


    /**
     * (根据发票号)获取汇总数据 数量、总值、折扣金额、货款金额
     * @param param headId 表头sid
     * @param userInfo 用户信息
     * @return 汇总数据
     */
    @ApiOperation("(根据发票号)获取汇总数据 数量、总值、折扣金额、货款金额")
    @PostMapping("/getSumDataByInvoice")
    public ResultObject getSumDataByInvoice(@RequestBody BizIPurchaseListParam param, UserInfoToken userInfo) {
        return bizIPurchaseListService.getSumDataByInvoice(param, userInfo);
    }

    @ApiOperation("新增装箱子表数据")
    @PostMapping("/addPushListInvoice")
    public ResultObject addPushListInvoice(@RequestBody @Valid PushListInvoiceParams params, UserInfoToken userInfo) {
        return bizIPurchaseListService.addPushListInvoice(params, userInfo);
    }



    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizIPurchaseListDto> list) {
        for(BizIPurchaseListDto item : list) {
        
        }
    }






    /**
     * 删除删除装箱子表
     * @param params 装箱子表sid
     * @param userInfo 用户信息
     * @return 结果
     */
    @ApiOperation("删除删除装箱子表")
    @PostMapping("/deletePurchaseList")
    public ResultObject deletePurchaseList(@RequestBody @Valid DeletePurchaseListParams params, UserInfoToken userInfo) {
        return bizIPurchaseListService.deletePurchaseList(params.getSids(), userInfo);
    }
}
