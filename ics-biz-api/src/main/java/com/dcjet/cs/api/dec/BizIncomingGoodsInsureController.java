package com.dcjet.cs.api.dec;


import com.dcjet.cs.dec.service.BizIncomingGoodsInsureService;
import com.dcjet.cs.dto.dec.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
import com.xdo.domain.ResultObject;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.dcjet.cs.common.service.ExcelService;
import com.xdo.pcode.service.PCodeHolder;
import xdoi18n.XdoI18nUtil;

/**
* BizIncomingGoodsInsureController层
*
* <AUTHOR>
* @date 2025-05-24 13:25:42
*/
@RestController
@RequestMapping("v1/bizIncomingGoodsInsure")
@Api(tags = "进货管理-投保信息接口")
public class BizIncomingGoodsInsureController{

    @Resource
    private BizIncomingGoodsInsureService bizIncomingGoodsInsureService;

    @Resource
    private PCodeHolder pCodeHolder;

    @Resource
    private ExcelService excelService;
 
    /**
     * 分页获取进货管理-投保信息数据
     * @param bizIncomingGoodsInsureParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页获取进货管理-投保信息数据")
    @PostMapping("list")
    public ResultObject<List<BizIncomingGoodsInsureDto>> getListPaged(@RequestBody BizIncomingGoodsInsureParam bizIncomingGoodsInsureParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizIncomingGoodsInsureDto>> paged = bizIncomingGoodsInsureService.getListPaged(bizIncomingGoodsInsureParam, pageParam,userInfo);
        return paged;
    }


    /**
     * @param bizIncomingGoodsInsureParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizIncomingGoodsInsureDto> insert(@Valid @RequestBody BizIncomingGoodsInsureParam bizIncomingGoodsInsureParam, UserInfoToken userInfo) {
        ResultObject<BizIncomingGoodsInsureDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizIncomingGoodsInsureDto bizIncomingGoodsInsureDto = bizIncomingGoodsInsureService.insert(bizIncomingGoodsInsureParam, userInfo);
        if (bizIncomingGoodsInsureDto != null) {
            resultObject.setData(bizIncomingGoodsInsureDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sid
     * @param bizIncomingGoodsInsureParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizIncomingGoodsInsureDto> update(@PathVariable String sid, @Valid @RequestBody BizIncomingGoodsInsureParam bizIncomingGoodsInsureParam, UserInfoToken userInfo) {
        bizIncomingGoodsInsureParam.setId(sid);
        BizIncomingGoodsInsureDto bizIncomingGoodsInsureDto = bizIncomingGoodsInsureService.update(bizIncomingGoodsInsureParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizIncomingGoodsInsureDto != null) {
            resultObject.setData(bizIncomingGoodsInsureDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		bizIncomingGoodsInsureService.delete(sids, userInfo);
        return resultObject;
    }


    /**
     * 导出数据
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizIncomingGoodsInsureExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizIncomingGoodsInsureDto> bizIncomingGoodsInsureDtos = bizIncomingGoodsInsureService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizIncomingGoodsInsureDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizIncomingGoodsInsureDtos);
    }


    /**
     * 新增或编辑 投保信息
     * @param bizIncomingGoodsInsureParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping("/insertOrUpdate")
    public ResultObject<BizIncomingGoodsInsureDto> insertOrUpdate(@Valid @RequestBody BizIncomingGoodsInsureParam bizIncomingGoodsInsureParam, UserInfoToken userInfo) {
        ResultObject<BizIncomingGoodsInsureDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizIncomingGoodsInsureDto bizIncomingGoodsInsureDto = bizIncomingGoodsInsureService.insertOrUpdate(bizIncomingGoodsInsureParam, userInfo);
        if (bizIncomingGoodsInsureDto != null) {
            resultObject.setData(bizIncomingGoodsInsureDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }

    /**
     * 根据表头sid获取投保信息
     */
    @ApiOperation("根据表头sid获取证件信息")
    @GetMapping(value = "/getDocumentByHeadId/{headId}")
    public ResultObject<BizIncomingGoodsInsureDto> getDocumentByHeadId(@PathVariable String headId, UserInfoToken userInfo) {
        return  bizIncomingGoodsInsureService.getDocumentByHeadId(headId, userInfo);
    }





                        

    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizIncomingGoodsInsureDto> list) {
        for(BizIncomingGoodsInsureDto item : list) {
        
        }
    }


}
