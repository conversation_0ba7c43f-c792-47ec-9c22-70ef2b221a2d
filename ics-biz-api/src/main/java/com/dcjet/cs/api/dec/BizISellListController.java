package com.dcjet.cs.api.dec;

import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dec.model.BizISellList;
import com.dcjet.cs.dec.service.BizIPurchaseListService;
import com.dcjet.cs.dec.service.BizISellListService;
import com.dcjet.cs.dto.dec.BizIOrderListParam;
import com.dcjet.cs.dto.dec.BizIPurchaseListParam;
import com.dcjet.cs.dto.dec.BizISellListDto;
import com.dcjet.cs.dto.dec.BizISellListParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
* BizISellListController层
*
* <AUTHOR>
* @date 2025-03-07 15:37:58
*/
@RestController
@RequestMapping("v1/BizISellList")
@Api(tags = "进口管理-进货信息表头接口")
public class BizISellListController {

    @Resource
    private BizISellListService BizISellListService;

    @Resource
    private BizIPurchaseListService bizIPurchaseListService;

    @Resource
    private PCodeHolder pCodeHolder;

    @Resource
    private ExcelService excelService;

    /**
     * 分页获取进口管理-进货信息表头数据
     * @param BizISellListParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页获取进口管理-进货信息表头数据")
    @PostMapping("list")
    public ResultObject<List<BizISellList>> getListPaged(@RequestBody BizISellListParam BizISellListParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizISellList>> paged = BizISellListService.getListPaged(BizISellListParam, pageParam,userInfo);
        return paged;
    }


    /**
     * @param sid
     * @param BizISellListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizISellListDto> update(@PathVariable String sid, @Valid @RequestBody BizISellListParam BizISellListParam, UserInfoToken userInfo) {
        BizISellListParam.setSid(sid);
        BizISellListDto BizISellListDto = BizISellListService.update(BizISellListParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (BizISellListDto != null) {
            resultObject.setData(BizISellListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }


    /**
     * 获取进货信息表头数据 根据订单表头sid
     * @param BizISellListParam 订单表头sid
     * @param userInfo 用户信息
     * @return 结果
     */
    @ApiOperation("获取进货信息表头数据 根据订单表头sid")
    @PostMapping("/getSellListByOrderSid")
    public ResultObject <BizISellListDto> getPurchaseHeadByOrderSid(@RequestBody BizISellListParam BizISellListParam, UserInfoToken userInfo) {
        return BizISellListService.getPurchaseHeadByOrderSid(BizISellListParam, userInfo);
    }

    @ApiOperation("维护销售信息")
    @PostMapping("/updateSellList")
    public ResultObject updateSellList(@RequestBody BizISellListParam BizISellListParam, UserInfoToken userInfo) {
        return BizISellListService.updateSellList(BizISellListParam, userInfo);
    }

                        

    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizISellListDto> list) {
        for(BizISellListDto item : list) {
        
        }
    }

    @ApiOperation("根据发票号_获取汇总数据")
    @PostMapping("/getSumDataByInvoice")
    public ResultObject getSumDataByInvoice(@RequestBody BizISellListParam param, UserInfoToken userInfo) {
        return BizISellListService.getSumDataByInvoiceSell(param, userInfo);
    }

    @ApiOperation("根据发票号_获取汇总数据")
    @PostMapping("/getSumDataByInvoiceSummary")
    public ResultObject getSumDataByInvoiceSummary(@RequestBody BizISellListParam param, UserInfoToken userInfo) {
        return BizISellListService.getSumDataByInvoiceSellSummary(param, userInfo);
    }

}
