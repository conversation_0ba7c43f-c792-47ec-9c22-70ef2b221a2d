package com.dcjet.cs.api.dec;

import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dec.service.BizIReceiptListService;
import com.dcjet.cs.dto.dec.BizIReceiptListDto;
import com.dcjet.cs.dto.dec.BizIReceiptListParam;
import com.dcjet.cs.dto.dec.BizISellListParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
* BizIReceiptListController层
*
* <AUTHOR>
* @date 2025-03-07 15:37:58
*/
@RestController
@RequestMapping("v1/BizIReceiptList")
@Api(tags = "进口管理-出库回单表体接口")
public class BizIReceiptListController {

    @Resource
    private BizIReceiptListService BizIReceiptListService;

    @Resource
    private PCodeHolder pCodeHolder;

    @Resource
    private ExcelService excelService;

    /**
     * 分页获取进口管理-出库回单表体数据
     * @param BizIReceiptListParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页获取进口管理-出库回单表体数据")
    @PostMapping("list")
    public ResultObject<List<BizIReceiptListDto>> getListPaged(@RequestBody BizIReceiptListParam BizIReceiptListParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizIReceiptListDto>> paged = BizIReceiptListService.getListPaged(BizIReceiptListParam, pageParam,userInfo);
        return paged;
    }


    /**
     * @param sid
     * @param BizIReceiptListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizIReceiptListDto> update(@PathVariable String sid, @Valid @RequestBody BizIReceiptListParam BizIReceiptListParam, UserInfoToken userInfo) {
        BizIReceiptListParam.setSid(sid);
        BizIReceiptListDto BizIReceiptListDto = BizIReceiptListService.update(BizIReceiptListParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (BizIReceiptListDto != null) {
            resultObject.setData(BizIReceiptListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }


    /**
     * 获取出库回单表头数据 根据订单表头sid
     * @param BizIReceiptListParam 订单表头sid
     * @param userInfo 用户信息
     * @return 结果
     */
    @ApiOperation("获取出库回单表头数据 根据订单表头sid")
    @PostMapping("/getReceiptListByOrderSid")
    public ResultObject <BizIReceiptListDto> getPurchaseHeadByOrderSid(@RequestBody BizIReceiptListParam BizIReceiptListParam, UserInfoToken userInfo) {
        return BizIReceiptListService.getPurchaseHeadByOrderSid(BizIReceiptListParam, userInfo);
    }

                        

    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizIReceiptListDto> list) {
        for(BizIReceiptListDto item : list) {
        
        }
    }

    @ApiOperation("获取汇总数据")
    @PostMapping("/getSumDataByInvoiceSummary")
    public ResultObject getSumDataByInvoiceSummary(@RequestBody BizIReceiptListParam param, UserInfoToken userInfo) {
        return BizIReceiptListService.getSumDataByInvoiceSellSummary(param, userInfo);
    }

}
