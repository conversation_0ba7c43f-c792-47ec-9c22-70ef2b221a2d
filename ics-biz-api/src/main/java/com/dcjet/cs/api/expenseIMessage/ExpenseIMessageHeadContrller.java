package com.dcjet.cs.api.expenseIMessage;

import com.dcjet.cs.baseInfoCustomerParams.dao.BaseInfoCustomerParamsMapper;
import com.dcjet.cs.baseInfoCustomerParams.model.BaseInfoCustomerParams;
import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.bi.service.ExpenseIHeadServise;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.bi.*;
import com.dcjet.cs.params.dao.CostTypeMapper;
import com.dcjet.cs.params.model.CostType;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.BaseController;
import com.xdo.springboot.annotation.FuYunMenuAuthentication;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

@RestController
@RequestMapping("v1/expenseIHead")
@Api(tags = "费用头接口")
public class ExpenseIMessageHeadContrller extends BaseController {
    @Resource
    private ExpenseIHeadServise expenseIHeadServise;
    @Resource
    private BaseInfoCustomerParamsMapper baseInfoCustomerParamsMapper;
    @Resource
    private BizMerchantMapper bizMerchantMapper;
    @Resource
    private CostTypeMapper costTypeMapper;
    @Resource
    private ExcelService excelService;

    @ApiOperation("分页查询接口")
    @PostMapping("list")
    @FuYunMenuAuthentication("/tobacco/cost/costIManage")
    public ResultObject<List<ExpenseIHeadDto>> getListPaged(@RequestBody ExpenseIHeadParam expenseIHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<ExpenseIHeadDto>> paged = expenseIHeadServise.getListPaged(expenseIHeadParam, pageParam,userInfo);
        return paged;
    }

    @ApiOperation("信息新增接口")
    @PostMapping()
    public ResultObject<ExpenseIHeadDto> insert(@Valid @RequestBody ExpenseIHeadParam expenseIHeadParam, UserInfoToken userInfo) {
        ResultObject<ExpenseIHeadDto> resultObject = expenseIHeadServise.insert(expenseIHeadParam, userInfo);
        return resultObject;
    }

    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<ExpenseIHeadDto> update(@PathVariable String sid, @Valid @RequestBody ExpenseIHeadParam expenseIHeadParam, UserInfoToken userInfo) {
        expenseIHeadParam.setSid(sid);
        ExpenseIHeadDto expenseIHeadDto = expenseIHeadServise.update(expenseIHeadParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (expenseIHeadDto != null) {
            resultObject.setData(expenseIHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }

    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids,UserInfoToken userInfo) throws Exception {
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("删除成功"));
        // 检查是否存在表体数据
        expenseIHeadServise.delete(sids,userInfo);
        return resultObject;
    }

    @ApiOperation("退单")
    @PostMapping("chargeback/{sids}")
    public ResultObject chargeback(@PathVariable List<String> sids,UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("退单成功"));
        expenseIHeadServise.chargeback(sids,userInfo);
        return resultObject;
    }
    @ApiOperation("确认")
    @PostMapping("affirm/{sids}")
    public ResultObject affirm(@PathVariable List<String> sids,UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("退单成功"));
        expenseIHeadServise.affirm(sids,userInfo);


        //todo 发送用友
        return resultObject;
    }
    @ApiOperation("作废")
    @PostMapping("cancellation/{sids}")
    public ResultObject cancellation(@PathVariable List<String> sids,UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("退单成功"));
        expenseIHeadServise.cancellation(sids,userInfo);
        return resultObject;
    }
    @ApiOperation("复制")
    @PostMapping("copy/{sids}")
    public ResultObject copy(@PathVariable List<String> sids,UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("退单成功"));
        expenseIHeadServise.copy(sids,userInfo);
        return resultObject;
    }
    @ApiOperation("Excel数据导出接口")
    @PostMapping("export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<ExpenseIHeadParam> exportParam, UserInfoToken userInfo) throws Exception{
        List<ExpenseIHeadDto> expenseIHeadDtos = expenseIHeadServise.selectAll(exportParam.getExportColumns(), userInfo);
        expenseIHeadDtos = convertForPrint(expenseIHeadDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), expenseIHeadDtos);
    }

    private List<ExpenseIHeadDto> convertForPrint(List<ExpenseIHeadDto> expenseIHeadDtos) {
        for (ExpenseIHeadDto expenseIHeadDto : expenseIHeadDtos) {
            //业务类型
            if(StringUtils.isNotBlank(expenseIHeadDto.getBusinessType())){
                expenseIHeadDto.setBusinessType(CommonEnum.businessTypeEnum.getValue(expenseIHeadDto.getBusinessType()));
            }
            //费用类型
            if(StringUtils.isNotBlank(expenseIHeadDto.getExpenseType())){
                //如果expenseType存在","则将其转为数组对数组每个对象进行转换，转换后将数组内信息进行","汇总
                String[] expenseTypes = expenseIHeadDto.getExpenseType().split(",");
                StringBuilder expenseTypeBuilder = new StringBuilder();
                for (String expenseType : expenseTypes) {
                    List<CostType> select = costTypeMapper.select(new CostType() {{
                        setParamCode(expenseType);
                    }});
                    if(select!= null && select.size() > 0){
                        expenseTypeBuilder.append(select.get(0).getCostName()).append(",");
                    }else{
                        expenseTypeBuilder.append(expenseType).append(",");
                    }
                }
                expenseIHeadDto.setExpenseType(expenseTypeBuilder.toString().substring(0, expenseTypeBuilder.length() - 1));
            }
            //收款方
            if(StringUtils.isNotBlank(expenseIHeadDto.getPayee())){
                List<BizMerchant> select = bizMerchantMapper.select(new BizMerchant() {{
                    setMerchantCode(expenseIHeadDto.getPayee());
                }});
                if(select!= null && select.size() > 0){
                    expenseIHeadDto.setPayee(select.get(0).getMerchantNameCn());
                }
            }
            //币种
            if(StringUtils.isNotBlank(expenseIHeadDto.getCurr())){
                List<BaseInfoCustomerParams> curr = baseInfoCustomerParamsMapper.select(new BaseInfoCustomerParams() {{
                    setParamsType("CURR");
                    setParamsCode(expenseIHeadDto.getCurr());
                }});
                if(curr!= null && curr.size() > 0){
                    BaseInfoCustomerParams baseInfoCustomerParams = curr.get(0);
                    expenseIHeadDto.setCurr(baseInfoCustomerParams.getCustomParamCode());
                }
            }

            //预付标志
            if(StringUtils.isNotBlank(expenseIHeadDto.getAdvanceFlag())){
                expenseIHeadDto.setAdvanceFlag(CommonEnum.isNotEnum.getValue(expenseIHeadDto.getAdvanceFlag()));
            }
            //发送用友
            if(StringUtils.isNotBlank(expenseIHeadDto.getSendUfida())){
                expenseIHeadDto.setSendUfida(CommonEnum.isNotEnum.getValue(expenseIHeadDto.getSendUfida()));
            }
        }
        return expenseIHeadDtos;
    };
}
