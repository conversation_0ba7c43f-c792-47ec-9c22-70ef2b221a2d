package com.dcjet.cs.api.dec;


import com.dcjet.cs.dec.service.BizIncomingGoodsDocumentService;

import com.dcjet.cs.dto.dec.BizIncomingGoodsDocumentDto;
import com.dcjet.cs.dto.dec.BizIncomingGoodsDocumentExportParam;

import com.dcjet.cs.dto.dec.BizIncomingGoodsDocumentParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
import com.xdo.domain.ResultObject;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.dcjet.cs.common.service.ExcelService;
import com.xdo.pcode.service.PCodeHolder;
import xdoi18n.XdoI18nUtil;
/**
* BizIncomingGoodsDocumentController层
*
* <AUTHOR>
* @date 2025-05-23 15:06:34
*/
@RestController
@RequestMapping("v1/bizIncomingGoodsDocument")
@Api(tags = "进过管理-表体列表接口")
public class BizIncomingGoodsDocumentController{

    @Resource
    private BizIncomingGoodsDocumentService bizIncomingGoodsDocumentService;

    @Resource
    private PCodeHolder pCodeHolder;

    @Resource
    private ExcelService excelService;
 
    /**
     * 分页获取进过管理-表体列表数据
     * @param bizIncomingGoodsDocumentParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页获取进过管理-表体列表数据")
    @PostMapping("list")
    public ResultObject<List<BizIncomingGoodsDocumentDto>> getListPaged(@RequestBody BizIncomingGoodsDocumentParam bizIncomingGoodsDocumentParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizIncomingGoodsDocumentDto>> paged = bizIncomingGoodsDocumentService.getListPaged(bizIncomingGoodsDocumentParam, pageParam,userInfo);
        return paged;
    }


    /**
     * @param bizIncomingGoodsDocumentParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizIncomingGoodsDocumentDto> insert(@Valid @RequestBody BizIncomingGoodsDocumentParam bizIncomingGoodsDocumentParam, UserInfoToken userInfo) {
        ResultObject<BizIncomingGoodsDocumentDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizIncomingGoodsDocumentDto bizIncomingGoodsDocumentDto = bizIncomingGoodsDocumentService.insert(bizIncomingGoodsDocumentParam, userInfo);
        if (bizIncomingGoodsDocumentDto != null) {
            resultObject.setData(bizIncomingGoodsDocumentDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sid
     * @param bizIncomingGoodsDocumentParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizIncomingGoodsDocumentDto> update(@PathVariable String sid, @Valid @RequestBody BizIncomingGoodsDocumentParam bizIncomingGoodsDocumentParam, UserInfoToken userInfo) {
        bizIncomingGoodsDocumentParam.setId(sid);
        BizIncomingGoodsDocumentDto bizIncomingGoodsDocumentDto = bizIncomingGoodsDocumentService.update(bizIncomingGoodsDocumentParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizIncomingGoodsDocumentDto != null) {
            resultObject.setData(bizIncomingGoodsDocumentDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }



    /**
     * @param bizIncomingGoodsDocumentParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping("/insertOrUpdate")
    public ResultObject<BizIncomingGoodsDocumentDto> insertOrUpdate(@Valid @RequestBody BizIncomingGoodsDocumentParam bizIncomingGoodsDocumentParam, UserInfoToken userInfo) {
        ResultObject<BizIncomingGoodsDocumentDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizIncomingGoodsDocumentDto bizIncomingGoodsDocumentDto = bizIncomingGoodsDocumentService.insertOrUpdate(bizIncomingGoodsDocumentParam, userInfo);
        if (bizIncomingGoodsDocumentDto != null) {
            resultObject.setData(bizIncomingGoodsDocumentDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		bizIncomingGoodsDocumentService.delete(sids, userInfo);
        return resultObject;
    }


    /**
     * 导出数据
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizIncomingGoodsDocumentExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizIncomingGoodsDocumentDto> bizIncomingGoodsDocumentDtos = bizIncomingGoodsDocumentService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizIncomingGoodsDocumentDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizIncomingGoodsDocumentDtos);
    }


    /**
     * 根据表头sid获取证件信息
     */
    @ApiOperation("根据表头sid获取证件信息")
    @GetMapping(value = "/getDocumentByHeadId/{headId}")
    public ResultObject<BizIncomingGoodsDocumentDto> getDocumentByHeadId(@PathVariable String headId, UserInfoToken userInfo) {
        return  bizIncomingGoodsDocumentService.getDocumentByHeadId(headId, userInfo);
    }




    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizIncomingGoodsDocumentDto> list) {
        for(BizIncomingGoodsDocumentDto item : list) {
        
        }
    }


}
