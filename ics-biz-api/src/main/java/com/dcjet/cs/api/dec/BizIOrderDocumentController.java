package com.dcjet.cs.api.dec;

import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dec.service.BizIOrderDocumentService;
import com.dcjet.cs.dec.service.BizIOrderHeadService;
import com.dcjet.cs.dto.dec.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

/**
* BizIOrderHeadController层
*
* <AUTHOR>
* @date 2025-03-07 15:37:18
*/
@RestController
@RequestMapping("v1/bizIOrderDocument")
@Api(tags = "进口管理-单证信息接口")
public class BizIOrderDocumentController {

    @Resource
    private BizIOrderDocumentService service;

    @Resource
    private PCodeHolder pCodeHolder;

    @Resource
    private ExcelService excelService;
 
    /**
     *
     * @param param
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("获取进口管理-单证信息")
    @PostMapping("list")
    public ResultObject<BizIOrderDocumentDto> getListPaged(@RequestBody BizIOrderDocumentParam param, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<BizIOrderDocumentDto> paged = service.getListPaged(param, pageParam,userInfo);
        return paged;
    }


    /**
     * @param param
     * @param userInfo
     * @return
     */
    @ApiOperation("新增和更新接口")
    @PostMapping()
    public ResultObject<BizIOrderDocumentDto> insert(@Valid @RequestBody BizIOrderDocumentParam param, UserInfoToken userInfo) {
        ResultObject<BizIOrderDocumentDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizIOrderDocumentDto documentDto = service.update(param, userInfo);
        if (documentDto != null) {
            resultObject.setData(documentDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }




}
