package com.dcjet.cs.api.auxiliaryMaterials;
import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.auxiliaryMaterials.*;
import com.dcjet.cs.auxiliaryMaterials.service.BizIAuxmatForContractHeadService;
import com.dcjet.cs.util.CommonEnum;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import com.xdo.springboot.annotation.FuYunMenuAuthentication;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.function.Function;
import java.util.LinkedHashMap;

/**
 * generated by Generate dc
 *
 *
 * <AUTHOR>
 * @date: 2025-5-22
 */
@RestController
@RequestMapping("v1/bizIAuxmatForContractHead")
@Api(tags = "接口")
public class BizIAuxmatForContractHeadController extends BaseController {
    @Resource
    private BizIAuxmatForContractHeadService bizIAuxmatForContractHeadService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    @Resource
    private BizMerchantMapper bizMerchantMapper;
    /**
     * @param bizIAuxmatForContractHeadParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    @FuYunMenuAuthentication("/tobacoo/auxiliaryMaterials/forContract")
    public ResultObject<List<BizIAuxmatForContractHeadDto>> getListPaged(@RequestBody BizIAuxmatForContractHeadParam bizIAuxmatForContractHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        bizIAuxmatForContractHeadParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<BizIAuxmatForContractHeadDto>> paged = bizIAuxmatForContractHeadService.getListPaged(bizIAuxmatForContractHeadParam, pageParam);
        return paged;
    }

    @ApiOperation("分页查询接口")
    @PostMapping("planList")
    public ResultObject<List<BizIAuxmatForContractListDto>> getPlanListPaged(@RequestBody BizIAuxmatForContractListParam bizIAuxmatForContractListParam, PageParam pageParam,  UserInfoToken userInfo) {
        bizIAuxmatForContractListParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<BizIAuxmatForContractListDto>> paged = bizIAuxmatForContractHeadService.getPlanListPaged(bizIAuxmatForContractListParam, pageParam);
        return paged;
    }
    /**
     * @param bizIAuxmatForContractHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizIAuxmatForContractHeadDto> insert(@RequestBody BizIAuxmatForContractHeadParam bizIAuxmatForContractHeadParam, UserInfoToken userInfo) {
        ResultObject<BizIAuxmatForContractHeadDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizIAuxmatForContractHeadDto bizIAuxmatForContractHeadDto = bizIAuxmatForContractHeadService.insert(bizIAuxmatForContractHeadParam, userInfo);
        if (bizIAuxmatForContractHeadDto != null) {
            resultObject.setData(bizIAuxmatForContractHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizIAuxmatForContractHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizIAuxmatForContractHeadDto> update(@PathVariable String sid, @Valid @RequestBody BizIAuxmatForContractHeadParam bizIAuxmatForContractHeadParam, UserInfoToken userInfo) {
        bizIAuxmatForContractHeadParam.setSid(sid);
        BizIAuxmatForContractHeadDto bizIAuxmatForContractHeadDto = bizIAuxmatForContractHeadService.update(bizIAuxmatForContractHeadParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizIAuxmatForContractHeadDto != null) {
            resultObject.setData(bizIAuxmatForContractHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizIAuxmatForContractHeadService.delete(sids, userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizIAuxmatForContractHeadExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizIAuxmatForContractHeadDto> bizIAuxmatForContractHeadDtos = bizIAuxmatForContractHeadService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizIAuxmatForContractHeadDtos, userInfo);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizIAuxmatForContractHeadDtos);
    }
    /**
     * 导出，pCode转换中文名称
     * 优化：支持过滤重复key，提供多种重复处理策略
     * @param list
     * @param userInfo
     */
    public void convertForPrint(List<BizIAuxmatForContractHeadDto> list, UserInfoToken userInfo) {
        BizMerchant bizMerchant = new BizMerchant();
        bizMerchant.setTradeCode(userInfo.getCompany());
        List<BizMerchant> bizMerchants = bizMerchantMapper.getList(bizMerchant);

        // 优化：支持过滤重复key，当存在重复的merchantCode时，保留第一个出现的merchantNameCn
        Map<String, String> bizMerchantMap = createMerchantMap(bizMerchants);

        for(BizIAuxmatForContractHeadDto item : list) {
            item.setBusinessType(CommonEnum.businessTypeEnum.getValue(item.getBusinessType()));
            item.setDocStatus(CommonEnum.OrderStatusEnum.getValue(item.getDocStatus()));
            item.setAuditStatus(CommonEnum.OrderApprStatusEnum.getValue(item.getAuditStatus()));

            // 优化：获取商户名称
            String customerNameCn = getMerchantNameSafely(bizMerchantMap, item.getCustomerName());
            String supplierNameCn = getMerchantNameSafely(bizMerchantMap, item.getSupplierName());

            item.setCustomerName(item.getCustomerName() + " " + customerNameCn);
            item.setSupplierName(item.getSupplierName() + " " + supplierNameCn);
        }
    }

    /**
     * 创建商户映射表，处理重复key的情况
     * @param bizMerchants 商户列表
     * @return 商户编码到中文名称的映射
     */
    private Map<String, String> createMerchantMap(List<BizMerchant> bizMerchants) {
        // 策略1：保留第一个出现的值（推荐）
        return bizMerchants.stream()
                .filter(merchant -> StringUtils.isNotBlank(merchant.getMerchantCode())
                        && StringUtils.isNotBlank(merchant.getMerchantNameCn()))
                .collect(Collectors.toMap(
                        BizMerchant::getMerchantCode,
                        BizMerchant::getMerchantNameCn,
                        (existing, replacement) -> existing, // 保留第一个出现的值
                        LinkedHashMap::new // 保持插入顺序
                ));

        // 其他可选策略（根据业务需求选择）：

        // 策略2：保留最后一个出现的值
        // (existing, replacement) -> replacement

        // 策略3：合并重复的值
        // (existing, replacement) -> existing + "|" + replacement

        // 策略4：选择更长的名称
        // (existing, replacement) -> existing.length() >= replacement.length() ? existing : replacement
    }

    /**
     * 安全地获取商户中文名称
     * @param bizMerchantMap 商户映射表
     * @param merchantCode 商户编码
     * @return 商户中文名称，如果不存在则返回空字符串
     */
    private String getMerchantNameSafely(Map<String, String> bizMerchantMap, String merchantCode) {
        if (StringUtils.isBlank(merchantCode)) {
            return "";
        }
        String merchantNameCn = bizMerchantMap.get(merchantCode);
        return StringUtils.isNotBlank(merchantNameCn) ? merchantNameCn : "";
    }

    /**
     * 确认
     * @param bizIAuxmatForContractHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("确认接口")
    @PostMapping("confirm")
    public ResultObject confirmDataStatus(@RequestBody BizIAuxmatForContractHeadParam bizIAuxmatForContractHeadParam, UserInfoToken userInfo) {
        ResultObject resultObject = bizIAuxmatForContractHeadService.confirmDataStatus(bizIAuxmatForContractHeadParam, userInfo);
        return resultObject;
    }

    /**
     * 发送审核
     * @param bizIAuxmatForContractHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("发送审核接口")
    @PostMapping("sendAudit")
    public ResultObject sendAudit(@RequestBody BizIAuxmatForContractHeadParam bizIAuxmatForContractHeadParam, UserInfoToken userInfo) {
        ResultObject resultObject = bizIAuxmatForContractHeadService.sendAudit(bizIAuxmatForContractHeadParam, userInfo);
        return resultObject;
    }

    /**
     * 作废
     * @param bizIAuxmatForContractHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("作废接口")
    @PostMapping("cancel")
    public ResultObject cancel(@RequestBody BizIAuxmatForContractHeadParam bizIAuxmatForContractHeadParam, UserInfoToken userInfo) {
        ResultObject resultObject = bizIAuxmatForContractHeadService.cancelDataStatus(bizIAuxmatForContractHeadParam, userInfo);
        return resultObject;
    }
}
