package com.dcjet.cs.api.importedCigarettes;
import com.dcjet.cs.baseInfoCustomerParams.model.BaseInfoCustomerParams;
import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.importedCigarettes.BizIContractHeadDto;
import com.dcjet.cs.dto.importedCigarettes.BizIContractHeadParam;
import com.dcjet.cs.dto.importedCigarettes.BizIContractHeadExportParam;
import com.dcjet.cs.importedCigarettes.service.BizIContractHeadService;
import com.dcjet.cs.util.CommonEnum;
import com.xdo.domain.KeyValuePair;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import com.xdo.springboot.annotation.FuYunMenuAuthentication;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-3-7
 */
@RestController
@RequestMapping("v1/bizIContractHead")
@Api(tags = "接口")
public class BizIContractHeadController extends BaseController {
    @Resource
    private BizIContractHeadService bizIContractHeadService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    @Resource
    private BizMerchantMapper bizMerchantMapper;
    /**
     * @param bizIContractHeadParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    @FuYunMenuAuthentication("/tobacco/importedCigarettes/contract")
    public ResultObject<List<BizIContractHeadDto>> getListPaged(@RequestBody BizIContractHeadParam bizIContractHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        bizIContractHeadParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<BizIContractHeadDto>> paged = bizIContractHeadService.getListPaged(bizIContractHeadParam, pageParam);
        return paged;
    }

    @ApiOperation("分页查询接口")
    @PostMapping("planList")
    public ResultObject<List<BizIContractHeadDto>> getPlanListPaged(@RequestBody BizIContractHeadParam bizIContractHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        bizIContractHeadParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<BizIContractHeadDto>> paged = bizIContractHeadService.getPlanListPaged(bizIContractHeadParam, pageParam);
        return paged;
    }

    /**
     * @param bizIContractHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizIContractHeadDto> insert(@Valid @RequestBody BizIContractHeadParam bizIContractHeadParam, UserInfoToken userInfo) {
        ResultObject<BizIContractHeadDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizIContractHeadDto bizIContractHeadDto = bizIContractHeadService.insert(bizIContractHeadParam, userInfo);
        if (bizIContractHeadDto != null) {
            resultObject.setData(bizIContractHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizIContractHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizIContractHeadDto> update(@PathVariable String sid, @Valid @RequestBody BizIContractHeadParam bizIContractHeadParam, UserInfoToken userInfo) {
        bizIContractHeadParam.setSid(sid);
        BizIContractHeadDto bizIContractHeadDto = bizIContractHeadService.update(bizIContractHeadParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizIContractHeadDto != null) {
            resultObject.setData(bizIContractHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizIContractHeadService.delete(sids, userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizIContractHeadExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizIContractHeadDto> bizIContractHeadDtos = bizIContractHeadService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizIContractHeadDtos, userInfo);

        List<KeyValuePair<String, String>> modifiedHeaders = new ArrayList<>(exportParam.getHeader());
        for (int i = 0; i < modifiedHeaders.size(); i++) {
            KeyValuePair<String, String> entry = modifiedHeaders.get(i);
            if ("shortOverPercent".equals(entry.getKey())) {
                // 替换为新的键值对（保持值不变，仅修改键）
                modifiedHeaders.set(i, new KeyValuePair<>("shortOverPercentStr", entry.getValue()));
            }
            if ("planYear".equals(entry.getKey())) {
                modifiedHeaders.set(i, new KeyValuePair<>("planYearStr", entry.getValue()));
            }
        }
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), modifiedHeaders, bizIContractHeadDtos);
    }
    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizIContractHeadDto> list, UserInfoToken userInfo) {
        BizMerchant bizMerchant = new BizMerchant();
        bizMerchant.setTradeCode(userInfo.getCompany());
        List<BizMerchant> bizMerchants = bizMerchantMapper.getList(bizMerchant);
        Map<String, String> bizMerchantMap = bizMerchants.stream().collect(Collectors.toMap(
                BizMerchant::getMerchantCode,
                BizMerchant::getMerchantNameCn
        ));
        for(BizIContractHeadDto item : list) {
            item.setBusinessType(CommonEnum.businessTypeEnum.getValue(item.getBusinessType()));
            item.setDataStatus(CommonEnum.OrderStatusEnum.getValue(item.getDataStatus()));
            item.setApprovalStatus(CommonEnum.OrderApprStatusEnum.getValue(item.getApprovalStatus()));
            item.setExportCountry(pCodeHolder.getValue(PCodeType.COUNTRY_OUTDATED, item.getExportCountry()));
            item.setLoadingPort(pCodeHolder.getValue(PCodeType.PORT_LIN, item.getLoadingPort()));
            item.setArrivalPort(pCodeHolder.getValue(PCodeType.PORT_LIN, item.getArrivalPort()));
//            if (StringUtils.isNotBlank(item.getPriceTermPort())) {
//                item.setPriceTermPort("0".equals(item.getPriceTermPort()) ? "0 起运港" : "1 目的港");
//            }
            if (StringUtils.isNotBlank(item.getHalfYear())){
                item.setHalfYear("0".equals(item.getHalfYear())? "0 上半年" : "1 下半年");
            }
            item.setBuyer(item.getBuyer() + " " + (StringUtils.isBlank(bizMerchantMap.get(item.getBuyer()))? "" : bizMerchantMap.get(item.getBuyer())));
            item.setSeller(item.getSeller() + " " + (StringUtils.isBlank(bizMerchantMap.get(item.getSeller()))? "" : bizMerchantMap.get(item.getSeller())));
            if (item.getShortOverPercent() != null) {
                DecimalFormat df = new DecimalFormat("#.##");  // 最多保留两位小数，且自动省略末尾的零
                String formattedValue = df.format(item.getShortOverPercent());
                item.setShortOverPercentStr(formattedValue + "%");
            }
            if (item.getPlanYear() != null) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
                item.setPlanYearStr(sdf.format(item.getPlanYear()));
            }
        }
    }

    /**
     * 根据常用标志 获取海关参数信息
     * @param params
     * @param type
     * @param userInfo
     * @return
     */
    @PostMapping("customsList/{type}")
    public ResultObject getMerchantSelectByType(@RequestBody BaseInfoCustomerParams params, @PathVariable String type, UserInfoToken userInfo) {
        ResultObject resultObject = bizIContractHeadService.getParamsSelectByType(params,type,userInfo);
        return resultObject;
    }

    /**
     * 获取本模块供应商下拉数据源
     */
    @PostMapping("sellerList")
    public ResultObject getSellerList(@RequestBody BizIContractHeadParam bizIContractHeadParam, UserInfoToken userInfo) {
        ResultObject resultObject = bizIContractHeadService.getSellerList(bizIContractHeadParam, userInfo);
        return resultObject;
    }

    /**
     * 确认
     * @param bizIContractHeadParam
     * @param userInfo
     * @return
     */
    @PostMapping("confirm")
    public ResultObject confirmDataStatus(@RequestBody BizIContractHeadParam bizIContractHeadParam, UserInfoToken userInfo) {
        ResultObject resultObject = bizIContractHeadService.confirmDataStatus(bizIContractHeadParam, userInfo);
        return resultObject;
    }

    /**
     * 发送审核
     * @param bizIContractHeadParam
     * @param userInfo
     * @return
     */
    @PostMapping("sendAudit")
    public ResultObject sendAudit(@RequestBody BizIContractHeadParam bizIContractHeadParam, UserInfoToken userInfo) {
        ResultObject resultObject = bizIContractHeadService.sendAudit(bizIContractHeadParam, userInfo);
        return resultObject;
    }

    /**
     * 作废
     * @param bizIContractHeadParam
     * @param userInfo
     * @return
     */
    @PostMapping("cancel")
    public ResultObject cancel(@RequestBody BizIContractHeadParam bizIContractHeadParam, UserInfoToken userInfo) {
        ResultObject resultObject = bizIContractHeadService.cancelDataStatus(bizIContractHeadParam, userInfo);
        return resultObject;
    }

    /**
     * 校验是否存在基础状态非2作废的
     * @param bizIContractHeadParam
     * @param userInfo
     * @return
     */
    @PostMapping("checkStatus")
    public ResultObject checkStatus(@RequestBody BizIContractHeadParam bizIContractHeadParam, UserInfoToken userInfo) {
        ResultObject resultObject = bizIContractHeadService.checkStatus(bizIContractHeadParam, userInfo);
        return resultObject;
    }

    /**
     * 版本复制
     * @param bizIContractHeadParam
     * @param userInfo
     * @return
     */
    @PostMapping("copy")
    public ResultObject versionCopy(@RequestBody BizIContractHeadParam bizIContractHeadParam, UserInfoToken userInfo) {
        ResultObject resultObject = bizIContractHeadService.versionCopy(bizIContractHeadParam, userInfo);
        return resultObject;
    }
}
