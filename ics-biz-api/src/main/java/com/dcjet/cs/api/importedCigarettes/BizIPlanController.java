package com.dcjet.cs.api.importedCigarettes;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.dec.BizIOrderHeadParam;
import com.dcjet.cs.dto.importedCigarettes.*;
import com.dcjet.cs.importedCigarettes.service.BizIPlanListService;
import com.dcjet.cs.importedCigarettes.service.BizIPlanService;
import com.dcjet.cs.util.CommonEnum;
import com.xdo.domain.KeyValuePair;
import com.xdo.pcode.service.PCodeHolder;
import org.apache.commons.lang3.StringUtils;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import com.xdo.springboot.annotation.FuYunMenuAuthentication;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * generated by Generate dc
 *
 *
 * <AUTHOR>
 * @date: 2025-3-13
 */
@RestController
@RequestMapping("v1/bizIplan")
@Api(tags = "接口")
public class BizIPlanController extends BaseController {
    @Resource
    private BizIPlanService bizIplanService;
    @Resource
    private BizIPlanListService bizIPlanListService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    /**
     * @param bizIPlanParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    @FuYunMenuAuthentication("/tobacco/importedCigarettes/plan")
    public ResultObject<List<BizIPlanDto>> getListPaged(@RequestBody BizIPlanParam bizIPlanParam, PageParam pageParam, UserInfoToken userInfo) {
        bizIPlanParam.setTradeCode(userInfo.getCompany());
        ResultObject<List<BizIPlanDto>> paged = bizIplanService.getListPaged(bizIPlanParam, pageParam);
        return paged;
    }
    /**
     * @param bizIPlanParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizIPlanDto> insert( @RequestBody BizIPlanParam bizIPlanParam, UserInfoToken userInfo) {
        ResultObject<BizIPlanDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizIPlanDto BizIPlanDto = bizIplanService.insert(bizIPlanParam, userInfo);
        if (BizIPlanDto != null) {
            resultObject.setData(BizIPlanDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * 新增表头和表体数据
     * @param bizIPlanWithDetailParam 包含表头和表体数据的参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("新增表头和表体数据")
    @PostMapping("/insertPlanWithDetails")
    public ResultObject<BizIPlanWithDetailDto> insertPlanWithDetails(@Valid @RequestBody BizIPlanWithDetailParam bizIPlanWithDetailParam, UserInfoToken userInfo) {
        ResultObject<BizIPlanWithDetailDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        try {
            BizIPlanWithDetailDto bizIPlanWithDetailDto = bizIplanService.insertPlanWithDetails(bizIPlanWithDetailParam, userInfo);
            if (bizIPlanWithDetailDto != null) {
                resultObject.setData(bizIPlanWithDetailDto);
            } else {
                resultObject.setSuccess(false);
                resultObject.setMessage(ResultObject.INSERT_FAIL);
            }
        } catch (Exception e) {
            resultObject.setSuccess(false);
            resultObject.setMessage(e.getMessage());
        }
        return resultObject;
    }
    /**
     * 修改表头和表体数据
     * @param bizIPlanWithDetailParam 包含表头和表体数据的参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("新增表头和表体数据")
    @PostMapping("/updatePlanWithDetails")
    public ResultObject<BizIPlanWithDetailDto> updatePlanWithDetails(@Valid @RequestBody BizIPlanWithDetailParam bizIPlanWithDetailParam, UserInfoToken userInfo) {
        ResultObject<BizIPlanWithDetailDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        try {
            BizIPlanWithDetailDto bizIPlanWithDetailDto = bizIplanService.updatePlanWithDetails(bizIPlanWithDetailParam, userInfo);
            if (bizIPlanWithDetailDto != null) {
                resultObject.setData(bizIPlanWithDetailDto);
            } else {
                resultObject.setSuccess(false);
                resultObject.setMessage(ResultObject.INSERT_FAIL);
            }
        } catch (Exception e) {
            resultObject.setSuccess(false);
            resultObject.setMessage(e.getMessage());
        }
        return resultObject;
    }


    /**
     * @param sid
     * @param bizIPlanParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizIPlanDto> update(@PathVariable String sid, @Valid @RequestBody BizIPlanParam bizIPlanParam, UserInfoToken userInfo) {
        bizIPlanParam.setSid(sid);
        BizIPlanDto BizIPlanDto = bizIplanService.update(bizIPlanParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (BizIPlanDto != null) {
            resultObject.setData(BizIPlanDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizIplanService.delete(sids, userInfo);
        return resultObject;
    }

    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export( @RequestBody BizIPlanExportParam exportParam, UserInfoToken userInfo) throws Exception{
        // 创建结果列表
        List<BizIPlanListDto> resultList = new ArrayList<>();

        // 检查是否提供了选中的数据
        if (exportParam.getSelectedData() != null && !exportParam.getSelectedData().isEmpty()) {
            // 遍历选中的数据
            for (Object selectedItem : exportParam.getSelectedData()) {
                // 获取选中项的headId
                String headId = null;
                if (selectedItem instanceof Map) {
                    Map<String, Object> item = (Map<String, Object>) selectedItem;
                    headId = (String) item.get("sid");
                }

                if (headId != null) {
                    // 获取表头数据，用于获取计划编号
                    BizIPlanDto headDto = bizIplanService.getById(headId);

                    if (headDto != null) {
                        // 创建查询参数
                        BizIPlanListParam listParam = new BizIPlanListParam();
                        listParam.setHeadId(headId);

                        // 获取表体数据
                        List<BizIPlanListDto> listDtos = bizIPlanListService.selectAll(listParam, userInfo);

                        // 将计划编号添加到每个表体数据中
                        if (listDtos != null && !listDtos.isEmpty()) {
                            for (BizIPlanListDto listDto : listDtos) {
                                // 将表头的计划编号添加到表体数据中
                                listDto.setExtend1(headDto.getPlanId());
                                resultList.add(listDto);
                            }
                        }
                    }
                }
            }
        }

        // 创建导出的表头信息
        List<KeyValuePair<String, String>> header = new ArrayList<>();
        header.add(new KeyValuePair<>("extend1", "计划编号"));
        header.add(new KeyValuePair<>("productName", "商品名称"));
        header.add(new KeyValuePair<>("supplier", "供应商"));
        header.add(new KeyValuePair<>("englishBrand", "英文品牌"));
        header.add(new KeyValuePair<>("origin", "原产地"));
        header.add(new KeyValuePair<>("planQuantity", "计划数量"));
        header.add(new KeyValuePair<>("unit", "单位"));
        header.add(new KeyValuePair<>("curr", "币种"));
        header.add(new KeyValuePair<>("unitPrice", "计划单价"));
        header.add(new KeyValuePair<>("totalAmount", "计划总金额"));
        header.add(new KeyValuePair<>("discountRate", "折扣率"));
        header.add(new KeyValuePair<>("discountAmount", "折扣金额"));

        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), header, resultList);
    }
    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizIPlanDto> list) {
        for(BizIPlanDto item : list) {
            item.setBusinessType(CommonEnum.businessTypeEnum.getValue(item.getBusinessType()));
            item.setHalfYear(CommonEnum.YearConstEnum.getValue(item.getHalfYear()));
            item.setStatus(CommonEnum.OrderStatusEnum.getValue(item.getStatus()));
            item.setApprStatus(CommonEnum.OrderApprStatusEnum.getValue(item.getApprStatus()));
            if(!ObjectUtils.isEmpty(item.getPlanYear())){
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(item.getPlanYear());
                int year = calendar.get(Calendar.YEAR);
                item.setPlanYearStr(String.valueOf(year));
            }
        }
    }

    /**
     * 确认数据状态接口
     * @param sid
     * @param userInfo
     * @return
     */
    @ApiOperation("确认数据状态接口")
    @PostMapping("confirm/{sid}")
    public ResultObject confirmStatus(@PathVariable String sid, UserInfoToken userInfo) {
        return bizIplanService.confirmStatus(sid, userInfo);
    }

    /**
     * @param sid
     * @param userInfo
     * @return
     */
    @ApiOperation("发送审批接口")
    @PostMapping("sendApproval/{sid}")
    public ResultObject sendApproval(@PathVariable String sid, UserInfoToken userInfo) {
        return bizIplanService.sendApproval(sid, userInfo);
    }

    /**
     * @param sid
     * @param userInfo
     * @return
     */
    @ApiOperation("作废接口")
    @PostMapping("invalidate/{sid}")
    public ResultObject invalidate(@PathVariable String sid, UserInfoToken userInfo) {
        return bizIplanService.invalidate(sid, userInfo);
    }


    /**
     * 校验是否存在 同一个订单号是否存在未作废的数据
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("校验是否存在 同一个订单号是否存在未作废的数据")
    @PostMapping("/checkPlanIdNotCancel")
    public ResultObject checkPlanIdNotCancel(@RequestBody BizIPlanParam params, UserInfoToken userInfo) {
        return bizIplanService.checkPlanIdNotCancel(params,userInfo);
    }

    /**
     * 版本复制
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("版本复制")
    @PostMapping("/copyVersion")
    public ResultObject copyVersion(@RequestBody BizIPlanParam params, UserInfoToken userInfo) {
        return bizIplanService.copyVersion(params,userInfo);
    }

    /**
     * 校验单行表体数据
     * @param detail 表体数据
     * @param userInfo 用户信息
     * @return 返回校验结果
     */
    @ApiOperation("校验单行表体数据")
    @PostMapping("/validateDetailData")
    public ResultObject<String> validateDetailData(@Valid @RequestBody BizIPlanListParam detail, UserInfoToken userInfo) {
        try {
            String errorMessage = bizIplanService.validateSingleDetailData(detail);

            if (StringUtils.isBlank(errorMessage)) {
                return ResultObject.createInstance(true, "校验通过", "");
            } else {
                return ResultObject.createInstance(false, "校验失败", errorMessage);
            }
        } catch (Exception e) {
            return ResultObject.createInstance(false, "校验异常: " + e.getMessage(), "");
        }
    }

}
