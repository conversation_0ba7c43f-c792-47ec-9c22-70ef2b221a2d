package com.dcjet.cs.api.dec;


import com.dcjet.cs.dec.service.BizIncomingGoodsHeadService;
import com.dcjet.cs.dto.dec.BizIOrderHeadParam;
import com.dcjet.cs.dto.dec.BizIncomingGoodsHeadDto;
import com.dcjet.cs.dto.dec.BizIncomingGoodsHeadExportParam;
import com.dcjet.cs.dto.dec.BizIncomingGoodsHeadParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
import com.xdo.domain.ResultObject;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.dcjet.cs.common.service.ExcelService;
import com.xdo.pcode.service.PCodeHolder;

/**
* TBizIncomingGoodsHeadController层
*
* <AUTHOR>
* @date 2025-05-22 15:28:59
*/
@RestController
@RequestMapping("v1/bizIncomingGoodsHead")
@Api(tags = "进货管理-表头数据接口")
public class BizIncomingGoodsHeadController {

    @Resource
    private BizIncomingGoodsHeadService tBizIncomingGoodsHeadService;

    @Resource
    private PCodeHolder pCodeHolder;

    @Resource
    private ExcelService excelService;
 
    /**
     * 分页获取进货管理-表头数据数据
     * @param tBizIncomingGoodsHeadParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页获取进货管理-表头数据数据")
    @PostMapping("list")
    public ResultObject<List<BizIncomingGoodsHeadDto>> getListPaged(@RequestBody BizIncomingGoodsHeadParam tBizIncomingGoodsHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizIncomingGoodsHeadDto>> paged = tBizIncomingGoodsHeadService.getListPaged(tBizIncomingGoodsHeadParam, pageParam,userInfo);
        return paged;
    }


    /**
     * @param tBizIncomingGoodsHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping("/insert")
    public ResultObject<BizIncomingGoodsHeadDto> insert(@Valid @RequestBody BizIncomingGoodsHeadParam tBizIncomingGoodsHeadParam, UserInfoToken userInfo) {
        ResultObject<BizIncomingGoodsHeadDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizIncomingGoodsHeadDto tBizIncomingGoodsHeadDto = tBizIncomingGoodsHeadService.insert(tBizIncomingGoodsHeadParam, userInfo);
        if (tBizIncomingGoodsHeadDto != null) {
            resultObject.setData(tBizIncomingGoodsHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sid
     * @param tBizIncomingGoodsHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("/{id}")
    public ResultObject<BizIncomingGoodsHeadDto> update(@PathVariable String id, @Valid @RequestBody BizIncomingGoodsHeadParam tBizIncomingGoodsHeadParam, UserInfoToken userInfo) {
        tBizIncomingGoodsHeadParam.setId(id);
        BizIncomingGoodsHeadDto tBizIncomingGoodsHeadDto = tBizIncomingGoodsHeadService.update(tBizIncomingGoodsHeadParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (tBizIncomingGoodsHeadDto != null) {
            resultObject.setData(tBizIncomingGoodsHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("/{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		tBizIncomingGoodsHeadService.delete(sids, userInfo);
        return resultObject;
    }


    /**
     * 导出数据
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizIncomingGoodsHeadExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizIncomingGoodsHeadDto> tBizIncomingGoodsHeadDtos = tBizIncomingGoodsHeadService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(tBizIncomingGoodsHeadDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), tBizIncomingGoodsHeadDtos);
    }


                        

    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizIncomingGoodsHeadDto> list) {
        for(BizIncomingGoodsHeadDto item : list) {
        
        }
    }



    /**
     * 进货管理-获取供应商列表信息
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("进货管理-获取供应商列表信息")
    @PostMapping("/getSupplierList")
    public ResultObject getSupplierList(BizIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        return tBizIncomingGoodsHeadService.getSupplierList(params,userInfo);
    }


    /**
     * 进货管理-获取港口列表信息
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("进货管理-获取供应商列表信息")
    @PostMapping("/getPortList")
    public ResultObject getPortList(BizIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        return tBizIncomingGoodsHeadService.getPortList(params,userInfo);
    }



    /**
     * 进货管理-获取币制信息
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("进货管理-获取币制信息")
    @PostMapping("/getCurrList")
    public ResultObject getCurrList(BizIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        return tBizIncomingGoodsHeadService.getCurrList(params,userInfo);
    }



    /**
     * 进货管理-获取价格条款
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("进货管理-获取价格条款")
    @PostMapping("/getPriceTermList")
    public ResultObject getPriceTermList(BizIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        return tBizIncomingGoodsHeadService.getPriceTermList(params,userInfo);
    }


    /**
     * 确定 进货信息
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @ApiOperation("确定进货信息")
    @PostMapping("/confirmIncomingGoods")
    public ResultObject<BizIncomingGoodsHeadDto> confirmIncomingGoods(@Valid @RequestBody BizIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        return tBizIncomingGoodsHeadService.confirmIncomingGoods(params,userInfo);
    }


}
