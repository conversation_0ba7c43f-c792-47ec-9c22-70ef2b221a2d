package com.dcjet.cs.api.dec;

import com.dcjet.cs.common.service.ExcelExportService;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.service.ExportService;
import com.dcjet.cs.dec.model.BizIPurchaseHead;
import com.dcjet.cs.dec.model.BizIWarehouseReceiptList;
import com.dcjet.cs.dec.service.BizIOrderHeadService;
import com.dcjet.cs.dec.service.BizIWarehouseReceiptHeadService;
import com.dcjet.cs.dto.dec.*;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.io.FileUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.File;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

/**
* BizIOrderHeadController层
*
* <AUTHOR>
* @date 2025-03-07 15:37:18
*/
@RestController
@RequestMapping("v1/bizIWarehouseReceiptHead")
@Api(tags = "进口管理-入库回单表头接口")
public class BizIWarehouseReceiptHeadController {

    @Resource
    private BizIWarehouseReceiptHeadService service;

    @Resource
    private PCodeHolder pCodeHolder;

    @Resource
    private ExcelService excelService;
 
    /**
     * 分页获取进口管理-订单信息表头数据
     * @param param
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页获取进口管理-表头数据")
    @PostMapping("list")
    public ResultObject<BizIWarehouseReceiptHeadDto> getListPaged(@RequestBody BizIWarehouseReceiptHeadParam param, PageParam pageParam, UserInfoToken userInfo) {
        return service.getListPaged(param, userInfo);

    }



    /**
     * @param sid
     * @param param
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizIWarehouseReceiptHeadDto> update(@PathVariable String sid, @Valid @RequestBody BizIWarehouseReceiptHeadParam param, UserInfoToken userInfo) {
        param.setSid(sid);
        BizIWarehouseReceiptHeadDto dto = service.update(param, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (dto != null) {
            resultObject.setData(dto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }

    //查询作废的进仓编号
    @ApiOperation("查询作废的进仓编号")
    @PostMapping("abandonedData/{status}")
    public ResultObject<List<String>> selectWarehouseEntryNumber(@PathVariable String status, UserInfoToken userInfo) {
        HashMap map=new HashMap();

        List<String> warehouseEntryNumberdto = service.selectWarehouseEntryNumber(status, userInfo);
        map.put("warehouseEntryNumber",warehouseEntryNumberdto);

        List<String> ladingNumberdto = service.selectladingNumber(status, userInfo);
        map.put("ladingNumber",ladingNumberdto);
        ResultObject resultObject = ResultObject.createInstance(true, "查询成功");
        resultObject.setData(map);
        return resultObject;
    }


    //生成入库回单表头表体
    @ApiOperation("生成入库回单表头表体")
    @PostMapping("generateBizIWarehouseReceipt")
    public ResultObject<BizIPurchaseHead> generateBizIWarehouseReceipt(@RequestBody BizIWarehouseReceiptHeadParam param, UserInfoToken userInfo) {
        BizIPurchaseHead bizIPurchaseHead= service.generateBizIWarehouseReceipt(param, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, "操作成功");
        resultObject.setData(bizIPurchaseHead);
        return resultObject;
    }

    @Resource
    private ExportService exportService;

    /**
     * 进仓通知单
     *
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "进仓通知单打印检查")
    @PostMapping("checkPrintNotice")
    public ResultObject checkPrintNotice(@RequestBody BizIWarehouseReceiptHeadParam param, UserInfoToken userInfo) throws Exception {
        //true 表示检查通过
        Boolean result = service.checkPrintNotice(param,userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, "进货信息装箱子表箱号不能为空");
        resultObject.setData(result);
        return resultObject;
    }

    /**
     * 进仓通知单
     *
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "进仓通知单")
    @PostMapping("printNotice")
    public ResponseEntity printNotic(@RequestBody BizIWarehouseReceiptHeadParam param, UserInfoToken userInfo) throws Exception {
        String outName = xdoi18n.XdoI18nUtil.t("进仓通知单") + param.getType();
        String exportFileName = service.printNotic(param,userInfo);
        HttpHeaders h = new HttpHeaders();
        byte[] fileBytes = FileUtils.readFileToByteArray(new File(exportFileName));

        if(".pdf".equals(param.getType())) {
            fileBytes = ExportService.excelToPdf(fileBytes);
            fileBytes = ExportService.pdfSetTitle(fileBytes, outName);
        }

//        outName = URLEncoder.encode(outName, CommonVariable.UTF8);
//        outName = outName.replaceAll("\\+","%20");
        h.setContentDispositionFormData("attachment", URLEncoder.encode(outName, "UTF-8"));
        h.setContentType(MediaType.APPLICATION_OCTET_STREAM);
//        h.add(HttpHeaders.CONTENT_TYPE, "application/vnd.ms-excel;charset=ISO8859-1");
//        h.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
//                + new String(outName.getBytes(CommonVariable.UTF8), "ISO8859-1"));
        return new ResponseEntity<byte[]>(fileBytes, h, HttpStatus.OK);
    }


    /**
     * 打印提货单
     *
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "打印提货单")
    @PostMapping("printOfLading")
    public ResponseEntity printOfLading(@RequestBody BizIWarehouseReceiptHeadParam param, UserInfoToken userInfo) throws Exception {
        String outName = xdoi18n.XdoI18nUtil.t("提货单") + param.getType();
        String exportFileName = service.printOfLading(param,userInfo);

        HttpHeaders h = new HttpHeaders();
        byte[] fileBytes = FileUtils.readFileToByteArray(new File(exportFileName));

        if(".pdf".equals(param.getType())) {
            fileBytes = ExportService.excelToPdf(fileBytes);
            fileBytes = ExportService.pdfSetTitle(fileBytes, outName);
        }

//        outName = URLEncoder.encode(outName, CommonVariable.UTF8);
//        outName = outName.replaceAll("\\+","%20");
        h.setContentDispositionFormData("attachment", URLEncoder.encode(outName, "UTF-8"));
        h.setContentType(MediaType.APPLICATION_OCTET_STREAM);
//        h.add(HttpHeaders.CONTENT_TYPE, "application/vnd.ms-excel;charset=ISO8859-1");
//        h.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
//                + new String(outName.getBytes(CommonVariable.UTF8), "ISO8859-1"));
        return new ResponseEntity<byte[]>(fileBytes, h, HttpStatus.OK);
    }
/*    //生成入库回单表头表体
    @ApiOperation("生成入库回单文件")
    @PostMapping("print/{sid}")
    public ResponseEntity print(@PathVariable String sid, UserInfoToken userInfo) throws Exception {
        ResponseEntity responseEntity =service.print(sid, userInfo);
        return responseEntity;
    }*/


    @ApiOperation(value = "确定")
    @PostMapping("onSure")
    public ResultObject<BizIWarehouseReceiptHeadDto> onSure(@RequestBody BizIWarehouseReceiptHeadParam param, UserInfoToken userInfo) throws Exception {
        BizIWarehouseReceiptHeadDto dto = service.onSure(param, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true,"操作成功");
        if (dto != null) {
            resultObject.setData(dto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage("操作失败");
        }
        return resultObject;
    }

    /**
     * 打印入库回单（支持指定格式）
     * @param sid 主键ID
     * @param fileType 文件类型（pdf 或 xlsx）
     * @param userInfo 用户信息
     * @return 响应实体
     * @throws Exception 可能发生的异常
     */
    @ApiOperation("生成入库回单文件（支持PDF和Excel格式）")
    @PostMapping("print/{sid}/{fileType}")
    public ResponseEntity printWithFormat(@PathVariable String sid, 
                                          @PathVariable String fileType, 
                                          UserInfoToken userInfo) throws Exception {
        return service.print(sid, fileType, userInfo);
    }

}
