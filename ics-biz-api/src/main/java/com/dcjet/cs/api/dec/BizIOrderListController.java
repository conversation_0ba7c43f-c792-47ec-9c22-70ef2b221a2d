package com.dcjet.cs.api.dec;

import com.dcjet.cs.dec.service.BizIOrderHeadService;
import com.dcjet.cs.dto.dec.BizIOrderListDto;
import com.dcjet.cs.dto.dec.BizIOrderListParam;
import com.dcjet.cs.dto.dec.BizIOrderListExportParam;
import com.dcjet.cs.dec.service.BizIOrderListService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
import com.xdo.domain.ResultObject;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.dcjet.cs.common.service.ExcelService;
import com.xdo.pcode.service.PCodeHolder;
import xdoi18n.XdoI18nUtil;

/**
* BizIOrderListController层
*
* <AUTHOR>
* @date 2025-03-07 15:37:37
*/
@RestController
@RequestMapping("v1/bizIOrderList")
@Api(tags = "进口管理-订单信息表头接口")
public class BizIOrderListController{

    @Resource
    private BizIOrderListService bizIOrderListService;

    @Resource
    private PCodeHolder pCodeHolder;

    @Resource
    private ExcelService excelService;

    @Resource
    private BizIOrderHeadService bizIOrderHeadService;
 
    /**
     * 分页获取进口管理-订单信息表头数据
     * @param bizIOrderListParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页获取进口管理-订单信息表头数据")
    @PostMapping("list")
    public ResultObject<List<BizIOrderListDto>> getListPaged(@RequestBody BizIOrderListParam bizIOrderListParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizIOrderListDto>> paged = bizIOrderListService.getListPaged(bizIOrderListParam, pageParam,userInfo);
        return paged;
    }


    /**
     * @param bizIOrderListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizIOrderListDto> insert(@Valid @RequestBody BizIOrderListParam bizIOrderListParam, UserInfoToken userInfo) {
        ResultObject<BizIOrderListDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizIOrderListDto bizIOrderListDto = bizIOrderListService.insert(bizIOrderListParam, userInfo);
        if (bizIOrderListDto != null) {
            resultObject.setData(bizIOrderListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sid
     * @param bizIOrderListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizIOrderListDto> update(@PathVariable String sid, @Valid @RequestBody BizIOrderListParam bizIOrderListParam, UserInfoToken userInfo) {
        bizIOrderListParam.setSid(sid);
        BizIOrderListDto bizIOrderListDto = bizIOrderListService.update(bizIOrderListParam, userInfo);
        // bizIOrderHeadService.checkData(bizIOrderListParam.getHeadId(),"所选数据已产生后续单据，请将后续单据作废后再进行编辑!");
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizIOrderListDto != null) {
            resultObject.setData(bizIOrderListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		bizIOrderListService.delete(sids, userInfo);
        return resultObject;
    }


    /**
     * 获取进口订单表体-数量，金额汇总
     * @param bizIOrderListParam 查询参数
     * @param userInfo 用户信息
     * @return 分页结果
     */
    @ApiOperation("获取进口订单表体-数量，金额汇总")
    @PostMapping("/getITotal")
    public ResultObject getITotal(@Valid @RequestBody BizIOrderListParam bizIOrderListParam,UserInfoToken userInfo) {
        return bizIOrderListService.getITotal(bizIOrderListParam,userInfo);
    }


    /**
     * 导出数据
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizIOrderListExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizIOrderListDto> bizIOrderListDtos = bizIOrderListService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizIOrderListDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizIOrderListDtos);
    }



    @PostMapping("/getOrderListBySid/{sid}")
    public ResultObject getOrderListBySid(@PathVariable String sid, UserInfoToken userInfo) {
        return bizIOrderListService.getOrderListBySid(sid,userInfo);
    }


                        

    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizIOrderListDto> list) {
        for(BizIOrderListDto item : list) {
        
        }
    }


}
