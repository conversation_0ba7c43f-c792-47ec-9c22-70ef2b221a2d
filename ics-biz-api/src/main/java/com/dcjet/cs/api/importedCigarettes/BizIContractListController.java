package com.dcjet.cs.api.importedCigarettes;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.importedCigarettes.BizIContractListDto;
import com.dcjet.cs.dto.importedCigarettes.BizIContractListParam;
import com.dcjet.cs.dto.importedCigarettes.BizIContractListExportParam;
import com.dcjet.cs.importedCigarettes.service.BizIContractListService;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@RestController
@RequestMapping("v1/bizIContractList")
@Api(tags = "接口")
public class BizIContractListController extends BaseController {
    @Resource
    private BizIContractListService bizIContractListService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    /**
     * @param bizIContractListParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BizIContractListDto>> getListPaged(@RequestBody BizIContractListParam bizIContractListParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizIContractListDto>> paged = bizIContractListService.getListPaged(bizIContractListParam, pageParam);
        return paged;
    }
    /**
     * @param bizIContractListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizIContractListDto> insert(@Valid @RequestBody BizIContractListParam bizIContractListParam, UserInfoToken userInfo) {
        ResultObject<BizIContractListDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizIContractListDto bizIContractListDto = bizIContractListService.insert(bizIContractListParam, userInfo);
        if (bizIContractListDto != null) {
            resultObject.setData(bizIContractListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizIContractListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizIContractListDto> update(@PathVariable String sid, @Valid @RequestBody BizIContractListParam bizIContractListParam, UserInfoToken userInfo) {
        bizIContractListParam.setSid(sid);
        BizIContractListDto bizIContractListDto = bizIContractListService.update(bizIContractListParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizIContractListDto != null) {
            resultObject.setData(bizIContractListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizIContractListService.delete(sids, userInfo);
        return resultObject;
    }

    @ApiOperation("汇总表体合同数量及总金额")
    @PostMapping("getContractTotal")
    public ResultObject<BizIContractListDto> getContractTotal(@RequestBody BizIContractListParam bizIContractListParam, UserInfoToken userInfo) {
        return  bizIContractListService.getContractTotal(bizIContractListParam, userInfo);
    }
}
