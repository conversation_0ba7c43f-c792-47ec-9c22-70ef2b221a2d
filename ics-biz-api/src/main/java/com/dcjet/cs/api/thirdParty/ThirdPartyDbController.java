package com.dcjet.cs.api.thirdparty;

import com.dcjet.cs.dto.thirdparty.ThirdPartyDbGenericInsertRequest;
import com.dcjet.cs.dto.thirdparty.ThirdPartyDbInsertRequest;
import com.dcjet.cs.service.ThirdPartyDbService;
import com.xdo.domain.ResultObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 第三方数据库操作控制器
 */
@Slf4j
@RestController
@RequestMapping("/v1/thirdparty/db")
@Api(tags = "第三方数据库操作接口")
public class ThirdPartyDbController {

    @Autowired
    private ThirdPartyDbService thirdPartyDbService;

    /**
     * 向第三方数据库插入数据
     *
     * @param request 插入请求
     * @return 操作结果
     */
    @PostMapping("/insert")
    @ApiOperation(value = "向第三方数据库插入数据", notes = "批量向指定的表中插入数据")
    public ResultObject<Map<String, Object>> insertData(@Valid @RequestBody ThirdPartyDbInsertRequest request) {
        log.info("Received request to insert data into table: {}", request.getTableName());

        try {
            int rowsAffected = thirdPartyDbService.batchInsertData(request.getTableName(), request.getData());

            Map<String, Object> result = new HashMap<>();
            result.put("rowsAffected", rowsAffected);
            result.put("success", true);

            log.info("Successfully inserted {} rows into table: {}", rowsAffected, request.getTableName());
            return ResultObject.createInstance(true, "数据插入成功", result);
        } catch (Exception e) {
            log.error("Failed to insert data into table: {}", request.getTableName(), e);
            return ResultObject.createInstance(false,"插入数据失败: " + e.getMessage());
        }
    }

    /**
     * 使用泛型对象向第三方数据库插入数据
     *
     * @param request 插入请求
     * @param <T> 数据类型
     * @return 操作结果
     */
    @PostMapping("/insert/generic")
    @ApiOperation(value = "使用泛型对象向第三方数据库插入数据", notes = "批量向指定的表中插入泛型对象数据")
    public <T> ResultObject<Map<String, Object>> insertGenericData(@Valid @RequestBody ThirdPartyDbGenericInsertRequest<T> request) {
        log.info("Received request to insert generic data into table: {}", request.getTableName());

        try {
            int rowsAffected = thirdPartyDbService.batchInsertEntities(request.getTableName(), request.getData());

            Map<String, Object> result = new HashMap<>();
            result.put("rowsAffected", rowsAffected);
            result.put("success", true);

            log.info("Successfully inserted {} generic entities into table: {}", rowsAffected, request.getTableName());
            return ResultObject.createInstance(true, "数据插入成功", result);
        } catch (Exception e) {
            log.error("Failed to insert generic data into table: {}", request.getTableName(), e);
            return ResultObject.createInstance(false,"插入数据失败: " + e.getMessage());
        }
    }
}