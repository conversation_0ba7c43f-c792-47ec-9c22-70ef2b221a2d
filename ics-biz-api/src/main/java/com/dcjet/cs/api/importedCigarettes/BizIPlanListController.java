package com.dcjet.cs.api.importedCigarettes;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.importedCigarettes.BizIPlanListDto;
import com.dcjet.cs.dto.importedCigarettes.BizIPlanListParam;
import com.dcjet.cs.dto.importedCigarettes.BizIPlanListExportParam;
import com.dcjet.cs.importedCigarettes.service.BizIPlanListService;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-3-13
 */
@RestController
@RequestMapping("v1/bizIPlanList")
@Api(tags = "接口")
public class BizIPlanListController extends BaseController {
    @Resource
    private BizIPlanListService bizIPlanListService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    /**
     * @param bizIPlanListParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BizIPlanListDto>> getListPaged(@RequestBody BizIPlanListParam bizIPlanListParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizIPlanListDto>> paged = bizIPlanListService.getListPaged(bizIPlanListParam, pageParam);
        return paged;
    }
    /**
     * @param bizIPlanListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizIPlanListDto> insert(@Valid @RequestBody BizIPlanListParam bizIPlanListParam, UserInfoToken userInfo) {
        ResultObject<BizIPlanListDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizIPlanListDto bizIPlanListDto = bizIPlanListService.insert(bizIPlanListParam, userInfo);
        if (bizIPlanListDto != null) {
            resultObject.setData(bizIPlanListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizIPlanListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizIPlanListDto> update(@PathVariable String sid, @Valid @RequestBody BizIPlanListParam bizIPlanListParam, UserInfoToken userInfo) {
        bizIPlanListParam.setSid(sid);
        BizIPlanListDto bizIPlanListDto = bizIPlanListService.update(bizIPlanListParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizIPlanListDto != null) {
            resultObject.setData(bizIPlanListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizIPlanListService.delete(sids, userInfo);
        return resultObject;
    }
}
