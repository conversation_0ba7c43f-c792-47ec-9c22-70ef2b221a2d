package com.dcjet.cs.api.dec;

import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.service.ExportService;
import com.dcjet.cs.dec.dao.BizIReceiptHeadMapper;
import com.dcjet.cs.dec.dao.BizIReceiptListMapper;
import com.dcjet.cs.dec.model.BizIReceiptHead;
import com.dcjet.cs.dec.model.BizIReceiptList;
import com.dcjet.cs.dec.service.BizIReceiptHeadService;
import com.dcjet.cs.dto.dec.BizIOrderHeadParam;
import com.dcjet.cs.dto.dec.BizIReceiptExportDto;
import com.dcjet.cs.dto.dec.BizIReceiptHeadDto;
import com.dcjet.cs.dto.dec.BizIReceiptHeadParam;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.io.FileUtils;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
* BizIReceiptHeadController层
*
* <AUTHOR>
* @date 2025-03-07 15:37:58
*/
@RestController
@RequestMapping("v1/BizIReceiptHead")
@Api(tags = "进口管理-出库回单表头接口")
public class BizIReceiptHeadController {

    @Resource
    private BizIReceiptHeadService BizIReceiptHeadService;

    @Resource
    private PCodeHolder pCodeHolder;

    @Resource
    private ExcelService excelService;

    @Resource
    private ExportService exportService;

    @Resource
    private BizIReceiptListMapper receiptListMapper;

    @Resource
    private BizIReceiptHeadMapper receiptHeadMapper;
 
    /**
     * 分页获取进口管理-出库回单表头数据
     * @param BizIReceiptHeadParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页获取进口管理-出库回单表头数据")
    @PostMapping("list")
    public ResultObject<List<BizIReceiptHeadDto>> getListPaged(@RequestBody BizIReceiptHeadParam BizIReceiptHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizIReceiptHeadDto>> paged = BizIReceiptHeadService.getListPaged(BizIReceiptHeadParam, pageParam,userInfo);
        return paged;
    }


    /**
     * @param sid
     * @param BizIReceiptHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizIReceiptHeadDto> update(@PathVariable String sid, @Valid @RequestBody BizIReceiptHeadParam BizIReceiptHeadParam, UserInfoToken userInfo) {
        BizIReceiptHeadParam.setSid(sid);
        BizIReceiptHeadDto BizIReceiptHeadDto = BizIReceiptHeadService.update(BizIReceiptHeadParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (BizIReceiptHeadDto != null) {
            resultObject.setData(BizIReceiptHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }


    /**
     * 获取出库回单表头数据 根据订单表头sid
     * @param BizIReceiptHeadParam 订单表头sid
     * @param userInfo 用户信息
     * @return 结果
     */
    @ApiOperation("获取出库回单息表头数据 根据出库回单sid")
    @PostMapping("/getReceiptHeadByOrderSid")
    public ResultObject <BizIReceiptHeadDto> getPurchaseHeadByOrderSid(@RequestBody BizIReceiptHeadParam BizIReceiptHeadParam, UserInfoToken userInfo) {
        return BizIReceiptHeadService.getPurchaseHeadByOrderSid(BizIReceiptHeadParam, userInfo);
    }

                        

    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizIReceiptHeadDto> list) {
        for(BizIReceiptHeadDto item : list) {
        
        }
    }

    @ApiOperation("打印")
    @PostMapping("/export/{sid}/{sType}")
    public ResponseEntity exportPriceReport(@PathVariable String sid,@PathVariable String sType, UserInfoToken userInfoToken) throws IOException {
        //dc.export.template=D\:/ics-cs-bck/template/
        //dc.export.temp=D\:/ics-cs-bck/temp/

        BizIReceiptExportDto bizIReceiptExportDto = BizIReceiptHeadService.selectExportMessage(sid,userInfoToken);

        String tempName = "receipt_head.xlsx";
        String outName = xdoi18n.XdoI18nUtil.t("出库回单")+bizIReceiptExportDto.getReceiptNumber() + ".pdf";
        String fileName = UUID.randomUUID().toString() +  ".xlsx";

        if ("excel".equals(sType)) {
            outName = xdoi18n.XdoI18nUtil.t("出库回单")+bizIReceiptExportDto.getReceiptNumber() + ".xlsx";
        }
        String exportFileName = exportService.export(Arrays.asList(bizIReceiptExportDto), bizIReceiptExportDto.getBizIReceiptListDtos(), fileName, tempName);

        HttpHeaders h = new HttpHeaders();
        byte[] fileBytes = FileUtils.readFileToByteArray(new File(exportFileName));

        if("pdf".equals(sType)) {
            fileBytes = ExportService.excelToPdf(fileBytes);
        }
        h.setContentDispositionFormData("attachment", URLEncoder.encode(outName, "UTF-8"));
        h.setContentType(MediaType.APPLICATION_OCTET_STREAM);
//        h.add(HttpHeaders.CONTENT_TYPE, "application/vnd.ms-excel;charset=ISO8859-1");
//        h.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
//                + new String(outName.getBytes(CommonVariable.UTF8), "ISO8859-1"));
        return new ResponseEntity<byte[]>(fileBytes, h, HttpStatus.OK);
    }

}
