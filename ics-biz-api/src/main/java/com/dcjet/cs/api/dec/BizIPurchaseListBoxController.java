package com.dcjet.cs.api.dec;

import com.dcjet.cs.dto.dec.*;
import com.dcjet.cs.dec.service.BizIPurchaseListBoxService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
import com.xdo.domain.ResultObject;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.dcjet.cs.common.service.ExcelService;
import com.xdo.pcode.service.PCodeHolder;
import xdoi18n.XdoI18nUtil;

/**
* BizIPurchaseListBoxController层
*
* <AUTHOR>
* @date 2025-03-16 14:16:02
*/
@RestController
@RequestMapping("v1/bizIPurchaseListBox")
@Api(tags = "进口管理-进货信息表体-装箱子表接口")
public class BizIPurchaseListBoxController{

    @Resource
    private BizIPurchaseListBoxService bizIPurchaseListBoxService;

    @Resource
    private PCodeHolder pCodeHolder;

    @Resource
    private ExcelService excelService;
 
    /**
     * 分页获取进口管理-进货信息表体-装箱子表数据
     * @param bizIPurchaseListBoxParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页获取进口管理-进货信息表体-装箱子表数据")
    @PostMapping("list")
    public ResultObject<List<BizIPurchaseListBoxDto>> getListPaged(@RequestBody BizIPurchaseListBoxParam bizIPurchaseListBoxParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizIPurchaseListBoxDto>> paged = bizIPurchaseListBoxService.getListPaged(bizIPurchaseListBoxParam, pageParam,userInfo);
        return paged;
    }


    /**
     * @param bizIPurchaseListBoxParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizIPurchaseListBoxDto> insert(@Valid @RequestBody BizIPurchaseListBoxParam bizIPurchaseListBoxParam, UserInfoToken userInfo) {
        ResultObject<BizIPurchaseListBoxDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizIPurchaseListBoxDto bizIPurchaseListBoxDto = bizIPurchaseListBoxService.insert(bizIPurchaseListBoxParam, userInfo);
        if (bizIPurchaseListBoxDto != null) {
            resultObject.setData(bizIPurchaseListBoxDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sid
     * @param bizIPurchaseListBoxParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizIPurchaseListBoxDto> update(@PathVariable String sid, @Valid @RequestBody BizIPurchaseListBoxParam bizIPurchaseListBoxParam, UserInfoToken userInfo) {
        bizIPurchaseListBoxParam.setSid(sid);
        BizIPurchaseListBoxDto bizIPurchaseListBoxDto = bizIPurchaseListBoxService.update(bizIPurchaseListBoxParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizIPurchaseListBoxDto != null) {
            resultObject.setData(bizIPurchaseListBoxDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		bizIPurchaseListBoxService.delete(sids, userInfo);
        return resultObject;
    }


    /**
     * 导出数据
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizIPurchaseListBoxExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizIPurchaseListBoxDto> bizIPurchaseListBoxDtos = bizIPurchaseListBoxService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizIPurchaseListBoxDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizIPurchaseListBoxDtos);
    }


    /**
     * 新增装箱子表数据
     * @param params 箱号+进货信息表体数据
     * @param userInfo 用户信息
     * @return ResultObject
     */
    @ApiOperation("新增装箱子表数据")
    @PostMapping("/addPushListBox")
    public ResultObject addPushListBox(@RequestBody @Valid PushListBoxParams params, UserInfoToken userInfo) {
        return bizIPurchaseListBoxService.addPushListBox(params, userInfo);
    }



    /**
     * 获取汇总数据 数量、件数
     * @param param headId 表头sid
     * @param userInfo 用户信息
     * @return 汇总数据
     */
    @ApiOperation("获取汇总数据 数量、件数")
    @PostMapping("/getSumData")
    public ResultObject getSumData(@RequestBody BizIPurchaseListBoxParam param, UserInfoToken userInfo) {
        return bizIPurchaseListBoxService.getSumData(param, userInfo);
    }


                        

    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizIPurchaseListBoxDto> list) {
        for(BizIPurchaseListBoxDto item : list) {
        
        }
    }


    /**
     * 批量更新装箱子表
     * @param params 装箱子表数据
     * @param userInfo 用户信息
     * @return 是否成功更新
     */
    @ApiOperation("批量更新装箱子表")
    @PostMapping("/batchUpdateBoxList")
    public ResultObject batchUpdateBoxList(@RequestBody List<BizIPurchaseListBoxParam> params, UserInfoToken userInfo) {
        return bizIPurchaseListBoxService.batchUpdateBoxList(params, userInfo);
    }


}
