package com.dcjet.cs.config.api.params;


import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.params.TransCodeDto;
import com.dcjet.cs.dto.params.TransCodeParam;
import com.dcjet.cs.dto.payment.NotifyHeadParam;
import com.dcjet.cs.params.service.TransCodeService;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.BaseController;
import com.xdo.springboot.annotation.FuYunMenuAuthentication;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

@RestController
@RequestMapping("v1/transCode")
@Api(tags = "自定义参数-划款参数接口")
public class TransCodeController extends BaseController {
    @Resource
    private TransCodeService transCodeService;
    @Resource
    private CommonService commonService;
    @Resource
    private ExcelService excelService;
    /**
     * @param transCodeParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("企业基础信息分页查询接口")
    @PostMapping("list")
    @FuYunMenuAuthentication("/tobacco/params/transCode")
    public ResultObject<List<TransCodeDto>> getListPaged(@RequestBody TransCodeParam transCodeParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<TransCodeDto>> paged = transCodeService.getListPaged(transCodeParam, pageParam,userInfo);
        return paged;
    }



    /**
     * @param transCodeParam
     * @param userInfo
     * @return
     */
    @ApiOperation("企业基础信息新增接口")
    @PostMapping()
    public ResultObject<TransCodeDto> insert(@Valid @RequestBody TransCodeParam transCodeParam, UserInfoToken userInfo) {
        ResultObject<TransCodeDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        TransCodeDto transCodeDto = transCodeService.insert(transCodeParam, userInfo);
        if (transCodeDto != null) {
            resultObject.setData(transCodeDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param transCodeParam
     * @param userInfo
     * @return
     */
    @ApiOperation("企业基础信息修改接口")
    @PutMapping("{sid}")
    public ResultObject<TransCodeDto> update(@PathVariable String sid, @Valid @RequestBody TransCodeParam transCodeParam, UserInfoToken userInfo) {
        transCodeParam.setId(sid);
        TransCodeDto transCodeDto = transCodeService.update(transCodeParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (transCodeDto != null) {
            resultObject.setData(transCodeDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * 功能描述：企业基础信息删除接口
     * @param sids
     * @return
     */
    @ApiOperation("企业基础信息删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids,UserInfoToken userInfo) throws Exception {
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("删除成功"));
        // 检查是否存在表体数据
        transCodeService.delete(sids,userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return4
     * @throws Exception
     */
    @ApiOperation("企业基础信息Excel数据导出接口")
    @PostMapping("export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<TransCodeParam> exportParam, UserInfoToken userInfo) throws Exception{
        List<TransCodeDto> transCodeDtos = transCodeService.selectAll(exportParam.getExportColumns(), userInfo);
        transCodeDtos = convertForPrint(transCodeDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), transCodeDtos);
    }
    /**
     * 导出pCode转换中文名称
     * @param list
     * @return
     */
    public List<TransCodeDto> convertForPrint(List<TransCodeDto> list) {
        for (TransCodeDto item : list) {

        }
        return list;
    }


//    @ApiOperation("获取下一个流水号")
//    @PostMapping("getNextCode")
//    public ResultObject getNextCode(UserInfoToken userInfo) {
//        ResultObject resultObject = ResultObject.createInstance(true);
//        String code  = transCodeService.getNextCode(userInfo);
//        resultObject.setData(code);
//        return resultObject;
//    }

    /**
     * 复制
     * @param transCodeParam
     * @param userInfo
     * @return
     */
    @PostMapping("copy")
    public ResultObject copy(@RequestBody TransCodeParam transCodeParam, UserInfoToken userInfo) {
        ResultObject resultObject = transCodeService.copy(transCodeParam, userInfo);
        return resultObject;
    }
}
