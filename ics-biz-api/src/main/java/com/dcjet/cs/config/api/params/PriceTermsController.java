package com.dcjet.cs.config.api.params;

import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.params.PriceTermsDto;
import com.dcjet.cs.dto.params.PriceTermsParam;
import com.dcjet.cs.params.service.PriceTermsService;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.BaseController;
import com.xdo.springboot.annotation.FuYunMenuAuthentication;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

@RestController
@RequestMapping("/v1/priceTerms")
@Api(tags = "企业参数库-价格条款")
public class PriceTermsController extends BaseController {
    @Resource
    private PriceTermsService priceTermsService;
    @Resource
    private ExcelService excelService;

    /**
     * 获取分页列表
     *
     * @param packageInfoParam 价格条款参数
     * @param pageParam        分页参数
     * @param userInfo         用户信息
     * @return 价格条款分页列表
     */
    @ApiOperation("获取价格条款分页列表")
    @PostMapping("/list")
    @FuYunMenuAuthentication("/tobacco/params/priceTerms")
    public ResultObject<List<PriceTermsDto>> getListPaged(@RequestBody PriceTermsParam priceTermsParam
            , PageParam pageParam, UserInfoToken<?> userInfo) {
        return this.priceTermsService.getListPaged(priceTermsParam, pageParam, userInfo);
    }


    /**
     * 新增价格条款
     *
     * @param priceTermsParam 价格条款参数
     * @param userInfo        用户信息
     * @return 价格参数传输模型
     */
    @ApiOperation("新增价格条款")
    @PostMapping
    public ResultObject<?> insert(@Valid @RequestBody PriceTermsParam priceTermsParam, UserInfoToken<?> userInfo) {
        PriceTermsDto priceTermsDto = this.priceTermsService.insert(priceTermsParam, userInfo);
        return ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS, priceTermsDto);
    }

    /**
     * 修改价格条款
     *
     * @param sid             主键
     * @param priceTermsParam 价格条款参数
     * @param userInfo        用户信息
     * @return 价格参数传输模型
     */
    @ApiOperation("修改价格条款")
    @PutMapping("/{sid}")
    public ResultObject<?> update(@PathVariable String sid, @Valid @RequestBody PriceTermsParam priceTermsParam
            , UserInfoToken<?> userInfo) {
        priceTermsParam.setSid(sid);
        PriceTermsDto priceTermsDto = this.priceTermsService.update(priceTermsParam, userInfo);
        return ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS, priceTermsDto);
    }

    /**
     * 删除价格条款
     *
     * @param sids     主键列表
     * @param userInfo 用户信息
     * @return 结果对象
     */
    @ApiOperation("删除价格条款")
    @DeleteMapping("/{sids}")
    public ResultObject<?> delete(@PathVariable List<String> sids, UserInfoToken<?> userInfo) {
        this.priceTermsService.deleteBySids(sids, userInfo);
        return ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
    }

    /**
     * 导出价格条款
     *
     * @param exportParam 导出参数
     * @param userInfo    用户信息
     * @return 响应实体
     */
    @ApiOperation("导出价格条款")
    @PostMapping("/export")
    public ResponseEntity<?> export(@RequestBody BasicExportParam<PriceTermsParam> exportParam, UserInfoToken<?> userInfo) {
        List<PriceTermsDto> priceTermsDtoList = this.priceTermsService.getExcelList(exportParam.getExportColumns(), userInfo);
        try {
            return this.excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8)
                    , exportParam.getHeader(), priceTermsDtoList);
        } catch (Exception e) {
            throw new ErrorException(500, "导出失败!");
        }
    }

    /**
     * 获取价格条款下一个流水号
     *
     * @param userInfo 用户信息
     * @return 下一个流水号
     */
    @ApiOperation("获取价格条款下一个流水号")
    @GetMapping("/nextParamCode")
    public ResultObject<String> getNextCode(UserInfoToken<?> userInfo) {
        String nextParamCode = this.priceTermsService.getNextParamCode(userInfo);
        return ResultObject.createInstance(true, "获取成功！", nextParamCode);
    }

    @ApiOperation("获取价格条款分页列表")
    @PostMapping("/listAll")
    public ResultObject<List<PriceTermsDto>> listAll(@RequestBody PriceTermsParam priceTermsParam, UserInfoToken<?> userInfo) {
        List<PriceTermsDto> list = this.priceTermsService.getExcelList(priceTermsParam, userInfo);
        return ResultObject.createInstance(true, "获取成功！", list);
    }
}
