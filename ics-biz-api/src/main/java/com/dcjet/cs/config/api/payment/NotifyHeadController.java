package com.dcjet.cs.config.api.payment;


import com.dcjet.cs.bi.dao.BizMaterialInformationMapper;
import com.dcjet.cs.bi.dao.ExpenseIListMapper;
import com.dcjet.cs.bi.model.BizMaterialInformation;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.service.ExportService;
import com.dcjet.cs.dec.model.BizIReceiptHead;
import com.dcjet.cs.dec.model.BizIReceiptList;
import com.dcjet.cs.dto.base.BasicExportParam;

import com.dcjet.cs.dto.bi.CostIShippingOrderParam;
import com.dcjet.cs.dto.importedCigarettes.BizIContractHeadParam;
import com.dcjet.cs.dto.payment.NotifyHeadDto;
import com.dcjet.cs.dto.payment.NotifyHeadParam;
import com.dcjet.cs.params.dao.EnterpriseRateMapper;
import com.dcjet.cs.params.dao.RateTableMapper;
import com.dcjet.cs.params.model.EnterpriseRate;
import com.dcjet.cs.params.model.RateTable;
import com.dcjet.cs.payment.dao.NotifyHeadMapper;
import com.dcjet.cs.payment.dao.NotifyListMapper;
import com.dcjet.cs.payment.model.NotifyHead;
import com.dcjet.cs.payment.model.NotifyList;
import com.dcjet.cs.payment.service.NotifyHeadService;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.springboot.BaseController;
import com.xdo.springboot.annotation.FuYunMenuAuthentication;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.File;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("v1/notifyHead")
@Api(tags = "基础管理-企业基础信息接口")
public class NotifyHeadController extends BaseController {
    @Resource
    private NotifyHeadService notifyHeadService;
    @Resource
    private NotifyHeadMapper notifyHeadMapper;
    @Resource
    private NotifyListMapper notifyListMapper;
    @Resource
    private ExportService exportService;
    @Resource
    private BizMaterialInformationMapper bizMaterialInformationMapper;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private RateTableMapper rateTableMapper;
    @Resource
    private EnterpriseRateMapper enterpriseRateMapper;
    @Resource
    private CommonService commonService;
    @Resource
    private ExcelService excelService;
    /**
     * @param notifyHeadParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("企业基础信息分页查询接口")
    @PostMapping("list")
    @FuYunMenuAuthentication("/tobacco/payment/notify")
    public ResultObject<List<NotifyHeadDto>> getListPaged(@RequestBody NotifyHeadParam notifyHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<NotifyHeadDto>> paged = notifyHeadService.getListPaged(notifyHeadParam, pageParam,userInfo);
        return paged;
    }



    /**
     * @param notifyHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("企业基础信息新增接口")
    @PostMapping()
    public ResultObject<NotifyHeadDto> insert(@Valid @RequestBody NotifyHeadParam notifyHeadParam, UserInfoToken userInfo) {
        ResultObject<NotifyHeadDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        NotifyHeadDto notifyHeadDto = notifyHeadService.insert(notifyHeadParam, userInfo);
        if (notifyHeadDto != null) {
            resultObject.setData(notifyHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param notifyHeadParam
     * @param userInfo
     * @return
     */
    @ApiOperation("企业基础信息修改接口")
    @PutMapping("{sid}")
    public ResultObject<NotifyHeadDto> update(@PathVariable String sid, @Valid @RequestBody NotifyHeadParam notifyHeadParam, UserInfoToken userInfo) {
        notifyHeadParam.setSid(sid);
        NotifyHeadDto notifyHeadDto = notifyHeadService.update(notifyHeadParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (notifyHeadDto != null) {
            resultObject.setData(notifyHeadDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * 功能描述：企业基础信息删除接口
     * @param sids
     * @return
     */
    @ApiOperation("企业基础信息删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids,UserInfoToken userInfo) throws Exception {
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("删除成功"));
        // 检查是否存在表体数据
        notifyHeadService.delete(sids,userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return4
     * @throws Exception
     */
    @ApiOperation("企业基础信息Excel数据导出接口")
    @PostMapping("export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<NotifyHeadParam> exportParam, UserInfoToken userInfo) throws Exception{
        List<NotifyHeadDto> notifyHeadDtos = notifyHeadService.selectAll(exportParam.getExportColumns(), userInfo);
        notifyHeadDtos = convertForPrint(notifyHeadDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), notifyHeadDtos);
    }
    /**
     * 导出pCode转换中文名称
     * @param list
     * @return
     */
    public List<NotifyHeadDto> convertForPrint(List<NotifyHeadDto> list) {
        for (NotifyHeadDto item : list) {

            if (StringUtils.isNotBlank(item.getBizType())){
                item.setBizType(CommonEnum.businessTypeEnum.getValue(item.getBizType()));
            }

            if (StringUtils.isNotBlank(item.getPrepayFlag())){
                item.setPrepayFlag(CommonEnum.isNotEnum.getValue(item.getPrepayFlag()));
            }
            if (StringUtils.isNotBlank(item.getSendUfida())){
                item.setSendUfida(CommonEnum.isNotEnum.getValue(item.getSendUfida()));
            }
            if (StringUtils.isNotBlank(item.getDocStatus())){
                item.setDocStatus(CommonEnum.OrderStatusEnum.getValue(item.getDocStatus()));
            }

        }
        return list;
    }


    @ApiOperation("获取下一个流水号")
    @PostMapping("getDocNo")
    public ResultObject getDocNo(UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true);
        String code  = notifyHeadService.getDocNo(userInfo);
        resultObject.setData(code);
        return resultObject;
    }
    @ApiOperation("获取汇率")
    @PostMapping("getRate/{curr}")
    public ResultObject getRate(@PathVariable String curr,UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true);


        RateTable rateTable = new RateTable();
        rateTable.setCurr(curr);
        rateTable.setTradeCode(userInfo.getCompany());
        EnterpriseRate enterpriseRate = new EnterpriseRate();
        enterpriseRate.setCurr(curr);
        enterpriseRate.setTradeCode(userInfo.getCompany());
        String rate = null;
        //上浮比例
        List<RateTable> rateTables = rateTableMapper.select(rateTable);
        //汇率
        List<EnterpriseRate> enterpriseRates = enterpriseRateMapper.selectMessage(enterpriseRate);
        if (CollectionUtils.isNotEmpty(rateTables) && CollectionUtils.isNotEmpty(enterpriseRates)){
            RateTable rateTable1 = rateTables.get(0);
            EnterpriseRate enterpriseRate1 = enterpriseRates.get(0);
            if (rateTable1.getFloatRate()!=null && enterpriseRate1.getRate() != null){
                rate = enterpriseRate1.getRate()
                        .multiply(
                                BigDecimal.ONE.add( // 计算 (1 + x/100)
                                        rateTable1.getFloatRate() // 获取浮点率
                                                .divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP) // 除以100，保留4位小数
                                )
                        )
                        .setScale(2, RoundingMode.HALF_UP) // 最终结果保留2位小数
                        .stripTrailingZeros() // 去除末尾零
                        .toPlainString(); // 转换为字符串
            }

        }else{
            throw new ErrorException(400, "未匹配到币种对应的汇率信息！");
        }



        resultObject.setData(rate);
        return resultObject;
    }
    @ApiOperation("获取用户信息")
    @PostMapping("getUserInfo")
    public ResultObject getUserInfo(UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true);
        resultObject.setData(userInfo);
        return resultObject;
    }


    /**
     * 确认
     * @param notifyHeadParam
     * @param userInfo
     * @return
     */
    @PostMapping("confirm")
    public ResultObject confirmDataStatus(@RequestBody NotifyHeadParam notifyHeadParam, UserInfoToken userInfo) {
        ResultObject resultObject = notifyHeadService.confirmDataStatus(notifyHeadParam, userInfo);
        return resultObject;
    }

    /**
     * 作废
     * @param notifyHeadParam
     * @param userInfo
     * @return
     */
    @PostMapping("cancel")
    public ResultObject cancel(@RequestBody NotifyHeadParam notifyHeadParam, UserInfoToken userInfo) {
        ResultObject resultObject = notifyHeadService.cancelDataStatus(notifyHeadParam, userInfo);
        return resultObject;
    }
    /**
     * 退回
     * @param notifyHeadParam
     * @param userInfo
     * @return
     */
    @PostMapping("back")
    public ResultObject back(@RequestBody NotifyHeadParam notifyHeadParam, UserInfoToken userInfo) {
        ResultObject resultObject = notifyHeadService.backDataStatus(notifyHeadParam, userInfo);
        return resultObject;
    }
    /**
     * 复制
     * @param notifyHeadParam
     * @param userInfo
     * @return
     */
    @PostMapping("copy")
    public ResultObject copy(@RequestBody NotifyHeadParam notifyHeadParam, UserInfoToken userInfo) {
        ResultObject resultObject = notifyHeadService.copy(notifyHeadParam, userInfo);
        return resultObject;
    }


    /**
     * 打印付款通知
     * @param notifyHeadParam
     * @param userInfo
     * @return
     */
    @PostMapping("print/{type}")
    public ResponseEntity print(@RequestBody NotifyHeadParam notifyHeadParam,@PathVariable String type, UserInfoToken userInfo) throws Exception {
        String templateName = "remittance_notice.xlsx";
        String outName = xdoi18n.XdoI18nUtil.t("付款通知") + ".xlsx";
        String fileName = java.util.UUID.randomUUID().toString() + ".xlsx";
        NotifyHead notifyHead = notifyHeadMapper.selectByPrimaryKey(notifyHeadParam.getSid());
        List<NotifyList> lists = notifyListMapper.select(new NotifyList() {{
            setHeadId(notifyHead.getSid());
        }});


        convertHeadForPrint(notifyHead,userInfo);
        convertListForPrint(lists,notifyHead,userInfo);


        //按合同号汇总小结
        lists  = summaryByContractNo(lists);



        String exportFileName = exportService.exportNotify(Arrays.asList(notifyHead), lists, fileName, templateName);

        HttpHeaders h = new HttpHeaders();
        byte[] fileBytes = FileUtils.readFileToByteArray(new File(exportFileName));

        if("pdf".equals(type)) {
            outName = xdoi18n.XdoI18nUtil.t("付款通知") + ".pdf";
            fileBytes = ExportService.excelToPdf(fileBytes);
        }

        h.setContentDispositionFormData("attachment", URLEncoder.encode(outName, "UTF-8"));
        h.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        return new ResponseEntity<byte[]>(fileBytes, h, HttpStatus.OK);

    }

    private List<NotifyList> summaryByContractNo(List<NotifyList> lists) {
        // 步骤1: 拆分合同号并分组
        Map<String, List<NotifyList>> contractGroupMap = lists.stream()
                .flatMap(notify -> {
                    String[] contracts = notify.getContractNo().split(",");
                    return Arrays.stream(contracts)
                            .map(contract -> new AbstractMap.SimpleEntry<>(contract, notify));
                })
                .collect(Collectors.groupingBy(
                        Map.Entry::getKey,
                        Collectors.mapping(Map.Entry::getValue, Collectors.toList())
                ));

        // 步骤2: 生成每个合同号组的汇总对象
        List<NotifyList> result = new ArrayList<>();

        contractGroupMap.forEach((contract, group) -> {
            // 添加当前合同号组的所有原始对象
            result.addAll(group);
            // 生成并添加当前组的汇总对象
            NotifyList summary = createSummary(contract, group);
            result.add(summary);
        });

        return result;
    }


    private NotifyList createSummary(String contractNo, List<NotifyList> group) {
        NotifyList summary = new NotifyList();
        summary.setContractNo(contractNo);
        summary.setInvoiceNumber(contractNo+"小计");
        summary.setQty(sumField(group, NotifyList::getQty));
        summary.setPayAmt(sumField(group, NotifyList::getPayAmt));
        summary.setIncludingTax(sumField(group, NotifyList::getIncludingTax));
        summary.setAgencyFees(sumField(group, NotifyList::getAgencyFees));
        summary.setAdvPymtTotal(sumField(group, NotifyList::getAdvPymtTotal));
        summary.setPayAmtRmb(sumField(group, NotifyList::getPayAmtRmb));
        return summary;
    }

    private BigDecimal sumField(List<NotifyList> group, java.util.function.Function<NotifyList, BigDecimal> mapper) {
        return group.stream()
                .map(mapper)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private void convertListForPrint(List<NotifyList> lists,NotifyHead notifyHead,UserInfoToken userInfo) {
        BizMaterialInformation bizMaterialInformation = new BizMaterialInformation();

        BigDecimal payAmtTotal = new BigDecimal(0);
        BigDecimal payAmtRmbTotal = new BigDecimal(0);
        BigDecimal qtyTotal = new BigDecimal(0);
        BigDecimal includingTaxTotal = new BigDecimal(0);
        BigDecimal agencyFeesTotal = new BigDecimal(0);
        BigDecimal advPymtTotalAll = new BigDecimal(0);
        for (NotifyList list : lists) {
            list.setOrderNo(notifyHead.getPayee()+"\r\n"+list.getOrderNo());

            //通过商品名称获取含税单价
            if (StringUtils.isNotBlank(list.getGoodsName())){
                bizMaterialInformation.setGname(list.getGoodsName());
                bizMaterialInformation.setTradeCode(userInfo.getCompany());
                List<BizMaterialInformation> bizMaterialInformations = bizMaterialInformationMapper.checkGNameReturn(bizMaterialInformation);
                if (CollectionUtils.isNotEmpty(bizMaterialInformations)){
                    BizMaterialInformation bizMaterialInformation1 = bizMaterialInformations.get(0);
                    if (bizMaterialInformation1.getIncludingTax()!=null){
                        list.setIncludingTax(bizMaterialInformation1.getIncludingTax());
                    }
                }

            }

            if (list.getIncludingTax()!=null && list.getQty()!=null){
                list.setAgencyFees(list.getIncludingTax().multiply(list.getQty()).multiply(BigDecimal.valueOf(0.04)).setScale(2,RoundingMode.HALF_UP));
            }

            if (list.getAgencyFees()!=null && list.getPayAmtRmb()!=null){
                list.setAdvPymtTotal(list.getAgencyFees().add(list.getPayAmtRmb()));
            }


            if (list.getPayAmt()!=null){
                payAmtTotal = payAmtTotal.add(list.getPayAmt());
            }
            if (list.getPayAmtRmb()!=null){
                payAmtRmbTotal = payAmtRmbTotal.add(list.getPayAmtRmb());
            }
            if (list.getQty()!=null){
                qtyTotal = qtyTotal.add(list.getQty());
            }
            if (list.getIncludingTax()!=null){
                includingTaxTotal = includingTaxTotal.add(list.getIncludingTax());
            }
            if (list.getAgencyFees()!=null){
                agencyFeesTotal = agencyFeesTotal.add(list.getAgencyFees());
            }
            if (list.getAdvPymtTotal()!=null){
                advPymtTotalAll = advPymtTotalAll.add(list.getAdvPymtTotal());
            }

        }

        notifyHead.setPayAmtTotal(payAmtTotal);
        notifyHead.setPayAmtRmbTotal(payAmtRmbTotal);
        notifyHead.setQtyTotal(qtyTotal);
        notifyHead.setIncludingTaxTotal(includingTaxTotal);
        notifyHead.setAgencyFeesTotal(agencyFeesTotal);
        notifyHead.setAdvPymtTotalAll(advPymtTotalAll);
    }

    private void convertHeadForPrint(NotifyHead notifyHead,UserInfoToken userInfo) {
        if (notifyHead.getRate()!=null){
            notifyHead.setRateStr("汇率："+notifyHead.getRate());
        }
    }

}
