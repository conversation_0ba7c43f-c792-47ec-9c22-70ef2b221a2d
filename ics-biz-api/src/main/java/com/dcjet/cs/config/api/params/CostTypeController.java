package com.dcjet.cs.config.api.params;


import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.params.CostTypeDto;
import com.dcjet.cs.dto.params.CostTypeParam;
import com.dcjet.cs.params.service.CostTypeService;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.BaseController;
import com.xdo.springboot.annotation.FuYunMenuAuthentication;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.Collections;
import java.util.List;

@RestController
@RequestMapping("v1/costType")
@Api(tags = "基础管理-企业基础信息接口")
public class CostTypeController extends BaseController {
    @Resource
    private CostTypeService costTypeService;
    @Resource
    private CommonService commonService;
    @Resource
    private ExcelService excelService;
    /**
     * @param costTypeParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("企业基础信息分页查询接口")
    @PostMapping("list")
    @FuYunMenuAuthentication("/tobacco/params/costType")
    public ResultObject<List<CostTypeDto>> getListPaged(@RequestBody CostTypeParam costTypeParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<CostTypeDto>> paged = costTypeService.getListPaged(costTypeParam, pageParam,userInfo);
        return paged;
    }



    /**
     * @param costTypeParam
     * @param userInfo
     * @return
     */
    @ApiOperation("企业基础信息新增接口")
    @PostMapping()
    public ResultObject<CostTypeDto> insert(@Valid @RequestBody CostTypeParam costTypeParam, UserInfoToken userInfo) {
        ResultObject<CostTypeDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        CostTypeDto costTypeDto = costTypeService.insert(costTypeParam, userInfo);
        if (costTypeDto != null) {
            resultObject.setData(costTypeDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param costTypeParam
     * @param userInfo
     * @return
     */
    @ApiOperation("企业基础信息修改接口")
    @PutMapping("{sid}")
    public ResultObject<CostTypeDto> update(@PathVariable String sid, @Valid @RequestBody CostTypeParam costTypeParam, UserInfoToken userInfo) {
        costTypeParam.setSid(sid);
        CostTypeDto costTypeDto = costTypeService.update(costTypeParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (costTypeDto != null) {
            resultObject.setData(costTypeDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * 功能描述：企业基础信息删除接口
     * @param sids
     * @return
     */
    @ApiOperation("企业基础信息删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids,UserInfoToken userInfo) throws Exception {
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("删除成功"));
        // 检查是否存在表体数据
        costTypeService.delete(sids,userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return4
     * @throws Exception
     */
    @ApiOperation("企业基础信息Excel数据导出接口")
    @PostMapping("export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<CostTypeParam> exportParam, UserInfoToken userInfo) throws Exception{
        List<CostTypeDto> costTypeDtos = costTypeService.selectAll(exportParam.getExportColumns(), userInfo);
        costTypeDtos = convertForPrint(costTypeDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), costTypeDtos);
    }
    /**
     * 导出pCode转换中文名称
     * @param list
     * @return
     */
    public List<CostTypeDto> convertForPrint(List<CostTypeDto> list) {
        for (CostTypeDto item : list) {
            if (CollectionUtils.isNotEmpty(item.getBusinessTypeList())){
                StringBuffer sb = new StringBuffer();
                for (String s : item.getBusinessTypeList()) {
                    sb.append(CommonEnum.commonMarkEnum.getValue(s)).append(",");
                }
                item.setCommonFlag(sb.substring(0, sb.length() - 1));
            }
        }
        return list;
    }


    @ApiOperation("获取下一个流水号")
    @PostMapping("getNextCode")
    public ResultObject getNextCode(UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true);
        String code  = costTypeService.getNextCode(userInfo);
        resultObject.setData(code);
        return resultObject;
    }

}
