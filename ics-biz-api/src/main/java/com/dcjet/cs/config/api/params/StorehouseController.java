package com.dcjet.cs.config.api.params;


import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.bi.*;
import com.dcjet.cs.dto.params.StorehouseDto;
import com.dcjet.cs.dto.params.StorehouseParam;
import com.dcjet.cs.params.service.StorehouseService;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.BaseController;
import com.xdo.springboot.annotation.FuYunMenuAuthentication;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.net.URLEncoder;
import java.util.List;

@RestController
@RequestMapping("v1/storehouse")
@Api(tags = "基础管理-企业基础信息接口")
public class StorehouseController extends BaseController {
    @Resource
    private StorehouseService storehouseService;
    @Resource
    private CommonService commonService;
    @Resource
    private ExcelService excelService;
    /**
     * @param storehouseParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("企业基础信息分页查询接口")
    @PostMapping("list")
    @FuYunMenuAuthentication("/tobacco/params/storehouse")
    public ResultObject<List<StorehouseDto>> getListPaged(@RequestBody StorehouseParam storehouseParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<StorehouseDto>> paged = storehouseService.getListPaged(storehouseParam, pageParam,userInfo);
        return paged;
    }



    /**
     * @param storehouseParam
     * @param userInfo
     * @return
     */
    @ApiOperation("企业基础信息新增接口")
    @PostMapping()
    public ResultObject<StorehouseDto> insert(@Valid @RequestBody StorehouseParam storehouseParam, UserInfoToken userInfo) {
        ResultObject<StorehouseDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        StorehouseDto storehouseDto = storehouseService.insert(storehouseParam, userInfo);
        if (storehouseDto != null) {
            resultObject.setData(storehouseDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param storehouseParam
     * @param userInfo
     * @return
     */
    @ApiOperation("企业基础信息修改接口")
    @PutMapping("{sid}")
    public ResultObject<StorehouseDto> update(@PathVariable String sid, @Valid @RequestBody StorehouseParam storehouseParam, UserInfoToken userInfo) {
        storehouseParam.setSid(sid);
        StorehouseDto storehouseDto = storehouseService.update(storehouseParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (storehouseDto != null) {
            resultObject.setData(storehouseDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * 功能描述：企业基础信息删除接口
     * @param sids
     * @return
     */
    @ApiOperation("企业基础信息删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids,UserInfoToken userInfo) throws Exception {
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("删除成功"));
        // 检查是否存在表体数据
        storehouseService.delete(sids,userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return4
     * @throws Exception
     */
    @ApiOperation("企业基础信息Excel数据导出接口")
    @PostMapping("export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<StorehouseParam> exportParam, UserInfoToken userInfo) throws Exception{
        List<StorehouseDto> storehouseDtos = storehouseService.selectAll(exportParam.getExportColumns(), userInfo);
        storehouseDtos = convertForPrint(storehouseDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), storehouseDtos);
    }
    /**
     * 导出pCode转换中文名称
     * @param list
     * @return
     */
    public List<StorehouseDto> convertForPrint(List<StorehouseDto> list) {
        for (StorehouseDto item : list) {

        }
        return list;
    }


    @ApiOperation("获取下一个流水号")
    @PostMapping("getNextCode")
    public ResultObject getNextCode(UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true);
        String code  = storehouseService.getNextCode(userInfo);
        resultObject.setData(code);
        return resultObject;
    }

}
