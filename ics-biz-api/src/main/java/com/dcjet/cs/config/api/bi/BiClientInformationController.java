package com.dcjet.cs.config.api.bi;

import com.dcjet.cs.bi.service.BiClientInformationService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.bi.*;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.net.URLEncoder;
import java.util.List;

@RestController
@RequestMapping("v1/biClientInformation")
@Api(tags = "基础管理-企业基础信息接口")
public class BiClientInformationController extends BaseController {
    @Resource
    private BiClientInformationService biClientInformationService;
    @Resource
    private CommonService commonService;
    @Resource
    private ExcelService excelService;
    /**
     * @param biClientInformationParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("企业基础信息分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BiClientInformationDto>> getListPaged(@RequestBody BiClientInformationParam biClientInformationParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BiClientInformationDto>> paged = biClientInformationService.getListPaged(biClientInformationParam, pageParam,userInfo);
        return paged;
    }

    /**
     * @param biClientInformationParam
     * @param userInfo
     * @return
     */
    @ApiOperation("企业基础信息新增接口")
    @PostMapping()
    public ResultObject<BiClientInformationDto> insert(@Valid @RequestBody BiClientInformationParam biClientInformationParam, UserInfoToken userInfo) {
        ResultObject<BiClientInformationDto> resultObject = biClientInformationService.insert(biClientInformationParam, userInfo);
        return resultObject;
    }
    /**
     * @param sid
     * @param biClientInformationParam
     * @param userInfo
     * @return
     */
    @ApiOperation("企业基础信息修改接口")
    @PutMapping("{sid}")
    public ResultObject<BiClientInformationDto> update(@PathVariable String sid, @Valid @RequestBody BiClientInformationParam biClientInformationParam, UserInfoToken userInfo) {
        biClientInformationParam.setSid(sid);
        return biClientInformationService.update(biClientInformationParam, userInfo);
    }
    // TODO patch api
    /**
     * 功能描述：企业基础信息删除接口
     * @param sids
     * @return
     */
    @ApiOperation("企业基础信息删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids,UserInfoToken userInfo) throws Exception {
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("删除成功"));
        // 检查是否存在表体数据
        biClientInformationService.delete(sids,userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return4
     * @throws Exception
     */
    @ApiOperation("企业基础信息Excel数据导出接口")
    @PostMapping("export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<BiClientInformationParam> exportParam, UserInfoToken userInfo) throws Exception{
        List<BiClientInformationDto> biClientInformationDtos = biClientInformationService.selectAll(exportParam.getExportColumns(), userInfo);
        biClientInformationDtos = convertForPrint(biClientInformationDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), biClientInformationDtos);
    }
    /**
     * 导出pCode转换中文名称
     * @param list
     * @return
     */
    public List<BiClientInformationDto> convertForPrint(List<BiClientInformationDto> list) {
        for (BiClientInformationDto item : list) {
            item.setMasterCustoms(commonService.convertPCode(item.getMasterCustoms(), PCodeType.CUSTOMS_REL));
            if ("0".equals(item.getAuthorize())) {
                item.setAuthorize("0 未授权");
            } else if ("1".equals(item.getAuthorize())) {
                item.setAuthorize("1 已授权");
            }
            if (StringUtils.equals(item.getStatus(),"0")) {
                item.setStatus("启用");
            } else if (StringUtils.equals(item.getStatus(),"1")) {
                item.setStatus("停用");
            }
        }
        return list;
    }
    @ApiOperation("根据客户类型企业下的数据")
    @PostMapping("getListByCustomerType/{customerType}")
    public ResultObject getListByCustomerType(@PathVariable("customerType") String customerType,UserInfoToken userInfo)
    {
        return  biClientInformationService.getListByCustomerType(customerType,userInfo);
    }
    /**
     * 功能描述: 查询
     *
     * <AUTHOR> 沈振宇
     * @version :   1.0
     * @date：2018-10-26
     * @param: rpObj 系统封装查询
     * @param: session
     */
    @ApiOperation("combox下拉所有数据")
    @PostMapping("selectComboxByCode/{customerTypes}")
    public ResultObject selectComboxByCode(@PathVariable List<String> customerTypes,UserInfoToken userInfo) {
        return biClientInformationService.selectComboxByCode(customerTypes, userInfo);
    }

    @ApiOperation("combox下拉所有数据")
    @PostMapping("selectComboxByCode/{customerTypes}/{isLoadStopData}")
    public ResultObject selectComboxByCode(@PathVariable List<String> customerTypes,@PathVariable Boolean isLoadStopData
            ,UserInfoToken userInfo) {
        return biClientInformationService.selectComboxByCode(customerTypes,isLoadStopData, userInfo);
    }

    /**
     * 功能描述：根据客户类型获取数据
     * @param customerType
     * @param userInfo
     * @return
     */
    @ApiOperation("根据客户类型获取数据")
    @PostMapping("selectKeyValueListByCustomerType/{customerType}")
    public ResultObject selectKeyValueListByCustomerType(@ApiParam("客户类型(供应商 PRD、客户CLI、货代FOD、报关行CUT、企业COM) ")
                                                         @PathVariable("customerType") String customerType, UserInfoToken userInfo) {
        return biClientInformationService.getComboxByCode(customerType,userInfo);
    }

    /**
     * 功能描述: 根据申报企业海关代码获取申报企业企业信用代码
     *
     * @auther: 张军林
     * @date: 2019/07/19 14:20
     * @param: customerCode
     * @param: session
     * @return: com.dcits.base.model.ResultObject
     */
    @ApiOperation("根据申报企业海关代码获取申报企业企业信用代码")
    @PostMapping("getCreditCode/{customerCode}")
    public ResultObject getCreditCode(@NotNull @PathVariable @ApiParam(name="customerCode",value="申报企业海关代码") String customerCode, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true);
        BiClientInformationDto biClientInformationDto = biClientInformationService.getCreditCode(customerCode,userInfo);
        resultObject.setData(biClientInformationDto != null ? biClientInformationDto.getCreditCode() : null);
        resultObject.setMessage(xdoi18n.XdoI18nUtil.t("查询成功"));
        return resultObject;
    }

    /**
     * @param transformParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("转换信息获取接口")
    @PostMapping("getTransformInfo")
    public ResultObject<BiTransformDto> getTransformInfo(@Valid @RequestBody BiTransformParam transformParam, UserInfoToken userInfo) throws Exception {
        return biClientInformationService.getTransformInfo(transformParam, userInfo);
    }

    @ApiOperation("查询报关行和货代下拉数据(代理设置专用)")
    @GetMapping("agent/list")
    public ResultObject<List<BiAeoDto>> selectCutAndFodInfo(UserInfoToken userInfoToken) {
        ResultObject resultObject = ResultObject.createInstance(true);
        resultObject.setData(biClientInformationService.selectCutAndFodInfo(userInfoToken.getCompany()));
        return resultObject;
    }

}
