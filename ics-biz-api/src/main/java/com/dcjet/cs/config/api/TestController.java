package com.dcjet.cs.config.api;

import com.dcjet.cs.common.dao.GwstdHttpConfigMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("v1/test")
@Api(tags = "测试接口")
public class TestController {

    @Resource
    GwstdHttpConfigMapper gwstdHttpConfigMapper;
    @ApiOperation("根据数据SID获取审核列表")
    @PostMapping("/selectListBySid")
    public String selectListBySid() {
        String a =gwstdHttpConfigMapper.selecttest();
        return a;
    }
}
