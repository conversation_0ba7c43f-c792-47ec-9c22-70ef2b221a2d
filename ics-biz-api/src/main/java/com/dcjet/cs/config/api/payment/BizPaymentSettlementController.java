package com.dcjet.cs.config.api.payment;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.payment.BizPaymentSettlementDto;
import com.dcjet.cs.dto.payment.BizPaymentSettlementParam;
import com.dcjet.cs.dto.payment.BizPaymentSettlementExportParam;
import com.dcjet.cs.dto.payment.NotifyHeadParam;
import com.dcjet.cs.payment.service.BizPaymentSettlementService;
import com.dcjet.cs.util.CommonEnum;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import com.xdo.springboot.annotation.FuYunMenuAuthentication;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-3-26
 */
@RestController
@RequestMapping("v1/bizPaymentSettlement")
@Api(tags = "接口")
public class BizPaymentSettlementController extends BaseController {
    @Resource
    private BizPaymentSettlementService bizPaymentSettlementService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    /**
     * @param bizPaymentSettlementParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    @FuYunMenuAuthentication("/tobacco/payment/settlement")
    public ResultObject<List<BizPaymentSettlementDto>> getListPaged(@RequestBody BizPaymentSettlementParam bizPaymentSettlementParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizPaymentSettlementDto>> paged = bizPaymentSettlementService.getListPaged(bizPaymentSettlementParam, pageParam);
        return paged;
    }
    /**
     * @param bizPaymentSettlementParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizPaymentSettlementDto> insert(@Valid @RequestBody BizPaymentSettlementParam bizPaymentSettlementParam, UserInfoToken userInfo) {
        ResultObject<BizPaymentSettlementDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizPaymentSettlementDto bizPaymentSettlementDto = bizPaymentSettlementService.insert(bizPaymentSettlementParam, userInfo);
        if (bizPaymentSettlementDto != null) {
            resultObject.setData(bizPaymentSettlementDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizPaymentSettlementParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizPaymentSettlementDto> update(@PathVariable String sid, @Valid @RequestBody BizPaymentSettlementParam bizPaymentSettlementParam, UserInfoToken userInfo) {
        bizPaymentSettlementParam.setSid(sid);
        BizPaymentSettlementDto bizPaymentSettlementDto = bizPaymentSettlementService.update(bizPaymentSettlementParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizPaymentSettlementDto != null) {
            resultObject.setData(bizPaymentSettlementDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizPaymentSettlementService.delete(sids, userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizPaymentSettlementExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BizPaymentSettlementDto> bizPaymentSettlementDtos = bizPaymentSettlementService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizPaymentSettlementDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), bizPaymentSettlementDtos);
    }
    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BizPaymentSettlementDto> list) {
        for(BizPaymentSettlementDto item : list) {
            item.setBusinessType(CommonEnum.businessTypeEnum.getValue(item.getBusinessType()));
            item.setStatus(CommonEnum.OrderStatusEnum.getValue(item.getStatus()));
            item.setSendToUf(item.getSendToUf()+" "+CommonEnum.isNotEnum.getValue(item.getSendToUf()));
            item.setCurr(item.getCurr()+" "+pCodeHolder.getValue(PCodeType.CURR, item.getCurr()));
        }
    }

    /**
     * 确认数据状态接口
     * @param sid
     * @param userInfo
     * @return
     */
    @ApiOperation("确认数据状态接口")
    @PostMapping("confirm/{sid}")
    public ResultObject confirmStatus(@PathVariable String sid, UserInfoToken userInfo) {
        return bizPaymentSettlementService.confirmStatus(sid, userInfo);
    }

    /**
     * 退回
     * @param bizPaymentSettlementParam
     * @param userInfo
     * @return
     */
    @PostMapping("back")
    public ResultObject back(@RequestBody BizPaymentSettlementParam bizPaymentSettlementParam, UserInfoToken userInfo) {
        ResultObject resultObject = bizPaymentSettlementService.backDataStatus(bizPaymentSettlementParam, userInfo);
        return resultObject;
    }

    /**
     * @param sid
     * @param userInfo
     * @return
     */
    @ApiOperation("作废接口")
    @PostMapping("invalidate/{sid}")
    public ResultObject invalidate(@PathVariable String sid, UserInfoToken userInfo) {
        return bizPaymentSettlementService.invalidate(sid, userInfo);
    }

}
