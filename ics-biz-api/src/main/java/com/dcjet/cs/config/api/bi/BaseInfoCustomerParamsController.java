package com.dcjet.cs.config.api.bi;

import com.dcjet.cs.baseInfoCustomerParams.model.BaseInfoCustomerParams;
import com.dcjet.cs.baseInfoCustomerParams.service.BaseInfoCustomerParamsService;
import com.dcjet.cs.common.service.ExcelService;

import com.dcjet.cs.dto.baseInfoCustomerParams.BaseInfoCustomerParamsDto;
import com.dcjet.cs.dto.baseInfoCustomerParams.BaseInfoCustomerParamsParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.springboot.BaseController;
import com.xdo.springboot.annotation.FuYunMenuAuthentication;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("v1/baseInfoCustomerParams")
@Api(tags = "海关参数自定义接口")
public class BaseInfoCustomerParamsController extends BaseController {
    @Resource
    private BaseInfoCustomerParamsService service;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;

    /**
     * @param params
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("企业参数库分页查询接口")
    @PostMapping("list")
    @FuYunMenuAuthentication("/tobacco/customsParams/curr")
    public ResultObject<List<BaseInfoCustomerParamsDto>> getListPaged(@RequestBody BaseInfoCustomerParamsParam params, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BaseInfoCustomerParamsDto>> paged = service.getListPaged(params, pageParam, userInfo);
        return paged;
    }

    /**
     * @param params
     * @param userInfo
     * @return
     */
    @ApiOperation("企业参数库新增接口")
    @PostMapping()
    public ResultObject<BaseInfoCustomerParamsDto> insert(@Valid @RequestBody BaseInfoCustomerParamsParam params, UserInfoToken userInfo) {
        ResultObject<BaseInfoCustomerParamsDto> resultObject = service.insert(params, userInfo);
        return resultObject;
    }

    /**
     * @param sid
     * @param params
     * @param userInfo
     * @return
     */
    @ApiOperation("企业参数库修改接口")
    @PutMapping("{sid}")
    public ResultObject<BaseInfoCustomerParamsDto> update(@PathVariable String sid, @Valid @RequestBody BaseInfoCustomerParamsParam params, UserInfoToken userInfo) {
        params.setSid(sid);
        ResultObject resultObject = service.update(params, userInfo);
        return resultObject;
    }
    // TODO patch api

    /**
     * @param sids
     * @return
     */
    @ApiOperation("企业参数库删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids) {
        ResultObject resultObject = ResultObject.createInstance(true,"删除成功");
        // 检查是否存在表体数据
        service.delete(sids);
        return resultObject;
    }
}
