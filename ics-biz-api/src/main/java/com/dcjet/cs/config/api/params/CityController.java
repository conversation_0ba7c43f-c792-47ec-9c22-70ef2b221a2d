package com.dcjet.cs.config.api.params;

import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.params.CityDto;
import com.dcjet.cs.dto.params.CityParam;
import com.dcjet.cs.params.service.CityService;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.BaseController;
import com.xdo.springboot.annotation.FuYunMenuAuthentication;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

@RestController
@RequestMapping("/v1/city")
@Api(tags = "企业参数库-城市")
public class CityController extends BaseController {
    @Resource
    private CityService cityService;
    @Resource
    private ExcelService excelService;

    /**
     * 获取分页列表
     *
     * @param cityParam 城市参数
     * @param pageParam 分页参数
     * @param userInfo  用户信息
     * @return 城市分页列表
     */
    @ApiOperation("获取城市分页列表")
    @PostMapping("/list")
    @FuYunMenuAuthentication("/tobacco/params/city")
    public ResultObject<List<CityDto>> getListPaged(@RequestBody CityParam cityParam
            , PageParam pageParam, UserInfoToken<?> userInfo) {
        return this.cityService.getListPaged(cityParam, pageParam, userInfo);
    }


    /**
     * 新增城市
     *
     * @param cityParam 城市参数
     * @param userInfo  用户信息
     * @return 城市传输模型
     */
    @ApiOperation("新增城市")
    @PostMapping
    public ResultObject<?> insert(@Valid @RequestBody CityParam cityParam, UserInfoToken<?> userInfo) {
        CityDto cityDto = this.cityService.insert(cityParam, userInfo);
        return ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS, cityDto);
    }

    /**
     * 修改城市
     *
     * @param sid       主键
     * @param cityParam 城市参数
     * @param userInfo  用户信息
     * @return 城市传输模型
     */
    @ApiOperation("修改城市")
    @PutMapping("/{sid}")
    public ResultObject<?> update(@PathVariable String sid, @Valid @RequestBody CityParam cityParam
            , UserInfoToken<?> userInfo) {
        cityParam.setSid(sid);
        CityDto cityDto = this.cityService.update(cityParam, userInfo);
        return ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS, cityDto);
    }

    /**
     * 删除城市
     *
     * @param sids     主键列表
     * @param userInfo 用户信息
     * @return 结果对象
     */
    @ApiOperation("删除城市")
    @DeleteMapping("/{sids}")
    public ResultObject<?> delete(@PathVariable List<String> sids, UserInfoToken<?> userInfo) {
        this.cityService.deleteBySids(sids, userInfo);
        return ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
    }

    /**
     * 导出城市
     *
     * @param exportParam 导出参数
     * @param userInfo    用户信息
     * @return 响应实体
     */
    @ApiOperation("导出城市")
    @PostMapping("/export")
    public ResponseEntity<?> export(@RequestBody BasicExportParam<CityParam> exportParam, UserInfoToken<?> userInfo) {
        List<CityDto> cityDtoList = this.cityService.getAllList(exportParam.getExportColumns(), userInfo);
        try {
            return this.excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8)
                    , exportParam.getHeader(), cityDtoList);
        } catch (Exception e) {
            throw new ErrorException(500, "导出失败!");
        }
    }

    /**
     * 获取城市下一个流水号
     *
     * @param userInfo 用户信息
     * @return 下一个流水号
     */
    @ApiOperation("获取城市下一个流水号")
    @GetMapping("/nextParamCode")
    public ResultObject<String> getNextCode(UserInfoToken<?> userInfo) {
        String nextParamCode = this.cityService.getNextParamCode(userInfo);
        return ResultObject.createInstance(true, "获取成功！", nextParamCode);
    }
}
