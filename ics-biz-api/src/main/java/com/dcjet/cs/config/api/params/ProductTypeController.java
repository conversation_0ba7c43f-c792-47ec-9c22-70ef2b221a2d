package com.dcjet.cs.config.api.params;


import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.params.ProductTypeDto;
import com.dcjet.cs.dto.params.ProductTypeParam;
import com.dcjet.cs.params.service.ProductTypeService;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.BaseController;
import com.xdo.springboot.annotation.FuYunMenuAuthentication;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

@RestController
@RequestMapping("v1/productType")
@Api(tags = "基础管理-企业基础信息接口")
public class ProductTypeController extends BaseController {
    @Resource
    private ProductTypeService productTypeService;
    @Resource
    private CommonService commonService;
    @Resource
    private ExcelService excelService;
    /**
     * @param productTypeParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("企业基础信息分页查询接口")
    @PostMapping("list")
    @FuYunMenuAuthentication("/tobacco/params/productType")
    public ResultObject<List<ProductTypeDto>> getListPaged(@RequestBody ProductTypeParam productTypeParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<ProductTypeDto>> paged = productTypeService.getListPaged(productTypeParam, pageParam,userInfo);
        return paged;
    }



    /**
     * @param productTypeParam
     * @param userInfo
     * @return
     */
    @ApiOperation("企业基础信息新增接口")
    @PostMapping()
    public ResultObject<ProductTypeDto> insert(@Valid @RequestBody ProductTypeParam productTypeParam, UserInfoToken userInfo) {
        ResultObject<ProductTypeDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        ProductTypeDto productTypeDto = productTypeService.insert(productTypeParam, userInfo);
        if (productTypeDto != null) {
            resultObject.setData(productTypeDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param productTypeParam
     * @param userInfo
     * @return
     */
    @ApiOperation("企业基础信息修改接口")
    @PutMapping("{sid}")
    public ResultObject<ProductTypeDto> update(@PathVariable String sid, @Valid @RequestBody ProductTypeParam productTypeParam, UserInfoToken userInfo) {
        productTypeParam.setSid(sid);
        ProductTypeDto productTypeDto = productTypeService.update(productTypeParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (productTypeDto != null) {
            resultObject.setData(productTypeDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * 功能描述：企业基础信息删除接口
     * @param sids
     * @return
     */
    @ApiOperation("企业基础信息删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids,UserInfoToken userInfo) throws Exception {
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("删除成功"));
        // 检查是否存在表体数据
        productTypeService.delete(sids,userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return4
     * @throws Exception
     */
    @ApiOperation("企业基础信息Excel数据导出接口")
    @PostMapping("export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<ProductTypeParam> exportParam, UserInfoToken userInfo) throws Exception{
        List<ProductTypeDto> productTypeDtos = productTypeService.selectAll(exportParam.getExportColumns(), userInfo);
        productTypeDtos = convertForPrint(productTypeDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), productTypeDtos);
    }
    /**
     * 导出pCode转换中文名称
     * @param list
     * @return
     */
    public List<ProductTypeDto> convertForPrint(List<ProductTypeDto> list) {
        for (ProductTypeDto item : list) {

        }
        return list;
    }


    @ApiOperation("获取下一个流水号")
    @PostMapping("getNextCode")
    public ResultObject getNextCode(UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true);
        String code  = productTypeService.getNextCode(userInfo);
        resultObject.setData(code);
        return resultObject;
    }

}
