package com.dcjet.cs.config.api.bi;
import com.dcjet.cs.bi.service.BizMaterialInformationService;
import com.dcjet.cs.bi.service.BizMerchantService;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;

import com.dcjet.cs.dto.bi.*;
import com.dcjet.cs.util.CommonEnum;
import com.xdo.domain.KeyValuePair;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import com.xdo.springboot.annotation.FuYunMenuAuthentication;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-3-12
 */
@RestController
@RequestMapping("v1/bizMaterialInformation")
@Api(tags = "接口")
public class BizMaterialInformationController extends BaseController {
    @Resource
    private BizMaterialInformationService bizMaterialInformationService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    @Resource
    private BizMerchantService bizMerchantService;
    /**
     * @param bizMaterialInformationParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    @FuYunMenuAuthentication("/tobacco/materialInformation")
    public ResultObject<List<BizMaterialInformationDto>> getListPaged(@RequestBody BizMaterialInformationParam bizMaterialInformationParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizMaterialInformationDto>> paged = bizMaterialInformationService.getListPaged(bizMaterialInformationParam, pageParam, userInfo);
        return paged;
    }
    /**
     * @param bizMaterialInformationParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizMaterialInformationDto> insert(@Valid @RequestBody BizMaterialInformationParam bizMaterialInformationParam, UserInfoToken userInfo) {
        ResultObject<BizMaterialInformationDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizMaterialInformationDto bizMaterialInformationDto = bizMaterialInformationService.insert(bizMaterialInformationParam, userInfo);
        if (bizMaterialInformationDto != null) {
            resultObject.setData(bizMaterialInformationDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    @ApiOperation("基础资料-客商信息")
    @PostMapping("getMerchantCodeValue")
    public ResultObject getMerchantCodeValue(BizMerchantParam bizMerchantParam, UserInfoToken userInfo){
        ResultObject resultObject = ResultObject.createInstance(true);
        List<BizMerchantDto> bizMerchants = bizMerchantService.selectAll(bizMerchantParam,userInfo);
        //物料信息键值对
        BizMaterialInformationKeyCodeList bizMaterialInformationKeyCode = new BizMaterialInformationKeyCodeList();
        //包装信息
        List<BizMaterialInformationKeyCode> packInfoList = bizMerchantService.selectPaclInfo(userInfo.getCompany());
        bizMaterialInformationKeyCode.setPackagingInformation(packInfoList);
        //商品类别
        List<BizMaterialInformationKeyCode> merchandiseCategoriesList = bizMerchantService.selectMerchandiseCategories(userInfo.getCompany());
        bizMaterialInformationKeyCode.setMerchandiseCategories(merchandiseCategoriesList);
        //供应商
        for (BizMerchantDto bizMerchantDto:bizMerchants
             ) {
            bizMaterialInformationKeyCode.getSupplierCodeMap().add(createFieldMap(bizMerchantDto.getMerchantCode(),bizMerchantDto.getMerchantNameCn()));
        }

        //币种
        Optional<List<Map<String, String>>> pcode = pCodeHolder.getTypeList(PCodeType.CURR_ALL);
        if (pcode.isPresent()){
            List<Map<String, String>> countryList = pcode.get();
            for (Map<String, String> countryMap : countryList) {
                bizMaterialInformationKeyCode.getCurrList().add(createFieldMap(countryMap.get("CODE"),countryMap.get("CODE")))  ;
            }

        }
        resultObject.setData(bizMaterialInformationKeyCode);
        return resultObject;
    }
    // 创建字段的Map对象
    private BizMaterialInformationKeyCode createFieldMap(String key, String label) {
        BizMaterialInformationKeyCode fieldMap = new BizMaterialInformationKeyCode();
        fieldMap.setValue(key);
        fieldMap.setLabel(label);
        return fieldMap;
    }
    /**
     * @param sid
     * @param bizMaterialInformationParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizMaterialInformationDto> update(@PathVariable String sid, @Valid @RequestBody BizMaterialInformationParam bizMaterialInformationParam, UserInfoToken userInfo) {
        bizMaterialInformationParam.setSid(sid);
        BizMaterialInformationDto bizMaterialInformationDto = bizMaterialInformationService.update(bizMaterialInformationParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizMaterialInformationDto != null) {
            resultObject.setData(bizMaterialInformationDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    @ApiOperation("作废接口")
    @PostMapping("/cancel/{sids}")
    public ResultObject<BizMaterialInformationDto> cancel(@PathVariable List<String> sids, UserInfoToken userInfo) {
        Boolean dto = bizMaterialInformationService.cancel(sids, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (dto == false) {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
        bizMaterialInformationService.delete(sids, userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BizMaterialInformationExportParam exportParam, UserInfoToken userInfo) throws Exception{
        BizMerchantParam bizMerchantParam = new BizMerchantParam();
        List<BizMerchantDto> bizMerchants = bizMerchantService.selectAll(bizMerchantParam,userInfo);
        //物料信息键值对
        BizMaterialInformationKeyCodeList bizMaterialInformationKeyCode = new BizMaterialInformationKeyCodeList();
        List<BizMaterialInformationKeyCode> packInfoList = bizMerchantService.selectPaclInfo(userInfo.getCompany());
        bizMaterialInformationKeyCode.setPackagingInformation(packInfoList);
        //供应商
        for (BizMerchantDto bizMerchantDto:bizMerchants
        ) {
            bizMaterialInformationKeyCode.getSupplierCodeMap().add(createFieldMap(bizMerchantDto.getMerchantCode(),bizMerchantDto.getMerchantNameCn()));
        }

        List<BizMaterialInformationDto> bizMaterialInformationDtos = bizMaterialInformationService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(bizMaterialInformationDtos,bizMaterialInformationKeyCode.getSupplierCodeMap(),bizMaterialInformationKeyCode.getPackagingInformation());
        List<KeyValuePair<String, String>> header = exportParam.getHeader();
        header.stream().map(pair -> {
                    if ("commonMarkList".equals(pair.getKey())) {
                        pair.setKey("commonMark");
                    }
                    return pair;
                }).collect(Collectors.toList());

        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), header, bizMaterialInformationDtos);
    }

    /**
     * 导出，pCode转换中文名称
     *
     * @param list
     * @param supplierCodeMap
     * @return
     */
    public void convertForPrint(List<BizMaterialInformationDto> list, List<BizMaterialInformationKeyCode> supplierCodeMap, List<BizMaterialInformationKeyCode> packagingInformationMap) {
        for(BizMaterialInformationDto item : list) {

            for (BizMaterialInformationKeyCode one:supplierCodeMap) {
                if(null!=item.getSupplierCode()){
                    if(item.getSupplierCode().equals(one.getValue())){
                        item.setSupplierCode(one.getValue()+" "+one.getLabel());
                    }
                }
            }
            for (BizMaterialInformationKeyCode one:packagingInformationMap) {
                if(null!=item.getPackagingInformation()){
                    if(item.getPackagingInformation().equals(one.getLabel())){
                        item.setPackagingInformation(one.getLabel()+" "+one.getValue());
                    }
                }
            }
            if(StringUtils.isNotBlank(item.getDataState())){
                item.setDataState(item.getDataState() + " " + CommonEnum.STATUS_ENUM.getValue(item.getDataState()));
            }
            if(StringUtils.isNotBlank(item.getCommonMark())){
                String comm = "";
                for (String x : item.getCommonMarkList()) {
                    comm = comm + "," + x + " " + CommonEnum.commonMarkEnum.getValue(x);
                }
                if(StringUtils.isNotBlank(comm)){
                    item.setCommonMark(comm.substring(1));
                }else {
                    item.setCommonMark("");
                }
            }
//            item.setCommonMark(item.getCommonMark()+" "+CommonEnum.commonMarkEnum.getValue(item.getCommonMark()));
        }
    }

    /**
     * 根据交易代码获取物料计划信息
     * @param userInfo 用户信息
     * @return 物料信息列表
     */
    @ApiOperation("根据交易代码获取物料计划信息")
    @PostMapping("/matForPlan")
    public ResultObject<List<BizMaterialInformationDto>> getMatForPlan(@RequestBody BizMaterialInformationDto mat,UserInfoToken userInfo ) {
        ResultObject<List<BizMaterialInformationDto>> resultObject = ResultObject.createInstance(true);
        List<BizMaterialInformationDto> materialList = bizMaterialInformationService.getMatForPlan(mat,userInfo);
        resultObject.setData(materialList);
        return resultObject;
    }
}
