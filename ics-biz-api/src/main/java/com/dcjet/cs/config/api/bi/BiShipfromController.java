package com.dcjet.cs.config.api.bi;

import com.dcjet.cs.dto.bi.BiShipfromDto;
import com.dcjet.cs.dto.bi.BiShipfromParam;
import com.dcjet.cs.dto.bi.BiShipfromExportParam;
import com.dcjet.cs.bi.service.BiShipfromService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
import com.xdo.domain.ResultObject;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.dcjet.cs.common.service.ExcelService;
import com.xdo.pcode.service.PCodeHolder;
import xdoi18n.XdoI18nUtil;

/**
* BiShipfromController层
*
* <AUTHOR>
* @date 2025-03-01 09:59:28
*/
@RestController
@RequestMapping("v1/biShipfrom")
@Api(tags = "shipfrom接口")
public class BiShipfromController{

    @Resource
    private BiShipfromService biShipfromService;

    @Resource
    private PCodeHolder pCodeHolder;

    @Resource
    private ExcelService excelService;
 
    /**
     * 分页获取shipfrom数据
     * @param biShipfromParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页获取shipfrom数据")
    @PostMapping("list")
    public ResultObject<List<BiShipfromDto>> getListPaged(@RequestBody BiShipfromParam biShipfromParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BiShipfromDto>> paged = biShipfromService.getListPaged(biShipfromParam, pageParam,userInfo);
        return paged;
    }


    /**
     * @param biShipfromParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BiShipfromDto> insert(@Valid @RequestBody BiShipfromParam biShipfromParam, UserInfoToken userInfo) {
        ResultObject<BiShipfromDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BiShipfromDto biShipfromDto = biShipfromService.insert(biShipfromParam, userInfo);
        if (biShipfromDto != null) {
            resultObject.setData(biShipfromDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sid
     * @param biShipfromParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BiShipfromDto> update(@PathVariable String sid, @Valid @RequestBody BiShipfromParam biShipfromParam, UserInfoToken userInfo) {
        biShipfromParam.setSid(sid);
        BiShipfromDto biShipfromDto = biShipfromService.update(biShipfromParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (biShipfromDto != null) {
            resultObject.setData(biShipfromDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }



    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		biShipfromService.delete(sids, userInfo);
        return resultObject;
    }


    /**
     * 导出数据
     * @param exportParam
     * @param userInfo
     * @return
     * @throws Exception
     */
    @ApiOperation("Excel数据导出接口")
    @PostMapping(value = "/export")
    public ResponseEntity export(@Valid @RequestBody BiShipfromExportParam exportParam, UserInfoToken userInfo) throws Exception{
        List<BiShipfromDto> biShipfromDtos = biShipfromService.selectAll(exportParam.getExportColumns(), userInfo);
        convertForPrint(biShipfromDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), "utf-8"), exportParam.getHeader(), biShipfromDtos);
    }


                        

    /**
     * 导出，pCode转换中文名称
     * @param list
     * @return
     */
    public void convertForPrint(List<BiShipfromDto> list) {
        for(BiShipfromDto item : list) {
        
        }
    }


}
