package com.dcjet.cs.config.api.params;


import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.bi.BizMaterialInformationKeyCode;
import com.dcjet.cs.dto.params.BoxTypeDto;
import com.dcjet.cs.dto.params.BoxTypeParam;
import com.dcjet.cs.params.service.BoxTypeService;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.springboot.BaseController;
import com.xdo.springboot.annotation.FuYunMenuAuthentication;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("v1/boxType")
@Api(tags = "基础管理-企业基础信息接口")
public class BoxTypeController extends BaseController {
    @Resource
    private BoxTypeService boxTypeService;
    @Resource
    private CommonService commonService;
    @Resource
    private ExcelService excelService;
    @Resource
    private PCodeHolder pCodeServiceHolder;
    /**
     * @param boxTypeParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("企业基础信息分页查询接口")
    @PostMapping("list")
    @FuYunMenuAuthentication("/tobacco/params/boxType")
    public ResultObject<List<BoxTypeDto>> getListPaged(@RequestBody BoxTypeParam boxTypeParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BoxTypeDto>> paged = boxTypeService.getListPaged(boxTypeParam, pageParam,userInfo);
        return paged;
    }



    /**
     * @param boxTypeParam
     * @param userInfo
     * @return
     */
    @ApiOperation("企业基础信息新增接口")
    @PostMapping()
    public ResultObject<BoxTypeDto> insert(@Valid @RequestBody BoxTypeParam boxTypeParam, UserInfoToken userInfo) {
        ResultObject<BoxTypeDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BoxTypeDto boxTypeDto = boxTypeService.insert(boxTypeParam, userInfo);
        if (boxTypeDto != null) {
            resultObject.setData(boxTypeDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param boxTypeParam
     * @param userInfo
     * @return
     */
    @ApiOperation("企业基础信息修改接口")
    @PutMapping("{sid}")
    public ResultObject<BoxTypeDto> update(@PathVariable String sid, @Valid @RequestBody BoxTypeParam boxTypeParam, UserInfoToken userInfo) {
        boxTypeParam.setSid(sid);
        BoxTypeDto boxTypeDto = boxTypeService.update(boxTypeParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (boxTypeDto != null) {
            resultObject.setData(boxTypeDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * 功能描述：企业基础信息删除接口
     * @param sids
     * @return
     */
    @ApiOperation("企业基础信息删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids,UserInfoToken userInfo) throws Exception {
        ResultObject resultObject = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("删除成功"));
        // 检查是否存在表体数据
        boxTypeService.delete(sids,userInfo);
        return resultObject;
    }
    /**
     * @param exportParam
     * @param userInfo
     * @return4
     * @throws Exception
     */
    @ApiOperation("企业基础信息Excel数据导出接口")
    @PostMapping("export")
    public ResponseEntity export(@Valid @RequestBody BasicExportParam<BoxTypeParam> exportParam, UserInfoToken userInfo) throws Exception{
        List<BoxTypeDto> boxTypeDtos = boxTypeService.selectAll(exportParam.getExportColumns(), userInfo);
        boxTypeDtos = convertForPrint(boxTypeDtos);
        return excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8), exportParam.getHeader(), boxTypeDtos);
    }
    /**
     * 导出pCode转换中文名称
     * @param list
     * @return
     */
    public List<BoxTypeDto> convertForPrint(List<BoxTypeDto> list) {
        for (BoxTypeDto item : list) {


        }
        return list;
    }


    @ApiOperation("获取下一个流水号")
    @PostMapping("getNextCode")
    public ResultObject getNextCode(UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true);
        String code  = boxTypeService.getNextCode(userInfo);
        resultObject.setData(code);
        return resultObject;
    }

    @ApiOperation("获取箱型")
    @PostMapping("getBoxTypeMap")
    public ResultObject getBoxTypeMap(BoxTypeParam boxTypeParam,UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true);
        List<BizMaterialInformationKeyCode> boxTypeMap = new ArrayList<>();
        List<BoxTypeDto> boxTypeDtos = boxTypeService.selectAll(boxTypeParam, userInfo);
        for (BoxTypeDto boxTypeDto : boxTypeDtos) {
            boxTypeMap.add(createFieldMap(boxTypeDto.getBoxType(),boxTypeDto.getBoxType()));
        }
        resultObject.setData(boxTypeMap);
        return resultObject;
    }


    private BizMaterialInformationKeyCode createFieldMap(String key, String label) {
        BizMaterialInformationKeyCode fieldMap = new BizMaterialInformationKeyCode();
        fieldMap.setValue(key);
        fieldMap.setLabel(label);
        return fieldMap;
    }

}
