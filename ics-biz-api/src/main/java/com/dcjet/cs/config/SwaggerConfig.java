package com.dcjet.cs.config;

import com.xdo.springboot.base.SwaggerConfigEntity;
import com.xdo.springboot.config.BaseSwaggerConfig;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * Swagger 文档配置
 *
 * <AUTHOR>
 * @date: 2019-4-17
 */
@Configuration
@EnableSwagger2
public class SwaggerConfig extends BaseSwaggerConfig {
    @Override
    public SwaggerConfigEntity getSwaggerConfig(){
        return new SwaggerConfigEntity("com.dcjet.cs","神码关务系统开发接口文档","神码关务系统开发接口文档","1.0");
    }
}
