package com.dcjet.cs.dto.importedCigarettes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import com.xdo.validation.annotation.XdoSize;
/**
 *
 * <AUTHOR>
 * @date: 2025-3-7
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class BizIContractHeadParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 业务类型
     */
	@XdoSize(max = 60, message = "业务类型长度不能超过60位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("业务类型")
	private  String businessType;
	/**
     * 计划编号
     */
	@XdoSize(max = 60, message = "计划编号长度不能超过60位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("计划编号")
	private  String planNo;
	/**
     * 计划年度（格式: YYYY）
     */
	@ApiModelProperty("计划年度（格式: YYYY）")
	private  Date planYear;
	/**
     * 上下半年（如：上半年/H1）
     */
	@XdoSize(max = 10, message = "上下半年（如：上半年/H1）长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("上下半年（如：上半年/H1）")
	private  String halfYear;
	/**
     * 客户
     */
	@XdoSize(max = 200, message = "客户长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("客户")
	private  String buyer;
	/**
     * 供应商
     */
	@XdoSize(max = 200, message = "供应商长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("供应商")
	private  String seller;
	/**
     * 合同编号
     */
	@XdoSize(max = 60, message = "合同号长度不能超过60位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("合同编号")
	private  String contractNo;
	/**
     * 合同生效日期
     */
	@XdoSize(max = 60, message = "合同生效日期长度不能超过60位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("合同生效日期")
	private  String contractEffectiveDate;
	/**
     * 合同有效期
     */
	@ApiModelProperty("合同有效期")
	private  Date contractExpiryDate;
	/**
     * 签约日期
     */
	@ApiModelProperty("签约日期")
	private  Date signDate;
	/**
     * 装货港
     */
	@XdoSize(max = 50, message = "装货港长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("装货港")
	private  String loadingPort;
	/**
     * 到货港
     */
	@XdoSize(max = 50, message = "到货港长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("到货港")
	private  String arrivalPort;
	/**
     * 贸易条款
     */
	@XdoSize(max = 50, message = "贸易条款长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("贸易条款")
	private  String tradeTerms;
	/**
     * 价格条款对应的港口
     */
	@XdoSize(max = 50, message = "价格条款对应的港口长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("价格条款对应的港口")
	private  String priceTermPort;
	/**
     * 出口国家或地区
     */
	@XdoSize(max = 40, message = "出口国家或地区长度不能超过40位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("出口国家或地区")
	private  String exportCountry;
	/**
     * 合同总金额
     */
	@Digits(integer = 17, fraction = 2, message = "合同总金额必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("合同总金额")
	private  BigDecimal totalAmount;
	/**
     * 合同总数量
     */
	@Digits(integer = 9, fraction = 0, message = "合同总数量必须为数字,整数位最大9位,小数最大0位!")
	@ApiModelProperty("合同总数量")
	private  Integer totalQuantity;
	/**
     * 短溢数%
     */
	@Digits(integer = 5, fraction = 4, message = "短溢数%必须为数字,整数位最大5位,小数最大4位!")
	@ApiModelProperty("短溢数%")
	private  BigDecimal shortOverPercent;
	/**
	 * 备注
	 */
	@XdoSize(max = 200, message = "备注长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("备注")
	private  String note;
	/**
     * 制单人
     */
	@XdoSize(max = 10, message = "制单人长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("制单人")
	private  String preparedBy;
	/**
     * 制单时间
     */
	@ApiModelProperty("制单时间")
	private  Date prepareTime;
	/**
    * 制单时间-开始
    */
	@ApiModelProperty("制单时间-开始")
	private String prepareTimeFrom;
	/**
    * 制单时间-结束
    */
	@ApiModelProperty("制单时间-结束")
    private String prepareTimeTo;
	/**
     * 数据状态
     */
	@XdoSize(max = 10, message = "数据状态长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("数据状态")
	private  String dataStatus;
	/**
     * 确认时间
     */
	@ApiModelProperty("确认时间")
	private  Date confirmTime;
	/**
     * 审批状态
     */
	@XdoSize(max = 10, message = "审批状态长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("审批状态")
	private  String approvalStatus;
	/**
     * 版本号
     */
	@XdoSize(max = 10, message = "版本号长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("版本号")
	private  String versionNo;
	/**
     * 
     */
	@XdoSize(max = 10, message = "长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String tradeCode;
	/**
     * 
     */
	@XdoSize(max = 40, message = "长度不能超过40位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String parentId;

	@XdoSize(max = 50, message = "贸易条款文本长度不能超过50位字节长度(一个汉字2位字节长度)!")
	private  String extend1;
}
