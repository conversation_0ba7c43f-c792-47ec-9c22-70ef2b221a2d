package com.dcjet.cs.dto.importedCigarettes;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date: 2025-3-13
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class BizIPlanWithDetailParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 父sid
     */
	@ApiModelProperty("父sid")
	private  String parentId;
	/**
     * 企业编码
     */
	@XdoSize(max = 10, message = "企业编码长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("企业编码")
	private  String tradeCode;
	/**
     * 业务类型，默认国营贸易进口卷烟，置灰不可修改
     */
	@NotEmpty(message="业务类型不能为空！")
	@XdoSize(max = 60, message = "业务类型长度不能超过60位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("业务类型")
	private  String businessType;
	/**
     * 计划编号，唯一性校验
     */
	@NotEmpty(message="计划编号，不能为空！")
	@XdoSize(max = 100, message = "计划编号，长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("计划编号")
	private  String planId;
	/**
     * 计划年度
     */
	@NotNull(message="计划年度不能为空！")
	@ApiModelProperty("计划年度")
	private  Date planYear;
	/**
     * 上下半年，0表示上半年，1表示下半年
     */
	@NotEmpty(message="上下半年不能为空！")
	@XdoSize(max = 10, message = "上下半年长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("上下半年")
	private  String halfYear;
	/**
     * 备注
     */
	@XdoSize(max = 500, message = "备注长度不能超过500位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("备注")
	private  String remark;
	/**
     * 单据状态，0编制，1确认，2作废
     */
	@XdoSize(max = 10, message = "单据状态长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("单据状态")
	private  String status;
	/**
     * 确认时间，点击确认按钮成功提交的时间
     */
	@ApiModelProperty("确认时间，点击确认按钮成功提交的时间")
	private  Date confirmTime;
	/**
     * 审批状态，0不涉及审批，1未审批，2审批中，3审批通过，4审批退回
     */
	@XdoSize(max = 10, message = "审批状态长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("审批状态")
	private  String apprStatus;
	/**
     * 版本号，初始为1，版本复制递增
     */
	@NotEmpty(message="版本号不能为空！")
	@XdoSize(max = 20, message = "版本号长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("版本号")
	private  String versionNo;
	/**
     * 
     */
	@XdoSize(max = 100, message = "长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("制单人")
	private  String insertUser;
	@XdoSize(max = 100, message = "长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String insertUserName;
	/**
     * 制单时间，系统自动生成
     */
	@ApiModelProperty("制单时间")
	private  Date insertTime;
	/**
    * 制单时间，系统自动生成-开始
    */
	@ApiModelProperty("制单时间-开始")
	private String insertTimeFrom;
	/**
    * 制单时间，系统自动生成-结束
    */
	@ApiModelProperty("制单时间-结束")
    private String insertTimeTo;
	/**
     * 
     */
	@XdoSize(max = 100, message = "长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String updateUserName;
	/**
     * 
     */
	@XdoSize(max = 400, message = "长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend1;
	/**
     * 
     */
	@XdoSize(max = 400, message = "长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend2;
	/**
     * 
     */
	@XdoSize(max = 400, message = "长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend3;
	/**
     * 
     */
	@XdoSize(max = 400, message = "长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend4;
	/**
     * 
     */
	@XdoSize(max = 400, message = "长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend5;
	/**
     * 
     */
	@XdoSize(max = 400, message = "长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend6;
	/**
     * 
     */
	@XdoSize(max = 400, message = "长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend7;
	/**
     * 
     */
	@XdoSize(max = 400, message = "长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend8;
	/**
     * 
     */
	@XdoSize(max = 400, message = "长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend9;
	/**
     * 
     */
	@XdoSize(max = 400, message = "长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend10;

	@ApiModelProperty("是否存在下游数据")
	private String hasHeadCtr;
	@ApiModelProperty("是否为复制数据")
	private String isCopy;

	List<BizIPlanListParam> details;
}
