package com.dcjet.cs.dto.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@ApiModel(value = "首页-首页预警返回数据")
public class WarringDto {
    /**
     * 进⼝制单超期预警
     */
    @ApiModelProperty("进⼝制单超期预警")
    private Integer recoreNum;
    /**
     * 证件卡类到期预警
     */
    @ApiModelProperty("证件卡类到期预警")
    private Integer cardNum;
    /**
     * 证件卡类到期预警
     */
    @ApiModelProperty("保金保函预警")
    private Integer bailNum;
    /**
     * 外发加工到期预警
     */
    @ApiModelProperty("外发加工到期预警")
    private Integer expireNum;
    /**
     * 减免税设备解除监管
     */
    @ApiModelProperty("减免税设备解除监管")
    private Integer equipNum;
    /**
     * 暂时进出口预警
     */
    @ApiModelProperty("暂时进出口预警")
    private Integer exportNum;
    /**
     * 修理物品台账预警
     */
    @ApiModelProperty("修理物品台账预警")
    private Integer rerairtNum;
    /**
     * 报关超期预警
     */
    @ApiModelProperty("报关超期预警")
    private Integer recordNum;
    /**
     * 进口未到货预警
     */
    @ApiModelProperty("进口未到货预警")
    private Integer recordHeadNum;
}
