package com.dcjet.cs.dto.bi;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
@Data
@ApiModel(value = "返回信息")
@Setter
@Getter
public class BizMaterialInformationKeyCodeList {
    //供应商
    private List<BizMaterialInformationKeyCode> supplierCodeMap;
    //商品类别
    private List<BizMaterialInformationKeyCode> merchandiseCategories;
    //包装信息
    private List<BizMaterialInformationKeyCode> commonMark;
    //包装信息
    private List<BizMaterialInformationKeyCode> packagingInformation;
    //币种
    private List<BizMaterialInformationKeyCode> currList;


    public BizMaterialInformationKeyCodeList() {
        this.supplierCodeMap = new ArrayList<>();
        this.merchandiseCategories = new ArrayList<>();
        this.commonMark = new ArrayList<>();
        this.packagingInformation = new ArrayList<>();
        this.currList = new ArrayList<>();
    }
}
