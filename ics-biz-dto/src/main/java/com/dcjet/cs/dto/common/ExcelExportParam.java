package com.dcjet.cs.dto.common;

import com.xdo.domain.KeyValuePair;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * @author: 高士勇
 * @date: 2018-12-26
 */
@Setter
@Getter
@ApiModel(value = "导出文件参数-ExcelExportParam")
public class ExcelExportParam<T> implements Serializable {
    private static final long serialVersionUID = 42L;


    /**
     * 导出文件名
     */
    @NotEmpty(message = "{导出文件名不能为空}")
    @ApiModelProperty(value = "导出文件名", required = true)
    private String name;

    /***
     * Excel表头
     */
    @NotEmpty(message = "{导出表头不能为空}")
    @ApiModelProperty(value = "导出表头", required = true)
    private List<KeyValuePair<String, String>> header;

    @ApiModelProperty("查询参数")
    private T exportColumns;
}
