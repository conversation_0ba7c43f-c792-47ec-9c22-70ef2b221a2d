package com.dcjet.cs.dto.payment;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class BizPaymentSettlementParam implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String sid;

    @ApiModelProperty("上游SID")
    private String headId;

    @XdoSize(max = 10, message = "企业编码长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("企业编码")
    private String tradeCode;

    @XdoSize(max = 50, message = "业务类型长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("业务类型")
    private String businessType;

    @XdoSize(max = 50, message = "进货单号长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("进货单号")
    private String purchaseOrderNo;

    @XdoSize(max = 50, message = "合同号长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("合同号")
    private String contractNo;

    @XdoSize(max = 100, message = "客户长度不能超过100位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("客户")
    private String customer;

    @XdoSize(max = 100, message = "付款单位长度不能超过100位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("付款单位")
    private String payer;

    @XdoSize(max = 50, message = "外商发票号长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("外商发票号")
    private String foreignInvoiceNo;

    @XdoSize(max = 10, message = "币种长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("币种")
    private String curr;

    @ApiModelProperty("汇率")
    private BigDecimal exchangeRate;

    @ApiModelProperty("原货币价")
    private BigDecimal originalAmount;

    @ApiModelProperty("人民币金额")
    private BigDecimal rmbAmount;

    @ApiModelProperty("口岸调拨总价")
    private BigDecimal portTransferAmount;

    @ApiModelProperty("代理费(不含税金额)")
    private BigDecimal agencyFee;

    @ApiModelProperty("代理费税额")
    private BigDecimal agencyFeeTax;

    @ApiModelProperty("合计值")
    private BigDecimal totalAmount;

    @ApiModelProperty("代理费率")
    private BigDecimal agencyFeeRate;

    @ApiModelProperty("代理费(价税合计)")
    private BigDecimal agencyFeeTotal;

    @XdoSize(max = 50, message = "商品类别长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("商品类别")
    private String productCategory;

    @ApiModelProperty("数量")
    private BigDecimal qty;

    @XdoSize(max = 10, message = "发送用友长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("发送用友")
    private String sendToUf;

    @XdoSize(max = 500, message = "备注长度不能超过500位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("备注")
    private String remark;

    @XdoSize(max = 20, message = "单据状态长度不能超过20位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("单据状态")
    private String status;

    @ApiModelProperty("确认时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date confirmTime;

    @XdoSize(max = 50, message = "创建人长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("创建人")
    private String insertUser;

    @XdoSize(max = 50, message = "创建人名称长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("创建人名称")
    private String insertUserName;

    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date insertTime;

    @XdoSize(max = 50, message = "更新人长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("更新人")
    private String updateUser;

    @XdoSize(max = 50, message = "修改人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("修改人姓名")
    private String updateUserName;

    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;

    @XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
    private String extend1;
    @XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
    private String extend2;
    @XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
    private String extend3;
    @XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
    private String extend4;
    @XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
    private String extend5;
    @XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
    private String extend6;
    @XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
    private String extend7;
    @XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
    private String extend8;
    @XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
    private String extend9;
    @XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
    private String extend10;

    //updateUser不为空取updateUser,否则取insertUser
    @ApiModelProperty("制单人")
    private String createrUser;
    //updateUser不为空取updateUserName,否则取insertUserName
    @ApiModelProperty("制单人姓名")
    private String createrUserName;

    //updateTime不为空取updateUser,否则取insertTime
    @ApiModelProperty("制单时间")
    private Date createrTime;

    private  String insertTimeFrom;
    private  String insertTimeTo;

    @ApiModelProperty("业务日期")
    private  Date businessDate;
}
