package com.dcjet.cs.dto.dec;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 订单表头数据分布情况
 */
@Getter
@Setter
public class OrderHeadIsNextModuleDto  implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 是否生成进货信息
     */
    private Integer showBodyPurchaseHeadCount;


    /**
     * 是否显示证件信息
     */
    private Integer showBodyDocumentHeadCount;


    /**
     * 是否生成入库信息
     */
    private Integer showBodyWarehouseReceiptHeadCount;


    /**
     * 是否生成销售信息
     */
    private Integer showBodyReceiptSellCount;

}
