package com.dcjet.cs.dto.base;


import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Setter @Getter
public class ImportResult implements Serializable {
    private static final long serialVersionUID = 1L;

    /***
     * 正确的数据
     */
    @ApiModelProperty("正确的数据")
    private int correctSize;

    /***
     * 错误的数据
     */
    @ApiModelProperty("错误的数据")
    private int errorSize;

    /***
     * 正确的key值
     */
    @ApiModelProperty("正确的key值")
    private String correctKey;

    /***
     * 错误的key值
     */
    @ApiModelProperty("错误的key值")
    private String errorKey;
}
