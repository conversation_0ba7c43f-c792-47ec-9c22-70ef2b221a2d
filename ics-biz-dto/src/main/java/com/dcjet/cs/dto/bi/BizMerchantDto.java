package com.dcjet.cs.dto.bi;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Digits;

/**
 * 
 * <AUTHOR>
 * @date: 2025-3-11
 */
@ApiModel(value = "返回信息")
@Setter @Getter
public class BizMerchantDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String sid;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String businessType;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String dataStatus;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String versionNo;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String tradeCode;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String parentId;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String insertUser;
	/**
      * 
      */
    @ApiModelProperty("")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date insertTime;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String updateUser;
	/**
      * 
      */
    @ApiModelProperty("")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String insertUserName;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String updateUserName;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend1;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend2;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend3;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend4;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend5;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend6;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend7;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend8;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend9;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend10;
	/**
      * 客商编码
      */
    @ApiModelProperty("客商编码")
	private  String merchantCode;
	/**
      * 客商中文名称
      */
    @ApiModelProperty("客商中文名称")
	private  String merchantNameCn;
	/**
      * 客商英文名称
      */
    @ApiModelProperty("客商英文名称")
	private  String merchantNameEn;
	/**
      * 客商简称
      */
    @ApiModelProperty("客商简称")
	private  String merchantShort;
	/**
      * 客商地址
      */
    @ApiModelProperty("客商地址")
	private  String merchantAddress;
	/**
      * 收款银行
      */
    @ApiModelProperty("收款银行")
	private  String receivingBank;
	/**
      * 收款方帐号
      */
    @ApiModelProperty("收款方帐号")
	private  String receiverAccountNum;
	/**
      * 备注
      */
    @ApiModelProperty("备注")
	private  String note;


	/**
	 * 创建人部门编码
	 */
	@ApiModelProperty("创建人部门编码")
	private  String sysOrgCode;
	@ApiModelProperty("财务系统编码")
	private  String financeCode;


	/**
	 * 序号
	 */
	@ApiModelProperty("序号")
	private Integer serialNo;
}
