package com.dcjet.cs.dto.base;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Setter
@Getter
public class BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private  String sid;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private  String insertUser;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private  String insertUserName;
    /**
     * 创建日期
     */
    @ApiModelProperty("创建日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;
    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private  String updateUser;
    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private  String updateUserName;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private  Date updateTime;

    /**
     *所属企业编码
     */
    @ApiModelProperty(value = "所属企业编码")
    private String tradeCode;


    public String getUserName() {
        return (this.insertUser==null?"":this.insertUser)+ (this.insertUserName==null?"":"("+this.insertUserName+")");
    }

    public String getUpdateUser() {
        return (this.updateUser==null?"":this.updateUser)+ (this.updateUserName==null?"":"("+this.updateUserName+")");
    }
}
