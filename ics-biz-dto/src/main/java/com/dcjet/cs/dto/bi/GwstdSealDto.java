package com.dcjet.cs.dto.bi;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
/**
 * 
 * <AUTHOR>
 * @date: 2020-10-26
 */
@ApiModel(value = "电子签章返回信息")
@Setter @Getter
public class GwstdSealDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 主键
      */
    @ApiModelProperty("主键")
	private  String sid;
	/**
      * 电子章类型 1：公章、2：报关专用章、3：合同专用章
      */
    @ApiModelProperty("电子章类型 1：公章、2：报关专用章、3：合同专用章")
	private  String type;
	/**
      * 电子章上传后的文件路径
      */
    @ApiModelProperty("电子章上传后的文件路径")
	private  String fdfsId;
	/**
      * 上传时间
      */
    @ApiModelProperty("上传时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date insertTime;
	/**
      * 导入文件名称
      */
    @ApiModelProperty("导入文件名称")
	private  String originFileName;
	/**
      * 企业编码
      */
    @ApiModelProperty("企业编码")
	private  String tradeCode;
}
