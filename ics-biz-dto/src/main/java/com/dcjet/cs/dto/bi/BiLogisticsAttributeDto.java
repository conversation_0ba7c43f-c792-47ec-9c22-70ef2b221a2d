package com.dcjet.cs.dto.bi;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
/**
 * 
 * <AUTHOR>
 * @date: 2021-9-5
 */
@ApiModel(value = "企业参数库-物流属性返回信息")
@Setter @Getter
public class BiLogisticsAttributeDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 唯一键
      */
    @ApiModelProperty("唯一键")
	private  String sid;
	/**
      * 企业编码
      */
    @ApiModelProperty("企业编码")
	private  String tradeCode;
	/**
      * 创建人
      */
    @ApiModelProperty("创建人")
	private  String insertUser;
	/**
      * 创建时间
      */
    @ApiModelProperty("创建时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private  Date insertTime;
	/**
      * 创建人用户名
      */
    @ApiModelProperty("创建人用户名")
	private  String insertUserName;
	/**
      * 更新人
      */
    @ApiModelProperty("更新人")
	private  String updateUser;
	/**
      * 更新时间
      */
    @ApiModelProperty("更新时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
	/**
      * 更新人用户名
      */
    @ApiModelProperty("更新人用户名")
	private  String updateUserName;
	/**
      * 物流属类型(1 国内段 2 国际段)
      */
    @ApiModelProperty("物流属类型(1 国内段 2 国际段)")
	private  String logisticsType;
	/**
      * 物流属性编码
      */
    @ApiModelProperty("物流属性编码")
	private  String logisticsCode;
	/**
      * 物流属性名称
      */
    @ApiModelProperty("物流属性名称")
	private  String logisticsName;
	/**
      * 备注
      */
    @ApiModelProperty("备注")
	private  String note;

    private String userName;
}
