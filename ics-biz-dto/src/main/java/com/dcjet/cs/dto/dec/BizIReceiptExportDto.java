package com.dcjet.cs.dto.dec;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 进口管理-进货信息表头
 */
@Getter
@Setter
@ApiModel(value = "信息表头-返回信息")
public class BizIReceiptExportDto implements Serializable{

    private String receiptNumber;
    private String consignee;
    private String deliveryDate;
    private String warehouse;
    private String supplier;
    private String remark;
    private String totalShipmentQuantity;
    private String totalActualQuantityIssued;
    private String totalAmount;
    private String createBy;

    private List<BizIReceiptListDto> bizIReceiptListDtos;
}