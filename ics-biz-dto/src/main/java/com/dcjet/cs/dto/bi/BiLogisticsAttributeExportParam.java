package com.dcjet.cs.dto.bi;
import com.xdo.domain.ExcelExportParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
/**
  * <AUTHOR>
  * @date: 2021-9-5
 */
@Getter
@Setter
@ApiModel(description = "企业参数库-物流属性导出传入参数")
public class BiLogisticsAttributeExportParam extends ExcelExportParam {
    /**
     * 导出传入参数
     */
    @ApiModelProperty("查询参数")
    private BiLogisticsAttributeParam exportColumns;
}
