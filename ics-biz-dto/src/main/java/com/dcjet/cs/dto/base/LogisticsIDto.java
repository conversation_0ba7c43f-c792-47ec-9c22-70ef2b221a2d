package com.dcjet.cs.dto.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@ApiModel(value = "首页-首页进口物流追踪返回数据")
public class LogisticsIDto {
    /**
     * 预录入单总量
     */
    @ApiModelProperty("预录入单总量")
    private Integer decIHeadNum;
    /**
     * 未发货
     */
    @ApiModelProperty("未发货")
    private Integer unshipped;
    /**
     * 已发货未到港
     */
    @ApiModelProperty("已发货未到港")
    private Integer shipNum;
    /**
     * 已到港清关
     */
    @ApiModelProperty("已到港清关")
    private Integer reachNum;
    /**
     * 已转关未到货
     */
    @ApiModelProperty("已转关未到货")
    private Integer transferCustomsNum;
    /**
     * 已到货
     */
    @ApiModelProperty("已到货")
    private Integer arrivalNum;
}
