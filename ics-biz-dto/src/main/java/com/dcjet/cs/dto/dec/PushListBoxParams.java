package com.dcjet.cs.dto.dec;

import com.xdo.validation.annotation.XdoSize;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;


/**
 * 新增装箱子表数据
 */
@Getter
@Setter
public class PushListBoxParams implements Serializable {

    /**
     * 箱号
     */
    @XdoSize(max = 15, message = "箱号长度不能超过15位字节长度(一个汉字2位字节长度)!")
    @NotEmpty(message = "箱号不能为空!")
    private String boxNo;


    /**
     * 装箱明细数据
     */
    private List<BizIPurchaseListParam> purchaseList;
}
