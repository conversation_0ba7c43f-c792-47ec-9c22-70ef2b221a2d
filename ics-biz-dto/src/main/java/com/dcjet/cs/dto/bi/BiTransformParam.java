package com.dcjet.cs.dto.bi;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date: 2019-6-12
 */
@Getter
@Setter
@ApiModel(description = "获取转换信息传入参数")
public class BiTransformParam implements Serializable {
    private static final long serialVersionUID = 42L;

    /**
     * 转换前值
     */
    @NotEmpty(message = "{转换前值不能为空}")
    @ApiModelProperty(value = "转换前值", required = true)
    private String origin;

    /**
     * 转换后值
     */
    @NotEmpty(message = "{转换后值不能为空}")
    @ApiModelProperty(value = "转换后值", required = true)
    private String dest;

    /**
     * 转换类型
     */
    @NotEmpty(message = "{转换类型不能为空}")
    @ApiModelProperty(value = "转换类型", required = true)
    private String type;
}
