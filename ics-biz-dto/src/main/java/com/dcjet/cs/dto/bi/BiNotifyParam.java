package com.dcjet.cs.dto.bi;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date: 2021-4-21
 */
@Setter
@Getter
@ApiModel(value = "notify信息传入参数")
public class BiNotifyParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;
    /**
     * notify代码
     */
    @XdoSize(max = 50, message = "{notify代码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("notify代码")
    private String notifyCode;
    /**
     * notify名称
     */
    @XdoSize(max = 255, message = "{notify名称长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("notify名称")
    private String notifyName;
    /**
     * notify详细信息
     */
    @XdoSize(max = 1000, message = "{notify详细信息长度不能超过1000位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("notify详细信息")
    private String notifyAddress;
    /**
     * 备注
     */
    @XdoSize(max = 255, message = "{备注长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("备注")
    private String remark;
    /**
     * 所属企业编码
     */
    @XdoSize(max = 20, message = "{所属企业编码长度不能超过20位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("所属企业编码")
    private String tradeCode;
    /**
     * 客户SID
     */
    @XdoSize(max = 50, message = "{客户SID长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("客户SID")
    private String headId;
    /**
     * 制单人姓名
     */
    @XdoSize(max = 50, message = "{制单人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("制单人姓名")
    private String insertUserName;
    /**
     * 修改人姓名
     */
    @XdoSize(max = 50, message = "{修改人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("修改人姓名")
    private String updateUserName;
    /**
     * 客户CODE
     */
    @XdoSize(max = 50, message = "{客户CODE长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("客户CODE")
    private String clientCode;
}
