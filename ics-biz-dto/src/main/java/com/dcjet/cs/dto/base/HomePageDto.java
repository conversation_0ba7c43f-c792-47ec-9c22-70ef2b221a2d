package com.dcjet.cs.dto.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date: 2019-12-17
 */
@ApiModel(value = "首页待审核、预警返回数据")
@Setter
@Getter
public class HomePageDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 类型
     */
    @ApiModelProperty("类型")
    private String type;
    /**
     * 类型
     */
    @ApiModelProperty("类型名称")
    private String name;
    /**
     * 数量
     */
    @ApiModelProperty("数量")
    private Integer count;
    /**
     * 排序
     */
    @ApiModelProperty("排序")
    private  Integer orderId;
    /**
     * 路径
     */
    @ApiModelProperty("路径")
    private  String url;
}
