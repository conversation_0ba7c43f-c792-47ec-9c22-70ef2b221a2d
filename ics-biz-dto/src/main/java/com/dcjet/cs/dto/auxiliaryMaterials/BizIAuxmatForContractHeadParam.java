package com.dcjet.cs.dto.auxiliaryMaterials;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

import lombok.Getter;
import lombok.Setter;
import com.xdo.validation.annotation.XdoSize;
/**
 *
 * <AUTHOR>
 * @date: 2025-5-22
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class BizIAuxmatForContractHeadParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 主键id
     */
	@NotEmpty(message="主键id不能为空！")
	@XdoSize(max = 40, message = "主键id长度不能超过40位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("主键id")
	private  String id;
	/**
     * 业务类型，下拉框
     */
	@XdoSize(max = 60, message = "业务类型长度不能超过60位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("业务类型，下拉框")
	private  String businessType;
	/**
     * 合同号，文本
     */
	@XdoSize(max = 120, message = "合同号长度不能超过120位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("合同号，文本")
	private  String contractNo;
	/**
     * 买方
     */
	@XdoSize(max = 400, message = "买方长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("买方")
	private  String customerName;
	/**
     * 卖方
     */
	@XdoSize(max = 400, message = "卖方长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("卖方")
	private  String supplierName;
	/**
     * 签约日期，日期控件
     */
	@ApiModelProperty("签约日期控件")
	private  Date signDate;
	/**
    * 签约日期，日期控件-开始
    */
	@ApiModelProperty("签约日期开始")
	private String signDateFrom;
	/**
    * 签约日期，日期控件-结束
    */
	@ApiModelProperty("签约日期-结束")
    private String signDateTo;
	/**
     * 合同生效期，日期控件
     */
	@ApiModelProperty("合同生效期，日期控件")
	private  Date contractStartDate;
	/**
    * 合同生效期，日期控件-开始
    */
	@ApiModelProperty("合同生效期，日期控件-开始")
	private String contractStartDateFrom;
	/**
    * 合同生效期，日期控件-结束
    */
	@ApiModelProperty("合同生效期，日期控件-结束")
    private String contractStartDateTo;
	/**
     * 合同有效期，日期控件
     */
	@ApiModelProperty("合同有效期，日期控件")
	private  Date contractEndDate;
	/**
     * 签约地点，下拉框
     */
	@XdoSize(max = 100, message = "签约地点长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("签约地点，下拉框")
	private  String signPlace;
	/**
     * 签约地点(英文)，文本
     */
	@XdoSize(max = 100, message = "签约地点(英文)长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("签约地点(英文)，文本")
	private  String signPlaceEn;
	/**
     * 装运港，下拉框
     */
	@XdoSize(max = 100, message = "装运港长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("装运港，下拉框")
	private  String portOfShipment;
	/**
     * 目的港，下拉框
     */
	@XdoSize(max = 100, message = "目的港长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("目的港，下拉框")
	private  String portOfDestination;
	/**
     * 报关口岸，下拉框
     */
	@XdoSize(max = 100, message = "报关口岸，下拉框长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("报关口岸，下拉框")
	private  String customsPort;
	/**
     * 付款方式，下拉框
     */
	@XdoSize(max = 40, message = "付款方式长度不能超过40位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("付款方式，下拉框")
	private  String paymentMethod;
	/**
     * 币种，下拉框
     */
	@XdoSize(max = 20, message = "币种长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("币种，下拉框")
	private  String currency;
	/**
     * 备注，文本
     */
	@XdoSize(max = 400, message = "备注长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("备注，文本")
	private  String remark;
	/**
     * 单据状态，文本
     */
	@XdoSize(max = 20, message = "单据状态长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("单据状态，文本")
	private  String docStatus;
	/**
     * 确认时间，日期控件
     */
	@ApiModelProperty("确认时间，日期控件")
	private  Date confirmDate;
	/**
     * 审核状态，文本
     */
	@XdoSize(max = 20, message = "审核状态长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("审核状态，文本")
	private  String auditStatus;
	/**
     * 数据状态
     */
	@XdoSize(max = 10, message = "数据状态不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("数据状态")
	private  String dataState;
	/**
     * 版本号
     */
	@XdoSize(max = 10, message = "版本号不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("版本号")
	private  String versionNo;
	/**
     * 业务编码
     */
	@XdoSize(max = 10, message = "业务编码长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("业务编码")
	private  String tradeCode;
	/**
     * 所属机构编码
     */
	@XdoSize(max = 10, message = "所属机构编码长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("所属机构编码")
	private  String sysOrgCode;
	/**
     * 父级id
     */
	@XdoSize(max = 40, message = "父级id长度不能超过40位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("父级id")
	private  String parentId;
	/**
     * 创建人
     */
	@NotEmpty(message="创建人不能为空！")
	@XdoSize(max = 50, message = "创建人长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("创建人")
	private  String createBy;
	/**
     * 创建时间
     */
	@NotNull(message="创建时间不能为空！")
	@ApiModelProperty("创建时间")
	private  Date createTime;
	/**
    * 创建时间-开始
    */
	@ApiModelProperty("创建时间-开始")
	private String createTimeFrom;
	/**
    * 创建时间-结束
    */
	@ApiModelProperty("创建时间-结束")
    private String createTimeTo;
	/**
     * 修改人
     */
	@XdoSize(max = 50, message = "修改人长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("修改人")
	private  String updateBy;
	/**
     * 创建人姓名
     */
	@XdoSize(max = 50, message = "创建人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("创建人姓名")
	private  String insertUserName;
	/**
     * 修改人姓名
     */
	@XdoSize(max = 50, message = "修改人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("修改人姓名")
	private  String updateUserName;
	/**
     * 扩展字段1
     */
	@XdoSize(max = 200, message = "扩展字段1长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段1")
	private  String extend1;
	/**
     * 扩展字段2
     */
	@XdoSize(max = 200, message = "扩展字段2长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段2")
	private  String extend2;
	/**
     * 扩展字段3
     */
	@XdoSize(max = 200, message = "扩展字段3长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段3")
	private  String extend3;
	/**
     * 扩展字段4
     */
	@XdoSize(max = 200, message = "扩展字段4长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段4")
	private  String extend4;
	/**
     * 扩展字段5
     */
	@XdoSize(max = 200, message = "扩展字段5长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段5")
	private  String extend5;
	/**
     * 扩展字段6
     */
	@XdoSize(max = 200, message = "扩展字段6长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段6")
	private  String extend6;
	/**
     * 扩展字段7
     */
	@XdoSize(max = 200, message = "扩展字段7长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段7")
	private  String extend7;
	/**
     * 扩展字段8
     */
	@XdoSize(max = 200, message = "扩展字段8长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段8")
	private  String extend8;
	/**
     * 扩展字段9
     */
	@XdoSize(max = 200, message = "扩展字段9长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段9")
	private  String extend9;
	/**
     * 扩展字段10
     */
	@XdoSize(max = 200, message = "扩展字段10长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("扩展字段10")
	private  String extend10;

	private List<String> orderNo;
}
