package com.dcjet.cs.dto.importedCigarettes;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
/**
 * 
 * <AUTHOR>
 * @date: 2025-3-7
 */
@ApiModel(value = "返回信息")
@Setter @Getter
public class BizIContractHeadDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 主键
      */
    @ApiModelProperty("主键")
	private  String sid;
	/**
      * 业务类型
      */
    @ApiModelProperty("业务类型")
	private  String businessType;
	/**
      * 计划编号
      */
    @ApiModelProperty("计划编号")
	private  String planNo;
	/**
      * 计划年度（格式: YYYY）
      */
    @ApiModelProperty("计划年度（格式: YYYY）")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date planYear;
	private  String planYearStr;
	/**
      * 上下半年（如：上半年/H1）
      */
    @ApiModelProperty("上下半年（如：上半年/H1）")
	private  String halfYear;
	/**
      * 客户
      */
    @ApiModelProperty("客户")
	private  String buyer;
	/**
      * 供应商
      */
    @ApiModelProperty("供应商")
	private  String seller;
	/**
      * 合同编号
      */
    @ApiModelProperty("合同号")
	private  String contractNo;
	/**
      * 合同生效日期
      */
    @ApiModelProperty("合同生效日期")
	private  String contractEffectiveDate;
	/**
      * 合同有效期
      */
    @ApiModelProperty("合同有效期")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private  Date contractExpiryDate;
	/**
      * 签约日期
      */
    @ApiModelProperty("签约日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private  Date signDate;
	/**
      * 装货港
      */
    @ApiModelProperty("装货港")
	private  String loadingPort;
	/**
      * 到货港
      */
    @ApiModelProperty("到货港")
	private  String arrivalPort;
	/**
      * 贸易条款
      */
    @ApiModelProperty("贸易条款")
	private  String tradeTerms;
	/**
      * 价格条款对应的港口
      */
    @ApiModelProperty("价格条款对应的港口")
	private  String priceTermPort;
	/**
      * 出口国家或地区
      */
    @ApiModelProperty("出口国家或地区")
	private  String exportCountry;
	/**
      * 合同总金额
      */
    @ApiModelProperty("合同总金额")
	private  BigDecimal totalAmount;
	/**
      * 合同总数量
      */
    @ApiModelProperty("合同总数量")
	private  Integer totalQuantity;
	/**
      * 短溢数%
      */
    @ApiModelProperty("短溢数%")
	private  BigDecimal shortOverPercent;
	private  String shortOverPercentStr;
	/**
	 * 备注
	 */
	@ApiModelProperty("制单人")
	private  String note;
	/**
      * 制单人
      */
    @ApiModelProperty("制单人")
	private  String preparedBy;
	/**
      * 制单时间
      */
    @ApiModelProperty("制单时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date prepareTime;
	/**
      * 数据状态
      */
    @ApiModelProperty("数据状态")
	private  String dataStatus;
	/**
      * 确认时间
      */
    @ApiModelProperty("确认时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date confirmTime;
	/**
      * 审批状态
      */
    @ApiModelProperty("审批状态")
	private  String approvalStatus;
	/**
      * 版本号
      */
    @ApiModelProperty("版本号")
	private  String versionNo;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String tradeCode;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String parentId;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String insertUser;
	/**
      * 
      */
    @ApiModelProperty("")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date insertTime;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String updateUser;
	/**
      * 
      */
    @ApiModelProperty("")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String insertUserName;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String updateUserName;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend1;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend2;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend3;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend4;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend5;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend6;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend7;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend8;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend9;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend10;

	private BigDecimal planQuantity;
}
