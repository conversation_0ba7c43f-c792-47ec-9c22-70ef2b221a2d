package com.dcjet.cs.dto.bi;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date: 2020-8-24
 */
@Setter
@Getter
@ApiModel(value = "发票参数传入参数")
public class BiInvoiceParamsParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;
    /**
     * 发票参数类型
     */
    @XdoSize(max = 50, message = "{发票参数类型长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("发票参数类型")
    private String paramsType;
    /**
     * 发票参数海关编码
     */
    @NotBlank(message = "{海关计量单位不能为空}")
    @XdoSize(max = 100, message = "{发票参数海关编码长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("发票参数海关编码")
    private String paramsCode;
    /**
     * 发票参数海关编码名称
     */
    @XdoSize(max = 160, message = "{发票参数海关编码名称长度不能超过160位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("发票参数海关编码名称")
    private String paramsName;
    /**
     * 单数对应名称
     */
    @XdoSize(max = 100, message = "{单数对应名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("单数对应名称")
    private String singerName;
    /**
     * 复数对应名称
     */
    @XdoSize(max = 50, message = "{复数对应名称长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("复数对应名称")
    private String multiName;
    /**
     * 备注
     */
    @XdoSize(max = 250, message = "{备注长度不能超过250位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("备注")
    private String note;
    /**
     * 所属企业编码
     */
    @XdoSize(max = 20, message = "{所属企业编码长度不能超过20位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("所属企业编码")
    private String tradeCode;
    /**
     * 制单人姓名
     */
    @XdoSize(max = 50, message = "{制单人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("制单人姓名")
    private String insertUserName;
    /**
     * 修改人姓名
     */
    @XdoSize(max = 50, message = "{修改人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("修改人姓名")
    private String updateUserName;
}
