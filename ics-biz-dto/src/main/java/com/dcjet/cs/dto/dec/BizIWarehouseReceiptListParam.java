package com.dcjet.cs.dto.dec;

import com.dcjet.cs.dto.utils.ValidatorUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 进口管理-订单信息表头
 *
 * <AUTHOR>
 * @date 2025-03-07 15:37:18
 */
@Getter
@Setter
public class BizIWarehouseReceiptListParam implements Serializable{
    private static final long serialVersionUID = 1L;
    /**
     * 主建sid
     * 字符类型(50)
     * 非必填
     */

    private String sid;

    /**
     * 制单人
     * 字符类型(50)
     * 非必填
     */
    private String insertUser;

    /**
     * 订单制单时间
     * timestamp
     * 非必填
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;

    /**
     * 订单制单时间-开始时间
     */
    private String insertTimeFrom;

    /**
     * 订单制单时间-结束时间
     */
    private String insertTimeTo;

    /**
     * 创建人姓名
     * 字符类型(50)
     * 非必填
     */
    private String insertUserName;

    /**
     * 更新人
     * 字符类型(50)
     * 非必填
     */
    private String updateUser;

    /**
     * 更新时间
     * timestamp
     * 非必填
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 更新时间-开始时间
     */

    private String updateTimeFrom;

    /**
     * 更新时间-结束时间
     */

    private String updateTimeTo;

    /**
     * 更新人姓名
     * 字符类型(50)
     * 非必填
     */
    private String updateUserName;

    /**
     * 企业代码
     * 字符类型(50)
     * 非必填
     */
    private String tradeCode;

    /**
     * 业务类型
     * 字符类型(60)
     * 非必填
     */
    private String businessType;

    /**
     */
    private String parentId;

    /**
     * 版本号
     * 字符类型(10)
     * 非必填
     */
    private String versionNo;

    /**
     * 数据状态
     * 字符类型(10)
     * 非必填
     */
    private String dataStatus;

    /**
     * 拓展字段1
     * 字符类型(200)
     * 非必填
     */
    private String extend1;

    /**
     * 拓展字段2
     * 字符类型(200)
     * 非必填
     */
    private String extend2;

    /**
     * 拓展字段3
     * 字符类型(200)
     * 非必填
     */
    private String extend3;

    /**
     * 拓展字段4
     * 字符类型(200)
     * 非必填
     */
    private String extend4;

    /**
     * 拓展字段5
     * 字符类型(200)
     * 非必填
     */
    private String extend5;

    /**
     * 拓展字段6
     * 字符类型(200)
     * 非必填
     */
    private String extend6;

    /**
     * 拓展字段7
     * 字符类型(200)
     * 非必填
     */
    private String extend7;

    /**
     * 拓展字段8
     * 字符类型(200)
     * 非必填
     */
    private String extend8;

    /**
     * 拓展字段9
     * 字符类型(200)
     * 非必填
     */
    private String extend9;

    /**
     * 拓展字段10
     * 字符类型(200)
     * 非必填
     */
    private String extend10;




    /**
     *商品名称
     */
    private String goodsName;

    /**
     *数量
     */
    private BigDecimal qty;

    /**
     *单位
     */
    private String unit;

    /**
     *进口发票号码
     */
    private String invoiceNumber;



    /**
     *外币单价
     */
    private BigDecimal foreignUnitPrice;

    /**
     *人民币单价
     */
    private BigDecimal rmbUnitPrice;

    /**
     *外币货价
     */
    @NotNull(message = "外币货价不能为空!")
    @Digits(integer = 19, fraction = 2, message = "外币货价必须为数字,整数位最大19位,小数最大2位!")
    private BigDecimal foreignPrices;

    /**
     *人民币货价
     */
    private BigDecimal rmbPrices;

    /**
     *关税
     */
    @Digits(integer = 19, fraction = 2, message = "关税必须为数字,整数位最大19位,小数最大2位!")
    private BigDecimal tariff;

    /**
     *消费税
     */
    @Digits(integer = 19, fraction = 2, message = "消费税必须为数字,整数位最大19位,小数最大2位!")
    private BigDecimal consumptionTax;

    /**
     *增值税
     */
    @Digits(integer = 19, fraction = 2, message = "增值税必须为数字,整数位最大19位,小数最大2位!")
    private BigDecimal valueAddedTax;



    /**
     *商品金额小计
     */
    private BigDecimal producAmount;

    /**
     *税金金额小计
     */
    private BigDecimal taxAmount;


    /**
     *成本金额小计
     */
    private BigDecimal costAmount;

    /**
     *合计金额
     */
    private BigDecimal totalAmount;

    /**
     * 能否提交
     * @return
     */
    public String canSubmit(int index) {
        return ValidatorUtil.validation(index,this);
    }

}