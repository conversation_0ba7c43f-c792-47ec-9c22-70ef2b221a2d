package com.dcjet.cs.dto.bi;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date: 2021-5-24
 */
@Setter
@Getter
@ApiModel(value = "传入参数")
public class GwstdDocTypeParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;
    /**
     * 企业代码
     */
    @XdoSize(max = 50, message = "{企业代码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("企业代码")
    private String tradeCode;
    /**
     * 制单人姓名
     */
    @XdoSize(max = 50, message = "{制单人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("制单人姓名")
    private String insertUserName;
    /**
     * 修改人姓名
     */
    @XdoSize(max = 50, message = "{修改人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("修改人姓名")
    private String updateUserName;
    /**
     * 状态 0正常
     */
    @XdoSize(max = 1, message = "{状态 0正常长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("状态 0正常")
    private String status;
    /**
     * 文档类型CODE
     */
    @XdoSize(max = 20, message = "{文档类型代码长度不能超过20位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("文档类型CODE")
    private String typeCode;
    /**
     * 文档类型NAME
     */
    @XdoSize(max = 50, message = "{文档类型名称长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("文档类型NAME")
    private String typeName;
    /**
     * 备注
     */
    @XdoSize(max = 255, message = "{备注长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("备注")
    private String note;
}
