package com.dcjet.cs.dto.bi;

import com.dcjet.cs.dto.base.BasicDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date: 2019-7-3
 */
@ApiModel(value = "商业伙伴信息返回信息")
@Setter
@Getter
public class BiOutEnterpriseDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;
    /**
     * 承揽者海关代码
     */
    @ApiModelProperty("承揽者海关代码")
    private String declareCode;
    /**
     * 承揽者名称
     */
    @ApiModelProperty("承揽者名称")
    private String companyName;
    /**
     * 承揽者社会信用代码
     */
    @ApiModelProperty("承揽者社会信用代码")
    private String creditCode;
    /**
     * 承揽者所在地主管海关
     */
    @ApiModelProperty("承揽者所在地主管海关")
    private String masterCustoms;
    /**
     * 承揽者所在地地区代码
     */
    @ApiModelProperty("承揽者所在地地区代码")
    private String districtCode;
    /**
     * 承揽者法人/电话
     */
    @ApiModelProperty("承揽者法人/电话")
    private String telephoneNo;
    /**
     * 承揽者联系人/电话
     */
    @ApiModelProperty("承揽者联系人/电话")
    private String mobilePhone;
    /**
     * 所属企业编码
     */
    @ApiModelProperty("所属企业编码")
    private String tradeCode;
    /**
     * 承揽者编码
     */
    @ApiModelProperty("承揽者编码")
    private String companyCode;
    /**
     *  承揽者所在地区名称
     */
    private String districtName;
}
