package com.dcjet.cs.dto.bi;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date: 2019-7-3
 */
@Setter
@Getter
@ApiModel(value = "商业伙伴传入参数")
public class BiOutEnterpriseParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;
    /**
     * 承揽者海关代码
     */
    @XdoSize(max = 10, message = "{承揽者海关代码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("承揽者海关代码")
    private String declareCode;
    /**
     * 承揽者名称
     */
    @NotEmpty(message = "{承揽者名称不能为空！}")
    @XdoSize(max = 100, message = "{承揽者名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("承揽者名称")
    private String companyName;
    /**
     * 承揽者社会信用代码
     */
    @XdoSize(max = 20, message = "{承揽者社会信用代码长度不能超过20位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("承揽者社会信用代码")
    private String creditCode;
    /**
     * 承揽者所在地主管海关
     */
    @NotEmpty(message = "{承揽者所在地主管海关不能为空！}")
    @XdoSize(max = 4, message = "{承揽者所在地主管海关长度不能超过4位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("承揽者所在地主管海关")
    private String masterCustoms;
    /**
     * 承揽者所在地地区代码
     */
    @NotEmpty(message = "{承揽者所在地地区代码不能为空！}")
    @XdoSize(max = 5, message = "{承揽者所在地地区代码长度不能超过5位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("承揽者所在地地区代码")
    private String districtCode;
    /**
     * 承揽者法人/电话
     */
    @XdoSize(max = 255, message = "{承揽者法人/电话长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("承揽者法人/电话")
    private String telephoneNo;
    /**
     * 承揽者联系人/电话
     */
    @XdoSize(max = 255, message = "{承揽者联系人/电话长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("承揽者联系人/电话")
    private String mobilePhone;
    /**
     * 所属企业编码
     */
    @XdoSize(max = 20, message = "{所属企业编码长度不能超过20位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("所属企业编码")
    private String tradeCode;
    /**
     * 承揽者编码
     */
    @XdoSize(max = 50, message = "{承揽者代码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("承揽者编码")
    private String companyCode;
}
