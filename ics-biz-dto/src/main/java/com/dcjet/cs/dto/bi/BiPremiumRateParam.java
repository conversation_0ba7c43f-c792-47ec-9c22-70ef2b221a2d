package com.dcjet.cs.dto.bi;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;

@Getter
@Setter
@ApiModel(value = "保费率传入参数")
public class BiPremiumRateParam {
    private String sid;
    @ApiModelProperty("年份")
    @NotEmpty(message = "{年份不能为空！}")
    private String year;
    @ApiModelProperty("保费率")
    private BigDecimal premiumRate;
    @ApiModelProperty("启用状态 0 停用 1 启用")
    private String status;

    private String tradeCode;
    private String yearFrom;
    private String yearTo;
}
