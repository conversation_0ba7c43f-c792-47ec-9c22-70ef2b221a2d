package com.dcjet.cs.dto.dec;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 进口管理-进货信息表头
 */
@Getter
@Setter
@ApiModel(value = "信息表头-返回信息")
public class BizIReceiptListDto implements Serializable{


    /**
     * 主建SID
     * 字符类型(50)
     * 非必填
     */
    private String sid;

    private String headId;

    private String tradeName;
    private String shipmentQuantity;
    private String actualQuantityIssued;
    private String unit;
    private BigDecimal decPrice;
    private String decPriceStr;
    private String curr;
    private BigDecimal amount;
    private String amountStr;

    private String insertUser;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;

    private String insertUserName;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String updateUser;

    private Date updateTime;

    private String updateUserName;

    private String tradeCode;

}