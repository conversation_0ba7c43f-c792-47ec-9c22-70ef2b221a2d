package com.dcjet.cs.dto.bi;

import com.dcjet.cs.dto.base.BasicDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2019-4-18
 */
@ApiModel(value = "企业基础信息返回信息")
@Setter
@Getter
public class BiClientInformationDto extends BasicDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 客户类型(供应商 PRD、客户CLI、货代FOD、报关行CUT、企业COM)
     */
    @ApiModelProperty("客户类型(供应商 PRD、客户CLI、货代FOD、报关行CUT、企业COM)")
    private String customerType;

    /**
     * 客户代码
     */
    @ApiModelProperty("客户代码")
    private String customerCode;

    /**
     * 客户中文名称
     */
    @ApiModelProperty("客户中文名称")
    private String companyName;

    /**
     * 海关信用等级
     */
    @ApiModelProperty("海关信用等级")
    private String customsCreditRating;

    /**
     * 商检代码
     */
    @ApiModelProperty("商检代码")
    private String inspectionCode;

    /**
     * 社会信用代码
     */
    @ApiModelProperty("社会信用代码")
    private String creditCode;

    /**
     * 海关注册编码
     */
    @ApiModelProperty("海关注册编码")
    private String declareCode;

    /**
     * 企业名称缩写
     */
    @ApiModelProperty("企业名称缩写")
    private String companyNameShort;

    /**
     * 客户电话
     */
    @ApiModelProperty("客户电话")
    private String telephoneNo;

    /**
     * 客户联系人
     */
    @ApiModelProperty("客户联系人")
    private String linkmanName;

    /**
     * 联系人职务
     */
    @ApiModelProperty("联系人职务")
    private String linkmanDuty;

    /**
     * 联系人电话
     */
    @ApiModelProperty("联系人电话")
    private String mobilePhone;

    /**
     * 联系人邮箱
     */
    @ApiModelProperty("联系人邮箱")
    private String EMail;

    /**
     * 企业中文地址
     */
    @ApiModelProperty("企业中文地址")
    private String address;

    /**
     * 企业英文名称
     */
    @ApiModelProperty("企业英文名称")
    private String companyNameEn;

    /**
     * 英文国家
     */
    @ApiModelProperty("英文国家")
    private String countryEn;

    /**
     * 英文地区
     */
    @ApiModelProperty("英文地区")
    private String areaEn;

    /**
     * 英文城市
     */
    @ApiModelProperty("英文城市")
    private String cityEn;

    /**
     * 英文地址
     */
    @ApiModelProperty("英文地址")
    private String addressEn;

    /**
     * 客户电话(英文)
     */
    @ApiModelProperty("客户电话(英文)")
    private String telephoneNoEn;

    /**
     * 客户联系人(英文)
     */
    @ApiModelProperty("客户联系人(英文)")
    private String linkmanNameEn;

    /**
     * 联系人电话(英文)
     */
    @ApiModelProperty("联系人电话(英文)")
    private String mobilePhoneEn;

    /**
     * 联系人邮箱(英文)
     */
    @ApiModelProperty("联系人邮箱(英文)")
    private String EMailEn;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String note;

    /**
     * 所属企业编码
     */
    @ApiModelProperty("所属企业编码")
    private String tradeCode;

    /**
     * 联系人职务(英文)
     */
    @ApiModelProperty("联系人职务(英文)")
    private String linkmanDutyEn;

    @ApiModelProperty("海关信用等级名称")
    private String customsCreditRatingName;

    /**
     * AEO代码
     */
    @ApiModelProperty("AEO代码")
    private String aeoCode;

    /**
     * AEO代码
     */
    @ApiModelProperty("MASTER_CUSTOMS")
    private String masterCustoms;

    /**
     * 传真
     */
    @ApiModelProperty("传真")
    private String fax;

    /**
     * 邮编
     */
    @ApiModelProperty("邮编")
    private String postal;

    //add by zhangjl 2019-09-12
    /**
     * 中文国家
     */
    @ApiModelProperty("中文国家")
    private String country;

    /**
     * 中文地区
     */
    @ApiModelProperty("中文地区")
    private String area;

    /**
     * 中文城市
     */
    @ApiModelProperty("中文城市")
    private String city;

    /**
     * 发票中文地址
     */
    @ApiModelProperty("发票中文地址")
    private String invoiceAddress;

    /**
     * 发票英文地址
     */
    @ApiModelProperty("发票英文地址")
    private String invoiceAddressEn;

    /**
     * 送货中文地址
     */
    @ApiModelProperty("送货中文地址")
    private String deliverAddress;

    /**
     * 送货英文地址
     */
    @ApiModelProperty("送货英文地址")
    private String deliverAddressEn;

    /**
     * 状态(0:启用，1：停用)
     */
    @ApiModelProperty("状态")
    private String status;


    @ApiModelProperty("shipToName")
    private String shipToName;

    @ApiModelProperty("shipToCode")
    private String shipToCode;

    @ApiModelProperty("shipToAddress")
    private String shipToAddress;

    /**
     * 是否授权(0:未授权; 1:已授权)
     */
    @ApiModelProperty("是否授权")
    private String authorize;

    /**
     * 授权截止日期
     */
    @ApiModelProperty("授权截止日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date authorizeDeadline;
    @ApiModelProperty("减免税货物使用地点")
    private String freeAddress;

    /**
     * 减免税物资属性
     */
    @ApiModelProperty("减免税物资属性")
    private String freeProperties;

    /**
     * 成本中心
     */
    @ApiModelProperty("成本中心")
    private String costCenter;

    /**
     * 报关人员
     */
    @ApiModelProperty("报关人员")
    private String decPersonnel;

    /**
     * 报关人员电话
     */
    @ApiModelProperty("报关人员电话")
    private String decPersonnelTel;
}
