package com.dcjet.cs.dto.importedCigarettes;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
/**
 * 
 * <AUTHOR>
 * @date: 2025-3-11
 */
@ApiModel(value = "返回信息")
@Setter @Getter
public class BizIContractListDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String sid;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String businessType;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String dataStatus;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String versionNo;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String tradeCode;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String parentId;
	/**
      * 关联主合同表ID
      */
    @ApiModelProperty("关联主合同表ID")
	private  String headId;
	/**
      * 商品品牌
      */
    @ApiModelProperty("商品名称")
	private  String goodsBrand;
	/**
      * 单位
      */
    @ApiModelProperty("单位")
	private  String unit;
	/**
      * 计划数量
      */
    @ApiModelProperty("计划数量")
	private  BigDecimal planQuantity;
	/**
      * 已形成合同数量
      */
    @ApiModelProperty("已形成合同数量")
	private  BigDecimal formedQuantity;
	/**
      * 可执行合同量
      */
    @ApiModelProperty("可执行合同量")
	private  BigDecimal executableQuantity;
	/**
      * 合同数量
      */
    @ApiModelProperty("合同数量")
	private  BigDecimal contractQuantity;
	/**
      * 货币代码
      */
    @ApiModelProperty("币制")
	private  String curr;
	/**
      * 单价
      */
    @ApiModelProperty("单价")
	private  BigDecimal unitPrice;
	/**
      * 总值
      */
    @ApiModelProperty("总值")
	private  BigDecimal totalValue;
	/**
      * 商品类别
      */
    @ApiModelProperty("商品类别")
	private  String goodsCategory;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String insertUser;
	/**
      * 
      */
    @ApiModelProperty("")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date insertTime;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String updateUser;
	/**
      * 
      */
    @ApiModelProperty("")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String insertUserName;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String updateUserName;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend1;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend2;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend3;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend4;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend5;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend6;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend7;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend8;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend9;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend10;
}
