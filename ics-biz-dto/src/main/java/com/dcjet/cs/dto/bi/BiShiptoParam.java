package com.dcjet.cs.dto.bi;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
/**
 *
 * <AUTHOR>
 * @date: 2019-7-26
 */
@Setter @Getter
@ApiModel(value = "客户对于ShipTo信息传入参数")
public class BiShiptoParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * shipto代码
     */
	@XdoSize(max = 50, message = "{shipto代码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("shipto代码")
	private  String shipToCode;
	/**
     * shipto名称
     */
	@XdoSize(max = 255, message = "{shipto名称长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("shipto名称")
	private  String shipToName;
	/**
     * shipto地址
     */
	@XdoSize(max = 512, message = "{shipto地址长度不能超过512位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("shipto地址")
	private  String shipToAddress;
	/**
     * 备注
     */
	@XdoSize(max = 255, message = "{备注长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("备注")
	private  String remark;
	/**
     * 所属企业编码
     */
	@XdoSize(max = 20, message = "{所属企业编码长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("所属企业编码")
	private  String tradeCode;
	/**
     * 客户SID
     */
	@XdoSize(max = 50, message = "{客户SID长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("客户SID")
	private  String headId;

	@ApiModelProperty("客户CODE")
	private  String clientCode;
}
