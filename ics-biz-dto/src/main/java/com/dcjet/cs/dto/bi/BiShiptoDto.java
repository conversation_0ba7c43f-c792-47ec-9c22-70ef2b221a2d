package com.dcjet.cs.dto.bi;

import com.dcjet.cs.dto.base.BasicDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
/**
 * 
 * <AUTHOR>
 * @date: 2019-7-26
 */
@ApiModel(value = "客户对于ShipTo信息返回信息")
@Setter @Getter
public class BiShiptoDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 主键
      */
    @ApiModelProperty("主键")
	private  String sid;
	/**
      * shipto代码
      */
    @ApiModelProperty("shipto代码")
	private  String shipToCode;
	/**
      * shipto名称
      */
    @ApiModelProperty("shipto名称")
	private  String shipToName;
	/**
      * shipto地址
      */
    @ApiModelProperty("shipto地址")
	private  String shipToAddress;
	/**
      * 备注
      */
    @ApiModelProperty("备注")
	private  String remark;
	/**
      * 所属企业编码
      */
    @ApiModelProperty("所属企业编码")
	private  String tradeCode;
	/**
      * 客户SID
      */
    @ApiModelProperty("客户SID")
	private  String headId;
	@ApiModelProperty("客户CODE")
	private  String clientCode;
}
