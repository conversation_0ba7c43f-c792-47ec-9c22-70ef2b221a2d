package com.dcjet.cs.dto.dec;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 进货信息表体 汇总数据
 * 数量、总值、折扣金额、货款金额
 */
@Getter
@Setter
public class BizIPurchaseListSumData implements Serializable {

    /**
     * 数量
     * 数据库字段:qty
     * 数值类型(19,6)
     */
    @ApiModelProperty("数量")
    private BigDecimal qty;

    /**
     * 总价
     * 数据库字段:dec_total
     * 数值类型(19,2)
     */
    @ApiModelProperty("总价")
    private BigDecimal decTotal;

    /**
     * 折扣金额
     * 数据库字段:discount_amount
     * 数值类型(19,2)
     */
    @ApiModelProperty("折扣金额")
    private BigDecimal discountAmount;

    /**
     * 贷款金额
     * 数据库字段:payment_amount
     * 数值类型(19,2)
     */
    @ApiModelProperty("贷款金额")
    private BigDecimal paymentAmount;
}
