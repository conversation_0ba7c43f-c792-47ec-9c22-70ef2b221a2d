package com.dcjet.cs.dto.dec;


import lombok.Getter;
import lombok.Setter;
import java.util.Date;
import java.math.BigDecimal;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;



/**
 * 进货管理-表头数据
 */
@Getter
@Setter
@ApiModel(value = "进货管理-表头数据-返回信息")
public class BizIncomingGoodsHeadDto implements Serializable{

    /**
     * 主键ID
     * 数据库字段:ID
     * 字符类型(40)
     */
    @ApiModelProperty("主键ID")
    private String id;

    /**
     * 业务类型
     * 数据库字段:BUSINESS_TYPE
     * 字符类型(120)
     */
    @ApiModelProperty("业务类型")
    private String businessType;

    /**
     * 数据状态
     * 数据库字段:DATA_STATE
     * 字符类型(20)
     */
    @ApiModelProperty("数据状态")
    private String dataState;

    /**
     * 版本号
     * 数据库字段:VERSION_NO
     * 字符类型(20)
     */
    @ApiModelProperty("版本号")
    private String versionNo;

    /**
     * 企业10位编码
     * 数据库字段:TRADE_CODE
     * 字符类型(20)
     */
    @ApiModelProperty("企业10位编码")
    private String tradeCode;

    /**
     * 组织机构代码
     * 数据库字段:SYS_ORG_CODE
     * 字符类型(20)
     */
    @ApiModelProperty("组织机构代码")
    private String sysOrgCode;

    /**
     * 父级ID
     * 数据库字段:PARENT_ID
     * 字符类型(80)
     */
    @ApiModelProperty("父级ID")
    private String parentId;

    /**
     * 创建人
     * 数据库字段:CREATE_BY
     * 字符类型(100)
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     * 数据库字段:CREATE_TIME
     * 日期类型(6)
     */
    @ApiModelProperty("创建时间")
    private Date createTime;
    /**
     * 创建时间-开始时间
     */
    @ApiModelProperty("创建时间-开始时间")
    private Date createTimeFrom;

    /**
     * 创建时间-结束时间
     */
    @ApiModelProperty("创建时间-结束时间")
    private Date createTimeTo;

    /**
     * 更新人
     * 数据库字段:UPDATE_BY
     * 字符类型(100)
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 更新时间
     * 数据库字段:UPDATE_TIME
     * 日期类型(6)
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;
    /**
     * 更新时间-开始时间
     */
    @ApiModelProperty("更新时间-开始时间")
    private Date updateTimeFrom;

    /**
     * 更新时间-结束时间
     */
    @ApiModelProperty("更新时间-结束时间")
    private Date updateTimeTo;

    /**
     * 插入用户名
     * 数据库字段:INSERT_USER_NAME
     * 字符类型(100)
     */
    @ApiModelProperty("插入用户名")
    private String insertUserName;

    /**
     * 更新用户名
     * 数据库字段:UPDATE_USER_NAME
     * 字符类型(100)
     */
    @ApiModelProperty("更新用户名")
    private String updateUserName;

    /**
     * 扩展字段1
     * 数据库字段:EXTEND1
     * 字符类型(400)
     */
    @ApiModelProperty("扩展字段1")
    private String extend1;

    /**
     * 扩展字段2
     * 数据库字段:EXTEND2
     * 字符类型(400)
     */
    @ApiModelProperty("扩展字段2")
    private String extend2;

    /**
     * 扩展字段3
     * 数据库字段:EXTEND3
     * 字符类型(400)
     */
    @ApiModelProperty("扩展字段3")
    private String extend3;

    /**
     * 扩展字段4
     * 数据库字段:EXTEND4
     * 字符类型(400)
     */
    @ApiModelProperty("扩展字段4")
    private String extend4;

    /**
     * 扩展字段5
     * 数据库字段:EXTEND5
     * 字符类型(400)
     */
    @ApiModelProperty("扩展字段5")
    private String extend5;

    /**
     * 扩展字段6
     * 数据库字段:EXTEND6
     * 字符类型(400)
     */
    @ApiModelProperty("扩展字段6")
    private String extend6;

    /**
     * 扩展字段7
     * 数据库字段:EXTEND7
     * 字符类型(400)
     */
    @ApiModelProperty("扩展字段7")
    private String extend7;

    /**
     * 扩展字段8
     * 数据库字段:EXTEND8
     * 字符类型(400)
     */
    @ApiModelProperty("扩展字段8")
    private String extend8;

    /**
     * 扩展字段9
     * 数据库字段:EXTEND9
     * 字符类型(400)
     */
    @ApiModelProperty("扩展字段9")
    private String extend9;

    /**
     * 扩展字段10
     * 数据库字段:EXTEND10
     * 字符类型(400)
     */
    @ApiModelProperty("扩展字段10")
    private String extend10;

    /**
     * 合同号
     * 数据库字段:CONTRACT_NO
     * 字符类型(120)
     */
    @ApiModelProperty("合同号")
    private String contractNo;

    /**
     * 进货单号
     * 数据库字段:PURCHASE_NO
     * 字符类型(120)
     */
    @ApiModelProperty("进货单号")
    private String purchaseNo;

    /**
     * 客户
     * 数据库字段:CUSTOMER
     * 字符类型(400)
     */
    @ApiModelProperty("客户")
    private String customer;

    /**
     * 供应商
     * 数据库字段:SUPPLIER
     * 字符类型(400)
     */
    @ApiModelProperty("供应商")
    private String supplier;

    /**
     * 发票号
     * 数据库字段:INVOICE_NO
     * 字符类型(120)
     */
    @ApiModelProperty("发票号")
    private String invoiceNo;

    /**
     * 启运港
     * 数据库字段:PORT_OF_DEPARTURE
     * 字符类型(100)
     */
    @ApiModelProperty("启运港")
    private String portOfDeparture;

    /**
     * 目的地/港
     * 数据库字段:DESTINATION
     * 字符类型(100)
     */
    @ApiModelProperty("目的地/港")
    private String destination;

    /**
     * 付款方式
     * 数据库字段:PAYMENT_METHOD
     * 字符类型(40)
     */
    @ApiModelProperty("付款方式")
    private String paymentMethod;

    /**
     * 价格条款
     * 数据库字段:PRICE_TERM
     * 字符类型(20)
     */
    @ApiModelProperty("价格条款")
    private String priceTerm;

    /**
     * 价格条款对应港口
     * 数据库字段:PRICE_TERM_PORT
     * 字符类型(40)
     */
    @ApiModelProperty("价格条款对应港口")
    private String priceTermPort;

    /**
     * 船名航次
     * 数据库字段:VESSEL_VOYAGE
     * 字符类型(200)
     */
    @ApiModelProperty("船名航次")
    private String vesselVoyage;

    /**
     * 开航日期
     * 数据库字段:SAILING_DATE
     * 日期类型(6)
     */
    @ApiModelProperty("开航日期")
    private Date sailingDate;
    /**
     * 开航日期-开始时间
     */
    @ApiModelProperty("开航日期-开始时间")
    private Date sailingDateFrom;

    /**
     * 开航日期-结束时间
     */
    @ApiModelProperty("开航日期-结束时间")
    private Date sailingDateTo;

    /**
     * 预计到达日期
     * 数据库字段:EXPECTED_ARRIVAL_DATE
     * 日期类型(6)
     */
    @ApiModelProperty("预计到达日期")
    private Date expectedArrivalDate;
    /**
     * 预计到达日期-开始时间
     */
    @ApiModelProperty("预计到达日期-开始时间")
    private Date expectedArrivalDateFrom;

    /**
     * 预计到达日期-结束时间
     */
    @ApiModelProperty("预计到达日期-结束时间")
    private Date expectedArrivalDateTo;

    /**
     * 做销日期
     * 数据库字段:SALES_DATE
     * 日期类型(6)
     */
    @ApiModelProperty("做销日期")
    private Date salesDate;
    /**
     * 做销日期-开始时间
     */
    @ApiModelProperty("做销日期-开始时间")
    private Date salesDateFrom;

    /**
     * 做销日期-结束时间
     */
    @ApiModelProperty("做销日期-结束时间")
    private Date salesDateTo;

    /**
     * 合同金额
     * 数据库字段:CONTRACT_AMOUNT
     * 数值类型(19,4)
     */
    @ApiModelProperty("合同金额")
    private BigDecimal contractAmount;

    /**
     * 保险费率%
     * 数据库字段:INSURANCE_RATE
     * 数值类型(19,4)
     */
    @ApiModelProperty("保险费率%")
    private BigDecimal insuranceRate;

    /**
     * 投保加成%
     * 数据库字段:INSURANCE_MARKUP
     * 数值类型(19,4)
     */
    @ApiModelProperty("投保加成%")
    private BigDecimal insuranceMarkup;

    /**
     * 制单人
     * 数据库字段:DOCUMENT_CREATOR
     * 字符类型(20)
     */
    @ApiModelProperty("制单人")
    private String documentCreator;

    /**
     * 制单日期
     * 数据库字段:DOCUMENT_DATE
     * 日期类型(6)
     */
    @ApiModelProperty("制单日期")
    private Date documentDate;
    /**
     * 制单日期-开始时间
     */
    @ApiModelProperty("制单日期-开始时间")
    private Date documentDateFrom;

    /**
     * 制单日期-结束时间
     */
    @ApiModelProperty("制单日期-结束时间")
    private Date documentDateTo;

    /**
     * 单据状态
     * 数据库字段:DOCUMENT_STATUS
     * 字符类型(20)
     */
    @ApiModelProperty("单据状态")
    private String documentStatus;

    /**
     * 确认时间
     * 数据库字段:CONFIRM_TIME
     * 日期类型(6)
     */
    @ApiModelProperty("确认时间")
    private Date confirmTime;
    /**
     * 确认时间-开始时间
     */
    @ApiModelProperty("确认时间-开始时间")
    private Date confirmTimeFrom;

    /**
     * 确认时间-结束时间
     */
    @ApiModelProperty("确认时间-结束时间")
    private Date confirmTimeTo;

    /**
     * 审批状态
     * 数据库字段:APPROVAL_STATUS
     * 字符类型(20)
     */
    @ApiModelProperty("审批状态")
    private String approvalStatus;

    /**
     * 签约日期
     * 数据库字段:DATE_OF_CONTRACT
     * 日期类型(6)
     */
    @ApiModelProperty("签约日期")
    private Date dateOfContract;
    /**
     * 签约日期-开始时间
     */
    @ApiModelProperty("签约日期-开始时间")
    private Date dateOfContractFrom;

    /**
     * 签约日期-结束时间
     */
    @ApiModelProperty("签约日期-结束时间")
    private Date dateOfContractTo;

    /**
     * 是否流入下一个节点
     * 数据库字段:IS_NEXT
     * 字符类型(1)
     */
    @ApiModelProperty("是否流入下一个节点")
    private String isNext;



    /**
     * 购销合同号
     * 数据库字段:PURCHASE_CONTRACT_NO
     * 字符类型(60)
     */
    @ApiModelProperty("购销合同号")
    private String purchaseContractNo;


    /**
     * 报关单号
     * 数据库字段:ENTRY_NO
     * 字符类型(18)
     */
    @ApiModelProperty("报关单号")
    private String entryNo;


    /**
     * 报关日期
     * 数据库字段:ENTRY_DATE
     * 日期类型(6)
     */
    @ApiModelProperty("报关日期")
    private Date entryDate;
    /**
     * 报关日期-开始时间
     */
    @ApiModelProperty("报关日期-开始时间")
    private Date entryDateFrom;
    /**
     * 报关日期-结束时间
     */
    @ApiModelProperty("报关日期-结束时间")
    private Date entryDateTo;


}