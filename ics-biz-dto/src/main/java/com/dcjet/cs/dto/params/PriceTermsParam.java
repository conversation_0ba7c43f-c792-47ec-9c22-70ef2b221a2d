package com.dcjet.cs.dto.params;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Setter
@Getter
@ApiModel(value = "价格条款参数模型")
public class PriceTermsParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;

    /**
     * 参数代码
     */
    @ApiModelProperty("参数代码")
    private String paramCode;

    /**
     * 价格条款
     */
    @ApiModelProperty("价格条款")
    @NotNull(message = "价格条款不能为空！")
    @XdoSize(max = 10, message = "价格条款长度不能超过10位字节长度(一个汉字2位字节长度)!")
    private String priceTerm;

    /**
     * 价格条款描述
     */
    @ApiModelProperty("价格条款描述")
    @XdoSize(max = 80, message = "价格条款描述长度不能超过80位字节长度(一个汉字2位字节长度)!")
    private String priceTermDesc;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @XdoSize(max = 200, message = "备注长度不能超过200位字节长度(一个汉字2位字节长度)!")
    private String note;

    /**
     * 扩展字段1
     */
    @ApiModelProperty("扩展字段1")
    private String extend1;

    /**
     * 扩展字段2
     */
    @ApiModelProperty("扩展字段2")
    private String extend2;

    /**
     * 扩展字段3
     */
    @ApiModelProperty("扩展字段3")
    private String extend3;

    /**
     * 扩展字段4
     */
    @ApiModelProperty("扩展字段4")
    private String extend4;

    /**
     * 扩展字段5
     */
    @ApiModelProperty("扩展字段5")
    private String extend5;

    /**
     * 扩展字段6
     */
    @ApiModelProperty("扩展字段6")
    private String extend6;

    /**
     * 扩展字段7
     */
    @ApiModelProperty("扩展字段7")
    private String extend7;

    /**
     * 扩展字段8
     */
    @ApiModelProperty("扩展字段8")
    private String extend8;

    /**
     * 扩展字段9
     */
    @ApiModelProperty("扩展字段9")
    private String extend9;

    /**
     * 扩展字段10
     */
    @ApiModelProperty("扩展字段10")
    private String extend10;
}