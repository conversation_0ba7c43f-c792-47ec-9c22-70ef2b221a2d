package com.dcjet.cs.dto.bi;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
/**
 *
 * <AUTHOR>
 * @date: 2020-5-21
 */
@Setter @Getter
@ApiModel(value = "文档中心-文件柜传入参数")
public class GwstdDocCenterParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 状态 0正常
     */
	@XdoSize(max = 1, message = "{状态 0正常长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("状态 0正常")
	private  String status;
	/**
	 * 文档序号
	 */
	@XdoSize(max = 20, message = "{文档名称长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("文档序号")
	private  String documentNo;
	/**
     * 文档名称
     */
	@XdoSize(max = 100, message = "{文档名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("文档名称")
	private  String documentName;
	/**
     * 文档类型
     */
	@XdoSize(max = 20, message = "{文档类型长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("文档类型")
	private  String documentType;
	/**
     * 备注
     */
	@XdoSize(max = 100, message = "{备注长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("备注")
	private  String note;
	/**
	 * 存放地点
	 */
	@XdoSize(max = 100, message = "{存放地点长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("存放地点")
	private  String address;
	/**
	 * 创建日期-开始
	 */
	@ApiModelProperty("创建日期-开始")
	private String insertTimeFrom;
	/**
	 * 创建日期-结束
	 */
	@ApiModelProperty("创建日期-结束")
	private String insertTimeTo;

}
