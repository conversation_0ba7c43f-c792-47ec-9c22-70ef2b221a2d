package com.dcjet.cs.dto.common;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExcelImportDto<T> implements Serializable {

    /***
     *
     */
    private String correctKey;

    /***
     *
     */
    private String errorKey;

    /***
     * 正确的数据
     */
    private List<T> correctList;

    /***
     * 错误的数据
     */
    private List<T> wrongList;

}
