package com.dcjet.cs.dto.dec;

import com.xdo.domain.ExcelExportParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 导出传入参数
 *
 * <AUTHOR>
 * @date 2025-05-26 10:38:22
 */
@Getter
@Setter
@ApiModel(description = "查询传入参数")
public class  BizIncomingGoodsListExportParam extends ExcelExportParam {
    /**
     * 导出传入参数
     */
    @ApiModelProperty("查询参数")
    private  BizIncomingGoodsListParam exportColumns;
}
