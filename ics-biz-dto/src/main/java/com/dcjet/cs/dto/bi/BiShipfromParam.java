package com.dcjet.cs.dto.bi;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2019-12-17
 */
@Setter
@Getter
@ApiModel(value = "基础管理-客户对于Shipfrom信息传入参数")
public class BiShipfromParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     * 数据库字段:sid
     * 字符类型(40)
     */
    @XdoSize(max = 40, message = "主键长度不能超过40位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("主键")
    private String sid;

    /**
     * shipFrom代码
     * 数据库字段:ship_from_code
     * 字符类型(50)
     */
    @XdoSize(max = 50, message = "shipFrom代码长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("shipFrom代码")
    private String shipFromCode;

    /**
     * shipFrom名称
     * 数据库字段:ship_from_name
     * 字符类型(255)
     */
    @XdoSize(max = 255, message = "shipFrom名称长度不能超过255位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("shipFrom名称")
    private String shipFromName;

    /**
     * shipFrom地址
     * 数据库字段:ship_from_address
     * 字符类型(512)
     */
    @XdoSize(max = 512, message = "shipFrom地址长度不能超过512位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("shipFrom地址")
    private String shipFromAddress;

    /**
     * 备注
     * 数据库字段:remark
     * 字符类型(255)
     */
    @XdoSize(max = 255, message = "备注长度不能超过255位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 创建人
     * 数据库字段:insert_user
     * 字符类型(50)
     */
    @XdoSize(max = 50, message = "创建人长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("创建人")
    private String insertUser;

    /**
     * 创建日期
     * 数据库字段:insert_time
     * timestamp
     */
    @ApiModelProperty("创建日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;
    /**
     * 创建日期-开始时间
     */
    @ApiModelProperty("创建日期-开始时间")
    private String insertTimeFrom;

    /**
     * 创建日期-结束时间
     */
    @ApiModelProperty("创建日期-结束时间")
    private String insertTimeTo;

    /**
     * 修改人
     * 数据库字段:update_user
     * 字符类型(50)
     */
    @XdoSize(max = 50, message = "修改人长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("修改人")
    private String updateUser;

    /**
     * 修改时间
     * 数据库字段:update_time
     * timestamp
     */
    @ApiModelProperty("修改时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    /**
     * 修改时间-开始时间
     */
    @ApiModelProperty("修改时间-开始时间")
    private String updateTimeFrom;

    /**
     * 修改时间-结束时间
     */
    @ApiModelProperty("修改时间-结束时间")
    private String updateTimeTo;

    /**
     * 所属企业编码
     * 数据库字段:trade_code
     * 字符类型(20)
     */
    @XdoSize(max = 20, message = "所属企业编码长度不能超过20位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("所属企业编码")
    private String tradeCode;

    /**
     * 供应商SID
     * 数据库字段:head_id
     * 字符类型(50)
     */
    @XdoSize(max = 50, message = "供应商SID长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("供应商SID")
    private String headId;

    /**
     * 制单人姓名
     * 数据库字段:insert_user_name
     * 字符类型(50)
     */
    @XdoSize(max = 50, message = "制单人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("制单人姓名")
    private String insertUserName;

    /**
     * 修改人姓名
     * 数据库字段:update_user_name
     * 字符类型(50)
     */
    @XdoSize(max = 50, message = "修改人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("修改人姓名")
    private String updateUserName;

    /**
     * 供应商CODE
     * 数据库字段:supplier_code
     * 字符类型(50)
     */
    @XdoSize(max = 50, message = "供应商CODE长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("供应商CODE")
    private String supplierCode;


}
