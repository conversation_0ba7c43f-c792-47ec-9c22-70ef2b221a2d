package com.dcjet.cs.dto.importedCigarettes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import com.xdo.validation.annotation.XdoSize;
/**
 *
 * <AUTHOR>
 * @date: 2025-3-13
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class BizIPlanListParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 主表ID
     */
	@XdoSize(max = 80, message = "主表ID长度不能超过80位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("主表ID")
	private  String headId;
	/**
     * 商品名称（关联物料信息）
     */
	@NotEmpty(message="商品名称不能为空！")
	@XdoSize(max = 160, message = "商品名称长度不能超过160位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("商品名称")
	private  String productName;
	/**
     * 供应商（关联物料信息，不可修改）
     */
	@NotEmpty(message="供应商不能为空！")
	@XdoSize(max = 400, message = "供应商长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("供应商")
	private  String supplier;
	/**
     * 英文品牌（关联物料信息，可修改）
     */
	@XdoSize(max = 400, message = "英文品牌长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("英文品牌")
	private  String englishBrand;
	/**
     * 原产地
     */
	@XdoSize(max = 200, message = "原产地长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("原产地")
	private  String origin;
	/**
     * 计划数量（数量）
     */
	@NotNull(message="计划数量（数量）不能为空！")
	@Digits(integer = 13, fraction = 6, message = "计划数量（数量）必须为数字,整数位最大13位,小数最大6位!")
	@ApiModelProperty("计划数量（数量）")
	private  BigDecimal planQuantity;
	/**
     * 计划数量单位（默认万支）
     */
	@NotEmpty(message="计划数量(单位)不能为空！")
	@XdoSize(max = 40, message = "计划数量单位长度不能超过40位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("计划数量(单位)")
	private  String unit;
	/**
     * 币种（三位字母代码）
     */
	@NotEmpty(message="币种不能为空！")
	@XdoSize(max = 20, message = "币种长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("币种")
	private  String curr;
	/**
     * 计划单价（关联物料信息，可修改）
     */
	@NotNull(message="计划金额（单价）不能为空！")
	@Digits(integer = 14, fraction = 5, message = "计划单价必须为数字,整数位最大14位,小数最大5位!")
	@ApiModelProperty("计划单价")
	private  BigDecimal unitPrice;
	/**
     * 计划总金额
     */
	@NotNull(message="计划总金额不能为空！")
	@Digits(integer = 14, fraction = 5, message = "计划总金额必须为数字,整数位最大14位,小数最大5位!")
	@ApiModelProperty("计划总金额")
	private  BigDecimal totalAmount;
	/**
     * 折扣率（单位：%）
     */
	@Digits(integer = 15, fraction = 4, message = "折扣率必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("折扣率")
	private  BigDecimal discountRate;
	/**
     * 
     */
	@Digits(integer = 14, fraction = 5, message = "必须为数字,整数位最大14位,小数最大5位!")
	@ApiModelProperty("折扣金额")
	private  BigDecimal discountAmount;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String insertUserName;
	private  String insertUser;
	private  Date insertTime;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String updateUserName;
	private  String updateUser;
	private  Date updateTime;
	/**
     * 
     */
	@XdoSize(max = 400, message = "长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend1;
	/**
     * 
     */
	@XdoSize(max = 400, message = "长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend2;
	/**
     * 
     */
	@XdoSize(max = 400, message = "长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend3;
	/**
     * 
     */
	@XdoSize(max = 400, message = "长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend4;
	/**
     * 
     */
	@XdoSize(max = 400, message = "长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend5;
	/**
     * 
     */
	@XdoSize(max = 400, message = "长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend6;
	/**
     * 
     */
	@XdoSize(max = 400, message = "长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend7;
	/**
     * 
     */
	@XdoSize(max = 400, message = "长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend8;
	/**
     * 
     */
	@XdoSize(max = 400, message = "长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend9;
	/**
     * 
     */
	@XdoSize(max = 400, message = "长度不能超过400位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend10;
	/**
     * 企业编码
     */
	@XdoSize(max = 50, message = "企业编码长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("企业编码")
	private  String tradeCode;

	/**
	 * 是否存在下游数据
	 */
	@ApiModelProperty("是否存在下游数据")
	private  String hasCtr;




	/**
	 * 创建人部门编码
	 */
	@ApiModelProperty("创建人部门编码")
	@XdoSize(max = 150, message = "创建人部门编码长度不能超过150位字节长度(一个汉字2位字节长度)!")
	private  String sysOrgCode;
}
