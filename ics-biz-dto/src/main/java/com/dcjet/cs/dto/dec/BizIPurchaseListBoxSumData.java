package com.dcjet.cs.dto.dec;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 进货信息表体 汇总数据
 * 数量、总值、折扣金额、货款金额
 */
@Getter
@Setter
public class BizIPurchaseListBoxSumData implements Serializable {

    /**
     * 数量
     * 数据库字段:qty
     * 数值类型(19,6)
     */
    @ApiModelProperty("数量")
    private BigDecimal qty;



    /**
     * 件数
     * 数据库字段:quantity
     * 数值类型(10,0)
     */
    @ApiModelProperty("件数")
    private BigDecimal quantity;
}
