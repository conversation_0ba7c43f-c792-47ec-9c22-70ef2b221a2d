package com.dcjet.cs.dto.dec;


import com.xdo.validation.annotation.XdoSize;
import lombok.Getter;
import lombok.Setter;
import java.util.Date;
import java.math.BigDecimal;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;


/**
 * 进口管理-进货信息表体
 */
@Getter
@Setter
@ApiModel(value = "进口管理-进货信息表体-返回信息")
public class BizIPurchaseListDto implements Serializable{

    /**
     * 主建SID
     * 数据库字段:sid
     * 字符类型(50)
     */
    @ApiModelProperty("主建SID")
    private String sid;

    /**
     * 制单人
     * 数据库字段:insert_user
     * 字符类型(50)
     */
    @ApiModelProperty("制单人")
    private String insertUser;

    /**
     * 订单制单时间
     * 数据库字段:insert_time
     * timestamp
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("订单制单时间")
    private Date insertTime;
    /**
     * 订单制单时间-开始时间
     */
    @ApiModelProperty("订单制单时间-开始时间")
    private String insertTimeFrom;

    /**
     * 订单制单时间-结束时间
     */
    @ApiModelProperty("订单制单时间-结束时间")
    private String insertTimeTo;

    /**
     * 创建人姓名
     * 数据库字段:insert_user_name
     * 字符类型(50)
     */
    @ApiModelProperty("创建人姓名")
    private String insertUserName;

    /**
     * 更新人
     * 数据库字段:update_user
     * 字符类型(50)
     */
    @ApiModelProperty("更新人")
    private String updateUser;

    /**
     * 更新时间
     * 数据库字段:update_time
     * timestamp
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("更新时间")
    private Date updateTime;
    /**
     * 更新时间-开始时间
     */
    @ApiModelProperty("更新时间-开始时间")
    private String updateTimeFrom;

    /**
     * 更新时间-结束时间
     */
    @ApiModelProperty("更新时间-结束时间")
    private String updateTimeTo;

    /**
     * 更新人姓名
     * 数据库字段:update_user_name
     * 字符类型(50)
     */
    @ApiModelProperty("更新人姓名")
    private String updateUserName;

    /**
     * 企业代码
     * 数据库字段:trade_code
     * 字符类型(50)
     */
    @ApiModelProperty("企业代码")
    private String tradeCode;

    /**
     * 商品牌号
     * 数据库字段:product_grade
     * 字符类型(80)
     */
    @ApiModelProperty("商品牌号")
    private String productGrade;

    /**
     * 单位
     * 数据库字段:unit
     * 字符类型(9)
     */
    @ApiModelProperty("单位")
    private String unit;

    /**
     * 数量
     * 数据库字段:qty
     * 数值类型(19,6)
     */
    @ApiModelProperty("数量")
    private BigDecimal qty;

    /**
     * 币种
     * 数据库字段:curr
     * 字符类型(10)
     */
    @ApiModelProperty("币种")
    private String curr;

    /**
     * 单价
     * 数据库字段:dec_price
     * 数值类型(19,5)
     */
    @ApiModelProperty("单价")
    private BigDecimal decPrice;

    /**
     * 总价
     * 数据库字段:dec_total
     * 数值类型(19,2)
     */
    @ApiModelProperty("总价")
    private BigDecimal decTotal;

    /**
     * 商品类别
     * 数据库字段:product_type
     * 字符类型(80)
     */
    @ApiModelProperty("商品类别")
    private String productType;

    /**
     * 表头HEAD_ID
     * 数据库字段:head_id
     * 字符类型(50)
     */
    @ApiModelProperty("表头HEAD_ID")
    private String headId;

    /**
     * 折扣率%
     * 数据库字段:discount_rate
     * 数值类型(19,4)
     */
    @ApiModelProperty("折扣率%")
    private BigDecimal discountRate;

    /**
     * 折扣金额
     * 数据库字段:discount_amount
     * 数值类型(19,2)
     */
    @ApiModelProperty("折扣金额")
    private BigDecimal discountAmount;

    /**
     * 贷款金额
     * 数据库字段:payment_amount
     * 数值类型(19,2)
     */
    @ApiModelProperty("贷款金额")
    private BigDecimal paymentAmount;

    /**
     * 进口发票号
     * 数据库字段:invoice_no
     * 字符类型(60)
     */
    @ApiModelProperty("进口发票号")
    private String invoiceNo;

    /**
     * 箱号
     * 数据库字段:box_no
     * 字符类型(200)
     */
    @ApiModelProperty("箱号")
    private String boxNo;

    /**
     * 件数
     * 数据库字段:quantity
     * 数值类型(10,0)
     */
    @ApiModelProperty("件数")
    private BigDecimal quantity;

    /**
     * 版本号

     * 数据库字段:version_no
     * 字符类型(10)
     */
    @ApiModelProperty("版本号")
    private String versionNo;

    /**
     * 数据状态
     * 数据库字段:data_status
     * 字符类型(10)
     */
    @ApiModelProperty("数据状态")
    private String dataStatus;

    /**
     * 拓展字段1
     * 数据库字段:extend1
     * 字符类型(200)
     */
    @ApiModelProperty("拓展字段1")
    private String extend1;

    /**
     * 拓展字段2
     * 数据库字段:extend2
     * 字符类型(200)
     */
    @ApiModelProperty("拓展字段2")
    private String extend2;

    /**
     * 拓展字段3
     * 数据库字段:extend3
     * 字符类型(200)
     */
    @ApiModelProperty("拓展字段3")
    private String extend3;

    /**
     * 拓展字段4
     * 数据库字段:extend4
     * 字符类型(200)
     */
    @ApiModelProperty("拓展字段4")
    private String extend4;

    /**
     * 拓展字段5
     * 数据库字段:extend5
     * 字符类型(200)
     */
    @ApiModelProperty("拓展字段5")
    private String extend5;

    /**
     * 拓展字段6
     * 数据库字段:extend6
     * 字符类型(200)
     */
    @ApiModelProperty("拓展字段6")
    private String extend6;

    /**
     * 拓展字段7
     * 数据库字段:extend7
     * 字符类型(200)
     */
    @ApiModelProperty("拓展字段7")
    private String extend7;

    /**
     * 拓展字段8
     * 数据库字段:extend8
     * 字符类型(200)
     */
    @ApiModelProperty("拓展字段8")
    private String extend8;

    /**
     * 拓展字段9
     * 数据库字段:extend9
     * 字符类型(200)
     */
    @ApiModelProperty("拓展字段9")
    private String extend9;

    /**
     * 拓展字段10
     * 数据库字段:extend10
     * 字符类型(200)
     */
    @ApiModelProperty("拓展字段10")
    private String extend10;

    /**
     * 进口合同表头SID
     * 数据库字段:contract_head_id
     * 字符类型(60)
     */
    @ApiModelProperty("进口合同表头SID")
    private String contractHeadId;



    /**
     * 进口合同表体SID
     */
    @ApiModelProperty("进口合同表体SID")
    private String contractListId;


    /**
     * 上一次表体的复制的SID
     */
    @ApiModelProperty("上一次表体的复制的SID")
    private String lastCopyListId;

}