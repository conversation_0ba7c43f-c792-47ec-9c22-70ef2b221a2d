package com.dcjet.cs.dto.importedCigarettes;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import com.xdo.validation.annotation.XdoSize;
/**
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class BizIContractListParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 
     */
	@XdoSize(max = 60, message = "长度不能超过60位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String businessType;
	/**
     * 
     */
	@XdoSize(max = 10, message = "长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String dataStatus;
	/**
     * 
     */
	@XdoSize(max = 10, message = "长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String versionNo;
	/**
     * 
     */
	@XdoSize(max = 10, message = "长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String tradeCode;
	/**
     * 
     */
	@XdoSize(max = 40, message = "长度不能超过40位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String parentId;
	/**
     * 关联主合同表ID
     */
	@XdoSize(max = 40, message = "关联主合同表ID长度不能超过40位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("关联主合同表ID")
	private  String headId;
	/**
     * 商品品牌
     */
	@XdoSize(max = 80, message = "商品名称牌长度不能超过80位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("商品名称")
	private  String goodsBrand;
	/**
     * 单位
     */
	@XdoSize(max = 10, message = "单位长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("单位")
	private  String unit;
	/**
     * 计划数量
     */
	@Digits(integer = 13, fraction = 6, message = "计划数量必须为数字,整数位最大13位,小数最大6位!")
	@ApiModelProperty("计划数量")
	private  BigDecimal planQuantity;
	/**
     * 已形成合同数量
     */
	@Digits(integer = 13, fraction = 6, message = "已形成合同数量必须为数字,整数位最大13位,小数最大6位!")
	@ApiModelProperty("已形成合同数量")
	private  BigDecimal formedQuantity;
	/**
     * 可执行合同量
     */
	@Digits(integer = 13, fraction = 6, message = "可执行合同量必须为数字,整数位最大13位,小数最大6位!")
	@ApiModelProperty("可执行合同量")
	private  BigDecimal executableQuantity;
	/**
     * 合同数量
     */
	@Digits(integer = 13, fraction = 6, message = "合同数量必须为数字,整数位最大13位,小数最大6位!")
	@ApiModelProperty("合同数量")
	private  BigDecimal contractQuantity;
	/**
     * 货币代码
     */
	@XdoSize(max = 10, message = "币制长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("币制")
	private  String curr;
	/**
     * 单价
     */
	@Digits(integer = 14, fraction = 5, message = "单价必须为数字,整数位最大14位,小数最大5位!")
	@ApiModelProperty("单价")
	private  BigDecimal unitPrice;
	/**
     * 总值
     */
	@Digits(integer = 17, fraction = 2, message = "总值必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("总值")
	private  BigDecimal totalValue;
	/**
     * 商品类别
     */
	@XdoSize(max = 80, message = "商品类别长度不能超过80位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("商品类别")
	private  String goodsCategory;
	/**
     * 
     */
	@XdoSize(max = 50, message = "长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String insertUserName;
	/**
     * 
     */
	@XdoSize(max = 50, message = "长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String updateUserName;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend1;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend2;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend3;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend4;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend5;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend6;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend7;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend8;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend9;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend10;
}
