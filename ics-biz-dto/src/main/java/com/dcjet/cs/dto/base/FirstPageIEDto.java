package com.dcjet.cs.dto.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date: 2019-12-17
 */
@ApiModel(value = "基础管理-首页待审核返回数据")
@Setter
@Getter
public class FirstPageIEDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 月份
     */
    @ApiModelProperty("月份")
    private String month;
    /**
     * 进口数据
     */
    @ApiModelProperty("进口数据")
    private MonthName ivalue;
    /**
     * 出口数据
     */
    @ApiModelProperty("出口数据")
    private  MonthName evalue;
}
