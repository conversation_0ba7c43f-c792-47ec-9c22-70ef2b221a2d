package com.dcjet.cs.dto.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@ApiModel(value = "待审核传入数据")
public class AuditInfoDto {
    /**
     * 企业料号待审记录数
     */
    @ApiModelProperty("企业料号待审记录数")
    private Integer facGNoNum;
    /**
     * 备案料号待审记录数
     */
    @ApiModelProperty("备案料号待审记录数")
    private Integer copGNoNum;
    /**
     * 单损耗待审记录数
     */
    @ApiModelProperty("单损耗待审记录数")
    private Integer emsNum;
    /**
     * 进口预录入单待审记录数
     */
    @ApiModelProperty("进口预录入单待审记录数")
    private Integer erpINum;
    /**
     * 出口预录入单待审记录数
     */
    @ApiModelProperty("出口预录入单待审记录数")
    private Integer erpENum;
    /**
     * 免表待审记录数
     */
    @ApiModelProperty("免表待审记录数")
    private Integer devENum;
}
