package com.dcjet.cs.dto.params;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class TransCodeParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String id;
	/**
     * 
     */
	@XdoSize(max = 10, message = "长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String tradeCode;
	/**
     * 
     */
	@XdoSize(max = 50, message = "长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String insertUserName;
	/**
     * 
     */
	@XdoSize(max = 50, message = "长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String updateUserName;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend1;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend2;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend3;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend4;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend5;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend6;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend7;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend8;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend9;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend10;

	/**
	 * 业务类型
	 */
	@XdoSize(max = 60, message = "业务类型长度不能超过60位字节长度!")
	@ApiModelProperty("业务类型")
	private String bizType;

	/**
	 * 关税率%
	 */
	@Digits(integer = 13, fraction = 6, message = "关税率整数部分最多13位，小数部分最多6位")
	@ApiModelProperty("关税率%")
	private BigDecimal tariffRate;

	/**
	 * 消费税率%
	 */
	@Digits(integer = 13, fraction = 6, message = "消费税率整数部分最多13位，小数部分最多6位")
	@ApiModelProperty("消费税率%")
	private BigDecimal consumptionTaxRate;

	/**
	 * 增值税率%
	 */
	@Digits(integer = 13, fraction = 6, message = "增值税率整数部分最多13位，小数部分最多6位")
	@ApiModelProperty("增值税率%")
	private BigDecimal vatRate;

	/**
	 * 进出口公司代理费率%
	 */
	@Digits(integer = 15, fraction = 4, message = "代理费率整数部分最多15位，小数部分最多4位")
	@ApiModelProperty("进出口公司代理费率%")
	private BigDecimal ieAgentFeeRate;

	/**
	 * 总公司代理费率%
	 */
	@Digits(integer = 15, fraction = 4, message = "代理费率整数部分最多15位，小数部分最多4位")
	@ApiModelProperty("总公司代理费率%")
	private BigDecimal hqAgentFeeRate;

	/**
	 * 国际运输类型
	 */
	@XdoSize(max = 20, message = "运输类型长度不能超过20位字节长度!")
	@ApiModelProperty("国际运输类型")
	private String intlTransType;

	/**
	 * 是否集装箱装运
	 */
	@XdoSize(max = 20, message = "状态长度不能超过20位字节长度!")
	@ApiModelProperty("是否集装箱装运")
	private String isContainerShip;

	/**
	 * 集装箱容量
	 */
	@XdoSize(max = 20, message = "容量长度不能超过20位字节长度!")
	@ApiModelProperty("集装箱容量")
	private String containerCap;

	/**
	 * 集装箱型号
	 */
	@XdoSize(max = 20, message = "型号长度不能超过20位字节长度!")
	@ApiModelProperty("集装箱型号")
	private String containerType;

	/**
	 * 国际运费
	 */
	@Digits(integer = 15, fraction = 4, message = "金额整数部分最多15位，小数部分最多4位")
	@ApiModelProperty("国际运费")
	private BigDecimal intlFreightAmt;

	/**
	 * 港杂费
	 */
	@Digits(integer = 15, fraction = 4, message = "金额整数部分最多15位，小数部分最多4位")
	@ApiModelProperty("港杂费")
	private BigDecimal portChargesAmt;

	/**
	 * 陆运费
	 */
	@Digits(integer = 15, fraction = 4, message = "金额整数部分最多15位，小数部分最多4位")
	@ApiModelProperty("陆运费")
	private BigDecimal landFreightAmt;

	/**
	 * 通关费
	 */
	@Digits(integer = 15, fraction = 4, message = "金额整数部分最多15位，小数部分最多4位")
	@ApiModelProperty("通关费")
	private BigDecimal customsFeeAmt;

	/**
	 * 验柜服务费
	 */
	@Digits(integer = 15, fraction = 4, message = "金额整数部分最多15位，小数部分最多4位")
	@ApiModelProperty("验柜服务费")
	private BigDecimal cntrInspFeeAmt;

	/**
	 * 保险费率
	 */
	@Digits(integer = 13, fraction = 6, message = "费率整数部分最多13位，小数部分最多6位")
	@ApiModelProperty("保险费率")
	private BigDecimal insuranceRate;

	/**
	 * 其他费用
	 */
	@Digits(integer = 15, fraction = 4, message = "金额整数部分最多15位，小数部分最多4位")
	@ApiModelProperty("其他费用")
	private BigDecimal otherChargesAmt;

	/**
	 * 备注
	 */
	@XdoSize(max = 200, message = "备注长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("备注")
	private String remark;
}
