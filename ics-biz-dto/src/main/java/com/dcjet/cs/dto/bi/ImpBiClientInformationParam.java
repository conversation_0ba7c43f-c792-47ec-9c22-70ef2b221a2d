package com.dcjet.cs.dto.bi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import java.io.Serializable;
/**
 *
 * <AUTHOR>
 * @date: 2021-11-30
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class ImpBiClientInformationParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 客户类型(供应商 PRD、客户CLI、货代FOD、报关行CUT、企业COM)
     */
	@XdoSize(max = 3, message = "{客户类型(供应商 PRD、客户CLI、货代FOD、报关行CUT、企业COM)长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("客户类型(供应商 PRD、客户CLI、货代FOD、报关行CUT、企业COM)")
	private  String customerType;
	/**
     * 客户代码
     */
	@XdoSize(max = 50, message = "{客户代码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("客户代码")
	private  String customerCode;
	/**
     * 客户中文名称
     */
	@XdoSize(max = 200, message = "{客户中文名称长度不能超过200位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("客户中文名称")
	private  String companyName;
	/**
     * 海关信用等级
     */
	@XdoSize(max = 3, message = "{海关信用等级长度不能超过3位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("海关信用等级")
	private  String customsCreditRating;
	/**
     * 商检代码
     */
	@XdoSize(max = 20, message = "{商检代码长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("商检代码")
	private  String inspectionCode;
	/**
     * 社会信用代码
     */
	@XdoSize(max = 20, message = "{社会信用代码长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("社会信用代码")
	private  String creditCode;
	/**
     * 海关注册编码
     */
	@XdoSize(max = 10, message = "{海关注册编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("海关注册编码")
	private  String declareCode;
	/**
     * 企业名称缩写
     */
	@XdoSize(max = 50, message = "{企业名称缩写长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("企业名称缩写")
	private  String companyNameShort;
	/**
     * 客户电话
     */
	@XdoSize(max = 50, message = "{客户电话长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("客户电话")
	private  String telephoneNo;
	/**
     * 客户联系人
     */
	@XdoSize(max = 100, message = "{客户联系人长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("客户联系人")
	private  String linkmanName;
	/**
     * 联系人职务
     */
	@XdoSize(max = 20, message = "{联系人职务长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("联系人职务")
	private  String linkmanDuty;
	/**
     * 联系人电话
     */
	@XdoSize(max = 20, message = "{联系人电话长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("联系人电话")
	private  String mobilePhone;
	/**
     * 联系人邮箱
     */
	@XdoSize(max = 100, message = "{联系人邮箱长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("联系人邮箱")
	@JsonProperty("EMail")
	private  String EMail;
	/**
     * 企业中文地址
     */
	@XdoSize(max = 250, message = "{中文地址长度不能超过250位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("中文地址")
	private  String address;
	/**
     * 企业英文名称
     */
	@XdoSize(max = 250, message = "{企业英文名称长度不能超过250位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("企业英文名称")
	private  String companyNameEn;
	/**
     * 英文国家
     */
	@XdoSize(max = 100, message = "{英文国家长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("英文国家")
	private  String countryEn;
	/**
     * 英文地区
     */
	@XdoSize(max = 100, message = "{英文地区长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("英文地区")
	private  String areaEn;
	/**
     * 英文城市
     */
	@XdoSize(max = 100, message = "{英文城市长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("英文城市")
	private  String cityEn;
	/**
     * 英文地址
     */
	@XdoSize(max = 250, message = "{英文地址长度不能超过250位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("英文地址")
	private  String addressEn;
	/**
     * 客户电话(英文)
     */
	@XdoSize(max = 20, message = "{客户电话(英文)长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("客户电话(英文)")
	private  String telephoneNoEn;
	/**
     * 客户联系人(英文)
     */
	@XdoSize(max = 50, message = "{客户联系人(英文)长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("客户联系人(英文)")
	private  String linkmanNameEn;
	/**
     * 联系人电话(英文)
     */
	@XdoSize(max = 20, message = "{联系人电话(英文)长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("联系人电话(英文)")
	private  String mobilePhoneEn;
	/**
     * 联系人邮箱(英文)
     */
	@XdoSize(max = 50, message = "{联系人邮箱(英文)长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("联系人邮箱(英文)")
	@JsonProperty("eMailEn")
	private  String eMailEn;
	/**
     * 备注
     */
	@XdoSize(max = 250, message = "{备注长度不能超过250位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("备注")
	private  String note;
	/**
     * 所属企业编码
     */
	@XdoSize(max = 20, message = "{所属企业编码长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("所属企业编码")
	private  String tradeCode;
	/**
     * 联系人职务(英文)
     */
	@XdoSize(max = 20, message = "{联系人职务(英文)长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("联系人职务(英文)")
	private  String linkmanDutyEn;
	/**
     * AEO代码
     */
	@XdoSize(max = 30, message = "{AEO代码长度不能超过30位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("AEO代码")
	private  String aeoCode;
	/**
     * 申报地海关
     */
	@XdoSize(max = 4, message = "{申报地海关长度不能超过4位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("申报地海关")
	private  String masterCustoms;
	/**
     * 传真
     */
	@XdoSize(max = 50, message = "{传真长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("传真")
	private  String fax;
	/**
     * 邮编
     */
	@XdoSize(max = 50, message = "{邮编长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("邮编")
	private  String postal;
	/**
     * 中文国家
     */
	@XdoSize(max = 100, message = "{中文国家长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("中文国家")
	private  String country;
	/**
     * 中文地区
     */
	@XdoSize(max = 100, message = "{中文地区长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("中文地区")
	private  String area;
	/**
     * 中文城市
     */
	@XdoSize(max = 100, message = "{中文城市长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("中文城市")
	private  String city;
	/**
     * 发票中文地址
     */
	@XdoSize(max = 250, message = "{发票中文地址长度不能超过250位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("发票中文地址")
	private  String invoiceAddress;
	/**
     * 发票英文地址
     */
	@XdoSize(max = 250, message = "{发票英文地址长度不能超过250位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("发票英文地址")
	private  String invoiceAddressEn;
	/**
     * 发货中文地址
     */
	@XdoSize(max = 250, message = "{发货中文地址长度不能超过250位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("发货中文地址")
	private  String deliverAddress;
	/**
     * 发货英文地址
     */
	@XdoSize(max = 250, message = "{发货英文地址长度不能超过250位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("发货英文地址")
	private  String deliverAddressEn;
	/**
     * 数据状态0:启用  1:停用
     */
	@XdoSize(max = 1, message = "{数据状态0:启用  1:停用长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("数据状态0:启用  1:停用")
	private  String status;
	/**
     * 制单人姓名
     */
	@XdoSize(max = 50, message = "{制单人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("制单人姓名")
	private  String insertUserName;
	/**
     * 修改人姓名
     */
	@XdoSize(max = 50, message = "{修改人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("修改人姓名")
	private  String updateUserName;
	/**
     * 标识
     */
	@Digits(integer = 4, fraction = 0, message = "{标识必须为数字,整数位最大4位,小数最大0位!}")
	@ApiModelProperty("标识")
	private  Integer tempFlag;
	/**
     * 导入数据序号
     */
	@Digits(integer = 18, fraction = 0, message = "{导入数据序号必须为数字,整数位最大18位,小数最大0位!}")
	@ApiModelProperty("导入数据序号")
	private  Integer tempIndex;
	/**
     * 用户
     */
	@XdoSize(max = 36, message = "{用户长度不能超过36位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("用户")
	private  String tempOwner;
	/**
     * 标识
     */
	@Digits(integer = 4, fraction = 0, message = "{标识必须为数字,整数位最大4位,小数最大0位!}")
	@ApiModelProperty("标识")
	private  Integer tempMark;
	/**
     * 提示
     */
	@XdoSize(max = 2000, message = "{提示长度不能超过2000位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("提示")
	private  String tempRemark;
	/**
	 * 成本中心
	 */
	@XdoSize(max = 150, message = "{成本中心属性长度不能超过150位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("成本中心")
	private String costCenter;

	/**
	 * shipto代码
	 */
	@XdoSize(max = 50, message = "{shipto代码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("shipto代码")
	private String shipToCode;
	/**
	 * shipto名称
	 */
	@XdoSize(max = 255, message = "{shipto名称长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("shipto名称")
	private String shipToName;
	/**
	 * shipto地址
	 */
	@XdoSize(max = 512, message = "{shipto地址长度不能超过512位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("shipto地址")
	private String shipToAddress;
	/**
	 * billto代码
	 */
	@XdoSize(max = 50, message = "{billto代码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("billto代码")
	private String billToCode;
	/**
	 * billto名称
	 */
	@XdoSize(max = 255, message = "{billto名称长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("billto名称")
	private String billToName;
	/**
	 * billto地址
	 */
	@XdoSize(max = 512, message = "{billto地址长度不能超过512位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("billto地址")
	private String billToAddress;
	/**
	 * notify代码
	 */
	@XdoSize(max = 50, message = "{notify代码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("notify代码")
	private String notifyCode;
	/**
	 * notify名称
	 */
	@XdoSize(max = 255, message = "{notify名称长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("notify名称")
	private String notifyName;
	/**
	 * notify地址
	 */
	@XdoSize(max = 512, message = "{notify详细信息长度不能超过512位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("notify详细信息")
	private String notifyAddress;

}
