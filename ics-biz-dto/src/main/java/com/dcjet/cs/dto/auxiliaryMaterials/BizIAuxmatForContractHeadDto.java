package com.dcjet.cs.dto.auxiliaryMaterials;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
/**
 * 
 * <AUTHOR>
 * @date: 2025-5-22
 */
@ApiModel(value = "返回信息")
@Setter @Getter
public class BizIAuxmatForContractHeadDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 主键id
      */
    @ApiModelProperty("主键id")
	private  String id;
	/**
      * 业务类型，下拉框
      */
    @ApiModelProperty("业务类型，下拉框")
	private  String businessType;
	/**
      * 合同号，文本
      */
    @ApiModelProperty("合同号，文本")
	private  String contractNo;
	/**
      * 买方
      */
    @ApiModelProperty("买方")
	private  String customerName;
	/**
      * 卖方
      */
    @ApiModelProperty("卖方")
	private  String supplierName;
	/**
      * 签约日期，日期控件
      */
    @ApiModelProperty("签约日期，日期控件")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private  Date signDate;
	/**
      * 合同生效期，日期控件
      */
    @ApiModelProperty("合同生效期，日期控件")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private  Date contractStartDate;
	/**
      * 合同有效期，日期控件
      */
    @ApiModelProperty("合同有效期，日期控件")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private  Date contractEndDate;
	/**
      * 签约地点，下拉框
      */
    @ApiModelProperty("签约地点，下拉框")
	private  String signPlace;
	/**
      * 签约地点(英文)，文本
      */
    @ApiModelProperty("签约地点(英文)，文本")
	private  String signPlaceEn;
	/**
      * 装运港，下拉框
      */
    @ApiModelProperty("装运港，下拉框")
	private  String portOfShipment;
	/**
      * 目的港，下拉框
      */
    @ApiModelProperty("目的港，下拉框")
	private  String portOfDestination;
	/**
      * 报关口岸，下拉框
      */
    @ApiModelProperty("报关口岸，下拉框")
	private  String customsPort;
	/**
      * 付款方式，下拉框
      */
    @ApiModelProperty("付款方式，下拉框")
	private  String paymentMethod;
	/**
      * 币种，下拉框
      */
    @ApiModelProperty("币种，下拉框")
	private  String currency;
	/**
      * 备注，文本
      */
    @ApiModelProperty("备注，文本")
	private  String remark;
	/**
      * 单据状态，文本
      */
    @ApiModelProperty("单据状态，文本")
	private  String docStatus;
	/**
      * 确认时间，日期控件
      */
    @ApiModelProperty("确认时间，日期控件")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date confirmDate;
	/**
      * 审核状态，文本
      */
    @ApiModelProperty("审核状态，文本")
	private  String auditStatus;
	/**
      * 数据状态
      */
    @ApiModelProperty("数据状态")
	private  String dataState;
	/**
      * 版本号
      */
    @ApiModelProperty("版本号")
	private  String versionNo;
	/**
      * 业务编码
      */
    @ApiModelProperty("业务编码")
	private  String tradeCode;
	/**
      * 所属机构编码
      */
    @ApiModelProperty("所属机构编码")
	private  String sysOrgCode;
	/**
      * 父级id
      */
    @ApiModelProperty("父级id")
	private  String parentId;
	/**
      * 创建人
      */
    @ApiModelProperty("创建人")
	private  String createBy;
	/**
      * 创建时间
      */
    @ApiModelProperty("创建时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date createTime;
	/**
      * 修改人
      */
    @ApiModelProperty("修改人")
	private  String updateBy;
	/**
      * 修改时间
      */
    @ApiModelProperty("修改时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
	/**
      * 创建人姓名
      */
    @ApiModelProperty("创建人姓名")
	private  String insertUserName;
	/**
      * 修改人姓名
      */
    @ApiModelProperty("修改人姓名")
	private  String updateUserName;
	/**
      * 扩展字段1
      */
    @ApiModelProperty("扩展字段1")
	private  String extend1;
	/**
      * 扩展字段2
      */
    @ApiModelProperty("扩展字段2")
	private  String extend2;
	/**
      * 扩展字段3
      */
    @ApiModelProperty("扩展字段3")
	private  String extend3;
	/**
      * 扩展字段4
      */
    @ApiModelProperty("扩展字段4")
	private  String extend4;
	/**
      * 扩展字段5
      */
    @ApiModelProperty("扩展字段5")
	private  String extend5;
	/**
      * 扩展字段6
      */
    @ApiModelProperty("扩展字段6")
	private  String extend6;
	/**
      * 扩展字段7
      */
    @ApiModelProperty("扩展字段7")
	private  String extend7;
	/**
      * 扩展字段8
      */
    @ApiModelProperty("扩展字段8")
	private  String extend8;
	/**
      * 扩展字段9
      */
    @ApiModelProperty("扩展字段9")
	private  String extend9;
	/**
      * 扩展字段10
      */
    @ApiModelProperty("扩展字段10")
	private  String extend10;

	private BigDecimal totalAmount;
}
