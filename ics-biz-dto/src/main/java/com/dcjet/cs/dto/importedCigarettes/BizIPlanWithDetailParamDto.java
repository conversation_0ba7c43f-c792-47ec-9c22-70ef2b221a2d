package com.dcjet.cs.dto.importedCigarettes;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * @date: 2025-3-13
 */
@ApiModel(value = "返回信息")
@Setter @Getter
public class BizIPlanWithDetailParamDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String sid;
	/**
      * 父sid
      */
    @ApiModelProperty("父sid")
	private  String parentId;
	/**
      * 企业编码
      */
    @ApiModelProperty("企业编码")
	private  String tradeCode;
	/**
      * 业务类型，默认国营贸易进口卷烟，置灰不可修改
      */
    @ApiModelProperty("业务类型，默认国营贸易进口卷烟，置灰不可修改")
	private  String businessType;
	/**
      * 计划编号，唯一性校验
      */
    @ApiModelProperty("计划编号，唯一性校验")
	private  String planId;
	/**
      * 计划年度
      */
    @ApiModelProperty("计划年度")
	private  Date planYear;
	/**
      * 上下半年，0表示上半年，1表示下半年
      */
    @ApiModelProperty("上下半年，0表示上半年，1表示下半年")
	private  String halfYear;
	/**
      * 备注
      */
    @ApiModelProperty("备注")
	private  String remark;
	/**
      * 单据状态，0编制，1确认，2作废
      */
    @ApiModelProperty("单据状态，0编制，1确认，2作废")
	private  String status;
	/**
      * 确认时间，点击确认按钮成功提交的时间
      */
    @ApiModelProperty("确认时间，点击确认按钮成功提交的时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date confirmTime;
	/**
      * 审批状态，0不涉及审批，1未审批，2审批中，3审批通过，4审批退回
      */
    @ApiModelProperty("审批状态，0不涉及审批，1未审批，2审批中，3审批通过，4审批退回")
	private  String apprStatus;
	/**
      * 版本号，初始为1，版本复制递增
      */
    @ApiModelProperty("版本号，初始为1，版本复制递增")
	private  String versionNo;
	/**
      * 制单人，自动识别最后操作人
      */
    @ApiModelProperty("制单人，自动识别最后操作人")
	private  String insertUser;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String insertUserName;
	/**
      * 制单时间，系统自动生成
      */
    @ApiModelProperty("制单时间，系统自动生成")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date insertTime;
	/**
      * 修改人，最后操作人
      */
    @ApiModelProperty("修改人，最后操作人")
	private  String updateUser;
	/**
      * 修改时间，最后操作时间
      */
    @ApiModelProperty("修改时间，最后操作时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String updateUserName;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend1;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend2;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend3;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend4;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend5;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend6;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend7;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend8;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend9;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend10;

	@ApiModelProperty("")
	private  String extend11;

	//updateUser不为空取updateUser,否则取insertUser
	@ApiModelProperty("制单人")
	private String createrUser;

	//updateTime不为空取updateUser,否则取insertTime
	@ApiModelProperty("制单时间")
	private Date createrTime;
	private String planYearStr;

	@ApiModelProperty("是否存在下游数据")
	private String hasHeadCtr;

	@ApiModelProperty("是否为复制数据")
	private String isCopy;

	List<BizIPlanListDto> bizIPlanListDtos;
}
