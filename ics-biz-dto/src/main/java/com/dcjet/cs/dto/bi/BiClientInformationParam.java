package com.dcjet.cs.dto.bi;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2019-4-18
 */
@Setter
@Getter
@ApiModel(value = "企业基础信息传入参数")
public class BiClientInformationParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;

    /**
     * 客户类型(供应商 PRD、客户CLI、货代FOD、报关行CUT、企业COM)
     */
    @NotEmpty(message = "{客户类型(供应商 PRD、客户CLI、货代FOD、报关行CUT、企业COM)不能为空!}")
    @XdoSize(max = 3, message = "{类型(供应商 PRD、客户CLI、货代FOD、报关行CUT、企业COM)长度不能超过3位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("客户类型(供应商 PRD、客户CLI、货代FOD、报关行CUT、企业COM)")
    private String customerType;

    /**
     * 客户代码
     */
    @XdoSize(max = 50, message = "{代码长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("客户代码")
    private String customerCode;

    /**
     * 客户中文名称
     */
    @XdoSize(max = 200, message = "{中文名称长度不能超过200位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("客户中文名称")
    private String companyName;

    /**
     * 海关信用等级
     */
    @XdoSize(max = 3, message = "{海关信用等级长度不能超过3位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("海关信用等级")
    private String customsCreditRating;

    /**
     * 商检代码
     */
    @XdoSize(max = 20, message = "{商检代码长度不能超过20位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("商检代码")
    private String inspectionCode;

    /**
     * 社会信用代码
     */
    @XdoSize(max = 20, message = "{社会信用代码长度不能超过20位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("社会信用代码")
    private String creditCode;

    /**
     * 海关注册编码
     */
    @XdoSize(max = 10, message = "{海关注册编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("海关注册编码")
    private String declareCode;

    /**
     * 企业名称缩写
     */
    @XdoSize(max = 50, message = "{名称缩写长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("企业名称缩写")
    private String companyNameShort;

    /**
     * 客户电话
     */
    @XdoSize(max = 50, message = "{电话长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("客户电话")
    private String telephoneNo;

    /**
     * 客户联系人
     */
    @XdoSize(max = 100, message = "{联系人长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("客户联系人")
    private String linkmanName;

    /**
     * 联系人职务
     */
    @XdoSize(max = 20, message = "{联系人职务长度不能超过20位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("联系人职务")
    private String linkmanDuty;

    /**
     * 联系人电话
     */
    @XdoSize(max = 20, message = "{联系人电话长度不能超过20位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("联系人电话")
    private String mobilePhone;

    /**
     * 联系人邮箱
     */
    @XdoSize(max = 100, message = "{联系人邮箱长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("联系人邮箱")
    private String EMail;

    /**
     * 企业中文地址
     */
    @XdoSize(max = 250, message = "{中文地址长度不能超过250位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("企业中文地址")
    private String address;

    /**
     * 企业英文名称
     */
    @XdoSize(max = 250, message = "{英文名称长度不能超过250位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("企业英文名称")
    private String companyNameEn;

    /**
     * 英文国家
     */
    @XdoSize(max = 100, message = "{英文国家长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("英文国家")
    private String countryEn;

    /**
     * 英文地区
     */
    @XdoSize(max = 100, message = "{英文地区长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("英文地区")
    private String areaEn;

    /**
     * 英文城市
     */
    @XdoSize(max = 100, message = "{英文城市长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("英文城市")
    private String cityEn;

    /**
     * 英文地址
     */
    @XdoSize(max = 250, message = "{英文地址长度不能超过250位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("英文地址")
    private String addressEn;

    /**
     * 客户电话(英文)
     */
    @XdoSize(max = 20, message = "{电话(英文)长度不能超过20位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("客户电话(英文)")
    private String telephoneNoEn;

    /**
     * 客户联系人(英文)
     */
    @XdoSize(max = 50, message = "{联系人(英文)长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("客户联系人(英文)")
    private String linkmanNameEn;

    /**
     * 联系人电话(英文)
     */
    @XdoSize(max = 20, message = "{联系人电话(英文)长度不能超过20位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("联系人电话(英文)")
    private String mobilePhoneEn;

    /**
     * 联系人邮箱(英文)
     */
    @XdoSize(max = 50, message = "{联系人邮箱(英文)长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("联系人邮箱(英文)")
    private String EMailEn;

    /**
     * 备注
     */
    @XdoSize(max = 250, message = "{备注长度不能超过250位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("备注")
    private String note;

    /**
     * 创建日期-开始
     */
    @ApiModelProperty("创建日期-开始")
    private String insertTimeFrom;

    /**
     * 创建日期-结束
     */
    @ApiModelProperty("创建日期-结束")
    private String insertTimeTo;

    /**
     * 修改时间-开始
     */
    @ApiModelProperty("修改时间-开始")
    private String updateTimeFrom;

    /**
     * 修改时间-结束
     */
    @ApiModelProperty("修改时间-结束")
    private String updateTimeTo;

    /**
     * 联系人职务(英文)
     */
    @XdoSize(max = 20, message = "{联系人职务(英文)长度不能超过20位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("联系人职务(英文)")
    private String linkmanDutyEn;

    /**
     * AEO代码
     */
    @XdoSize(max = 30, message = "{AEO代码长度不能超过30位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("AEO代码")
    private String aeoCode;

    /**
     * 主管海关
     */
    @ApiModelProperty("主管海关")
    private String masterCustoms;

    /**
     * 传真
     */
    @ApiModelProperty("传真")
    @XdoSize(max = 50, message = "{传真长度不能超过30位字节长度(一个汉字2位字节长度)!}")
    private String fax;

    /**
     * 邮编
     */
    @ApiModelProperty("邮编")
    @XdoSize(max = 50, message = "{邮编长度不能超过30位字节长度(一个汉字2位字节长度)!}")
    private String postal;

    //add by zhangjl 2019-09-12
    /**
     * 中文国家
     */
    @XdoSize(max = 100, message = "{中文国家长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("中文国家")
    private String country;

    /**
     * 中文地区
     */
    @XdoSize(max = 100, message = "{中文地区长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("中文地区")
    private String area;

    /**
     * 中文城市
     */
    @XdoSize(max = 100, message = "{中文城市长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("中文城市")
    private String city;

    /**
     * 发票中文地址
     */
    @XdoSize(max = 250, message = "{发票中文地址长度不能超过250位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("发票中文地址")
    private String invoiceAddress;

    /**
     * 发票英文地址
     */
    @XdoSize(max = 250, message = "{发票英文地址长度不能超过250位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("发票英文地址")
    private String invoiceAddressEn;

    /**
     * 发货中文地址
     */
    @XdoSize(max = 250, message = "{发货中文地址长度不能超过250位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("发货中文地址")
    private String deliverAddress;

    /**
     * 发货英文地址
     */
    @XdoSize(max = 250, message = "{发货英文地址长度不能超过250位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("发货英文地址")
    private String deliverAddressEn;

    /**
     * 减免税货物使用地点
     */
    @XdoSize(max = 250, message = "{减免税货物使用地点长度不能超过250位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("减免税货物使用地点")
    private String freeAddress;

    /**
     * 状态(0:启用，1：停用)
     */
    @ApiModelProperty("状态")
    private String status;

    /**
     * 是否授权(0:未授权; 1:已授权)
     */
    @ApiModelProperty("是否授权")
    private String authorize;

    /**
     * 授权截止日期
     */
    @ApiModelProperty("授权截止日期")
    private Date authorizeDeadline;

    /**
     * 授权截止日期-开始
     */
    @ApiModelProperty("授权截止日期-开始")
    private String authorizeDeadlineFrom;

    /**
     * 授权截止日期-结束
     */
    @ApiModelProperty("授权截止日期-结束")
    private String authorizeDeadlineTo;

    /**
     * 减免税物资属性
     */
    @XdoSize(max = 50, message = "{减免税物资属性长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("减免税物资属性")
    private String freeProperties;

    /**
     * 成本中心
     */
    @XdoSize(max = 150, message = "{成本中心属性长度不能超过150位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("成本中心")
    private String costCenter;

    /**
     * 报关人员
     */
    @XdoSize(max = 50, message = "{报关人员长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("报关人员")
    private String decPersonnel;

    /**
     * 报关人员电话
     */
    @XdoSize(max = 50, message = "{报关人员电话长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("报关人员电话")
    private String decPersonnelTel;
}
