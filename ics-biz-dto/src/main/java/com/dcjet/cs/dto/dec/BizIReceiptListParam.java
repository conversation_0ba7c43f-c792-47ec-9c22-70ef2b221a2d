package com.dcjet.cs.dto.dec;


import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 进口管理-进货信息表头
 */
@Getter
@Setter
@ApiModel(value = "销售表头-传入参数")
public class BizIReceiptListParam implements Serializable{


    /**
     * 主建SID
     * 字符类型(50)
     * 非必填
     */
    private String sid;

    private String headId;


    private String tradeName;
    @NotEmpty(message = "出货数量不能为空")
    @Digits(integer = 13, fraction = 6, message = "出货数量最多13位，6位小数!")
    private String shipmentQuantity;
    @NotEmpty(message = "实发数量不能为空")
    @Digits(integer = 13, fraction = 6, message = "实发数量最多13位，6位小数!")
    private String actualQuantityIssued;
    private String unit;
    private String decPrice;
    private String curr;
    private String amount;



    private String insertUser;

    private Date insertTime;

    private String insertUserName;

    private String updateUser;

    private Date updateTime;

    private String updateUserName;

    private String tradeCode;

}