package com.dcjet.cs.dto.bi;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@ApiModel(value = "国家地区分区传如参数")
public class BiCountryRegionParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 国家名称
     */
    @ApiModelProperty("国家名称")
    private String countryName;
    /**
     * 国家代码
     */
    @ApiModelProperty("国家代码")
    private String countryCode;
    /**
     * 分区
     */
    @ApiModelProperty("分区")
    private String region;
    /**
     * 代码类型
     */
    @ApiModelProperty("代码类型")
    private String paramsType;
}
