package com.dcjet.cs.dto.thirdparty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

/**
 * 第三方数据库插入请求DTO
 */
@Data
@ApiModel(value = "第三方数据库插入请求")
public class ThirdPartyDbInsertRequest {

    @NotBlank(message = "表名不能为空")
    @ApiModelProperty(value = "表名", required = true, example = "TEST_TABLE")
    private String tableName;

    @NotEmpty(message = "数据不能为空")
    @ApiModelProperty(value = "要插入的数据，key为列名，value为列值", required = true)
    private List<Map<String, Object>> data;
}
