package com.dcjet.cs.dto.payment;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
/**
 * 
 * <AUTHOR>
 * @date: 2025-3-11
 */
@ApiModel(value = "返回信息")
@Setter @Getter
public class NotifyHeadDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String sid;


	/**
      * 
      */
    @ApiModelProperty("")
	private  String tradeCode;

	/**
      * 
      */
    @ApiModelProperty("")
	private  String insertUser;
	/**
      * 
      */
    @ApiModelProperty("")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date insertTime;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String updateUser;
	/**
      * 
      */
    @ApiModelProperty("")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
	private  String updateTimeFrom;
	private  String updateTimeTo;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String insertUserName;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String updateUserName;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend1;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend2;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend3;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend4;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend5;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend6;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend7;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend8;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend9;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend10;
	/**
      * 业务类型
      */
    @ApiModelProperty("业务类型")
	private  String bizType;
	/**
      * 收款方
      */
    @ApiModelProperty("收款方")
	private  String payee;
	/**
      * 合同号
      */
    @ApiModelProperty("合同号")
	private  String contractNo;

	/**
      * 进/出货单号
      */
    @ApiModelProperty("进/出货单号")
	private  String orderNumber;

	/**
      * 付款金额
      */
    @ApiModelProperty("付款金额")
	private BigDecimal payAmt;

	/**
      * 币种
      */
    @ApiModelProperty("币种")
	private  String curr;

	/**
      * 预付标志
      */
    @ApiModelProperty("预付标志")
	private  String prepayFlag;

	/**
      * 发送用友
      */
    @ApiModelProperty("发送用友")
	private  String sendUfida;

	/**
      * 单据状态
      */
    @ApiModelProperty("单据状态")
	private  String docStatus;

	/**
      * 确认时间
      */
    @ApiModelProperty("确认时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date cfmTime;
	/**
	 * 业务日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date bizDate;
	/**
	 * 收款银行
	 */
	private  String recvBank;
	/**
	 * 收款方帐号
	 */
	private  String recvAcct;
	/**
	 * RMB金额
	 */
	private BigDecimal payAmtRmb;
	/**
	 * 汇率
	 */
	private BigDecimal rate;
	/**
	 * 单据号
	 */
	private  String docNo;
	/**
	 * 部门
	 */
	private  String department;
	/**
	 * 备注
	 */
	private  String note;
}
