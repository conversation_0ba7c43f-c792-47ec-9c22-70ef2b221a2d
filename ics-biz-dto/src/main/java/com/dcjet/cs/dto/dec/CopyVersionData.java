package com.dcjet.cs.dto.dec;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 复制 版本请求参数
 */
@Getter
@Setter
public class CopyVersionData implements Serializable {

    /**
     * 新订单表头sid
     */
    private String newOrderHeadSid;

    /**
     * 新增 用户账号
     */
    private String insertUserNo;

    /**
     * 新增 用户名称
     */
    private String insertUserName;

    /**
     * 新 进货信息表头sid
     */
    private String purchaseHeadSid;


    /**
     * 新 证件信息表头sid
     */
    private String certificateHeadSid;


    /**
     * 新 销售信息表头sid
     */
    private String sellHeadSid;

    /**
     * 新 出库信息表头sid
     */
    private String outHeadSid;

    /**
     * 新 入库信息表头sid
     */
    private String inHeadSid;


    /**
     * 原订单表头Sid
     */
    private String oldOrderHeadSid;




    /**
     * 企业代码
     */
    private String tradeCode;


    @Override
    public String toString() {
        return "CopyVersionData【" +
                "newOrderHeadSid='" + newOrderHeadSid + '\'' +
                ", insertUserNo='" + insertUserNo + '\'' +
                ", insertUserName='" + insertUserName + '\'' +
                ", purchaseHeadSid='" + purchaseHeadSid + '\'' +
                ", certificateHeadSid='" + certificateHeadSid + '\'' +
                ", sellHeadSid='" + sellHeadSid + '\'' +
                ", outHeadSid='" + outHeadSid + '\'' +
                ", inHeadSid='" + inHeadSid + '\'' +
                ", oldOrderHeadSid='" + oldOrderHeadSid + '\'' +
                ", tradeCode='" + tradeCode + '\'' +
                '】';
    }
}
