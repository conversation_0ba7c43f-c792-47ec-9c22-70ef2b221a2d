package com.dcjet.cs.dto.bi;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
/**
 *
 * <AUTHOR>
 * @date: 2020-10-26
 */
@Setter @Getter
@ApiModel(value = "电子签章传入参数")
public class GwstdSealParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 电子章类型  1：公章、2：报关专用章、3：合同专用章
     */
	@NotEmpty(message="{电子章类型 1：公章、2：报关专用章、3：合同专用章不能为空！}")
	@XdoSize(max = 20, message = "{电子章类型 1：公章、2：报关专用章、3：合同专用章长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("电子章类型 1：公章、2：报关专用章、3：合同专用章")
	private  String type;
	/**
     * 电子章上传后的文件路径
     */
	@NotEmpty(message="{电子章上传后的文件路径不能为空！}")
	@XdoSize(max = 1000, message = "{电子章上传后的文件路径长度不能超过1000位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("电子章上传后的文件路径")
	private  String fdfsId;
	/**
     * 导入文件名称
     */
	@XdoSize(max = 1024, message = "{导入文件名称长度不能超过1024位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("导入文件名称")
	private  String originFileName;
	/**
     * 企业编码
     */
	@XdoSize(max = 10, message = "{企业编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("企业编码")
	private  String tradeCode;

//	/** 发票箱单文件服务器返回id */
//	private String invoiceBoxId;
//	/** 发票箱单文件名称 */
//	private String invoiceFileName;
	/** 发票箱单表头sid */
	private String headId;
	/** 进出口类型 */
	private String ieMark;
	/** 文件类型 EXCEL/PDF */
	private String fileType;
	/** 用章位置  d2:右下、d0左下、u0左上、u2右上 */
	private String location;
	/** 是否存在电子章信息 */
	private Boolean isExistSeal;
	/** 内部编号**/
	private String emsListNo;
	/** 发票号码 */
	private String invoiceNo;

	/*是否定制功能
	* */
	private Boolean isCustomized;

	/**
	 * 导出标题
	 */
	private String title;
}
