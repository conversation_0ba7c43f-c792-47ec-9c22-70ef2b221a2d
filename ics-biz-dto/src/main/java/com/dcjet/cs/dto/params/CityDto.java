package com.dcjet.cs.dto.params;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@ApiModel("城市传输模型")
public class CityDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;

    /**
     * 参数代码
     */
    @ApiModelProperty("参数代码")
    private String paramCode;

    /**
     * 城市中文名称
     */
    @ApiModelProperty("城市中文名称")
    private String cityCnName;

    /**
     * 城市英文名称
     */
    @ApiModelProperty("城市英文名称")
    private String cityEnName;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String note;

    /**
     * 企业编码
     */
    @ApiModelProperty("企业编码")
    private String tradeCode;

    /**
     * 新增用户
     */
    @ApiModelProperty("新增用户")
    private String insertUser;

    /**
     * 新增时间
     */
    @ApiModelProperty("新增时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;

    /**
     * 修改用户
     */
    @ApiModelProperty("修改用户")
    private String updateUser;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 扩展字段1
     */
    @ApiModelProperty("扩展字段1")
    private String extend1;

    /**
     * 扩展字段2
     */
    @ApiModelProperty("扩展字段2")
    private String extend2;

    /**
     * 扩展字段3
     */
    @ApiModelProperty("扩展字段3")
    private String extend3;

    /**
     * 扩展字段4
     */
    @ApiModelProperty("扩展字段4")
    private String extend4;

    /**
     * 扩展字段5
     */
    @ApiModelProperty("扩展字段5")
    private String extend5;

    /**
     * 扩展字段6
     */
    @ApiModelProperty("扩展字段6")
    private String extend6;

    /**
     * 扩展字段7
     */
    @ApiModelProperty("扩展字段7")
    private String extend7;

    /**
     * 扩展字段8
     */
    @ApiModelProperty("扩展字段8")
    private String extend8;

    /**
     * 扩展字段9
     */
    @ApiModelProperty("扩展字段9")
    private String extend9;

    /**
     * 扩展字段10
     */
    @ApiModelProperty("扩展字段10")
    private String extend10;
}