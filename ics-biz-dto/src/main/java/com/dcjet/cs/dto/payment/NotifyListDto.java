package com.dcjet.cs.dto.payment;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @date: 2025-3-11
 */
@ApiModel(value = "返回信息")
@Setter @Getter
public class NotifyListDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String sid;
	private String headId;


	/**
      * 
      */
    @ApiModelProperty("")
	private  String tradeCode;

	/**
      * 
      */
    @ApiModelProperty("")
	private  String insertUser;
	/**
      * 
      */
    @ApiModelProperty("")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date insertTime;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String updateUser;
	/**
      * 
      */
    @ApiModelProperty("")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
	private  String updateTimeFrom;
	private  String updateTimeTo;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String insertUserName;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String updateUserName;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend1;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend2;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend3;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend4;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend5;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend6;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend7;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend8;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend9;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend10;
	/**
	 * 合同编号
	 */
	private String contractNo;

	/**
	 * 订单号码
	 */
	private String orderNumber;

	/**
	 * 订单号
	 */
	private String orderNo;

	/**
	 * 商品名称
	 */
	private String goodsName;

	/**
	 * 发票号码
	 */
	private String invoiceNumber;

	/**
	 * 数量
	 */
	private BigDecimal qty;

	/**
	 * 单位
	 */
	private String unit;

	/**
	 * 支付金额
	 */
	private BigDecimal payAmt;

	/**
	 * 支付金额（人民币）
	 */
	private BigDecimal payAmtRmb;
}
