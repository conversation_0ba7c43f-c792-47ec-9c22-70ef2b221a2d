package com.dcjet.cs.dto.bi;

import com.dcjet.cs.dto.base.BasicDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date: 2019-6-12
 */
@ApiModel(value = "获取转换信息返回信息")
@Setter
@Getter
public class BiTransformDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;
    /**
     * 转换前值
     */
    @ApiModelProperty("转换前值")
    private String origin;
    /**
     * 转换后值
     */
    @ApiModelProperty("转换后值")
    private String dest;
    /**
     * 比例
     */
    @ApiModelProperty("比例")
    private String rate;
    /**
     * 企业代码
     */
    @ApiModelProperty("企业代码")
    private String tradeCode;
    /**
     * 类型
     */
    @ApiModelProperty("类型")
    private String type;
}
