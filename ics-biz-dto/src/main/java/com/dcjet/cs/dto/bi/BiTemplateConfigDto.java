package com.dcjet.cs.dto.bi;

import com.dcjet.cs.dto.base.BasicDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
/**
 * 
 * <AUTHOR>
 * @date: 2019-5-8
 */
@ApiModel(value = "模板返回信息")
@Setter @Getter
public class BiTemplateConfigDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String sid;
	/**
      * 模板类型
      */
    @ApiModelProperty("模板类型")
	private  String templateType;
	/**
      * 模板名称
      */
    @ApiModelProperty("模板名称")
	private  String templateName;
	/**
      * 文件服务器返回链接地址
      */
    @ApiModelProperty("文件服务器返回链接地址")
	private  String fdfsId;
	/**
      * 供应商id
      */
    @ApiModelProperty("供应商id")
	private  String headId;
	/**
      * 备注
      */
    @ApiModelProperty("备注")
	private  String note;
	/**
      * 默认装运方式
      */
    @ApiModelProperty("默认装运方式")
	private  String shipment;

	/**
	 * 所属企业编码
	 */
	@ApiModelProperty("所属企业编码")
	private  String tradeCode;
}
