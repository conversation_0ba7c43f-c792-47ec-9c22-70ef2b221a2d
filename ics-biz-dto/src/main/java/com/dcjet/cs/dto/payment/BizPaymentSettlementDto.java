package com.dcjet.cs.dto.payment;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @date: 2025-3-11
 */
@ApiModel(value = "返回信息")
@Setter @Getter
public class BizPaymentSettlementDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String sid;

    @ApiModelProperty("上游SID")
    private String headId;

    @ApiModelProperty("企业编码")
    private String tradeCode;

    @ApiModelProperty("业务类型")
    private String businessType;

    @ApiModelProperty("进货单号")
    private String purchaseOrderNo;

    @ApiModelProperty("合同号")
    private String contractNo;

    @ApiModelProperty("客户")
    private String customer;

    @ApiModelProperty("付款单位")
    private String payer;

    @ApiModelProperty("外商发票号")
    private String foreignInvoiceNo;

    @ApiModelProperty("币种")
    private String curr;

    @ApiModelProperty("汇率")
    private BigDecimal exchangeRate;

    @ApiModelProperty("原货币价")
    private BigDecimal originalAmount;

    @ApiModelProperty("人民币金额")
    private BigDecimal rmbAmount;

    @ApiModelProperty("口岸调拨总价")
    private BigDecimal portTransferAmount;

    @ApiModelProperty("代理费(不含税金额)")
    private BigDecimal agencyFee;

    @ApiModelProperty("代理费税额")
    private BigDecimal agencyFeeTax;

    @ApiModelProperty("合计值")
    private BigDecimal totalAmount;

    @ApiModelProperty("代理费率")
    private BigDecimal agencyFeeRate;

    @ApiModelProperty("代理费(价税合计)")
    private BigDecimal agencyFeeTotal;

    @ApiModelProperty("商品类别")
    private String productCategory;

    @ApiModelProperty("数量")
    private BigDecimal qty;

    @ApiModelProperty("发送用友")
    private String sendToUf;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("单据状态")
    private String status;

    @ApiModelProperty("确认时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date confirmTime;

    @ApiModelProperty("创建人")
    private String insertUser;

    @ApiModelProperty("创建人名称")
    private String insertUserName;

    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date insertTime;

    @ApiModelProperty("更新人")
    private String updateUser;

    @ApiModelProperty("修改人姓名")
    private String updateUserName;

    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;

    private String extend1;
    private String extend2;
    private String extend3;
    private String extend4;
    private String extend5;
    private String extend6;
    private String extend7;
    private String extend8;
    private String extend9;
    private String extend10;

    //updateUser不为空取updateUser,否则取insertUser
    @ApiModelProperty("制单人")
    private String createrUser;
    //updateUser不为空取updateUserName,否则取insertUserName
    @ApiModelProperty("制单人姓名")
    private String createrUserName;

    //updateTime不为空取updateUser,否则取insertTime
    @ApiModelProperty("制单时间")
    private Date createrTime;

    @ApiModelProperty("业务日期")
    private  Date businessDate;
}
