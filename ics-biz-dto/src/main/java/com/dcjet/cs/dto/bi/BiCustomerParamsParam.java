package com.dcjet.cs.dto.bi;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
/**
 *
 * <AUTHOR>
 * @date: 2019-6-6
 */
@Setter @Getter
@ApiModel(value = "企业参数库传入参数")
public class BiCustomerParamsParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 自定义参数类型
     */
	@XdoSize(max = 50, message = "{自定义参数类型长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("自定义参数类型")
	private  String paramsType;
	/**
     * 自定义参数编码
     */
	@XdoSize(max = 100, message = "{自定义参数编码长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("自定义参数编码")
	private  String paramsCode;
	/**
     * 自定义参数名称
     */
	@XdoSize(max = 160, message = "{自定义参数名称长度不能超过160位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("自定义参数名称")
	private  String paramsName;
	/**
     * 备注
     */
	@XdoSize(max = 250, message = "{备注长度不能超过250位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("备注")
	private  String note;
	/**
     * 对应海关参数编码
     */
	@XdoSize(max = 100, message = "{对应海关参数编码长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("对应海关参数编码")
	private  String customParamCode;
	/**
     * 对应海关参数名称
     */
	@XdoSize(max = 50, message = "{对应海关参数名称长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("对应海关参数名称")
	private  String customParamName;
	/**
     * 所属企业编码
     */
	@XdoSize(max = 20, message = "{所属企业编码长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("所属企业编码")
	private  String tradeCode;
	/**
	 * 查询类型
	 */
	@ApiModelProperty("查询类型")
	private  String inType;
}
