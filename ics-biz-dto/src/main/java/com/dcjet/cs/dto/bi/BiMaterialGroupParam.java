package com.dcjet.cs.dto.bi;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date: 2022-5-19
 */
@Setter
@Getter
@ApiModel(value = "传入参数")
public class BiMaterialGroupParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;
    /**
     * 物料群组
     */
    @XdoSize(max = 10, message = "{物料群组长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("物料群组")
    private String matGroup;
    /**
     * 物料群组说明
     */
    @XdoSize(max = 50, message = "{物料群组说明长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("物料群组说明")
    private String matGroupName;
    /**
     * 所属企业编码
     */
    @XdoSize(max = 20, message = "{所属企业编码长度不能超过20位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("所属企业编码")
    private String tradeCode;
    /**
     * 制单人姓名
     */
    @XdoSize(max = 50, message = "{制单人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("制单人姓名")
    private String insertUserName;
    /**
     * 修改人姓名
     */
    @XdoSize(max = 50, message = "{修改人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("修改人姓名")
    private String updateUserName;
    /**
     * 备注
     */
    @XdoSize(max = 255, message = "{备注长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("备注")
    private String note;
}
