package com.dcjet.cs.dto.dec;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 进口管理-进货信息表头
 */
@Getter
@Setter
@ApiModel(value = "销售表头-传入参数")
public class BizIReceiptHeadParam implements Serializable{


    /**
     * 主建SID
     * 字符类型(50)
     * 非必填
     */
    private String sid;

    private String headId;

    private String receiptNumber; // 出库回单编号
    private String contractNumber; // 合同号
    private String orderNumber; // 订单号
    private String deliveryNumber; // 交货单号
    @NotEmpty(message = "提货人不能为空!")
    @XdoSize(max = 200, message = "提货人长度不能超过200位字节长度(一个汉字2位字节长度)!")
    private String consignee; // 提货人
    @NotEmpty(message = "仓库不能为空!")
    @XdoSize(max = 100, message = "仓库长度不能超过100位字节长度(一个汉字2位字节长度)!")
    private String warehouse; // 仓库
    @NotNull(message = "出库日期不能为空!")
    private Date deliveryDate; // 出库日期
    private String supplier; // 供应商
    @NotNull(message = "发送用友不能为空!")
    private String sendUfida; // 发送用友
    private String inspectionOutstock; // 抽检出库
    @XdoSize(max = 200, message = "备注长度不能超过200位字节长度(一个汉字2位字节长度)!")
    private String remark; // 备注
    private String createBy; // 制单人
    private Date createDate; // 制单时间
    private String outstockDocumentStatus; // 出库单据状态

    private String insertUser;

    private Date insertTime;

    private String insertUserName;

    private String updateUser;

    private Date updateTime;

    private String updateUserName;

    private String tradeCode;

}