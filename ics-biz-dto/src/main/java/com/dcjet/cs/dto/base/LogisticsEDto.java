package com.dcjet.cs.dto.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@ApiModel(value = "首页-首页进口物流追踪返回数据")
public class LogisticsEDto {
    /**
     * 预录入单总量
     */
    @ApiModelProperty("预录入单总量")
    private Integer decEHeadNum;
    /**
     * 未委托
     */
    @ApiModelProperty("未委托")
    private Integer unassigned;
    /**
     * 已委托未申报
     */
    @ApiModelProperty("已委托未申报")
    private Integer declaredNum;
    /**
     * 已申报未完税
     */
    @ApiModelProperty("已申报未完税")
    private Integer dutyNum;
    /**
     * 已完税未放行
     */
    @ApiModelProperty("已完税未放行")
    private Integer passNum;
    /**
     * 已放行
     */
    @ApiModelProperty("已放行")
    private Integer releasedNum;
}
