package com.dcjet.cs.dto.base;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Setter
@Getter
@ApiModel(value = "首页-首页传入数据")
public class HomePageParam {

    private String tradeCode;
    /**
     * 审核时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date apprDate;
    /**
     * 审批时间-开始
     */
    @ApiModelProperty("审批时间-开始")
    private String apprDateFrom;

    /**
     * 审批时间-结束
     */
    @ApiModelProperty("审批时间-结束")
    private String apprDateTo;
    /**
     * 审批时间-结束
     */
    @ApiModelProperty("审批时间-结束")
    private String insertUser;
}
