package com.dcjet.cs.dto.dec;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 进货信息表体 汇总数据
 * 数量、总值、折扣金额、货款金额
 */
@Getter
@Setter
public class BizISellListSumData implements Serializable {


    @ApiModelProperty("税额")
    private BigDecimal amountOfTax;


    @ApiModelProperty("不含税金额")
    private BigDecimal taxNotIncluded;


    @ApiModelProperty("价税合计")
    private BigDecimal totalValueTax;

}
