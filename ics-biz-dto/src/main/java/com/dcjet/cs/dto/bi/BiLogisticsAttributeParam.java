package com.dcjet.cs.dto.bi;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
/**
 *
 * <AUTHOR>
 * @date: 2021-9-5
 */
@Setter @Getter
@ApiModel(value = "企业参数库-物流属性传入参数")
public class BiLogisticsAttributeParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 企业编码
     */
	@XdoSize(max = 10, message = "{企业编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("企业编码")
	private  String tradeCode;
	/**
     * 创建人用户名
     */
	@XdoSize(max = 50, message = "{创建人用户名长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("创建人用户名")
	private  String insertUserName;
	/**
     * 更新人用户名
     */
	@XdoSize(max = 50, message = "{更新人用户名长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("更新人用户名")
	private  String updateUserName;
	/**
     * 国内/国际
     */
	@XdoSize(max = 1, message = "{物流属类型(1 国内段 2 国际段)长度不能超过1位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("国内/国际")
	@NotEmpty(message = "{国内/国际不可为空}")
	private  String logisticsType;
	/**
     * 代码
     */
	@XdoSize(max = 30, message = "{物流属性编码长度不能超过30位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("代码")
	@NotEmpty(message = "{代码不可为空}")
	private  String logisticsCode;
	/**
     * 名称
     */
	@XdoSize(max = 60, message = "{物流属性名称长度不能超过60位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("名称")
	@NotEmpty(message = "{名称不可为空}")
	private  String logisticsName;
	/**
     * 备注
     */
	@XdoSize(max = 255, message = "{备注长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("备注")
	private  String note;
}
