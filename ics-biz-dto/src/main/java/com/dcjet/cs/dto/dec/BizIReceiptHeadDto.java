package com.dcjet.cs.dto.dec;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 进口管理-进货信息表头
 */
@Getter
@Setter
@ApiModel(value = "信息表头-返回信息")
public class BizIReceiptHeadDto implements Serializable{


    /**
     * 主建SID
     * 字符类型(50)
     * 非必填
     */
    private String sid;

    private String headId;


    private String receiptNumber; // 出库回单编号
    private String contractNumber; // 合同号
    private String orderNumber; // 订单号
    private String deliveryNumber; // 交货单号
    private String consignee; // 提货人
    private String warehouse; // 仓库
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryDate; // 出库日期
    private String supplier; // 供应商
    private String sendUfida; // 发送用友
    private String inspectionOutstock; // 抽检出库
    private String remark; // 备注
    private String createBy; // 制单人
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate; // 制单时间
    private String outstockDocumentStatus; // 出库单据状态



    private String insertUser;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;

    private String insertUserName;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String updateUser;

    private Date updateTime;

    private String updateUserName;

    private String tradeCode;

}