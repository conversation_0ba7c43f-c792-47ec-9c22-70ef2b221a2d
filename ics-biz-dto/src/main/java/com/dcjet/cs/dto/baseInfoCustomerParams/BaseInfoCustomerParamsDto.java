package com.dcjet.cs.dto.baseInfoCustomerParams;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2019-4-18
 */
@Setter
@Getter

public class BaseInfoCustomerParamsDto{
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private  String sid;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private  String insertUser;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private  String insertUserName;
    /**
     * 创建日期
     */
    @ApiModelProperty("创建日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;
    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private  String updateUser;
    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private  String updateUserName;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private  Date updateTime;

    /**
     *所属企业编码
     */
    @ApiModelProperty(value = "所属企业编码")
    private String tradeCode;


    /**
     * 常用标志（业务类型
     */
    private  String businessType;

    private List<String> businessTypeList;

    /**
     * 自定义参数类型
     */
    private  String paramsType;
    /**
     * 自定义参数编码
     */
    private  String paramsCode;
    /**
     * 自定义参数名称
     */
    private  String paramsName;

    /**
     * 对应海关参数编码
     */
    private  String customParamCode;
    /**
     * 对应海关参数名称
     */
    private  String customParamName;

    /**
     * 拓展备用字段
     */
    private  String extend1;


    /**
     * 拓展备用字段
     */
    private  String extend2;

    /**
     * 拓展备用字段
     */
    private  String extend3;

    /**
     * 拓展备用字段
     */
    private  String extend4;

    /**
     * 拓展备用字段
     */
    private  String extend5;

    /**
     * 拓展备用字段
     */
    private  String extend6;

    /**
     * 拓展备用字段
     */
    private  String extend7;

    /**
     * 拓展备用字段
     */
    private  String extend8;

    /**
     * 拓展备用字段
     */
    private  String extend9;


    /**
     * 拓展备用字段
     */
    private  String extend10;

    private String unitYonyou;

    //用友代码
    private String yyCode;
}
