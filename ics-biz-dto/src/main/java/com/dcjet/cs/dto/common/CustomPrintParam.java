package com.dcjet.cs.dto.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @Auther: zhuhui
 * @Date: 2019/5/6 11:17
 * @Description: 自定义打印参数
 */
@Setter
@Getter
@ApiModel(value = "自定义打印参数")
public class CustomPrintParam {

    @ApiModelProperty(value = "单据主键",required = true)
    private String sid;
    @ApiModelProperty(value = "企业编码",required = false)
    private String tradeCode;
    @ApiModelProperty(value = "模板类型",required = false)
    private String templateType;
}
