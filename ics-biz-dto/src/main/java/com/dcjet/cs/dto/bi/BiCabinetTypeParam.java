package com.dcjet.cs.dto.bi;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date: 2021-9-5
 */
@Setter
@Getter
@ApiModel(value = "企业参数库-车柜类型传入参数")
public class BiCabinetTypeParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;
    /**
     * 企业编码
     */
    @XdoSize(max = 10, message = "{企业编码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("企业编码")
    private String tradeCode;
    /**
     * 创建人用户名
     */
    @XdoSize(max = 50, message = "{创建人用户名长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("创建人用户名")
    private String insertUserName;
    /**
     * 更新人用户名
     */
    @XdoSize(max = 50, message = "{更新人用户名长度不能超过50位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("更新人用户名")
    private String updateUserName;
    /**
     * 国内/国际
     */
    @XdoSize(max = 1, message = "{国内/国际长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("国内/国际")
    @NotEmpty(message = "{国内/国际不能为空}")
    private String cabinetAttribute;
    /**
     * 车柜类型
     */
    @XdoSize(max = 30, message = "{车柜类型长度不能超过30位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("车柜类型")
    @NotEmpty(message = "{车柜类型不能为空}")
    private String cabinetType;
    /**
     * 车柜名称
     */
    @XdoSize(max = 60, message = "{车柜名称长度不能超过60位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("车柜名称")
    @NotEmpty(message = "{车柜名称不能为空}")
    private String cabinetName;
    /**
     * 备注
     */
    @XdoSize(max = 255, message = "{备注长度不能超过255位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("备注")
    private String note;
}
