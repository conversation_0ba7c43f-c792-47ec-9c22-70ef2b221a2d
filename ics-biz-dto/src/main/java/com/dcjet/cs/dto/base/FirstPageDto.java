package com.dcjet.cs.dto.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date: 2019-12-17
 */
@ApiModel(value = "基础管理-首页待审核返回数据")
@Setter
@Getter
public class FirstPageDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 审核类型
     */
    @ApiModelProperty("审核类型")
    private String auditType;
    /**
     * 待审核数量
     */
    @ApiModelProperty("待审核数量")
    private Integer auditCount;
    /**
     * 排序
     */
    @ApiModelProperty("排序")
    private  Integer orderId;
}
