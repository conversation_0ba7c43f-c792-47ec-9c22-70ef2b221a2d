package com.dcjet.cs.dto.bi;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
/**
 *
 * <AUTHOR>
 * @date: 2019-9-4
 */
@Setter @Getter
@ApiModel(value = "进出口生成内部编号传入参数")
public class BiOrdernoGenRuleParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     *
     */
//	@NotEmpty(message="{不能为空！}")
	@XdoSize(max = 20, message = "{长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("企业内部编号")
	private  String tradeCode;
	/**
     * 模块名称 LI:进口提单 LE:出口提单
     */
	@NotEmpty(message="{模块名称不能为空！}")
	@XdoSize(max = 50, message = "{模块名称 LI:进口提单 LE:出口提单长度不能超过50位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("模块名称 LI:进口提单 LE:出口提单")
	private  String moduleName;
	/**
     * 监管方式
     */
	@NotEmpty(message="{监管方式不能为空！}")
	@XdoSize(max = 20, message = "{监管方式长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("监管方式")
	private  String tradeMode;
	/**
     * 固定前缀
     */
	@NotEmpty(message="{固定前缀不能为空！}")
	@XdoSize(max = 30, message = "{固定前缀长度不能超过30位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("固定前缀")
	private  String fixedPrefix;
	/**
     * 备注
     */
	@XdoSize(max = 250, message = "{备注长度不能超过250位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("备注")
	private  String note;
	/**
     * 日期值：？人工录入显示日期格式，如:yyyyMM
     */
	@NotEmpty(message="{日期格式不能为空！}")
	@XdoSize(max = 20, message = "{日期值：？人工录入显示日期格式，如:yyyyMM长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("日期格式：？人工录入显示日期格式，如:yyyyMM")
	private  String dateFmt;
	/**
     * 流水位数
     */
	@Digits(integer = 22, fraction = 0, message = "{流水位数必须为数字,整数位最大22位,小数最大0位!}")
	@ApiModelProperty("流水位数")
	private  Integer seqNoDigits;
}
