package com.dcjet.cs.dto.bi;

import com.dcjet.cs.dto.base.BasicDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * @date: 2020-5-21
 */
@ApiModel(value = "文档中心-文件柜返回信息")
@Setter @Getter
public class GwstdDocCenterDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 唯一键
      */
    @ApiModelProperty("唯一键")
	private  String sid;
	/**
      * 状态 0正常
      */
    @ApiModelProperty("状态 0正常")
	private  String status;
	/**
      * 文档序号
      */
    @ApiModelProperty("文档序号")
	private  String documentNo;
	/**
      * 文档名称
      */
    @ApiModelProperty("文档名称")
	private  String documentName;
	/**
      * 文档类型
      */
    @ApiModelProperty("文档类型")
	private  String documentType;
	/**
      * 备注
      */
    @ApiModelProperty("备注")
	private  String note;
	
	/**
	 * 存放地址
	 */
	@ApiModelProperty("存放地址")
	private  String address;
}
