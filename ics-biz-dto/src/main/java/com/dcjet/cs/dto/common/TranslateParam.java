package com.dcjet.cs.dto.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @Auther: z<PERSON><PERSON><PERSON>
 * @Date: 2019/11/8
 * @Description: 繁转简参数
 */
@Setter
@Getter
@ApiModel(value = "繁转简参数")
public class TranslateParam {

    @ApiModelProperty(value = "企业编码")
    private String tradeCode;

    @ApiModelProperty(value = "单据类型")
    private String billType;

    @ApiModelProperty(value = "操作人")
    private String insertUser;

    @ApiModelProperty(value = "单据主键")
    private List<String> sids;


}
