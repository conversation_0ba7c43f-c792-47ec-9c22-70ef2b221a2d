package com.dcjet.cs.dto.bi;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Setter
@Getter
@ApiModel(value = "进口费用信息传入参数")
public class ExpenseIListParam implements Serializable {
    private static final long serialVersionUID = 1L;

    private String sid;
    private String headId;

    //合同号
    @NotEmpty(message = "{合同号不能为空!}")
    @XdoSize(max = 60, message = "{合同号长度不能超过60位字节长度(一个汉字2位字节长度)!}")
    private String contractNumber;
    //进货单号
    @NotEmpty(message = "{进货单号不能为空!}")
    @XdoSize(max = 60, message = "{进货单号长度不能超过60位字节长度(一个汉字2位字节长度)!}")
    private String purchaseNumber;
    //商品名称
    @NotEmpty(message = "{商品名称不能为空!}")
    @XdoSize(max = 80, message = "{商品名称长度不能超过80位字节长度(一个汉字2位字节长度)!}")
    private String productName;
    //发票号
    @XdoSize(max = 60, message = "{发票号长度不能超过60位字节长度(一个汉字2位字节长度)!}")
    private String invoiceNumber;
    //费用类型
    @NotEmpty(message = "{费用类型不能为空!}")
    @XdoSize(max = 100, message = "{费用类型长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    private String expenseType;
    //数量
    @NotNull(message = "{数量不能为空!}")
    @Digits(integer = 19, fraction = 6, message = "{数量必须为数字,整数位最大19位,小数最大6位!}")
    private BigDecimal quantity;
    //税额
    @NotNull(message = "{税额不能为空!}")
    @Digits(integer = 19, fraction = 2, message = "{税额必须为数字,整数位最大19位,小数最大2位!}")
    private BigDecimal taxAmount;
    //无税金额
    @NotNull(message = "{无税金额不能为空!}")
    @Digits(integer = 19, fraction = 2, message = "{无税金额必须为数字,整数位最大19位,小数最大2位!}")
    private BigDecimal noTaxAmount;
    //费用金额
    @NotNull(message = "{费用金额不能为空!}")
    @Digits(integer = 19, fraction = 2, message = "{费用金额必须为数字,整数位最大19位,小数最大2位!}")
    private BigDecimal expenseAmount;

    private String tradeCode;

    private String insertUser;

    private Date insertTime;

    private String updateUser;

    private Date updateTime;
}
