package com.dcjet.cs.dto.base.annotation;


import java.lang.annotation.*;
import java.math.RoundingMode;

@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD})
public @interface ExcelColumn {

    /***
     * excel 的列头Head
     *
     * @return
     */
    String name() default "";

    /***
     *
     *
     * @return
     */
    String[] value() default {""};

    /**
     * @return
     */
    String converter() default "";

    /***
     * tag 属性
     * @return
     */
    String tag() default "";

    /***
     * 是否由容器管理converter实例化
     *
     * @return
     */
    boolean IOC() default false;

    /***
     * format
     *
     * @return
     */
    String format() default "";

    /****
     * 设置format 舍入模式
     * @return
     */
    RoundingMode roundingMode() default RoundingMode.HALF_UP;

    /**
     * 手工构建的映射表
     * key:value, key:value
     *
     * @return
     */
    String map() default "";

    /***
     * 必填
     * 用于生成导入模版
     *
     */
    boolean required() default false;

    /***
     * 排序
     * 用于生成导入模版
     *
     * @return
     */
    int order() default 99;

    /***
     * 字符类型
     * 用于生成导入模版
     * @return
     */
    String type() default "字符型";

    /***
     * 备注
     * 用于生成导入模版
     *
     * @return
     */
    String note() default "";

}
