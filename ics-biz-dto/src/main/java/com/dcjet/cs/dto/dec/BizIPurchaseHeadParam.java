package com.dcjet.cs.dto.dec;


import lombok.Getter;
import lombok.Setter;
import java.util.Date;
import java.math.BigDecimal;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;

import com.xdo.validation.annotation.XdoSize;
import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;


/**
 * 进口管理-进货信息表头
 */
@Getter
@Setter
@ApiModel(value = "进口管理-进货信息表头-传入参数")
public class BizIPurchaseHeadParam implements Serializable{
    /**
     * 主建SID
     * 数据库字段:sid
     * 字符类型(50)
     */
    @XdoSize(max = 50, message = "主建SID长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("主建SID")
    private String sid;

    /**
     * 制单人
     * 数据库字段:insert_user
     * 字符类型(50)
     */
    @XdoSize(max = 50, message = "制单人长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("制单人")
    private String insertUser;

    /**
     * 订单制单时间
     * 数据库字段:insert_time
     * 日期类型(6)
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("订单制单时间")
    private Date insertTime;
    /**
     * 订单制单时间-开始时间
     */
    @ApiModelProperty("订单制单时间-开始时间")
    private String insertTimeFrom;

    /**
    * 订单制单时间-结束时间
    */
    @ApiModelProperty("订单制单时间-结束时间")
    private String insertTimeTo;

    /**
     * 创建人姓名
     * 数据库字段:insert_user_name
     * 字符类型(50)
     */
    @XdoSize(max = 50, message = "创建人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("创建人姓名")
    private String insertUserName;

    /**
     * 更新人
     * 数据库字段:update_user
     * 字符类型(50)
     */
    @XdoSize(max = 50, message = "更新人长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("更新人")
    private String updateUser;

    /**
     * 更新时间
     * 数据库字段:update_time
     * 日期类型(6)
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("更新时间")
    private Date updateTime;
    /**
     * 更新时间-开始时间
     */
    @ApiModelProperty("更新时间-开始时间")
    private String updateTimeFrom;

    /**
    * 更新时间-结束时间
    */
    @ApiModelProperty("更新时间-结束时间")
    private String updateTimeTo;

    /**
     * 更新人姓名
     * 数据库字段:update_user_name
     * 字符类型(50)
     */
    @XdoSize(max = 50, message = "更新人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("更新人姓名")
    private String updateUserName;

    /**
     * 企业代码
     * 数据库字段:trade_code
     * 字符类型(50)
     */
    @XdoSize(max = 50, message = "企业代码长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("企业代码")
    private String tradeCode;

    /**
     * 进货单号
     * 数据库字段:purchase_order_no
     * 字符类型(60)
     */
    @XdoSize(max = 60, message = "进货单号长度不能超过60位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("进货单号")
    @NotNull(message = "进货单号不能为空！")
    private String purchaseOrderNo;

    /**
     * 表头HEAD_ID
     * 数据库字段:head_id
     * 字符类型(50)
     */
    @XdoSize(max = 50, message = "表头HEAD_ID长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("表头HEAD_ID")
    @NotNull(message = "表头HEAD_ID不能为空！")
    private String headId;

    /**
     * 船名航次
     * 数据库字段:vessel_voyage
     * 字符类型(60)
     */
    @XdoSize(max = 60, message = "船名航次长度不能超过60位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("船名航次")
    private String vesselVoyage;

    /**
     * 开航日期
     * 数据库字段:sailing_date
     * 日期类型(6)
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("开航日期")
    private Date sailingDate;
    /**
     * 开航日期-开始时间
     */
    @ApiModelProperty("开航日期-开始时间")
    private String sailingDateFrom;

    /**
    * 开航日期-结束时间
    */
    @ApiModelProperty("开航日期-结束时间")
    private String sailingDateTo;

    /**
     * 预计抵达日期
     * 数据库字段:expected_arrival_date
     * 日期类型(6)
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("预计抵达日期")
    private Date expectedArrivalDate;
    /**
     * 预计抵达日期-开始时间
     */
    @ApiModelProperty("预计抵达日期-开始时间")
    private String expectedArrivalDateFrom;

    /**
    * 预计抵达日期-结束时间
    */
    @ApiModelProperty("预计抵达日期-结束时间")
    private String expectedArrivalDateTo;

    /**
     * 报关单号
     * 数据库字段:entry_no
     * 字符类型(18)
     */
    @XdoSize(max = 18, message = "报关单号长度不能超过18位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("报关单号")
    private String entryNo;

    /**
     * 报关日期
     * 数据库字段:entry_date
     * 日期类型(6)
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("报关日期")
    private Date entryDate;
    /**
     * 报关日期-开始时间
     */
    @ApiModelProperty("报关日期-开始时间")
    private String entryDateFrom;

    /**
    * 报关日期-结束时间
    */
    @ApiModelProperty("报关日期-结束时间")
    private String entryDateTo;

    /**
     * 进货数据状态
     * 数据库字段:purchasing_data_status
     * 字符类型(10)
     */
    @XdoSize(max = 10, message = "进货数据状态长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("进货数据状态")
    private String purchasingDataStatus;

    /**
     * 进货确认时间
     * 数据库字段:purchase_confirmation_time
     * 日期类型(6)
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("进货确认时间")
    private Date purchaseConfirmationTime;
    /**
     * 进货确认时间-开始时间
     */
    @ApiModelProperty("进货确认时间-开始时间")
    private String purchaseConfirmationTimeFrom;

    /**
    * 进货确认时间-结束时间
    */
    @ApiModelProperty("进货确认时间-结束时间")
    private String purchaseConfirmationTimeTo;

    /**
     * 版本号
     * 数据库字段:version_no
     * 字符类型(10)
     */
    @XdoSize(max = 10, message = "版本号长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("版本号")
    private String versionNo;

    /**
     * 数据状态
     * 数据库字段:data_status
     * 字符类型(10)
     */
    @XdoSize(max = 10, message = "数据状态长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("数据状态")
    private String dataStatus;

    /**
     * 拓展字段1
     * 数据库字段:extend1
     * 字符类型(200)
     */
    @XdoSize(max = 200, message = "拓展字段1长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("拓展字段1")
    private String extend1;

    /**
     * 拓展字段2
     * 数据库字段:extend2
     * 字符类型(200)
     */
    @XdoSize(max = 200, message = "拓展字段2长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("拓展字段2")
    private String extend2;

    /**
     * 拓展字段3
     * 数据库字段:extend3
     * 字符类型(200)
     */
    @XdoSize(max = 200, message = "拓展字段3长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("拓展字段3")
    private String extend3;

    /**
     * 拓展字段4
     * 数据库字段:extend4
     * 字符类型(200)
     */
    @XdoSize(max = 200, message = "拓展字段4长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("拓展字段4")
    private String extend4;

    /**
     * 拓展字段5
     * 数据库字段:extend5
     * 字符类型(200)
     */
    @XdoSize(max = 200, message = "拓展字段5长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("拓展字段5")
    private String extend5;

    /**
     * 拓展字段6
     * 数据库字段:extend6
     * 字符类型(200)
     */
    @XdoSize(max = 200, message = "拓展字段6长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("拓展字段6")
    private String extend6;

    /**
     * 拓展字段7
     * 数据库字段:extend7
     * 字符类型(200)
     */
    @XdoSize(max = 200, message = "拓展字段7长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("拓展字段7")
    private String extend7;

    /**
     * 拓展字段8
     * 数据库字段:extend8
     * 字符类型(200)
     */
    @XdoSize(max = 200, message = "拓展字段8长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("拓展字段8")
    private String extend8;

    /**
     * 拓展字段9
     * 数据库字段:extend9
     * 字符类型(200)
     */
    @XdoSize(max = 200, message = "拓展字段9长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("拓展字段9")
    private String extend9;

    /**
     * 拓展字段10
     * 数据库字段:extend10
     * 字符类型(200)
     */
    @XdoSize(max = 200, message = "拓展字段10长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("拓展字段10")
    private String extend10;

    /**
     * 备注
     * 数据库字段:note
     * 字符类型(200)
     */
    @XdoSize(max = 200, message = "备注长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("备注")
    private String note;

    /**
     * 计划编号
     * 数据库字段:plan_no
     * 字符类型(60)
     */
    @XdoSize(max = 60, message = "计划编号长度不能超过60位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("计划编号")
    private String planNo;

    /**
     * 序号
     * 数据库字段:serial_no
     * 数值类型(19,0)
     */
    @Digits(integer = 19, fraction = 0, message = "序号必须为数字,整数位最大19位,小数最大0位!")
    @ApiModelProperty("序号")
    private BigDecimal serialNo;

    /**
     * 订单信息表头-订单号
     * 数据库字段:order_no
     * 字符类型(60)
     */
    @XdoSize(max = 60, message = "订单信息表头-订单号长度不能超过60位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("订单信息表头-订单号")
    private String orderNo;



    /**
     * 业务类型
     * 数据库字段:business_type
     * 字符类型(60)
     */
    @ApiModelProperty("业务类型")
    @XdoSize(max = 60, message = "业务类型长度不能超过60位字节长度(一个汉字2位字节长度)!")
    @NotNull(message = "业务类型不能为空！")
    private String businessType;



    /**
     * 是否流入下一个节点
     */
    @ApiModelProperty("是否流入下一个节点")
    @XdoSize(max = 10, message = "是否流入下一个节点长度不能超过10位字节长度(一个汉字2位字节长度)!")
    private String isNext;


    /**
     * 进货信息表体数据
     */
    @ApiModelProperty("进货信息表体数据")
    private List<BizIPurchaseListParam> purchaseListParams;


}