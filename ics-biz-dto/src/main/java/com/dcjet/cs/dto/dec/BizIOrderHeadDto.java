package com.dcjet.cs.dto.dec;


import com.xdo.validation.annotation.XdoSize;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;

import javax.validation.constraints.Digits;


/**
 * 进口管理-订单信息表头
 */
@Getter
@Setter
@ApiModel(value = "进口管理-订单信息表头-返回信息")
public class BizIOrderHeadDto implements Serializable{

    /**
     * 主建sid
     * 数据库字段:sid
     * 字符类型(50)
     */
    @ApiModelProperty("主建sid")
    private String sid;

    /**
     * 制单人
     * 数据库字段:insert_user
     * 字符类型(50)
     */
    @ApiModelProperty("制单人")
    private String insertUser;

    /**
     * 订单制单时间
     * 数据库字段:insert_time
     * timestamp
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("订单制单时间")
    private Date insertTime;
    /**
     * 订单制单时间-开始时间
     */
    @ApiModelProperty("订单制单时间-开始时间")
    private String insertTimeFrom;

    /**
     * 订单制单时间-结束时间
     */
    @ApiModelProperty("订单制单时间-结束时间")
    private String insertTimeTo;

    /**
     * 创建人姓名
     * 数据库字段:insert_user_name
     * 字符类型(50)
     */
    @ApiModelProperty("创建人姓名")
    private String insertUserName;

    /**
     * 更新人
     * 数据库字段:update_user
     * 字符类型(50)
     */
    @ApiModelProperty("更新人")
    private String updateUser;

    /**
     * 更新时间
     * 数据库字段:update_time
     * timestamp
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("更新时间")
    private Date updateTime;
    /**
     * 更新时间-开始时间
     */
    @ApiModelProperty("更新时间-开始时间")
    private String updateTimeFrom;

    /**
     * 更新时间-结束时间
     */
    @ApiModelProperty("更新时间-结束时间")
    private String updateTimeTo;

    /**
     * 更新人姓名
     * 数据库字段:update_user_name
     * 字符类型(50)
     */
    @ApiModelProperty("更新人姓名")
    private String updateUserName;

    /**
     * 企业代码
     * 数据库字段:trade_code
     * 字符类型(50)
     */
    @ApiModelProperty("企业代码")
    private String tradeCode;

    /**
     * 业务类型
     * 数据库字段:business_type
     * 字符类型(60)
     */
    @ApiModelProperty("业务类型")
    private String businessType;

    /**
     * 订单数据状态
     * 数据库字段:order_data_status
     * 字符类型(10)
     */
    @ApiModelProperty("订单数据状态")
    private String orderDataStatus;

    /**
     * 合同编号
     * 数据库字段:contract_no
     * 字符类型(60)
     */
    @ApiModelProperty("合同编号")
    private String contractNo;

    /**
     * 订单编号
     * 数据库字段:order_no
     * 字符类型(60)
     */
    @ApiModelProperty("订单编号")
    private String orderNo;

    /**
     * 甲方
     * 数据库字段:party_a
     * 字符类型(200)
     */
    @ApiModelProperty("甲方")
    private String partyA;

    /**
     * 乙方
     * 数据库字段:party_b
     * 字符类型(200)
     */
    @ApiModelProperty("乙方")
    private String partyB;

    /**
     * 交货日期
     * 数据库字段:delivery_date
     * 字符类型(20)
     */
    @ApiModelProperty("交货日期")
    private String deliveryDate;

    /**
     * 付款方式
     * 数据库字段:payment_method
     * 字符类型(30)
     */
    @ApiModelProperty("付款方式")
    private String paymentMethod;

    /**
     * 进货单号
     * 数据库字段:purchase_order_no
     * 字符类型(60)
     */
    @ApiModelProperty("进货单号")
    private String purchaseOrderNo;

    /**
     * 进口发票号码
     * 数据库字段:import_invoice_no
     * 字符类型(60)
     */
    @ApiModelProperty("进口发票号码")
    private String importInvoiceNo;

    /**
     * 许可证号
     * 数据库字段:license_no
     * 字符类型(60)
     */
    @ApiModelProperty("许可证号")
    private String licenseNo;

    /**
     * 准运证编号
     * 数据库字段:transport_permit_no
     * 字符类型(60)
     */
    @ApiModelProperty("准运证编号")
    private String transportPermitNo;

    /**
     * 销售发票号
     * 数据库字段:sales_invoice_no
     * 字符类型(60)
     */
    @ApiModelProperty("销售发票号")
    private String salesInvoiceNo;

    /**
     * 销售合同号
     * 数据库字段:sales_contract_no
     * 字符类型(60)
     */
    @ApiModelProperty("销售合同号")
    private String salesContractNo;

    /**
     * 进货数据状态
     * 数据库字段:purchase_data_status
     * 字符类型(10)
     */
    @ApiModelProperty("进货数据状态")
    private String purchaseDataStatus;

    /**
     * 销售数据状态
     * 数据库字段:sales_data_status
     * 字符类型(10)
     */
    @ApiModelProperty("销售数据状态")
    private String salesDataStatus;

    /**
     * 入库回单状态
     * 数据库字段:inbound_receipt_status
     * 字符类型(10)
     */
    @ApiModelProperty("入库回单状态")
    private String inboundReceiptStatus;

    /**
     * 出库回单状态
     * 数据库字段:outbound_receipt_status
     * 字符类型(10)
     */
    @ApiModelProperty("出库回单状态")
    private String outboundReceiptStatus;

    /**
     * 版本号

     * 数据库字段:version_no
     * 字符类型(10)
     */
    @ApiModelProperty("版本号")
    private String versionNo;

    /**
     * 数据状态
     * 数据库字段:data_status
     * 字符类型(10)
     */
    @ApiModelProperty("数据状态")
    private String dataStatus;

    /**
     * 拓展字段1
     * 数据库字段:extend1
     * 字符类型(200)
     */
    @ApiModelProperty("拓展字段1")
    private String extend1;

    /**
     * 拓展字段2
     * 数据库字段:extend2
     * 字符类型(200)
     */
    @ApiModelProperty("拓展字段2")
    private String extend2;

    /**
     * 拓展字段3
     * 数据库字段:extend3
     * 字符类型(200)
     */
    @ApiModelProperty("拓展字段3")
    private String extend3;

    /**
     * 拓展字段4
     * 数据库字段:extend4
     * 字符类型(200)
     */
    @ApiModelProperty("拓展字段4")
    private String extend4;

    /**
     * 拓展字段5
     * 数据库字段:extend5
     * 字符类型(200)
     */
    @ApiModelProperty("拓展字段5")
    private String extend5;

    /**
     * 拓展字段6
     * 数据库字段:extend6
     * 字符类型(200)
     */
    @ApiModelProperty("拓展字段6")
    private String extend6;

    /**
     * 拓展字段7
     * 数据库字段:extend7
     * 字符类型(200)
     */
    @ApiModelProperty("拓展字段7")
    private String extend7;

    /**
     * 拓展字段8
     * 数据库字段:extend8
     * 字符类型(200)
     */
    @ApiModelProperty("拓展字段8")
    private String extend8;

    /**
     * 拓展字段9
     * 数据库字段:extend9
     * 字符类型(200)
     */
    @ApiModelProperty("拓展字段9")
    private String extend9;

    /**
     * 拓展字段10
     * 数据库字段:extend10
     * 字符类型(200)
     */
    @ApiModelProperty("拓展字段10")
    private String extend10;

    /**
     * 签订日期
     * 数据库字段:date_of_signing
     * timestamp
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("签订日期")
    private Date dateOfSigning;
    /**
     * 签订日期-开始时间
     */
    @ApiModelProperty("签订日期-开始时间")
    private String dateOfSigningFrom;

    /**
     * 签订日期-结束时间
     */
    @ApiModelProperty("签订日期-结束时间")
    private String dateOfSigningTo;

    /**
     * 计划编号
     * 数据库字段:plan_no
     * 字符类型(60)
     */
    @ApiModelProperty("计划编号")
    private String planNo;

    /**
     * 订单确认时间
     * 数据库字段:order_confirmation_time
     * timestamp
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("订单确认时间")
    private Date orderConfirmationTime;
    /**
     * 订单确认时间-开始时间
     */
    @ApiModelProperty("订单确认时间-开始时间")
    private String orderConfirmationTimeFrom;

    /**
     * 订单确认时间-结束时间
     */
    @ApiModelProperty("订单确认时间-结束时间")
    private String orderConfirmationTimeTo;

    /**
     * 审批状态
     * 数据库字段:appr_status
     * 字符类型(10)
     */
    @ApiModelProperty("审批状态")
    private String apprStatus;

    /**
     * 备注
     * 数据库字段：note
     * 字符类型(200)
     */
    @ApiModelProperty("备注")
    private String note;


    /**
     * 序号
     * 数据库字段：SERIAL_NO
     * 整数类型(19,0)
     */
    @ApiModelProperty("备注")
    private BigDecimal serialNo;

    /**
     * 进口合同表头SID
     * 数据库字段：HEAD_ID
     * 字符类型(50)
     */
    @ApiModelProperty("进口合同表头SID")
    private String headId;


    /**
     * 是否流入下一个节点
     * IS_NEXT
     * 字符类型(10)
     */
    @ApiModelProperty("是否流入下一个节点")
    private String isNext;


    /**
     * 进口合同号 前缀信息
     */
    @ApiModelProperty("进口合同号-前缀信息")
    private String contractPrefix;

    private List<BizIOrderListDto> orderList;

    @ApiModelProperty("总价")
    private String decTotal;

    @ApiModelProperty("数量")
    private String qty;


    /**
     * 表头是否删除
     * IS_DELETE
     * 字符类型(10)
     */
    @ApiModelProperty("是否删除")
    private String isDelete;

}