package com.dcjet.cs.dto.bi;
import com.dcjet.cs.dto.base.BasicDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
/**
 * 
 * <AUTHOR>
 * @date: 2019-9-4
 */
@ApiModel(value = "进出口生成内部编号返回信息")
@Setter @Getter
public class BiOrdernoGenRuleDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 主键
      */
    @ApiModelProperty("主键")
	private  String sid;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String tradeCode;
	/**
      * 模块名称 LI:进口提单 LE:出口提单
      */
    @ApiModelProperty("模块名称 LI:进口提单 LE:出口提单")
	private  String moduleName;
	/**
      * 监管方式
      */
    @ApiModelProperty("监管方式")
	private  String tradeMode;
	/**
      * 固定前缀
      */
    @ApiModelProperty("固定前缀")
	private  String fixedPrefix;
	/**
      * 备注
      */
    @ApiModelProperty("备注")
	private  String note;
	/**
      * 日期值：？人工录入显示日期格式，如:yyyyMM
      */
    @ApiModelProperty("日期值：？人工录入显示日期格式，如:yyyyMM")
	private  String dateFmt;
	/**
      * 流水位数
      */
    @ApiModelProperty("流水位数")
	private  Integer seqNoDigits;
}
