package com.dcjet.cs.dto.attach;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @date: 2019-4-25
 */
@Setter @Getter
@ApiModel(value = "随附单据传入参数")
public class AttachedParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 外键，关联手册/账册表头ID，或者清单、报核表头ID
     */
	@XdoSize(max = 40, message = "外键，关联手册/账册表头ID，或者清单、报核表头ID长度不能超过40位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("外键，关联手册/账册表头ID，或者清单、报核表头ID")
	private  String businessSid;
	/**
	 * 随附单据类型
	 */
	@XdoSize(max = 20, message = "随附单据类型长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("随附单据类型")
	private  String acmpType;
	/**
     * 业务单证类型
     */
	@XdoSize(max = 2, message = "业务单证类型长度不能超过2位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("业务单证类型")
	private  String businessType;

	/**
	 * 随附单据编号
	 */
	@ApiModelProperty("随附单据编号")
	private String acmpNo;
	/**
	 * 随附单据名称
	 */
	@ApiModelProperty("随附单据名称")
	private String originFileName;
	/**
     * 备注
     */
	@XdoSize(max = 512, message = "备注长度不能超过512位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("备注")
	private  String note;
	/**
	 * 提单内部编号
	 */
	@ApiModelProperty("提单内部编号")
	private String emsListNo;
	/**
	 * 数据来源
	 */
	@ApiModelProperty("数据来源")
	private String dataSource;
}
