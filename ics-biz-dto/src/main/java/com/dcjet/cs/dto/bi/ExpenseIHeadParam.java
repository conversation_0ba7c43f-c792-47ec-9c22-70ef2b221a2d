package com.dcjet.cs.dto.bi;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Setter
@Getter
@ApiModel(value = "进口费用信息传入参数")
public class ExpenseIHeadParam implements Serializable {
    private static final long serialVersionUID = 1L;

    private String sid;
    //单据号
    private String documentNumber;
    //业务类型
    @NotEmpty(message = "{业务类型不能为空!}")
    @XdoSize(max = 60, message = "{业务类型长度不能超过60位字节长度(一个汉字2位字节长度)!}")
    private String businessType;
    //预付标志
    @NotEmpty(message = "{预付标志不能为空!}")
    @XdoSize(max = 10, message = "{预付标志长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    private String advanceFlag;
    //部门
    @NotEmpty(message = "{部门不能为空!}")
    @XdoSize(max = 60, message = "{部门长度不能超过60位字节长度(一个汉字2位字节长度)!}")
    private String deptName;
    //收款方
    @NotEmpty(message = "{收款方不能为空!}")
    @XdoSize(max = 200, message = "{收款方长度不能超过200位字节长度(一个汉字2位字节长度)!}")
    private String payee;
    //费用类型
    @XdoSize(max = 100, message = "{费用类型长度不能超过100位字节长度(一个汉字2位字节长度)!}")
    private String expenseType;
    //合同号
    @XdoSize(max = 60, message = "{合同号长度不能超过60位字节长度(一个汉字2位字节长度)!}")
    private String contractNumber;
    //进/出货单号
    @XdoSize(max = 60, message = "{进/出货单号长度不能超过60位字节长度(一个汉字2位字节长度)!}")
    private String orderNumber;
    //币种
    @NotEmpty(message = "{币种不能为空!}")
    @XdoSize(max = 200, message = "{币种长度不能超过200位字节长度(一个汉字2位字节长度)!}")
    private String curr;
    //预付标志
    @XdoSize(max = 10, message = "{预付标志长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    private String advanceMark;
    //发送用友
    @XdoSize(max = 10, message = "{发送用友长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    private String sendUfida;
    //单据状态
    @XdoSize(max = 10, message = "{单据状态长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    private String state;
    //制单人
    private String createUser;
    //制单日期
    private Date createUserTime;
    private String createUserTimeFrom;
    private String createUserTimeTo;
    //确认时间
    private Date confirmationTime;
    //备注
    @XdoSize(max = 200, message = "{备注长度不能超过200位字节长度(一个汉字2位字节长度)!}")
    private String remark;

    private String tradeCode;

    private String insertUser;

    private Date insertTime;

    private String updateUser;

    private Date updateTime;
    //总金额
    private BigDecimal totalAmount;
}
