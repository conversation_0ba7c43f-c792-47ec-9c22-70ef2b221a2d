package com.dcjet.cs.dto.bi;

import com.dcjet.cs.dto.base.BasicDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2019-12-17
 */
@ApiModel(value = "基础管理-客户对于Shipfrom信息返回信息")
@Setter
@Getter
public class BiShipfromDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     * 数据库字段:sid
     * 字符类型(40)
     */
    @ApiModelProperty("主键")
    private String sid;

    /**
     * shipFrom代码
     * 数据库字段:ship_from_code
     * 字符类型(50)
     */
    @ApiModelProperty("shipFrom代码")
    private String shipFromCode;

    /**
     * shipFrom名称
     * 数据库字段:ship_from_name
     * 字符类型(255)
     */
    @ApiModelProperty("shipFrom名称")
    private String shipFromName;

    /**
     * shipFrom地址
     * 数据库字段:ship_from_address
     * 字符类型(512)
     */
    @ApiModelProperty("shipFrom地址")
    private String shipFromAddress;

    /**
     * 备注
     * 数据库字段:remark
     * 字符类型(255)
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 创建人
     * 数据库字段:insert_user
     * 字符类型(50)
     */
    @ApiModelProperty("创建人")
    private String insertUser;

    /**
     * 创建日期
     * 数据库字段:insert_time
     * timestamp
     */
    @ApiModelProperty("创建日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date insertTime;
    /**
     * 创建日期-开始时间
     */
    @ApiModelProperty("创建日期-开始时间")
    private String insertTimeFrom;

    /**
     * 创建日期-结束时间
     */
    @ApiModelProperty("创建日期-结束时间")
    private String insertTimeTo;

    /**
     * 修改人
     * 数据库字段:update_user
     * 字符类型(50)
     */
    @ApiModelProperty("修改人")
    private String updateUser;

    /**
     * 修改时间
     * 数据库字段:update_time
     * timestamp
     */
    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     * 修改时间-开始时间
     */
    @ApiModelProperty("修改时间-开始时间")
    private String updateTimeFrom;

    /**
     * 修改时间-结束时间
     */
    @ApiModelProperty("修改时间-结束时间")
    private String updateTimeTo;

    /**
     * 所属企业编码
     * 数据库字段:trade_code
     * 字符类型(20)
     */
    @ApiModelProperty("所属企业编码")
    private String tradeCode;

    /**
     * 供应商SID
     * 数据库字段:head_id
     * 字符类型(50)
     */
    @ApiModelProperty("供应商SID")
    private String headId;

    /**
     * 制单人姓名
     * 数据库字段:insert_user_name
     * 字符类型(50)
     */
    @ApiModelProperty("制单人姓名")
    private String insertUserName;

    /**
     * 修改人姓名
     * 数据库字段:update_user_name
     * 字符类型(50)
     */
    @ApiModelProperty("修改人姓名")
    private String updateUserName;

    /**
     * 供应商CODE
     * 数据库字段:supplier_code
     * 字符类型(50)
     */
    @ApiModelProperty("供应商CODE")
    private String supplierCode;


}
