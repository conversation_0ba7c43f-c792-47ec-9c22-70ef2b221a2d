package com.dcjet.cs.dto.bi;

import com.dcjet.cs.dto.base.BasicDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date: 2021-5-24
 */
@ApiModel(value = "返回信息")
@Setter
@Getter
public class GwstdDocTypeDto  extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 状态 0正常
     */
    @ApiModelProperty("状态 0正常")
    private String status;
    /**
     * 文档类型CODE
     */
    @ApiModelProperty("文档类型CODE")
    private String typeCode;
    /**
     * 文档类型NAME
     */
    @ApiModelProperty("文档类型NAME")
    private String typeName;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String note;
}
