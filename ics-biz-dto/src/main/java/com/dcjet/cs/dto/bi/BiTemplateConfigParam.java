package com.dcjet.cs.dto.bi;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @date: 2019-5-8
 */
@Setter @Getter
@ApiModel(value = "魔板配置模板传入参数")
public class BiTemplateConfigParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 模板类型
     */
	@NotEmpty(message="{模板类型不能为空！}")
	@XdoSize(max = 10, message = "{模板类型长度不能超过10位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("模板类型")
	private  String templateType;
	/**
     * 模板名称
     */
	@NotEmpty(message="{模板名称不能为空！}")
	@XdoSize(max = 100, message = "{模板名称长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("模板名称")
	private  String templateName;
	/**
     * 文件服务器返回链接地址
     */
	@NotEmpty(message="{文件服务器返回链接地址不能为空！}")
	@XdoSize(max = 500, message = "{文件服务器返回链接地址长度不能超过500位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("文件服务器返回链接地址")
	private  String fdfsId;
	/**
     * 供应商id
     */
	@NotEmpty(message="{供应商id不能为空！}")
	@XdoSize(max = 36, message = "{供应商id长度不能超过36位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("供应商id")
	private  String headId;
	/**
     * 备注
     */
	@XdoSize(max = 255, message = "{备注长度不能超过255位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("备注")
	private  String note;
	/**
     * 默认装运方式
     */
	@XdoSize(max = 100, message = "{默认装运方式长度不能超过100位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("默认装运方式")
	private  String shipment;
	/**
	 * 所属企业编码
	 */
	@XdoSize(max = 20, message = "{所属企业编码长度不能超过20位字节长度(一个汉字2位字节长度)!}")
	@ApiModelProperty("所属企业编码")
	private  String tradeCode;
}
