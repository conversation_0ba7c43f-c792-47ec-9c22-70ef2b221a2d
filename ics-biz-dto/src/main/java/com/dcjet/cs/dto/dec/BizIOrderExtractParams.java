package com.dcjet.cs.dto.dec;

import com.xdo.validation.annotation.XdoSize;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 进口合同提取数据
 */
@Getter
@Setter
public class BizIOrderExtractParams implements Serializable {

    /**
     * 合同号
     */
    @XdoSize(max = 200, message = "合同号长度不能超过200位字节长度(一个汉字2位字节长度)!")
    private String contractNo;


    /**
     * 进口合同表头的SID
     */
    private List<String> sids;


    /**
     * 订单号
     */
    @XdoSize(max = 200, message = "订单号长度不能超过200位字节长度(一个汉字2位字节长度)!")
    private String orderNo;

}
