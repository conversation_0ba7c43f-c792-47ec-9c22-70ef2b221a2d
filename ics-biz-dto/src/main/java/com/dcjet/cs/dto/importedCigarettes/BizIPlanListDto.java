package com.dcjet.cs.dto.importedCigarettes;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
/**
 * 
 * <AUTHOR>
 * @date: 2025-3-13
 */
@ApiModel(value = "返回信息")
@Setter @Getter
public class BizIPlanListDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String sid;
	/**
      * 主表ID
      */
    @ApiModelProperty("主表ID")
	private  String headId;
	/**
      * 商品名称（关联物料信息）
      */
    @ApiModelProperty("商品名称（关联物料信息）")
	private  String productName;
	/**
      * 供应商（关联物料信息，不可修改）
      */
    @ApiModelProperty("供应商（关联物料信息，不可修改）")
	private  String supplier;
	/**
      * 英文品牌（关联物料信息，可修改）
      */
    @ApiModelProperty("英文品牌（关联物料信息，可修改）")
	private  String englishBrand;
	/**
      * 原产地
      */
    @ApiModelProperty("原产地")
	private  String origin;
	/**
      * 计划数量（数值）
      */
    @ApiModelProperty("计划数量（数值）")
	private  BigDecimal planQuantity;
	/**
      * 计划数量单位（默认万支）
      */
    @ApiModelProperty("计划数量单位（默认万支）")
	private  String unit;
	/**
      * 币种（三位字母代码）
      */
    @ApiModelProperty("币种（三位字母代码）")
	private  String curr;
	/**
      * 计划单价（关联物料信息，可修改）
      */
    @ApiModelProperty("计划单价（关联物料信息，可修改）")
	private  BigDecimal unitPrice;
	/**
      * 计划总金额
      */
    @ApiModelProperty("计划总金额")
	private  BigDecimal totalAmount;
	/**
      * 折扣率（单位：%）
      */
    @ApiModelProperty("折扣率（单位：%）")
	private  BigDecimal discountRate;
	/**
      * 
      */
    @ApiModelProperty("折扣金额")
	private  BigDecimal discountAmount;
	/**
      * 制单人，自动识别最后操作人
      */
    @ApiModelProperty("制单人，自动识别最后操作人")
	private  String insertUser;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String insertUserName;
	/**
      * 制单时间，系统自动生成
      */
    @ApiModelProperty("制单时间，系统自动生成")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date insertTime;
	/**
      * 修改人，最后操作人
      */
    @ApiModelProperty("修改人，最后操作人")
	private  String updateUser;
	/**
      * 修改时间，最后操作时间
      */
    @ApiModelProperty("修改时间，最后操作时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String updateUserName;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend1;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend2;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend3;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend4;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend5;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend6;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend7;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend8;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend9;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend10;
	/**
      * 企业编码
      */
    @ApiModelProperty("企业编码")
	private  String tradeCode;

	/**
	 * 是否存在下游数据
	 */
	@ApiModelProperty("是否存在下游数据")
	private  String hasCtr;



	/**
	 * 创建人部门编码
	 */
	@ApiModelProperty("创建人部门编码")
	private  String sysOrgCode;
}
