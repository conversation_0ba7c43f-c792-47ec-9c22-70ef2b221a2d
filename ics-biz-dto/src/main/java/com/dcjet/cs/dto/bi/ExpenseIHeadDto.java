package com.dcjet.cs.dto.bi;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Setter
@Getter
public class ExpenseIHeadDto implements Serializable {
    private String sid;
    //单据号
    private String documentNumber;
    //业务类型
    private String businessType;
    //预付标志
    private String advanceFlag;
    //部门
    private String deptName;
    //收款方
    private String payee;
    //费用类型
    private String expenseType;
    //合同号
    private String contractNumber;
    //进/出货单号
    private String orderNumber;
    //币种
    private String curr;
    //预付标志
    private String advanceMark;
    //发送用友
    private String sendUfida;
    //单据状态
    private String state;
    //制单人
    private String createUser;
    //制单日期
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createUserTime;
    //确认时间
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date confirmationTime;
    //备注
    private String remark;

    private String tradeCode;

    private String insertUser;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;

    private String updateUser;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    //总金额
    private BigDecimal totalAmount;
}
