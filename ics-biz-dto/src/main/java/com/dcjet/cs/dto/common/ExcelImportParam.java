package com.dcjet.cs.dto.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;


@Setter
@Getter
@ApiModel(value = "Excel导入参数")
public class ExcelImportParam implements Serializable {

    @ApiModelProperty("导入数据表头所在行")
    private Integer headRow = 0;

    /**
     * 导入起始行
     */
    @ApiModelProperty("导入起始行")
    private Integer startRow = 5;

    /**
     * 允许直接导入
     */
    @ApiModelProperty("允许直接导入")
    private boolean directImport;

    /***
     *
     */
    @ApiModelProperty("业务参数,使用json序列化成字符串")
    private String bizParam;
}
