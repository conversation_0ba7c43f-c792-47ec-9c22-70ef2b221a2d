package com.dcjet.cs.dto.dec;


import lombok.Getter;
import lombok.Setter;
import java.util.Date;
import java.math.BigDecimal;
import javax.validation.constraints.Digits;
import com.xdo.validation.annotation.XdoSize;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;



/**
 * 进货管理-投保信息
 */
@Getter
@Setter
@ApiModel(value = "进货管理-投保信息-传入参数")
public class BizIncomingGoodsInsureParam implements Serializable{
    /**
     * 主键ID
     * 数据库字段:id
     * 字符类型(40)
     */
    @XdoSize(max = 40, message = "主键ID长度不能超过40位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("主键ID")
    private String id;

    /**
     * 业务类型
     * 数据库字段:business_type
     * 字符类型(60)
     */
    @XdoSize(max = 60, message = "业务类型长度不能超过60位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("业务类型")
    private String businessType;

    /**
     * 数据状态
     * 数据库字段:data_state
     * 字符类型(10)
     */
    @XdoSize(max = 10, message = "数据状态长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("数据状态")
    private String dataState;

    /**
     * 版本号
     * 数据库字段:version_no
     * 字符类型(10)
     */
    @XdoSize(max = 10, message = "版本号长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("版本号")
    private String versionNo;

    /**
     * 企业10位编码
     * 数据库字段:trade_code
     * 字符类型(10)
     */
    @XdoSize(max = 10, message = "企业10位编码长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("企业10位编码")
    private String tradeCode;

    /**
     * 组织机构代码
     * 数据库字段:sys_org_code
     * 字符类型(10)
     */
    @XdoSize(max = 10, message = "组织机构代码长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("组织机构代码")
    private String sysOrgCode;

    /**
     * 父级ID
     * 数据库字段:parent_id
     * 字符类型(40)
     */
    @XdoSize(max = 40, message = "父级ID长度不能超过40位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("父级ID")
    private String parentId;

    /**
     * 创建人
     * 数据库字段:create_by
     * 字符类型(50)
     */
    @XdoSize(max = 50, message = "创建人长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     * 数据库字段:create_time
     * timestamp
     */
    @ApiModelProperty("创建时间")
    private Date createTime;
    /**
     * 创建时间-开始时间
     */
    @ApiModelProperty("创建时间-开始时间")
    private Date createTimeFrom;

    /**
     * 创建时间-结束时间
     */
    @ApiModelProperty("创建时间-结束时间")
    private Date createTimeTo;

    /**
     * 更新人
     * 数据库字段:update_by
     * 字符类型(50)
     */
    @XdoSize(max = 50, message = "更新人长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 更新时间
     * 数据库字段:update_time
     * timestamp
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;
    /**
     * 更新时间-开始时间
     */
    @ApiModelProperty("更新时间-开始时间")
    private Date updateTimeFrom;

    /**
     * 更新时间-结束时间
     */
    @ApiModelProperty("更新时间-结束时间")
    private Date updateTimeTo;

    /**
     * 插入用户名
     * 数据库字段:insert_user_name
     * 字符类型(50)
     */
    @XdoSize(max = 50, message = "插入用户名长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("插入用户名")
    private String insertUserName;

    /**
     * 更新用户名
     * 数据库字段:update_user_name
     * 字符类型(50)
     */
    @XdoSize(max = 50, message = "更新用户名长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("更新用户名")
    private String updateUserName;

    /**
     * 扩展字段1
     * 数据库字段:extend1
     * 字符类型(200)
     */
    @XdoSize(max = 200, message = "扩展字段1长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段1")
    private String extend1;

    /**
     * 扩展字段2
     * 数据库字段:extend2
     * 字符类型(200)
     */
    @XdoSize(max = 200, message = "扩展字段2长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段2")
    private String extend2;

    /**
     * 扩展字段3
     * 数据库字段:extend3
     * 字符类型(200)
     */
    @XdoSize(max = 200, message = "扩展字段3长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段3")
    private String extend3;

    /**
     * 扩展字段4
     * 数据库字段:extend4
     * 字符类型(200)
     */
    @XdoSize(max = 200, message = "扩展字段4长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段4")
    private String extend4;

    /**
     * 扩展字段5
     * 数据库字段:extend5
     * 字符类型(200)
     */
    @XdoSize(max = 200, message = "扩展字段5长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段5")
    private String extend5;

    /**
     * 扩展字段6
     * 数据库字段:extend6
     * 字符类型(200)
     */
    @XdoSize(max = 200, message = "扩展字段6长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段6")
    private String extend6;

    /**
     * 扩展字段7
     * 数据库字段:extend7
     * 字符类型(200)
     */
    @XdoSize(max = 200, message = "扩展字段7长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段7")
    private String extend7;

    /**
     * 扩展字段8
     * 数据库字段:extend8
     * 字符类型(200)
     */
    @XdoSize(max = 200, message = "扩展字段8长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段8")
    private String extend8;

    /**
     * 扩展字段9
     * 数据库字段:extend9
     * 字符类型(200)
     */
    @XdoSize(max = 200, message = "扩展字段9长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段9")
    private String extend9;

    /**
     * 扩展字段10
     * 数据库字段:extend10
     * 字符类型(200)
     */
    @XdoSize(max = 200, message = "扩展字段10长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("扩展字段10")
    private String extend10;

    /**
     * 准运证编号
     * 数据库字段:permit_no
     * 字符类型(120)
     */
    @XdoSize(max = 60, message = "准运证编号长度不能超过60位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("准运证编号")
    private String permitNo;

    /**
     * 准运证申办日期
     * 数据库字段:permit_apply_date
     * date
     */
    @ApiModelProperty("准运证申办日期")
    private Date permitApplyDate;

    /**
     * 报关单号
     * 数据库字段:customs_declaration_no
     * 字符类型(36)
     */
    @XdoSize(max = 18, message = "报关单号长度不能超过18位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("报关单号")
    private String customsDeclarationNo;

    /**
     * 申报日期
     * 数据库字段:declaration_date
     * date
     */
    @ApiModelProperty("申报日期")
    private Date declarationDate;

    /**
     * 放行日期
     * 数据库字段:release_date
     * date
     */
    @ApiModelProperty("放行日期")
    private Date releaseDate;

    /**
     * 编号
     * 数据库字段:code_no
     * 字符类型(120)
     */
    @XdoSize(max = 60, message = "编号长度不能超过60位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("编号")
    private String codeNo;

    /**
     * 保险公司
     * 数据库字段:insurance_company
     * 字符类型(400)
     */
    @XdoSize(max = 200, message = "保险公司长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("保险公司")
    private String insuranceCompany;

    /**
     * 被保险人
     * 数据库字段:insured_person
     * 字符类型(400)
     */
    @XdoSize(max = 200, message = "被保险人长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("被保险人")
    private String insuredPerson;

    /**
     * 投保险别
     * 数据库字段:insurance_type
     * 字符类型(400)
     */
    @XdoSize(max = 200, message = "投保险别长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("投保险别")
    private String insuranceType;

    /**
     * 币种
     * 数据库字段:currency
     * 字符类型(20)
     */
    @XdoSize(max = 10, message = "币种长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("币种")
    private String currency;

    /**
     * 保险金额
     * 数据库字段:insurance_amount
     * 数值类型(19,4)
     */
    @Digits(integer = 19, fraction = 4, message = "保险金额必须为数字,整数位最大19位,小数最大4位!")
    @ApiModelProperty("保险金额")
    private BigDecimal insuranceAmount;

    /**
     * 保险费率
     * 数据库字段:insurance_rate
     * 数值类型(19,4)
     */
    @Digits(integer = 19, fraction = 4, message = "保险费率必须为数字,整数位最大19位,小数最大4位!")
    @ApiModelProperty("保险费率")
    private BigDecimal insuranceRate;

    /**
     * 保费
     * 数据库字段:premium
     * 数值类型(19,2)
     */
    @Digits(integer = 19, fraction = 2, message = "保费必须为数字,整数位最大19位,小数最大2位!")
    @ApiModelProperty("保费")
    private BigDecimal premium;

    /**
     * 备注
     * 数据库字段:remark
     * 字符类型(400)
     */
    @XdoSize(max = 200, message = "备注长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 表头head_id
     * 数据库字段:head_id
     * 字符类型(80)
     */
    @XdoSize(max = 40, message = "表头head_id长度不能超过40位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("表头head_id")
    private String headId;


}