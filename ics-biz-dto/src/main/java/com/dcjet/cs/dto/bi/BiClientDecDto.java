package com.dcjet.cs.dto.bi;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2020-8-17
 */
@ApiModel(value = "单位返回信息")
@Setter
@Getter
public class BiClientDecDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;
    /**
     * 所属企业编码
     */
    @ApiModelProperty("所属企业编码")
    private String tradeCode;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String insertUser;
    /**
     * 创建日期
     */
    @ApiModelProperty("创建日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;
    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updateUser;
    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    /**
     * 制单人姓名
     */
    @ApiModelProperty("制单人姓名")
    private String insertUserName;
    /**
     * 修改人姓名
     */
    @ApiModelProperty("修改人姓名")
    private String updateUserName;
    /**
     * 数据状态
     */
    @ApiModelProperty("数据状态")
    private String status;
    /**
     * 客户类型(生产销售单位 I ,消费使用单位 E)
     */
    @ApiModelProperty("客户类型(生产销售单位 I ,消费使用单位 E)")
    private String customerType;
    /**
     * 海关注册编码
     */
    @ApiModelProperty("海关注册编码")
    private String ownerCode;
    /**
     * 企业名称
     */
    @ApiModelProperty("企业名称")
    private String ownerName;
    /**
     * 社会信用代码
     */
    @ApiModelProperty("社会信用代码")
    private String ownerCreditCode;
}
