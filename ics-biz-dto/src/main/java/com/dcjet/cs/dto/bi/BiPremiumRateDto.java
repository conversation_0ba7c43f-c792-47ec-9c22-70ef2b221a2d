package com.dcjet.cs.dto.bi;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@ApiModel(value = "保费率返回参数")
public class BiPremiumRateDto {
    private String sid;
    private String insertUser;
    private String insertUserName;
    private Date insertTime;
    private String updateUser;
    private Date updateTime;
    private String updateUserName;
    private String tradeCode;
    @ApiModelProperty("年份")
    private String year;
    @ApiModelProperty("保费率")
    private BigDecimal premiumRate;
    @ApiModelProperty("启用状态 0 停用 1 启用")
    private String status;
}
