package com.dcjet.cs.dto.bi;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2021-4-21
 */
@ApiModel(value = "notify信息返回信息")
@Setter
@Getter
public class BiNotifyDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;
    /**
     * notify代码
     */
    @ApiModelProperty("notify代码")
    private String notifyCode;
    /**
     * notify名称
     */
    @ApiModelProperty("notify名称")
    private String notifyName;
    /**
     * notify地址
     */
    @ApiModelProperty("notify地址")
    private String notifyAddress;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String insertUser;
    /**
     * 创建日期
     */
    @ApiModelProperty("创建日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;
    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updateUser;
    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    /**
     * 所属企业编码
     */
    @ApiModelProperty("所属企业编码")
    private String tradeCode;
    /**
     * 客户SID
     */
    @ApiModelProperty("客户SID")
    private String headId;
    /**
     * 制单人姓名
     */
    @ApiModelProperty("制单人姓名")
    private String insertUserName;
    /**
     * 修改人姓名
     */
    @ApiModelProperty("修改人姓名")
    private String updateUserName;
    /**
     * 客户CODE
     */
    @ApiModelProperty("客户CODE")
    private String clientCode;
}
