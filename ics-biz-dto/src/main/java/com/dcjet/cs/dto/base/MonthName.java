package com.dcjet.cs.dto.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date: 2019-12-17
 */
@ApiModel(value = "基础管理-首页待审核返回数据")
@Setter
@Getter
public class MonthName implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 月份
     */
    @ApiModelProperty("月份")
    private String name;
    /**
     * 数据
     */
    @ApiModelProperty("数据")
    private List<MonthValue> data;
}
