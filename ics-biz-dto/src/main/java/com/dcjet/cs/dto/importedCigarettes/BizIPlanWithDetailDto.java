package com.dcjet.cs.dto.importedCigarettes;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 包含表头和表体数据的DTO
 * <AUTHOR>
 * @date: 2025-3-13
 */
@ApiModel(value = "返回信息（包含表头和表体数据）")
@Setter @Getter
public class BizIPlanWithDetailDto implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 表头数据
     */
    @ApiModelProperty("表头数据")
    private BizIPlanDto head;
    
    /**
     * 表体数据列表
     */
    @ApiModelProperty("表体数据列表")
    private List<BizIPlanListDto> details;
}
