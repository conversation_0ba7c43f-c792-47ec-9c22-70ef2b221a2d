package com.dcjet.cs.dto.params;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @date: 2025-3-11
 */
@ApiModel(value = "返回信息")
@Setter @Getter
public class TransCodeDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String id;


	/**
      * 
      */
    @ApiModelProperty("")
	private  String tradeCode;

	/**
      * 
      */
    @ApiModelProperty("")
	private  String createBy;
	/**
      * 
      */
    @ApiModelProperty("")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date createTime;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String updateBy;
	/**
      * 
      */
    @ApiModelProperty("")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String insertUserName;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String updateUserName;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend1;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend2;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend3;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend4;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend5;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend6;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend7;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend8;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend9;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend10;
	/**
      * 业务类型
      */
    @ApiModelProperty("业务类型")
	private  String bizType;
	/**
      * 关税率%
      */
    @ApiModelProperty("关税率%")
	private BigDecimal tariffRate;
	/**
      * 消费税率%
      */
    @ApiModelProperty("消费税率%")
	private  BigDecimal consumptionTaxRate;
	/**
	 * 增值税率%
	 */
	@ApiModelProperty("增值税率%")
	private BigDecimal vatRate;

	/**
	 * 进出口公司代理费率%
	 */
	@ApiModelProperty("进出口公司代理费率%")
	private BigDecimal ieAgentFeeRate;

	/**
	 * 总公司代理费率%
	 */
	@ApiModelProperty("总公司代理费率%")
	private BigDecimal hqAgentFeeRate;

	/**
	 * 国际运输类型
	 */
	@ApiModelProperty("国际运输类型")
	private String intlTransType;

	/**
	 * 是否集装箱装运
	 */
	@ApiModelProperty("是否集装箱装运")
	private Integer isContainerShip;

	/**
	 * 集装箱容量
	 */
	@ApiModelProperty("集装箱容量")
	private String containerCap;

	/**
	 * 集装箱型号
	 */
	@ApiModelProperty("集装箱型号")
	private String containerType;

	/**
	 * 国际运费
	 */
	@ApiModelProperty("国际运费")
	private BigDecimal intlFreightAmt;

	/**
	 * 港杂费
	 */
	@ApiModelProperty("港杂费")
	private BigDecimal portChargesAmt;

	/**
	 * 陆运费
	 */
	@ApiModelProperty("陆运费")
	private BigDecimal landFreightAmt;

	/**
	 * 通关费
	 */
	@ApiModelProperty("通关费")
	private BigDecimal customsFeeAmt;

	/**
	 * 验柜服务费
	 */
	@ApiModelProperty("验柜服务费")
	private BigDecimal cntrInspFeeAmt;

	/**
	 * 保险费率
	 */
	@ApiModelProperty("保险费率")
	private BigDecimal insuranceRate;

	/**
	 * 其他费用
	 */
	@ApiModelProperty("其他费用")
	private BigDecimal otherChargesAmt;

	/**
	 * 备注
	 */
	@ApiModelProperty("备注")
	private String remark;

}
