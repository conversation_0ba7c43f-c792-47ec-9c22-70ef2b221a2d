package com.dcjet.cs.dto.bi;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date: 2020-8-24
 */
@ApiModel(value = "发票参数返回信息")
@Setter
@Getter
public class BiInvoiceParamsDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 唯一键
     */
    @ApiModelProperty("唯一键")
    private String sid;
    /**
     * 发票参数类型
     */
    @ApiModelProperty("发票参数类型")
    private String paramsType;
    /**
     * 发票参数海关编码
     */
    @ApiModelProperty("发票参数海关编码")
    private String paramsCode;
    /**
     * 发票参数海关编码名称
     */
    @ApiModelProperty("发票参数海关编码名称")
    private String paramsName;
    /**
     * 单数对应名称
     */
    @ApiModelProperty("单数对应名称")
    private String singerName;
    /**
     * 复数对应名称
     */
    @ApiModelProperty("复数对应名称")
    private String multiName;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String note;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String insertUser;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;
    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updateUser;
    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    /**
     * 所属企业编码
     */
    @ApiModelProperty("所属企业编码")
    private String tradeCode;
    /**
     * 制单人姓名
     */
    @ApiModelProperty("制单人姓名")
    private String insertUserName;
    /**
     * 修改人姓名
     */
    @ApiModelProperty("修改人姓名")
    private String updateUserName;
}
