package com.dcjet.cs.dto.bi;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Setter
@Getter
public class ExpenseIListDto implements Serializable {

    private String sid;
    private String headId;

    //合同号
    private String contractNumber;
    //进货单号
    private String purchaseNumber;
    //商品名称
    private String productName;
    //发票号
    private String invoiceNumber;
    //费用类型
    private String expenseType;
    //数量
    private BigDecimal quantity;
    //税额
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal taxAmount;
    //无税金额
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal noTaxAmount;
    //费用金额
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal expenseAmount;

    private String tradeCode;

    private String insertUser;
    
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;

    private String updateUser;
    
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
