package com.dcjet.cs.dto.dec;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 进口管理-进货信息表头
 */
@Getter
@Setter
@ApiModel(value = "销售表头-传入参数")
public class BizISellHeadParam implements Serializable{


    /**
     * 主建SID
     * 字符类型(50)
     * 非必填
     */
    private String sid;

    private String headId;

    // purchaseOrderNumber: '', // 进货单号
    private String purchaseOrderNumber;

    //  purchasingUnit: '',      // 购货单位
    private String purchasingUnit;

    //  sellingUnit: '',        // 销货单位
    private String sellingUnit;

    //  taxRate: '',            // 税率%
    private BigDecimal taxRate;

    //  dateOfSale: '',         // 作销日期
    private Date dateOfSale;

    //  remark: '',             // 备注
    private String remark;

    //  salesDocumentStatus: '',// 销售单据状态
    private String salesDocumentStatus;

    //  salesDataConfirmationTime: '', // 销售数据确认时间
    private Date salesDataConfirmationTime;

    //  sendUFida: '',          // 发送用友
    private String sendUfida;

    //  drawer: '',             // 开票人
    private String drawer;

    //  businessDate: ''        // 业务日期
    private Date businessDate;

    private String insertUser;

    private Date insertTime;

    private String insertUserName;

    private String updateUser;

    private Date updateTime;

    private String updateUserName;

    private String tradeCode;

}