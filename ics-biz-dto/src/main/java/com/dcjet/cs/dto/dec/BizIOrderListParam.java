package com.dcjet.cs.dto.dec;


import com.dcjet.cs.dto.utils.ValidatorUtil;
import lombok.Getter;
import lombok.Setter;
import java.util.Date;
import java.math.BigDecimal;
import javax.validation.constraints.Digits;
import com.xdo.validation.annotation.XdoSize;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;


/**
 * 进口管理-订单信息表头
 */
@Getter
@Setter
@ApiModel(value = "进口管理-订单信息表头-传入参数")
public class BizIOrderListParam implements Serializable{
    /**
     * 主建SID
     * 数据库字段:sid
     * 字符类型(50)
     */
    @XdoSize(max = 50, message = "主建SID长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("主建SID")
    private String sid;

    /**
     * 制单人
     * 数据库字段:insert_user
     * 字符类型(50)
     */
    @XdoSize(max = 50, message = "制单人长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("制单人")
    private String insertUser;

    /**
     * 订单制单时间
     * 数据库字段:insert_time
     * timestamp
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("订单制单时间")
    private Date insertTime;
    /**
     * 订单制单时间-开始时间
     */
    @ApiModelProperty("订单制单时间-开始时间")
    private String insertTimeFrom;

    /**
    * 订单制单时间-结束时间
    */
    @ApiModelProperty("订单制单时间-结束时间")
    private String insertTimeTo;

    /**
     * 创建人姓名
     * 数据库字段:insert_user_name
     * 字符类型(50)
     */
    @XdoSize(max = 50, message = "创建人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("创建人姓名")
    private String insertUserName;

    /**
     * 更新人
     * 数据库字段:update_user
     * 字符类型(50)
     */
    @XdoSize(max = 50, message = "更新人长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("更新人")
    private String updateUser;

    /**
     * 更新时间
     * 数据库字段:update_time
     * timestamp
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("更新时间")
    private Date updateTime;
    /**
     * 更新时间-开始时间
     */
    @ApiModelProperty("更新时间-开始时间")
    private String updateTimeFrom;

    /**
    * 更新时间-结束时间
    */
    @ApiModelProperty("更新时间-结束时间")
    private String updateTimeTo;

    /**
     * 更新人姓名
     * 数据库字段:update_user_name
     * 字符类型(50)
     */
    @XdoSize(max = 50, message = "更新人姓名长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("更新人姓名")
    private String updateUserName;

    /**
     * 企业代码
     * 数据库字段:trade_code
     * 字符类型(50)
     */
    @XdoSize(max = 50, message = "企业代码长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("企业代码")
    private String tradeCode;

    /**
     * 商品牌号
     * 数据库字段:product_grade
     * 字符类型(80)
     */
    @XdoSize(max = 80, message = "商品牌号长度不能超过80位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("商品牌号")
    private String productGrade;

    /**
     * 单位
     * 数据库字段:unit
     * 字符类型(9)
     */
    @XdoSize(max = 9, message = "单位长度不能超过9位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("单位")
    private String unit;

    /**
     * 数量
     * 数据库字段:qty
     * 数值类型(19,6)
     */
    @Digits(integer = 19, fraction = 6, message = "数量必须为数字,整数位最大19位,小数最大6位!")
    @ApiModelProperty("数量")
    private BigDecimal qty;

    /**
     * 币种
     * 数据库字段:curr
     * 字符类型(10)
     */
    @XdoSize(max = 10, message = "币种长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("币种")
    private String curr;

    /**
     * 单价
     * 数据库字段:dec_price
     * 数值类型(19,5)
     */
    @Digits(integer = 19, fraction = 5, message = "单价必须为数字,整数位最大19位,小数最大5位!")
    @ApiModelProperty("单价")
    private BigDecimal decPrice;

    /**
     * 总价
     * 数据库字段:dec_total
     * 数值类型(19,2)
     */
    @Digits(integer = 19, fraction = 2, message = "总价必须为数字,整数位最大19位,小数最大2位!")
    @ApiModelProperty("总价")
    private BigDecimal decTotal;

    /**
     * 表头IO
     * 数据库字段:head_id
     * 字符类型(50)
     */
    @XdoSize(max = 50, message = "表头IO长度不能超过50位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("表头IO")
    private String headId;

    /**
     * 商品类别
     * 数据库字段:product_type
     * 字符类型(80)
     */
    @XdoSize(max = 80, message = "商品类别长度不能超过80位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("商品类别")
    private String productType;

    /**
     * 版本号

     * 数据库字段:version_no
     * 字符类型(10)
     */
    @XdoSize(max = 10, message = "版本号长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("版本号")
    private String versionNo;

    /**
     * 数据状态
     * 数据库字段:data_status
     * 字符类型(10)
     */
    @XdoSize(max = 10, message = "数据状态长度不能超过10位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("数据状态")
    private String dataStatus;

    /**
     * 拓展字段1
     * 数据库字段:extend1
     * 字符类型(200)
     */
    @XdoSize(max = 200, message = "拓展字段1长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("拓展字段1")
    private String extend1;

    /**
     * 拓展字段2
     * 数据库字段:extend2
     * 字符类型(200)
     */
    @XdoSize(max = 200, message = "拓展字段2长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("拓展字段2")
    private String extend2;

    /**
     * 拓展字段3
     * 数据库字段:extend3
     * 字符类型(200)
     */
    @XdoSize(max = 200, message = "拓展字段3长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("拓展字段3")
    private String extend3;

    /**
     * 拓展字段4
     * 数据库字段:extend4
     * 字符类型(200)
     */
    @XdoSize(max = 200, message = "拓展字段4长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("拓展字段4")
    private String extend4;

    /**
     * 拓展字段5
     * 数据库字段:extend5
     * 字符类型(200)
     */
    @XdoSize(max = 200, message = "拓展字段5长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("拓展字段5")
    private String extend5;

    /**
     * 拓展字段6
     * 数据库字段:extend6
     * 字符类型(200)
     */
    @XdoSize(max = 200, message = "拓展字段6长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("拓展字段6")
    private String extend6;

    /**
     * 拓展字段7
     * 数据库字段:extend7
     * 字符类型(200)
     */
    @XdoSize(max = 200, message = "拓展字段7长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("拓展字段7")
    private String extend7;

    /**
     * 拓展字段8
     * 数据库字段:extend8
     * 字符类型(200)
     */
    @XdoSize(max = 200, message = "拓展字段8长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("拓展字段8")
    private String extend8;

    /**
     * 拓展字段9
     * 数据库字段:extend9
     * 字符类型(200)
     */
    @XdoSize(max = 200, message = "拓展字段9长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("拓展字段9")
    private String extend9;

    /**
     * 拓展字段10
     * 数据库字段:extend10
     * 字符类型(200)
     */
    @XdoSize(max = 200, message = "拓展字段10长度不能超过200位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("拓展字段10")
    private String extend10;



    /**
     * 合同编号
     * 数据库字段:contract_no
     * 字符类型(60)
     */
    @XdoSize(max = 60, message = "合同编号长度不能超过60位字节长度(一个汉字2位字节长度)!")
    @ApiModelProperty("合同编号")
    private String contractNo;

    /**
     * 进口合同表头SID
     * 数据库字段:contract_head_id
     * 字符类型(60)
     */
    @ApiModelProperty("进口合同表头SID")
    @XdoSize(max = 60, message = "进口合同表头SID长度不能超过60位字节长度(一个汉字2位字节长度)!")
    private String contractHeadId;


    /**
     * 进口合同表体的SID
     */
    @ApiModelProperty("进口合同表体的SID")
    @XdoSize(max = 60, message = "进口合同表体的SID长度不能超过60位字节长度(一个汉字2位字节长度)!")
    private String contractListId;



    /**
     * 上一次复制的表体SID
     */
    @ApiModelProperty("上一次复制的表体SID")
    @XdoSize(max = 60, message = "上一次复制的表体SID长度不能超过60位字节长度(一个汉字2位字节长度)!")
    private String lastCopyListId;



    /**
     * 能否提交
     * @return
     */
    public String canSubmit(int index) {
        return ValidatorUtil.validation(index,this);
    }
}