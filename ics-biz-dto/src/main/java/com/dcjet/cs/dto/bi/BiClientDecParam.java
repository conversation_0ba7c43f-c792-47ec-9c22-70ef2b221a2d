package com.dcjet.cs.dto.bi;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date: 2020-8-17
 */
@Setter
@Getter
@ApiModel(value = "单位传入参数")
public class BiClientDecParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String sid;
    /**
     * 数据状态
     */
    @XdoSize(max = 1, message = "{数据状态长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("数据状态")
    private String status;
    /**
     * 客户类型(生产销售单位 I ,消费使用单位 E)
     */
    @XdoSize(max = 1, message = "{客户类型(生产销售单位 I ,消费使用单位 E)长度不能超过1位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("客户类型(生产销售单位 I ,消费使用单位 E)")
    private String customerType;
    /**
     * 海关注册编码
     */
    @XdoSize(max = 10, message = "{海关十位代码长度不能超过10位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("海关注册编码")
    private String ownerCode;
    /**
     * 企业名称
     */
    @NotBlank(message = "{企业中文名称不能为空}")
    @XdoSize(max = 70, message = "{企业中文名称长度不能超过70位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("企业名称")
    private String ownerName;
    /**
     * 社会信用代码
     */
    @XdoSize(max = 18, message = "{社会信用代码长度不能超过18位字节长度(一个汉字2位字节长度)!}")
    @ApiModelProperty("社会信用代码")
    private String ownerCreditCode;
}
