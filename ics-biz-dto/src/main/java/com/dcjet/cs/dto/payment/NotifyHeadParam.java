package com.dcjet.cs.dto.payment;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class NotifyHeadParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String sid;
	/**
     * 
     */
	@XdoSize(max = 10, message = "长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String tradeCode;
	/**
     * 
     */
	@XdoSize(max = 50, message = "长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String insertUserName;
	/**
     * 
     */
	@XdoSize(max = 50, message = "长度不能超过50位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String updateUserName;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend1;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend2;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend3;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend4;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend5;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend6;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend7;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend8;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend9;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend10;
	/**
	 * 业务类型
	 */
	@ApiModelProperty("业务类型")
	private  String bizType;
	/**
	 * 收款方
	 */
	@ApiModelProperty("收款方")
	private  String payee;
	/**
	 * 合同号
	 */
	@ApiModelProperty("合同号")
	private  String contractNo;

	/**
	 * 进/出货单号
	 */
	@ApiModelProperty("进/出货单号")
	private  String orderNumber;

	/**
	 * 付款金额
	 */
	@ApiModelProperty("付款金额")
	private BigDecimal payAmt;

	/**
	 * 币种
	 */
	@ApiModelProperty("币种")
	private  String curr;

	/**
	 * 预付标志
	 */
	@ApiModelProperty("预付标志")
	private  String prepayFlag;

	/**
	 * 发送用友
	 */
	@ApiModelProperty("发送用友")
	private  String sendUfida;

	/**
	 * 单据状态
	 */
	@ApiModelProperty("单据状态")
	private  String docStatus;

	/**
	 * 确认时间
	 */
	@ApiModelProperty("确认时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date cfmTime;
	/**
	 * 业务日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date bizDate;
	/**
	 * 收款银行
	 */
	private  String recvBank;
	/**
	 * 收款方帐号
	 */
	private  String recvAcct;
	/**
	 * RMB金额
	 */
	private BigDecimal payAmtRmb;
	/**
	 * 汇率
	 */
	private BigDecimal rate;
	/**
	 * 单据号
	 */
	private  String docNo;
	/**
	 * 部门
	 */
	private  String department;
	private  String updateTimeFrom;
	private  String updateTimeTo;
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;

	/**
	 * 备注
	 */
	private  String note;
}
