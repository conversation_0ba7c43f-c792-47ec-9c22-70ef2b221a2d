package com.dcjet.cs.dto.bi;
import com.dcjet.cs.dto.base.BasicDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
/**
 * 
 * <AUTHOR>
 * @date: 2019-6-6
 */
@ApiModel(value = "企业参数库返回信息")
@Setter @Getter
public class BiCustomerParamsDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 唯一键
      */
    @ApiModelProperty("唯一键")
	private  String sid;
	/**
      * 自定义参数类型
      */
    @ApiModelProperty("自定义参数类型")
	private  String paramsType;
	/**
      * 自定义参数编码
      */
    @ApiModelProperty("自定义参数编码")
	private  String paramsCode;
	/**
      * 自定义参数名称
      */
    @ApiModelProperty("自定义参数名称")
	private  String paramsName;
	/**
      * 备注
      */
    @ApiModelProperty("备注")
	private  String note;
	/**
      * 对应海关参数编码
      */
    @ApiModelProperty("对应海关参数编码")
	private  String customParamCode;
	/**
      * 对应海关参数名称
      */
    @ApiModelProperty("对应海关参数名称")
	private  String customParamName;
	/**
      * 所属企业编码
      */
    @ApiModelProperty("所属企业编码")
	private  String tradeCode;
}
