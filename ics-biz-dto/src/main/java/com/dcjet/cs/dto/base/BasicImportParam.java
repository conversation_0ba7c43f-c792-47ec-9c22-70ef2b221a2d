package com.dcjet.cs.dto.base;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter @Setter
public class BasicImportParam extends BasicParam {



    /***
     * 同步导入
     *
     * 导入数据错误描述
     */
    private String errMsg;

    /***
     * Xdo导入的时候使用
     */
    private int tempFlag;

    /***
     * Xdo导入的时候使用
     */
    private String tempRemark;

    public String getErrMsg() {
        if (errMsg == null) {
            errMsg = "";
        }
        return errMsg;
    }
}
