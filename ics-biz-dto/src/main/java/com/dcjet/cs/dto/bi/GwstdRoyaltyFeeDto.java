package com.dcjet.cs.dto.bi;

import com.dcjet.cs.dto.base.BasicDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
/**
 * 
 * <AUTHOR>
 * @date: 2020-5-22
 */
@ApiModel(value = "企业参数库-特许权使用费返回信息")
@Setter @Getter
public class GwstdRoyaltyFeeDto extends BasicDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 主键
      */
    @ApiModelProperty("主键")
	private  String sid;
	/**
      * 进出口标记，I进口 E出口
      */
    @ApiModelProperty("进出口标记，I进口 E出口")
	private  String iemark;
	/**
      * 特许权关联号
      */
    @ApiModelProperty("特许权关联号")
	private  String royalityNo;
	/**
      * 特许权使用费：0-否，1-是
      */
    @ApiModelProperty("特许权使用费：0-否，1-是")
	private  String royalityFee;
	/**
      * 备注
      */
    @ApiModelProperty("备注")
	private  String note;
}
