buildscript {
    repositories {
        maven { url "http://devops.dcjet.com.cn:52000/nexus/content/repositories/public/" }
        maven { url "http://maven.aliyun.com/nexus/content/groups/public" }
        mavenCentral()
    }
    dependencies {
        classpath("org.springframework.boot:spring-boot-gradle-plugin:2.5.7")
        classpath("org.liquibase:liquibase-gradle-plugin:2.1.1")
    }
    ext {
        lombokVersion = '1.18.4'
        mapstructVersion = '1.2.0.Final'
    }
}

plugins {
    id "ua.eshepelyuk.ManifestClasspath" version "1.0.0"
}

subprojects {
    apply plugin: 'maven'
    apply plugin: 'java'
    apply plugin: 'idea'
    apply plugin: 'maven-publish'
    apply plugin: 'war'
    apply plugin: 'io.spring.dependency-management'

    repositories {
        mavenLocal()
        maven { url "http://devops.dcjet.com.cn:52000/nexus/content/repositories/public/" }
        maven { url 'http://maven.aliyun.com/nexus/content/groups/public/' }
        maven { url 'http://maven.aliyun.com/nexus/content/repositories/google' }
        maven { url 'http://maven.aliyun.com/nexus/content/repositories/jcenter'}
        maven { url "https://plugins.gradle.org/m2/" }
        mavenCentral()
    }
    // 这个是新加的
    dependencyManagement {
        imports {
            mavenBom org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES
        }
    }
    compileJava.options.encoding = 'UTF-8'
    group 'com.dc.cs'
    version '1.0-SNAPSHOT'
    sourceCompatibility = 1.8
    targetCompatibility = 1.8
    configurations {
        provided
        all {
            resolutionStrategy.cacheDynamicVersionsFor 0, 'seconds'
        }
    }
    dependencies {
        compile "org.projectlombok:lombok:${lombokVersion}",
                "org.mapstruct:mapstruct-jdk8:${mapstructVersion}"
        testCompile  "org.projectlombok:lombok:${lombokVersion}",
                "org.mapstruct:mapstruct-jdk8:${mapstructVersion}"
        annotationProcessor "org.projectlombok:lombok:${lombokVersion}",
                "org.mapstruct:mapstruct-processor:${mapstructVersion}"
        testAnnotationProcessor "org.projectlombok:lombok:${lombokVersion}",
                "org.mapstruct:mapstruct-processor:${mapstructVersion}"
        implementation 'com.hankcs:hanlp:portable-1.7.5'
    }
    task sourceJar(type: Jar) {
        from sourceSets.main.allJava
    }

    publishing {
        publications {
            mavenJava(MavenPublication) {
                from components.java

                artifact sourceJar {
                    classifier "sources"
                }
            }
        }
        repositories {
            maven {
                url "http://devops.dcjet.com.cn:52000/nexus/content/repositories/thirdparty/"
                credentials {
                    username 'admin'
                    password 'dcjet'
                }
            }
        }
    }
}
project(':ics-biz-api') {
    dependencies {
        compile project(':ics-biz-dto')
        compile project(':ics-biz-services')
    }
}
project(':ics-biz-services') {
    dependencies {
        compile project(':ics-biz-dto')
        compile project(':ics-biz-utils')
    }
}

project(':ics-biz-executor') {
    dependencies {
        compile project(':ics-biz-services')
    }
}

project(':ics-biz-async') {
    dependencies {
        compile project(':ics-biz-services')
    }
}

project(':ics-biz-export') {
    dependencies {
        compile project(':ics-biz-services')
    }
}

