package com.dcjet.cs.params.dao;


import com.dcjet.cs.params.model.CostType;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
* generated by Generate 神码
* CostType
* <AUTHOR>
* @date: 2025-3-11
*/
public interface CostTypeMapper extends Mapper<CostType> {
    /**
     * 查询获取数据
     * @param costType
     * @return
     */
    List<CostType> getList(CostType costType);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    String getNextCode(@Param("tradeCode") String tradeCode);


    // 新增时校验 storehouseName 是否重复
    @Select("SELECT COUNT(*) FROM T_BIZ_COST_TYPE WHERE cost_name = #{costName} and trade_code = #{tradeCode}")
    int countByCostTypeName(@Param("costName")String costName,@Param("tradeCode") String tradeCode);
    // 新增时校验 storehouseName 是否重复
    @Select("SELECT COUNT(*) FROM T_BIZ_COST_TYPE WHERE  account_subject = #{accountSubject} and trade_code = #{tradeCode}")
    int countByAccountSubject(@Param("accountSubject")String accountSubject,@Param("tradeCode") String tradeCode);

    // 更新时校验 storehouseName 是否重复（排除自身）
    @Select("SELECT COUNT(*) FROM T_BIZ_COST_TYPE WHERE cost_name = #{costName}  AND sid != #{sid} and trade_code = #{tradeCode}")
    int countByCostTypeNameAndNotSid(@Param("costName")String costName, @Param("sid") String sid,@Param("tradeCode") String tradeCode);    // 更新时校验 storehouseName 是否重复（排除自身）
    @Select("SELECT COUNT(*) FROM T_BIZ_COST_TYPE WHERE  account_subject = #{accountSubject} AND sid != #{sid} and trade_code = #{tradeCode}")
    int countByAccountSubjectAndNotSid(@Param("accountSubject")String accountSubject, @Param("sid") String sid,@Param("tradeCode") String tradeCode);
}
