package com.dcjet.cs.params.dao;


import com.dcjet.cs.params.model.TransCode;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
* generated by Generate 神码
* TransCode
* <AUTHOR>
* @date: 2025-3-11
*/
public interface TransCodeMapper extends Mapper<TransCode> {
    /**
     * 查询获取数据
     * @param transCode
     * @return
     */
    List<TransCode> getList(TransCode transCode);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

}
