package com.dcjet.cs.params.dao;


import com.dcjet.cs.params.model.Storehouse;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
* generated by Generate 神码
* Storehouse
* <AUTHOR>
* @date: 2025-3-11
*/
public interface StorehouseMapper extends Mapper<Storehouse> {
    /**
     * 查询获取数据
     * @param storehouse
     * @return
     */
    List<Storehouse> getList(Storehouse storehouse);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    String getNextCode(@Param("tradeCode") String tradeCode);


    // 新增时校验 storehouseName 是否重复
    @Select("SELECT COUNT(*) FROM T_BIZ_STOREHOUSE WHERE storehouse_name = #{storehouseName} and trade_code = #{tradeCode}")
    int countByStorehouseName(@Param("storehouseName")String storehouseName,@Param("tradeCode") String tradeCode);

    // 更新时校验 storehouseName 是否重复（排除自身）
    @Select("SELECT COUNT(*) FROM T_BIZ_STOREHOUSE WHERE storehouse_name = #{storehouseName} AND sid != #{sid} and trade_code = #{tradeCode}")
    int countByStorehouseNameAndNotSid(@Param("storehouseName") String storehouseName, @Param("sid") String sid,@Param("tradeCode") String tradeCode);
}
