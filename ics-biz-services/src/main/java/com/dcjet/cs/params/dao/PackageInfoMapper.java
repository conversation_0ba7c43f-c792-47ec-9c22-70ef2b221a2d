package com.dcjet.cs.params.dao;


import com.dcjet.cs.params.model.PackageInfo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
* generated by Generate 神码
* PackageInfo
* <AUTHOR>
* @date: 2025-3-11
*/
public interface PackageInfoMapper extends Mapper<PackageInfo> {
    /**
     * 查询获取数据
     * @param packageInfo
     * @return
     */
    List<PackageInfo> getList(PackageInfo packageInfo);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    String getNextCode(@Param("tradeCode") String tradeCode);


    // 新增时校验 storehouseName 是否重复
    @Select("SELECT COUNT(*) FROM T_BIZ_PACKAGE_INFO WHERE pack_unit_cn_name = #{packUnitCnName} and trade_code = #{tradeCode}")
    int countByPackUnitCnName(@Param("packUnitCnName")String packUnitCnName,@Param("tradeCode") String tradeCode);

    // 更新时校验 storehouseName 是否重复（排除自身）
    @Select("SELECT COUNT(*) FROM T_BIZ_PACKAGE_INFO WHERE pack_unit_cn_name = #{packUnitCnName} AND sid != #{sid} and trade_code = #{tradeCode}")
    int countByPackUnitCnNameAndNotSid(@Param("packUnitCnName")String packUnitCnName, @Param("sid") String sid,@Param("tradeCode") String tradeCode);
}
