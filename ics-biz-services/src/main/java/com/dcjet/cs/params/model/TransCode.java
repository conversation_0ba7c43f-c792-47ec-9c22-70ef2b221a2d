package com.dcjet.cs.params.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Setter
@Getter
@Table(name = "T_BIZ_TRANSCODE")
public class TransCode implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
	 * 主键
	 */
	@Id
	@Column(name = "ID")
	private String id;
	/**
	 * 机构编码
	 */
	@Column(name = "SYS_ORG_CODE")
	private String sysOrgCode;
	/**
     * 
     */
	@Column(name = "trade_code")
	private  String tradeCode;
	/**
	 * 插入人
	 */
	@Column(name = "CREATE_BY")
	private String createBy;
	/**
	 * 插入时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@Column(name = "CREATE_TIME")
	private Date createTime;
	/**
	 * 更新人
	 */
	@Column(name = "UPDATE_BY")
	private String updateBy;

	/**
	 * 更新时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@Column(name = "UPDATE_TIME")
	private Date updateTime;
	/**
     * 
     */
	@Column(name = "insert_user_name")
	private  String insertUserName;
	/**
     * 
     */
	@Column(name = "update_user_name")
	private  String updateUserName;
	/**
     * 
     */
	@Column(name = "extend1")
	private  String extend1;
	/**
     * 
     */
	@Column(name = "extend2")
	private  String extend2;
	/**
     * 
     */
	@Column(name = "extend3")
	private  String extend3;
	/**
     * 
     */
	@Column(name = "extend4")
	private  String extend4;
	/**
     * 
     */
	@Column(name = "extend5")
	private  String extend5;
	/**
     * 
     */
	@Column(name = "extend6")
	private  String extend6;
	/**
     * 
     */
	@Column(name = "extend7")
	private  String extend7;
	/**
     * 
     */
	@Column(name = "extend8")
	private  String extend8;
	/**
     * 
     */
	@Column(name = "extend9")
	private  String extend9;
	/**
     * 
     */
	@Column(name = "extend10")
	private  String extend10;
	/**
	 * 业务类型
	 */
	@Column(name = "BIZ_TYPE")
	private String bizType;

	/**
	 * 关税率%
	 */
	@Column(name = "TARIFF_RATE")
	private BigDecimal tariffRate;

	/**
	 * 消费税率%
	 */
	@Column(name = "CONSUMPTION_TAX_RATE")
	private BigDecimal consumptionTaxRate;

	/**
	 * 增值税率%
	 */
	@Column(name = "VAT_RATE")
	private BigDecimal vatRate;

	/**
	 * 进出口公司代理费率%
	 */
	@Column(name = "IE_AGENT_FEE_RATE")
	private BigDecimal ieAgentFeeRate;

	/**
	 * 总公司代理费率%
	 */
	@Column(name = "HQ_AGENT_FEE_RATE")
	private BigDecimal hqAgentFeeRate;

	/**
	 * 国际运输类型
	 */
	@Column(name = "INTL_TRANS_TYPE")
	private String intlTransType;

	/**
	 * 是否集装箱装运
	 */
	@Column(name = "IS_CONTAINER_SHIP")
	private String isContainerShip;

	/**
	 * 集装箱容量
	 */
	@Column(name = "CONTAINER_CAP")
	private String containerCap;

	/**
	 * 集装箱型号
	 */
	@Column(name = "CONTAINER_TYPE")
	private String containerType;

	/**
	 * 国际运费
	 */
	@Column(name = "INTL_FREIGHT_AMT")
	private BigDecimal intlFreightAmt;

	/**
	 * 港杂费
	 */
	@Column(name = "PORT_CHARGES_AMT")
	private BigDecimal portChargesAmt;

	/**
	 * 陆运费
	 */
	@Column(name = "LAND_FREIGHT_AMT")
	private BigDecimal landFreightAmt;

	/**
	 * 通关费
	 */
	@Column(name = "CUSTOMS_FEE_AMT")
	private BigDecimal customsFeeAmt;

	/**
	 * 验柜服务费
	 */
	@Column(name = "CNTR_INSP_FEE_AMT")
	private BigDecimal cntrInspFeeAmt;

	/**
	 * 保险费率
	 */
	@Column(name = "INSURANCE_RATE")
	private BigDecimal insuranceRate;

	/**
	 * 其他费用
	 */
	@Column(name = "OTHER_CHARGES_AMT")
	private BigDecimal otherChargesAmt;

	/**
	 * 备注
	 */
	@Column(name = "REMARK")
	private String remark;
}
