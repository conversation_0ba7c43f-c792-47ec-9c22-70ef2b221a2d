package com.dcjet.cs.params.service;

import com.dcjet.cs.dto.params.PriceTermsDto;
import com.dcjet.cs.dto.params.PriceTermsParam;
import com.dcjet.cs.params.dao.PriceTermsMapper;
import com.dcjet.cs.params.mapper.PriceTermsDtoMapper;
import com.dcjet.cs.params.model.PriceTerms;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PriceTermsService extends BaseService<PriceTerms> {
    private final static ReentrantLock seqLock = new ReentrantLock();
    @Resource
    private PriceTermsMapper priceTermsMapper;
    @Resource
    private PriceTermsDtoMapper priceTermsDtoMapper;

    @Override
    public Mapper<PriceTerms> getMapper() {
        return this.priceTermsMapper;
    }

    /**
     * 获取分页列表
     *
     * @param priceTermsParam 价格条款参数
     * @param pageParam       分页参数
     * @param userInfo        用户信息
     * @return 价格条款分页列表
     */
    public ResultObject<List<PriceTermsDto>> getListPaged(PriceTermsParam priceTermsParam
            , PageParam pageParam, UserInfoToken<?> userInfo) {
        PriceTerms priceTerms = this.priceTermsDtoMapper.toPo(priceTermsParam);
        priceTerms.setTradeCode(userInfo.getCompany());
        Page<PriceTerms> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> this.priceTermsMapper.getList(priceTerms));
        // 启用分页查询
        List<PriceTermsDto> priceTermsDtoList = page.getResult().stream()
                .map(pt -> this.priceTermsDtoMapper.toDto(pt))
                .collect(Collectors.toList());
        return ResultObject.createInstance(priceTermsDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 新增价格条款
     *
     * @param priceTermsParam 价格条款参数
     * @param userInfo        用户信息
     * @return 价格条款参数模型
     */
    @Transactional(rollbackFor = Exception.class)
    public PriceTermsDto insert(PriceTermsParam priceTermsParam, UserInfoToken<?> userInfo) {
        int countByPriceTerm = this.priceTermsMapper.getCountByPriceTerm(priceTermsParam.getPriceTerm(), userInfo.getCompany());
        if (countByPriceTerm > 0) {
            throw new ErrorException(500, "价格条款已存在，请重新输入！");
        }
        PriceTerms priceTerms = this.priceTermsDtoMapper.toPo(priceTermsParam);
        priceTerms.setSid(UUID.randomUUID().toString());
        priceTerms.setTradeCode(userInfo.getCompany());
        priceTerms.setInsertUser(userInfo.getUserNo());
        priceTerms.setInsertUserName(userInfo.getUserName());
        priceTerms.setParamCode(this.getNextParamCode(userInfo));
        priceTerms.setInsertTime(new Date());
        // 新增数据
        int insertRows = this.priceTermsMapper.insert(priceTerms);
        return insertRows > 0 ? this.priceTermsDtoMapper.toDto(priceTerms) : null;
    }

    /**
     * 修改价格条款
     *
     * @param priceTermsParam 价格条款参数
     * @param userInfo        用户信息
     * @return 价格条款参数模型
     */
    @Transactional(rollbackFor = Exception.class)
    public PriceTermsDto update(PriceTermsParam priceTermsParam, UserInfoToken<?> userInfo) {
        PriceTerms priceTerms = this.priceTermsMapper.selectByPrimaryKey(priceTermsParam.getSid());
        if (StringUtils.isNotBlank(priceTerms.getPriceTerm()) &&
                !Objects.equals(priceTermsParam.getPriceTerm(), priceTerms.getPriceTerm())) {
            int countByPriceTerm = this.priceTermsMapper.getCountByPriceTerm(priceTermsParam.getPriceTerm(), userInfo.getCompany());
            if (countByPriceTerm > 0) {
                throw new ErrorException(500, "价格条款已存在，请重新输入！");
            }
        }
        priceTerms.setPriceTerm(priceTermsParam.getPriceTerm());
        priceTerms.setPriceTermDesc(priceTermsParam.getPriceTermDesc());
        priceTerms.setNote(priceTermsParam.getNote());
        priceTerms.setUpdateUser(userInfo.getUserNo());
        priceTerms.setUpdateUserName(userInfo.getUserName());
        priceTerms.setUpdateTime(new Date());
        // 更新数据
        int updateRows = this.priceTermsMapper.updateByPrimaryKey(priceTerms);
        return updateRows > 0 ? this.priceTermsDtoMapper.toDto(priceTerms) : null;
    }

    /**
     * 根据主键列表删除
     *
     * @param sids     主键列表
     * @param userInfo 用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteBySids(List<String> sids, UserInfoToken<?> userInfo) {
        if (CollectionUtils.isEmpty(sids)) {
            return;
        }
        log.info("user {} of company {} deleted records with {}", userInfo.getUserNo(), userInfo.getCompany(), sids);
        this.priceTermsMapper.deleteBySids(sids);
    }

    /**
     * 查询所有价格条款数据
     *
     * @param exportParam 导出参数
     * @param userInfo    用户信息
     * @return 价格条款传输列表
     */
    public List<PriceTermsDto> getExcelList(PriceTermsParam exportParam, UserInfoToken<?> userInfo) {
        PriceTerms priceTerms = this.priceTermsDtoMapper.toPo(exportParam);
        priceTerms.setTradeCode(userInfo.getCompany());
        List<PriceTerms> priceTermsList = this.priceTermsMapper.getList(priceTerms);
        if (CollectionUtils.isEmpty(priceTermsList)) {
            return Collections.emptyList();
        }
        List<PriceTermsDto> priceTermsDtoList = priceTermsList.stream()
                .map(pt -> this.priceTermsDtoMapper.toDto(pt))
                .collect(Collectors.toList());
        priceTermsDtoList.forEach(priceTermsDto -> {

        });
        return priceTermsDtoList;
    }

    /**
     * 获取下一个参数代码
     *
     * @param userInfo 用户信息
     * @return 下一个参数代码
     */
    public String getNextParamCode(UserInfoToken<?> userInfo) {
        return this.priceTermsMapper.getNextParamCode(userInfo.getCompany());
    }
}
