package com.dcjet.cs.params.mapper;



import com.dcjet.cs.dto.params.EnterpriseRateDto;
import com.dcjet.cs.dto.params.EnterpriseRateParam;
import com.dcjet.cs.params.model.EnterpriseRate;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface EnterpriseRateDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    EnterpriseRateDto toDto(EnterpriseRate po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    EnterpriseRate toPo(EnterpriseRateParam param);
    /**
     * 数据库原始数据更新
     * @param rateTableParam
     * @param rateTable
     */
    void updatePo(EnterpriseRateParam rateTableParam, @MappingTarget EnterpriseRate rateTable);
    default void patchPo(EnterpriseRateParam rateTableParam, EnterpriseRate rateTable) {
        // TODO 自行实现局部更新
    }
}
