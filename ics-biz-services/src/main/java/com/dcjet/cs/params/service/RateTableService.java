package com.dcjet.cs.params.service;


import com.dcjet.cs.dto.params.RateTableDto;
import com.dcjet.cs.dto.params.RateTableParam;
import com.dcjet.cs.params.dao.RateTableMapper;
import com.dcjet.cs.params.mapper.RateTableDtoMapper;
import com.dcjet.cs.params.model.RateTable;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.*;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Service
public class RateTableService extends BaseService<RateTable> {
    @Resource
    private RateTableMapper rateTableMapper;
    @Resource
    private RateTableDtoMapper rateTableDtoMapper;
    @Override
    public Mapper<RateTable> getMapper() {
        return rateTableMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param rateTableParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<RateTableDto>> getListPaged(RateTableParam rateTableParam, PageParam pageParam,UserInfoToken userInfo) {
        // 启用分页查询
        RateTable rateTable = rateTableDtoMapper.toPo(rateTableParam);
        rateTable.setTradeCode(userInfo.getCompany());
        Page<RateTable> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> rateTableMapper.getList(rateTable));
        List<RateTableDto> rateTableDtos = page.getResult().stream().map(head -> {
            RateTableDto dto = rateTableDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<RateTableDto>> paged = ResultObject.createInstance(rateTableDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param rateTableParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public RateTableDto insert(RateTableParam rateTableParam, UserInfoToken userInfo) {
        // 校验 storehouseName 是否重复
        int count = rateTableMapper.countByRateTableName(rateTableParam.getCurr(),userInfo.getCompany());
        if (count > 0) {
            throw new RuntimeException("币种已存在，请重新输入！");
        }
        RateTable rateTable = rateTableDtoMapper.toPo(rateTableParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        rateTable.setSid(sid);
        rateTable.setInsertUser(userInfo.getUserNo());
        rateTable.setInsertTime(new Date());
        rateTable.setTradeCode(userInfo.getCompany());

        // 新增数据
        int insertStatus = rateTableMapper.insert(rateTable);
        return  insertStatus > 0 ? rateTableDtoMapper.toDto(rateTable) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param rateTableParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public RateTableDto update(RateTableParam rateTableParam, UserInfoToken userInfo) {
        RateTable rateTable = rateTableMapper.selectByPrimaryKey(rateTableParam.getSid());

        // 校验 storehouseName 是否重复（排除自身）
        int count = rateTableMapper.countByRateTableNameAndNotSid(
                rateTableParam.getCurr(), rateTableParam.getSid(),userInfo.getCompany());
        if (count > 0) {
            throw new RuntimeException("币种已存在，请重新输入！");
        }

        rateTableDtoMapper.updatePo(rateTableParam, rateTable);
        rateTable.setUpdateUser(userInfo.getUserNo());
        rateTable.setUpdateTime(new Date());

        // 更新数据
        int update = rateTableMapper.updateByPrimaryKey(rateTable);
        return update > 0 ? rateTableDtoMapper.toDto(rateTable) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		rateTableMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<RateTableDto> selectAll(RateTableParam exportParam, UserInfoToken userInfo) {
        RateTable rateTable = rateTableDtoMapper.toPo(exportParam);
        rateTable.setTradeCode(userInfo.getCompany());
        List<RateTableDto> rateTableDtos = new ArrayList<>();
        List<RateTable> rateTables = rateTableMapper.getList(rateTable);
        if (CollectionUtils.isNotEmpty(rateTables)) {
            rateTableDtos = rateTables.stream().map(head -> {
                RateTableDto dto = rateTableDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return rateTableDtos;
    }

    public String getNextCode(UserInfoToken userInfo) {
        String code = rateTableMapper.getNextCode(userInfo.getCompany());
        if (StringUtils.isBlank(code)){
            code = "001";
        }else {
            int nextNumber = Integer.parseInt(code) + 1; // 转换为数字并加 1
            if (nextNumber > 999) {
                nextNumber = Integer.parseInt("001"); // 重置为最小值
            }
            code = String.format("%03d", nextNumber);
        }
        return code;
    }
}
