package com.dcjet.cs.params.mapper;



import com.dcjet.cs.dto.params.TransCodeDto;
import com.dcjet.cs.dto.params.TransCodeParam;
import com.dcjet.cs.params.model.TransCode;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TransCodeDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    TransCodeDto toDto(TransCode po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    TransCode toPo(TransCodeParam param);
    /**
     * 数据库原始数据更新
     * @param transCodeParam
     * @param transCode
     */
    void updatePo(TransCodeParam transCodeParam, @MappingTarget TransCode transCode);
    default void patchPo(TransCodeParam transCodeParam, TransCode transCode) {
        // TODO 自行实现局部更新
    }
}
