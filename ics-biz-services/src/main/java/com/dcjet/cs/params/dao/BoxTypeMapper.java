package com.dcjet.cs.params.dao;


import com.dcjet.cs.params.model.BoxType;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
* generated by Generate 神码
* BoxType
* <AUTHOR>
* @date: 2025-3-11
*/
public interface BoxTypeMapper extends Mapper<BoxType> {
    /**
     * 查询获取数据
     * @param boxType
     * @return
     */
    List<BoxType> getList(BoxType boxType);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    String getNextCode(@Param("tradeCode") String tradeCode);


    // 新增时校验是否重复
    @Select("SELECT COUNT(*) FROM T_BIZ_BOX_TYPE WHERE box_type = #{boxType} and trade_code = #{tradeCode}")
    int countByBoxTypeName(@Param("boxType")String boxType,@Param("tradeCode") String tradeCode);

    // 更新时校验是否重复（排除自身）
    @Select("SELECT COUNT(*) FROM T_BIZ_BOX_TYPE WHERE box_type = #{boxType}  AND sid != #{sid} and trade_code = #{tradeCode}")
    int countByBoxTypeNameAndNotSid(@Param("boxType")String curr, @Param("sid") String sid,@Param("tradeCode") String tradeCode);
}
