package com.dcjet.cs.params.service;


import com.dcjet.cs.dto.params.TransCodeDto;
import com.dcjet.cs.dto.params.TransCodeParam;
import com.dcjet.cs.params.dao.TransCodeMapper;
import com.dcjet.cs.params.mapper.TransCodeDtoMapper;
import com.dcjet.cs.params.model.TransCode;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Service
public class TransCodeService extends BaseService<TransCode> {
    @Resource
    private TransCodeMapper transCodeMapper;
    @Resource
    private TransCodeDtoMapper transCodeDtoMapper;
    @Override
    public Mapper<TransCode> getMapper() {
        return transCodeMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param transCodeParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<TransCodeDto>> getListPaged(TransCodeParam transCodeParam, PageParam pageParam,UserInfoToken userInfo) {
        // 启用分页查询
        TransCode transCode = transCodeDtoMapper.toPo(transCodeParam);
        transCode.setTradeCode(userInfo.getCompany());
        Page<TransCode> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> transCodeMapper.getList(transCode));
        List<TransCodeDto> transCodeDtos = page.getResult().stream().map(head -> {
            TransCodeDto dto = transCodeDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<TransCodeDto>> paged = ResultObject.createInstance(transCodeDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param transCodeParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public TransCodeDto insert(TransCodeParam transCodeParam, UserInfoToken userInfo) {
//        // 校验 storehouseName 是否重复
//        int count1 = transCodeMapper.countByTransCodeName(transCodeParam.getCategoryCode(),userInfo.getCompany());
//        if (count1 > 0) {
//            throw new RuntimeException("类别编号已存在，请重新输入！");
//        }
        TransCode transCode = transCodeDtoMapper.toPo(transCodeParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        transCode.setId(sid);
        transCode.setCreateBy(userInfo.getUserNo());
        transCode.setInsertUserName(userInfo.getUserName());
        transCode.setCreateTime(new Date());
        transCode.setTradeCode(userInfo.getCompany());
        // 新增数据
        int insertStatus = transCodeMapper.insert(transCode);
        return  insertStatus > 0 ? transCodeDtoMapper.toDto(transCode) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param transCodeParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public TransCodeDto update(TransCodeParam transCodeParam, UserInfoToken userInfo) {
        TransCode transCode = transCodeMapper.selectByPrimaryKey(transCodeParam.getId());

        // 校验 storehouseName 是否重复（排除自身）
//        int count1 = transCodeMapper.countByTransCodeNameAndNotSid(
//                transCodeParam.getCategoryCode(), transCodeParam.getSid() ,userInfo.getCompany());
//        if (count1 > 0) {
//            throw new RuntimeException("类别编号已存在，请重新输入！");
//        }


        transCodeDtoMapper.updatePo(transCodeParam, transCode);
        transCode.setUpdateBy(userInfo.getUserNo());
        transCode.setUpdateUserName(userInfo.getUserName());
        transCode.setUpdateTime(new Date());
        transCode.setTradeCode(userInfo.getCompany());
        // 更新数据
        int update = transCodeMapper.updateByPrimaryKey(transCode);
        return update > 0 ? transCodeDtoMapper.toDto(transCode) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		transCodeMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<TransCodeDto> selectAll(TransCodeParam exportParam, UserInfoToken userInfo) {
        TransCode transCode = transCodeDtoMapper.toPo(exportParam);
        transCode.setTradeCode(userInfo.getCompany());
        List<TransCodeDto> transCodeDtos = new ArrayList<>();
        List<TransCode> storehouses = transCodeMapper.getList(transCode);
        if (CollectionUtils.isNotEmpty(storehouses)) {
            transCodeDtos = storehouses.stream().map(head -> {
                TransCodeDto dto = transCodeDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return transCodeDtos;
    }

    public ResultObject copy(TransCodeParam transCodeParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("复制成功"));
        TransCode transCode = transCodeMapper.selectByPrimaryKey(transCodeParam.getId());
        if (transCode == null) {
            throw new ErrorException(400, "划款参数数据不存在，请刷新");
        }

        //表头
        TransCode newTransCode = new TransCode();
        BeanUtils.copyProperties(transCode, newTransCode);  // 复制所有属性
        String sid = UUID.randomUUID().toString();
        newTransCode.setId(sid);
        newTransCode.setCreateTime(new Date());
        newTransCode.setInsertUserName(userInfo.getUserName());
        newTransCode.setCreateBy(userInfo.getUserNo());
        newTransCode.setTradeCode(userInfo.getCompany());
        transCodeMapper.insert(newTransCode);




        return result;
    }
}
