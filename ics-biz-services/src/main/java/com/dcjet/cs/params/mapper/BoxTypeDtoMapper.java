package com.dcjet.cs.params.mapper;



import com.dcjet.cs.dto.params.BoxTypeDto;
import com.dcjet.cs.dto.params.BoxTypeParam;
import com.dcjet.cs.params.model.BoxType;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BoxTypeDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BoxTypeDto toDto(BoxType po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BoxType toPo(BoxTypeParam param);
    /**
     * 数据库原始数据更新
     * @param boxTypeParam
     * @param boxType
     */
    void updatePo(BoxTypeParam boxTypeParam, @MappingTarget BoxType boxType);
    default void patchPo(BoxTypeParam boxTypeParam, BoxType boxType) {
        // TODO 自行实现局部更新
    }
}
