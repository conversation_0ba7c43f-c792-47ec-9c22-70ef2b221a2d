package com.dcjet.cs.params.mapper;

import com.dcjet.cs.dto.params.PriceTermsDto;
import com.dcjet.cs.dto.params.PriceTermsParam;
import com.dcjet.cs.params.model.PriceTerms;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface PriceTermsDtoMapper {

    /**
     * po to dto
     *
     * @param po po
     * @return dto
     */
    PriceTermsDto toDto(PriceTerms po);

    /**
     * param to po
     *
     * @param param param
     * @return po
     */
    PriceTerms toPo(PriceTermsParam param);

    /**
     * update po from param
     *
     * @param param param
     * @param po    po
     */
    void updatePo(PriceTermsParam param, @MappingTarget PriceTerms po);
}
