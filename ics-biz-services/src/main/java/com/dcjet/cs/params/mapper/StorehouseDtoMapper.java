package com.dcjet.cs.params.mapper;



import com.dcjet.cs.dto.params.StorehouseDto;
import com.dcjet.cs.dto.params.StorehouseParam;
import com.dcjet.cs.params.model.Storehouse;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface StorehouseDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    StorehouseDto toDto(Storehouse po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    Storehouse toPo(StorehouseParam param);
    /**
     * 数据库原始数据更新
     * @param storehouseParam
     * @param storehouse
     */
    void updatePo(StorehouseParam storehouseParam, @MappingTarget Storehouse storehouse);
    default void patchPo(StorehouseParam storehouseParam, Storehouse storehouse) {
        // TODO 自行实现局部更新
    }
}
