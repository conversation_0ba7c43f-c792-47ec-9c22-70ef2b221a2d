package com.dcjet.cs.params.mapper;

import com.dcjet.cs.dto.params.CityDto;
import com.dcjet.cs.dto.params.CityParam;
import com.dcjet.cs.params.model.City;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CityDtoMapper {

    /**
     * po to dto
     *
     * @param po po
     * @return dto
     */
    CityDto toDto(City po);

    /**
     * param to po
     *
     * @param param param
     * @return po
     */
    City toPo(CityParam param);

    /**
     * update po from param
     *
     * @param param param
     * @param po    po
     */
    void updatePo(CityParam param, @MappingTarget City po);
}
