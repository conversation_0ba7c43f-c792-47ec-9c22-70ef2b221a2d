package com.dcjet.cs.params.service;


import com.dcjet.cs.dto.params.EnterpriseRateDto;
import com.dcjet.cs.dto.params.EnterpriseRateParam;
import com.dcjet.cs.params.dao.EnterpriseRateMapper;
import com.dcjet.cs.params.dao.RateTableMapper;
import com.dcjet.cs.params.mapper.EnterpriseRateDtoMapper;
import com.dcjet.cs.params.model.EnterpriseRate;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Service
public class EnterpriseRateService extends BaseService<EnterpriseRate> {
    @Resource
    private EnterpriseRateMapper enterpriseRateMapper;
    @Resource
    private EnterpriseRateDtoMapper enterpriseRateDtoMapper;
    @Override
    public Mapper<EnterpriseRate> getMapper() {
        return enterpriseRateMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param rateTableParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<EnterpriseRateDto>> getListPaged(EnterpriseRateParam rateTableParam, PageParam pageParam,UserInfoToken userInfo) {
        // 启用分页查询
        EnterpriseRate rateTable = enterpriseRateDtoMapper.toPo(rateTableParam);
        rateTable.setTradeCode(userInfo.getCompany());
        Page<EnterpriseRate> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> enterpriseRateMapper.getList(rateTable));
        List<EnterpriseRateDto> rateTableDtos = page.getResult().stream().map(head -> {
            EnterpriseRateDto dto = enterpriseRateDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<EnterpriseRateDto>> paged = ResultObject.createInstance(rateTableDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param rateTableParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public EnterpriseRateDto insert(EnterpriseRateParam rateTableParam, UserInfoToken userInfo) {
        // 校验 storehouseName 是否重复
        int count = enterpriseRateMapper.countByRateTableName(rateTableParam.getMonth(),rateTableParam.getCurr(),userInfo.getCompany());
        if (count > 0) {
            throw new RuntimeException("币种+月份 已存在，请重新输入！");
        }
        EnterpriseRate rateTable = enterpriseRateDtoMapper.toPo(rateTableParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        rateTable.setSid(sid);
        rateTable.setInsertUser(userInfo.getUserNo());
        rateTable.setInsertTime(new Date());
        rateTable.setTradeCode(userInfo.getCompany());

        // 新增数据
        int insertStatus = enterpriseRateMapper.insert(rateTable);
        return  insertStatus > 0 ? enterpriseRateDtoMapper.toDto(rateTable) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param rateTableParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public EnterpriseRateDto update(EnterpriseRateParam rateTableParam, UserInfoToken userInfo) {
        EnterpriseRate rateTable = enterpriseRateMapper.selectByPrimaryKey(rateTableParam.getSid());

        // 校验 storehouseName 是否重复（排除自身）
        int count = enterpriseRateMapper.countByRateTableNameAndNotSid(
                rateTableParam.getMonth(),rateTableParam.getCurr(), rateTableParam.getSid(),userInfo.getCompany());
        if (count > 0) {
            throw new RuntimeException("币种+月份已存在，请重新输入！");
        }

        enterpriseRateDtoMapper.updatePo(rateTableParam, rateTable);
        rateTable.setUpdateUser(userInfo.getUserNo());
        rateTable.setUpdateTime(new Date());

        // 更新数据
        int update = enterpriseRateMapper.updateByPrimaryKey(rateTable);
        return update > 0 ? enterpriseRateDtoMapper.toDto(rateTable) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		enterpriseRateMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<EnterpriseRateDto> selectAll(EnterpriseRateParam exportParam, UserInfoToken userInfo) {
        EnterpriseRate rateTable = enterpriseRateDtoMapper.toPo(exportParam);
        rateTable.setTradeCode(userInfo.getCompany());
        List<EnterpriseRateDto> rateTableDtos = new ArrayList<>();
        List<EnterpriseRate> rateTables = enterpriseRateMapper.getList(rateTable);
        if (CollectionUtils.isNotEmpty(rateTables)) {
            rateTableDtos = rateTables.stream().map(head -> {
                EnterpriseRateDto dto = enterpriseRateDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return rateTableDtos;
    }

    public String getNextCode(UserInfoToken userInfo) {
        String code = enterpriseRateMapper.getNextCode(userInfo.getCompany());
        if (StringUtils.isBlank(code)){
            code = "001";
        }else {
            int nextNumber = Integer.parseInt(code) + 1; // 转换为数字并加 1
            if (nextNumber > 999) {
                nextNumber = Integer.parseInt("001"); // 重置为最小值
            }
            code = String.format("%03d", nextNumber);
        }
        return code;
    }
}
