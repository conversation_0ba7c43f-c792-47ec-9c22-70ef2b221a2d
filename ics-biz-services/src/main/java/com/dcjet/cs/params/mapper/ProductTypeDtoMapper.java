package com.dcjet.cs.params.mapper;



import com.dcjet.cs.dto.params.ProductTypeDto;
import com.dcjet.cs.dto.params.ProductTypeParam;
import com.dcjet.cs.params.model.ProductType;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ProductTypeDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    ProductTypeDto toDto(ProductType po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    ProductType toPo(ProductTypeParam param);
    /**
     * 数据库原始数据更新
     * @param productTypeParam
     * @param productType
     */
    void updatePo(ProductTypeParam productTypeParam, @MappingTarget ProductType productType);
    default void patchPo(ProductTypeParam productTypeParam, ProductType productType) {
        // TODO 自行实现局部更新
    }
}
