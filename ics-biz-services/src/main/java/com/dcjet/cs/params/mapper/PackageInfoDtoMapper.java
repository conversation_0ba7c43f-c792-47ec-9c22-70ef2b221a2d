package com.dcjet.cs.params.mapper;



import com.dcjet.cs.dto.params.PackageInfoDto;
import com.dcjet.cs.dto.params.PackageInfoParam;
import com.dcjet.cs.params.model.PackageInfo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface PackageInfoDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    PackageInfoDto toDto(PackageInfo po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    PackageInfo toPo(PackageInfoParam param);
    /**
     * 数据库原始数据更新
     * @param packageInfoParam
     * @param packageInfo
     */
    void updatePo(PackageInfoParam packageInfoParam, @MappingTarget PackageInfo packageInfo);
    default void patchPo(PackageInfoParam packageInfoParam, PackageInfo packageInfo) {
        // TODO 自行实现局部更新
    }
}
