package com.dcjet.cs.params.service;


import com.dcjet.cs.dto.params.PackageInfoDto;
import com.dcjet.cs.dto.params.PackageInfoParam;
import com.dcjet.cs.params.dao.PackageInfoMapper;
import com.dcjet.cs.params.mapper.PackageInfoDtoMapper;
import com.dcjet.cs.params.model.PackageInfo;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Service
public class PackageInfoService extends BaseService<PackageInfo> {
    @Resource
    private PackageInfoMapper packageInfoMapper;
    @Resource
    private PackageInfoDtoMapper packageInfoDtoMapper;
    @Override
    public Mapper<PackageInfo> getMapper() {
        return packageInfoMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param packageInfoParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<PackageInfoDto>> getListPaged(PackageInfoParam packageInfoParam, PageParam pageParam,UserInfoToken userInfo) {
        // 启用分页查询
        PackageInfo packageInfo = packageInfoDtoMapper.toPo(packageInfoParam);
        packageInfo.setTradeCode(userInfo.getCompany());
        Page<PackageInfo> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> packageInfoMapper.getList(packageInfo));
        List<PackageInfoDto> packageInfoDtos = page.getResult().stream().map(head -> {
            PackageInfoDto dto = packageInfoDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<PackageInfoDto>> paged = ResultObject.createInstance(packageInfoDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param packageInfoParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public PackageInfoDto insert(PackageInfoParam packageInfoParam, UserInfoToken userInfo) {
        // 校验 storehouseName 是否重复
        int count = packageInfoMapper.countByPackUnitCnName(packageInfoParam.getPackUnitCnName(),userInfo.getCompany());
        if (count > 0) {
            throw new RuntimeException("包装单位中文名称已存在，请重新输入！");
        }
        PackageInfo packageInfo = packageInfoDtoMapper.toPo(packageInfoParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        packageInfo.setSid(sid);
        packageInfo.setInsertUser(userInfo.getUserNo());
        packageInfo.setInsertTime(new Date());
        packageInfo.setTradeCode(userInfo.getCompany());
        // 新增数据
        int insertStatus = packageInfoMapper.insert(packageInfo);
        return  insertStatus > 0 ? packageInfoDtoMapper.toDto(packageInfo) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param packageInfoParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public PackageInfoDto update(PackageInfoParam packageInfoParam, UserInfoToken userInfo) {
        PackageInfo packageInfo = packageInfoMapper.selectByPrimaryKey(packageInfoParam.getSid());

        // 校验 storehouseName 是否重复（排除自身）
        int count = packageInfoMapper.countByPackUnitCnNameAndNotSid(
                packageInfoParam.getPackUnitCnName(), packageInfoParam.getSid(),userInfo.getCompany());
        if (count > 0) {
            throw new RuntimeException("包装单位中文名称已存在，请重新输入！");
        }

        packageInfoDtoMapper.updatePo(packageInfoParam, packageInfo);
        packageInfo.setUpdateUser(userInfo.getUserNo());
        packageInfo.setUpdateTime(new Date());
        // 更新数据
        int update = packageInfoMapper.updateByPrimaryKey(packageInfo);
        return update > 0 ? packageInfoDtoMapper.toDto(packageInfo) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		packageInfoMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<PackageInfoDto> selectAll(PackageInfoParam exportParam, UserInfoToken userInfo) {
        PackageInfo packageInfo = packageInfoDtoMapper.toPo(exportParam);
        packageInfo.setTradeCode(userInfo.getCompany());
        List<PackageInfoDto> packageInfoDtos = new ArrayList<>();
        List<PackageInfo> storehouses = packageInfoMapper.getList(packageInfo);
        if (CollectionUtils.isNotEmpty(storehouses)) {
            packageInfoDtos = storehouses.stream().map(head -> {
                PackageInfoDto dto = packageInfoDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return packageInfoDtos;
    }

    public String getNextCode(UserInfoToken userInfo) {
        String code = packageInfoMapper.getNextCode(userInfo.getCompany());
        if (StringUtils.isBlank(code)){
            code = "001";
        }else {
            int nextNumber = Integer.parseInt(code) + 1; // 转换为数字并加 1
            if (nextNumber > 999) {
                nextNumber = Integer.parseInt("001"); // 重置为最小值
            }
            code = String.format("%03d", nextNumber);
        }
        return code;
    }
}
