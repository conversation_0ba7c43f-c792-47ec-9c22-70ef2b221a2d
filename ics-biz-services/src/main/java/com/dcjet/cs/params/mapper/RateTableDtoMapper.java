package com.dcjet.cs.params.mapper;



import com.dcjet.cs.dto.params.RateTableDto;
import com.dcjet.cs.dto.params.RateTableParam;
import com.dcjet.cs.params.model.RateTable;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RateTableDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    RateTableDto toDto(RateTable po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    RateTable toPo(RateTableParam param);
    /**
     * 数据库原始数据更新
     * @param rateTableParam
     * @param rateTable
     */
    void updatePo(RateTableParam rateTableParam, @MappingTarget RateTable rateTable);
    default void patchPo(RateTableParam rateTableParam, RateTable rateTable) {
        // TODO 自行实现局部更新
    }
}
