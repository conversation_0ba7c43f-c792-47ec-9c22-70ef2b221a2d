package com.dcjet.cs.params.mapper;



import com.dcjet.cs.dto.params.CostTypeDto;
import com.dcjet.cs.dto.params.CostTypeParam;
import com.dcjet.cs.params.model.CostType;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CostTypeDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    CostTypeDto toDto(CostType po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    CostType toPo(CostTypeParam param);
    /**
     * 数据库原始数据更新
     * @param costTypeParam
     * @param costType
     */
    void updatePo(CostTypeParam costTypeParam, @MappingTarget CostType costType);
    default void patchPo(CostTypeParam costTypeParam, CostType costType) {
        // TODO 自行实现局部更新
    }
}
