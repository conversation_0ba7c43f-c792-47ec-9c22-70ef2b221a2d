<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.params.dao.TransCodeMapper">
    <resultMap id="transCodeResultMap" type="com.dcjet.cs.params.model.TransCode">
        <id column="id" property="id" jdbcType="VARCHAR"/>

		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />

		<result column="create_by" property="createBy" jdbcType="VARCHAR" />
		<result column="sys_org_code" property="sysOrgCode" jdbcType="VARCHAR" />
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
		<result column="update_by" property="updateBy" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR" />
		<result column="update_user_name" property="updateUserName" jdbcType="VARCHAR" />
		<result column="extend1" property="extend1" jdbcType="VARCHAR" />
		<result column="extend2" property="extend2" jdbcType="VARCHAR" />
		<result column="extend3" property="extend3" jdbcType="VARCHAR" />
		<result column="extend4" property="extend4" jdbcType="VARCHAR" />
		<result column="extend5" property="extend5" jdbcType="VARCHAR" />
		<result column="extend6" property="extend6" jdbcType="VARCHAR" />
		<result column="extend7" property="extend7" jdbcType="VARCHAR" />
		<result column="extend8" property="extend8" jdbcType="VARCHAR" />
		<result column="extend9" property="extend9" jdbcType="VARCHAR" />
		<result column="extend10" property="extend10" jdbcType="VARCHAR" />
        <!-- 业务核心字段 -->
        <result column="BIZ_TYPE"           property="bizType"          jdbcType="VARCHAR" />
        <result column="TARIFF_RATE"        property="tariffRate"       jdbcType="NUMERIC" />
        <result column="CONSUMPTION_TAX_RATE" property="consumptionTaxRate" jdbcType="NUMERIC" />
        <result column="VAT_RATE"           property="vatRate"         jdbcType="NUMERIC" />
        <result column="IE_AGENT_FEE_RATE"  property="ieAgentFeeRate"  jdbcType="NUMERIC" />
        <result column="HQ_AGENT_FEE_RATE"  property="hqAgentFeeRate"  jdbcType="NUMERIC" />

        <!-- 运输相关字段 -->
        <result column="INTL_TRANS_TYPE"   property="intlTransType"   jdbcType="VARCHAR" />
        <result column="IS_CONTAINER_SHIP" property="isContainerShip" jdbcType="VARCHAR" />
        <result column="CONTAINER_CAP"     property="containerCap"    jdbcType="VARCHAR" />
        <result column="CONTAINER_TYPE"    property="containerType"   jdbcType="VARCHAR" />

        <!-- 费用字段 -->
        <result column="INTL_FREIGHT_AMT"  property="intlFreightAmt"  jdbcType="NUMERIC" />
        <result column="PORT_CHARGES_AMT"  property="portChargesAmt"   jdbcType="NUMERIC" />
        <result column="LAND_FREIGHT_AMT"  property="landFreightAmt"  jdbcType="NUMERIC" />
        <result column="CUSTOMS_FEE_AMT"   property="customsFeeAmt"    jdbcType="NUMERIC" />
        <result column="CNTR_INSP_FEE_AMT" property="cntrInspFeeAmt"   jdbcType="NUMERIC" />
        <result column="INSURANCE_RATE"    property="insuranceRate"    jdbcType="NUMERIC" />
        <result column="OTHER_CHARGES_AMT" property="otherChargesAmt"  jdbcType="NUMERIC" />

        <!-- 其他字段 -->
        <result column="REMARK"            property="remark"          jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List" >
        id,
        trade_code,
        sys_org_code,
        create_by,
        create_time,
        update_by,
        update_time,
        insert_user_name,
        update_user_name,
        biz_type,
        tariff_rate,
        consumption_tax_rate,
        vat_rate,
        ie_agent_fee_rate,
        hq_agent_fee_rate,
        intl_trans_type,
        is_container_ship,
        container_cap,
        container_type,
        intl_freight_amt,
        port_charges_amt,
        land_freight_amt,
        customs_fee_amt,
        cntr_insp_fee_amt,
        insurance_rate,
        other_charges_amt,
        remark,
        extend1,
        extend2,
        extend3,
        extend4,
        extend5,
        extend6,
        extend7,
        extend8,
        extend9,
        extend10
    </sql>
    <sql id="condition">
        <if test="bizType != null and bizType != ''"> and biz_type = #{bizType}  </if>
        <if test="insertUserName != null and insertUserName != ''"> and insert_user_name like '%'|| #{insertUserName} || '%' </if>
         and trade_code = #{tradeCode}
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="transCodeResultMap" parameterType="com.dcjet.cs.params.model.TransCode">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_BIZ_TRANSCODE t
        <where>
            <include refid="condition"></include>
        </where>
        order by  create_time DESC
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_BIZ_TRANSCODE t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>


</mapper>
