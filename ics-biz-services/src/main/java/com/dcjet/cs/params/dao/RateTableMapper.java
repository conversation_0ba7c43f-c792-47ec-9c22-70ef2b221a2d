package com.dcjet.cs.params.dao;


import com.dcjet.cs.params.model.RateTable;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
* generated by Generate 神码
* RateTable
* <AUTHOR>
* @date: 2025-3-11
*/
public interface RateTableMapper extends Mapper<RateTable> {
    /**
     * 查询获取数据
     * @param rateTable
     * @return
     */
    List<RateTable> getList(RateTable rateTable);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    String getNextCode(@Param("tradeCode") String tradeCode);


    // 新增时校验 storehouseName 是否重复
    @Select("SELECT COUNT(*) FROM T_BIZ_RATE_TABLE WHERE curr = #{curr} and trade_code = #{tradeCode}")
    int countByRateTableName(@Param("curr")String curr,@Param("tradeCode") String tradeCode);

    // 更新时校验 storehouseName 是否重复（排除自身）
    @Select("SELECT COUNT(*) FROM T_BIZ_RATE_TABLE WHERE curr = #{curr}  AND sid != #{sid} and trade_code = #{tradeCode}")
    int countByRateTableNameAndNotSid(@Param("curr")String curr, @Param("sid") String sid,@Param("tradeCode") String tradeCode);
}
