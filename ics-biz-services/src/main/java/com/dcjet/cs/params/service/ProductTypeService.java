package com.dcjet.cs.params.service;


import com.dcjet.cs.dto.params.ProductTypeDto;
import com.dcjet.cs.dto.params.ProductTypeParam;
import com.dcjet.cs.params.dao.ProductTypeMapper;
import com.dcjet.cs.params.mapper.ProductTypeDtoMapper;
import com.dcjet.cs.params.model.ProductType;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Service
public class ProductTypeService extends BaseService<ProductType> {
    @Resource
    private ProductTypeMapper productTypeMapper;
    @Resource
    private ProductTypeDtoMapper productTypeDtoMapper;
    @Override
    public Mapper<ProductType> getMapper() {
        return productTypeMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param productTypeParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<ProductTypeDto>> getListPaged(ProductTypeParam productTypeParam, PageParam pageParam,UserInfoToken userInfo) {
        // 启用分页查询
        ProductType productType = productTypeDtoMapper.toPo(productTypeParam);
        productType.setTradeCode(userInfo.getCompany());
        Page<ProductType> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> productTypeMapper.getList(productType));
        List<ProductTypeDto> productTypeDtos = page.getResult().stream().map(head -> {
            ProductTypeDto dto = productTypeDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<ProductTypeDto>> paged = ResultObject.createInstance(productTypeDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param productTypeParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ProductTypeDto insert(ProductTypeParam productTypeParam, UserInfoToken userInfo) {
        // 校验 storehouseName 是否重复
        int count1 = productTypeMapper.countByProductTypeName(productTypeParam.getCategoryCode(),userInfo.getCompany());
        if (count1 > 0) {
            throw new RuntimeException("类别编号已存在，请重新输入！");
        }
        int count2 = productTypeMapper.countByCategoryName(productTypeParam.getCategoryName(),userInfo.getCompany());
        if (count2 > 0) {
            throw new RuntimeException("类别名称已存在，请重新输入！");
        }
        ProductType productType = productTypeDtoMapper.toPo(productTypeParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        productType.setSid(sid);
        productType.setInsertUser(userInfo.getUserNo());
        productType.setInsertTime(new Date());
        productType.setTradeCode(userInfo.getCompany());
        // 新增数据
        int insertStatus = productTypeMapper.insert(productType);
        return  insertStatus > 0 ? productTypeDtoMapper.toDto(productType) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param productTypeParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ProductTypeDto update(ProductTypeParam productTypeParam, UserInfoToken userInfo) {
        ProductType productType = productTypeMapper.selectByPrimaryKey(productTypeParam.getSid());

        // 校验 storehouseName 是否重复（排除自身）
        int count1 = productTypeMapper.countByProductTypeNameAndNotSid(
                productTypeParam.getCategoryCode(), productTypeParam.getSid() ,userInfo.getCompany());
        if (count1 > 0) {
            throw new RuntimeException("类别编号已存在，请重新输入！");
        }
        int count2 = productTypeMapper.countByCategoryNameAndNotSid(
                productTypeParam.getCategoryName(), productTypeParam.getSid() ,userInfo.getCompany());
        if (count2 > 0) {
            throw new RuntimeException("类别名称已存在，请重新输入！");
        }

        productTypeDtoMapper.updatePo(productTypeParam, productType);
        productType.setUpdateUser(userInfo.getUserNo());
        productType.setUpdateTime(new Date());
        // 更新数据
        int update = productTypeMapper.updateByPrimaryKey(productType);
        return update > 0 ? productTypeDtoMapper.toDto(productType) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		productTypeMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<ProductTypeDto> selectAll(ProductTypeParam exportParam, UserInfoToken userInfo) {
        ProductType productType = productTypeDtoMapper.toPo(exportParam);
        productType.setTradeCode(userInfo.getCompany());
        List<ProductTypeDto> productTypeDtos = new ArrayList<>();
        List<ProductType> storehouses = productTypeMapper.getList(productType);
        if (CollectionUtils.isNotEmpty(storehouses)) {
            productTypeDtos = storehouses.stream().map(head -> {
                ProductTypeDto dto = productTypeDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return productTypeDtos;
    }

    public String getNextCode(UserInfoToken userInfo) {
        String code = productTypeMapper.getNextCode(userInfo.getCompany());
        if (StringUtils.isBlank(code)){
            code = "001";
        }else {
            int nextNumber = Integer.parseInt(code) + 1; // 转换为数字并加 1
            if (nextNumber > 999) {
                nextNumber = Integer.parseInt("001"); // 重置为最小值
            }
            code = String.format("%03d", nextNumber);
        }
        return code;
    }
}
