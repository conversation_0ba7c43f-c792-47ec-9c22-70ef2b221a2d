package com.dcjet.cs.params.service;


import com.dcjet.cs.dto.params.StorehouseDto;
import com.dcjet.cs.dto.params.StorehouseParam;
import com.dcjet.cs.params.dao.StorehouseMapper;
import com.dcjet.cs.params.mapper.StorehouseDtoMapper;
import com.dcjet.cs.params.model.Storehouse;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Service
public class StorehouseService extends BaseService<Storehouse> {
    @Resource
    private StorehouseMapper storehouseMapper;
    @Resource
    private StorehouseDtoMapper storehouseDtoMapper;
    @Override
    public Mapper<Storehouse> getMapper() {
        return storehouseMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param storehouseParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<StorehouseDto>> getListPaged(StorehouseParam storehouseParam, PageParam pageParam,UserInfoToken userInfo) {
        // 启用分页查询
        Storehouse storehouse = storehouseDtoMapper.toPo(storehouseParam);
        storehouse.setTradeCode(userInfo.getCompany());
        Page<Storehouse> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> storehouseMapper.getList(storehouse));
        List<StorehouseDto> storehouseDtos = page.getResult().stream().map(head -> {
            StorehouseDto dto = storehouseDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<StorehouseDto>> paged = ResultObject.createInstance(storehouseDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param storehouseParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public StorehouseDto insert(StorehouseParam storehouseParam, UserInfoToken userInfo) {
        // 校验 storehouseName 是否重复
        int count = storehouseMapper.countByStorehouseName(storehouseParam.getStorehouseName(),userInfo.getCompany());
        if (count > 0) {
            throw new RuntimeException("仓库名称已存在，请重新输入！");
        }
        Storehouse storehouse = storehouseDtoMapper.toPo(storehouseParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        storehouse.setSid(sid);
        storehouse.setInsertUser(userInfo.getUserNo());
        storehouse.setInsertTime(new Date());
        storehouse.setTradeCode(userInfo.getCompany());
        // 新增数据
        int insertStatus = storehouseMapper.insert(storehouse);
        return  insertStatus > 0 ? storehouseDtoMapper.toDto(storehouse) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param storehouseParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public StorehouseDto update(StorehouseParam storehouseParam, UserInfoToken userInfo) {
        Storehouse storehouse = storehouseMapper.selectByPrimaryKey(storehouseParam.getSid());

        // 校验 storehouseName 是否重复（排除自身）
        int count = storehouseMapper.countByStorehouseNameAndNotSid(
                storehouseParam.getStorehouseName(), storehouseParam.getSid(),userInfo.getCompany());
        if (count > 0) {
            throw new RuntimeException("仓库名称已存在，请重新输入！");
        }

        storehouseDtoMapper.updatePo(storehouseParam, storehouse);
        storehouse.setUpdateUser(userInfo.getUserNo());
        storehouse.setUpdateTime(new Date());
        // 更新数据
        int update = storehouseMapper.updateByPrimaryKey(storehouse);
        return update > 0 ? storehouseDtoMapper.toDto(storehouse) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		storehouseMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<StorehouseDto> selectAll(StorehouseParam exportParam, UserInfoToken userInfo) {
        Storehouse storehouse = storehouseDtoMapper.toPo(exportParam);
        storehouse.setTradeCode(userInfo.getCompany());
        List<StorehouseDto> storehouseDtos = new ArrayList<>();
        List<Storehouse> storehouses = storehouseMapper.getList(storehouse);
        if (CollectionUtils.isNotEmpty(storehouses)) {
            storehouseDtos = storehouses.stream().map(head -> {
                StorehouseDto dto = storehouseDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return storehouseDtos;
    }

    public String getNextCode(UserInfoToken userInfo) {
        String code = storehouseMapper.getNextCode(userInfo.getCompany());
        if (StringUtils.isBlank(code)){
            code = "001";
        }else {
            int nextNumber = Integer.parseInt(code) + 1; // 转换为数字并加 1
            if (nextNumber > 999) {
                nextNumber = Integer.parseInt("001"); // 重置为最小值
            }
            code = String.format("%03d", nextNumber);
        }
        return code;
    }
}
