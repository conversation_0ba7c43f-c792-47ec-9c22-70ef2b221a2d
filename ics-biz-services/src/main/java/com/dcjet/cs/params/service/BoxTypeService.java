package com.dcjet.cs.params.service;


import com.dcjet.cs.dto.params.BoxTypeDto;
import com.dcjet.cs.dto.params.BoxTypeParam;
import com.dcjet.cs.params.dao.BoxTypeMapper;
import com.dcjet.cs.params.mapper.BoxTypeDtoMapper;
import com.dcjet.cs.params.model.BoxType;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Service
public class BoxTypeService extends BaseService<BoxType> {
    @Resource
    private BoxTypeMapper boxTypeMapper;
    @Resource
    private BoxTypeDtoMapper boxTypeDtoMapper;
    @Override
    public Mapper<BoxType> getMapper() {
        return boxTypeMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param boxTypeParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BoxTypeDto>> getListPaged(BoxTypeParam boxTypeParam, PageParam pageParam,UserInfoToken userInfo) {
        // 启用分页查询
        BoxType boxType = boxTypeDtoMapper.toPo(boxTypeParam);
        boxType.setTradeCode(userInfo.getCompany());
        Page<BoxType> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> boxTypeMapper.getList(boxType));
        List<BoxTypeDto> boxTypeDtos = page.getResult().stream().map(head -> {
            BoxTypeDto dto = boxTypeDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BoxTypeDto>> paged = ResultObject.createInstance(boxTypeDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param boxTypeParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BoxTypeDto insert(BoxTypeParam boxTypeParam, UserInfoToken userInfo) {
        // 校验 storehouseName 是否重复
        int count = boxTypeMapper.countByBoxTypeName(boxTypeParam.getBoxType(),userInfo.getCompany());
        if (count > 0) {
            throw new RuntimeException("箱型已存在，请重新输入！");
        }
        BoxType boxType = boxTypeDtoMapper.toPo(boxTypeParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        boxType.setSid(sid);
        boxType.setInsertUser(userInfo.getUserNo());
        boxType.setInsertTime(new Date());
        boxType.setTradeCode(userInfo.getCompany());

        // 新增数据
        int insertStatus = boxTypeMapper.insert(boxType);
        return  insertStatus > 0 ? boxTypeDtoMapper.toDto(boxType) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param boxTypeParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BoxTypeDto update(BoxTypeParam boxTypeParam, UserInfoToken userInfo) {
        BoxType boxType = boxTypeMapper.selectByPrimaryKey(boxTypeParam.getSid());

        // 校验 storehouseName 是否重复（排除自身）
        int count = boxTypeMapper.countByBoxTypeNameAndNotSid(
                boxTypeParam.getBoxType(), boxTypeParam.getSid(),userInfo.getCompany());
        if (count > 0) {
            throw new RuntimeException("箱型已存在，请重新输入！");
        }

        boxTypeDtoMapper.updatePo(boxTypeParam, boxType);
        boxType.setUpdateUser(userInfo.getUserNo());
        boxType.setUpdateTime(new Date());

        // 更新数据
        int update = boxTypeMapper.updateByPrimaryKey(boxType);
        return update > 0 ? boxTypeDtoMapper.toDto(boxType) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		boxTypeMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BoxTypeDto> selectAll(BoxTypeParam exportParam, UserInfoToken userInfo) {
        BoxType boxType = boxTypeDtoMapper.toPo(exportParam);
        boxType.setTradeCode(userInfo.getCompany());
        List<BoxTypeDto> boxTypeDtos = new ArrayList<>();
        List<BoxType> boxTypes = boxTypeMapper.getList(boxType);
        if (CollectionUtils.isNotEmpty(boxTypes)) {
            boxTypeDtos = boxTypes.stream().map(head -> {
                BoxTypeDto dto = boxTypeDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return boxTypeDtos;
    }

    public String getNextCode(UserInfoToken userInfo) {
        String code = boxTypeMapper.getNextCode(userInfo.getCompany());
        if (StringUtils.isBlank(code)){
            code = "001";
        }else {
            int nextNumber = Integer.parseInt(code) + 1; // 转换为数字并加 1
            if (nextNumber > 999) {
                nextNumber = Integer.parseInt("001"); // 重置为最小值
            }
            code = String.format("%03d", nextNumber);
        }
        return code;
    }
}
