package com.dcjet.cs.payment.mapper;
import com.dcjet.cs.dto.payment.*;
import com.dcjet.cs.payment.model.BizPaymentSettlement;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-3-26
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizPaymentSettlementDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizPaymentSettlementDto toDto(BizPaymentSettlement po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizPaymentSettlement toPo(BizPaymentSettlementParam param);
    /**
     * 数据库原始数据更新
     * @param bizPaymentSettlementParam
     * @param bizPaymentSettlement
     */
    void updatePo(BizPaymentSettlementParam bizPaymentSettlementParam, @MappingTarget BizPaymentSettlement bizPaymentSettlement);
    default void patchPo(BizPaymentSettlementParam bizPaymentSettlementParam, BizPaymentSettlement bizPaymentSettlement) {
        // TODO 自行实现局部更新
    }
}
