package com.dcjet.cs.payment.mapper;




import com.dcjet.cs.dto.payment.NotifyHeadDto;
import com.dcjet.cs.dto.payment.NotifyHeadParam;
import com.dcjet.cs.payment.model.NotifyHead;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface NotifyHeadDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    NotifyHeadDto toDto(NotifyHead po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    NotifyHead toPo(NotifyHeadParam param);
    /**
     * 数据库原始数据更新
     * @param notifyHeadParam
     * @param notifyHead
     */
    void updatePo(NotifyHeadParam notifyHeadParam, @MappingTarget NotifyHead notifyHead);
    default void patchPo(NotifyHeadParam notifyHeadParam, NotifyHead notifyHead) {
        // TODO 自行实现局部更新
    }
}
