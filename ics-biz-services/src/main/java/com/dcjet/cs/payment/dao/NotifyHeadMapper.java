package com.dcjet.cs.payment.dao;



import com.dcjet.cs.payment.model.NotifyHead;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
* generated by Generate 神码
* NotifyHead
* <AUTHOR>
* @date: 2025-3-11
*/
public interface NotifyHeadMapper extends Mapper<NotifyHead> {
    /**
     * 查询获取数据
     * @param notifyHead
     * @return
     */
    List<NotifyHead> getList(NotifyHead notifyHead);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    String getDocNo(@Param("tradeCode") String tradeCode);

}
