package com.dcjet.cs.payment.mapper;




import com.dcjet.cs.dto.payment.NotifyListDto;
import com.dcjet.cs.dto.payment.NotifyListParam;
import com.dcjet.cs.payment.model.NotifyList;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface NotifyListDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    NotifyListDto toDto(NotifyList po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    NotifyList toPo(NotifyListParam param);
    /**
     * 数据库原始数据更新
     * @param notifyHeadParam
     * @param notifyHead
     */
    void updatePo(NotifyListParam notifyHeadParam, @MappingTarget NotifyList notifyHead);
    default void patchPo(NotifyListParam notifyHeadParam, NotifyList notifyHead) {
        // TODO 自行实现局部更新
    }
}
