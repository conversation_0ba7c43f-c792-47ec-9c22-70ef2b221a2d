package com.dcjet.cs.payment.service;



import com.dcjet.cs.bi.dao.ExpenseIListMapper;
import com.dcjet.cs.bi.service.ExpenseIListServise;
import com.dcjet.cs.dto.bi.CostIContractParam;
import com.dcjet.cs.dto.bi.CostIShippingOrderParam;
import com.dcjet.cs.dto.payment.NotifyListDto;
import com.dcjet.cs.dto.payment.NotifyListParam;
import com.dcjet.cs.payment.dao.NotifyHeadMapper;
import com.dcjet.cs.payment.dao.NotifyListMapper;
import com.dcjet.cs.payment.mapper.NotifyListDtoMapper;
import com.dcjet.cs.payment.model.NotifyHead;
import com.dcjet.cs.payment.model.NotifyList;
import com.dcjet.cs.util.bulkSql.BulkSqlOpt;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.beans.Transient;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Service
public class NotifyListService extends BaseService<NotifyList> {
    @Resource
    private NotifyListMapper notifyListMapper;
    @Resource
    private ExpenseIListMapper expenseIListMapper;
    @Resource
    private ExpenseIListServise expenseIListServise;
    @Resource
    private NotifyHeadMapper notifyHeadMapper;
    @Resource
    private NotifyListDtoMapper notifyListDtoMapper;
    @Override
    public Mapper<NotifyList> getMapper() {
        return notifyListMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param notifyListParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<NotifyListDto>> getListPaged(NotifyListParam notifyListParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        NotifyList notifyList = notifyListDtoMapper.toPo(notifyListParam);
        notifyList.setTradeCode(userInfo.getCompany());
        Page<NotifyList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> notifyListMapper.getList(notifyList));
        List<NotifyListDto> notifyListDtos = page.getResult().stream().map(head -> {
            NotifyListDto dto = notifyListDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<NotifyListDto>> paged = ResultObject.createInstance(notifyListDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param costIContractParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public NotifyHead insert(CostIContractParam costIContractParam, UserInfoToken userInfo) {
        //获取合同信息
        List<CostIContractParam> contractList = expenseIListMapper.getContractList(costIContractParam);
        //合同校验
        expenseIListServise.contractCheck(contractList);
        List<NotifyList> insertNotifyList  = new ArrayList<>();
        NotifyHead notifyHead = notifyHeadMapper.selectByPrimaryKey(costIContractParam.getHeadId());


        for (CostIContractParam iContractParam : contractList) {
            NotifyList notifyList = new NotifyList();
            String sid = UUID.randomUUID().toString();
            notifyList.setSid(sid);
            notifyList.setHeadId(costIContractParam.getHeadId());
            notifyList.setInsertUser(userInfo.getUserNo());
            notifyList.setInsertTime(new Date());
            notifyList.setTradeCode(userInfo.getCompany());
            //合同号
            notifyList.setContractNo(iContractParam.getContractNo());
            //订单号
            notifyList.setOrderNo(iContractParam.getOrderNo());
            //进货单号
            notifyList.setOrderNumber(iContractParam.getPurchaseOrderNo());
            //金额
            notifyList.setPayAmt(iContractParam.getDecTotal());
            //发票号
            notifyList.setInvoiceNumber(iContractParam.getInvoiceNumber());
            //数量
            notifyList.setQty(iContractParam.getQty());
            //单位
            notifyList.setUnit(iContractParam.getUnit());
            //rmb金额
            if (notifyHead!=null && notifyHead.getRate()!=null && iContractParam.getDecTotal()!=null){
                notifyList.setPayAmtRmb(notifyHead.getRate().multiply(iContractParam.getDecTotal()));
            }else {
                notifyList.setPayAmtRmb(BigDecimal.ZERO);
            }
            //商品名称
            notifyList.setGoodsName(iContractParam.getProductGrade());
            //listsid
            notifyList.setTurnoverSid(iContractParam.getSid());
            insertNotifyList.add(notifyList);

        }


        if (CollectionUtils.isNotEmpty(insertNotifyList)) {
            BulkSqlOpt.batchInsertForErp(insertNotifyList, NotifyListMapper.class);
        }


        List<NotifyList> notifyLists = notifyListMapper.selectByHeadId(costIContractParam.getHeadId());




        if (CollectionUtils.isNotEmpty(notifyLists)){
            Set<String> uniqueContracts = notifyLists.stream()
                    // 1. 提取合同号字段
                    .map(NotifyList::getContractNo)
                    // 2. 过滤空值
                    .filter(Objects::nonNull)
                    // 3. 拆分逗号分隔的字符串为数组
                    .flatMap(contract -> Arrays.stream(contract.split(",")))
                    // 4. 去除空格并过滤空字符串
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    // 5. 去重并保持顺序
                    .collect(Collectors.toCollection(LinkedHashSet::new));
            Set<String> uniqueOrderNumbers = notifyLists.stream()
                    // 1. 提取合同号字段
                    .map(NotifyList::getOrderNumber)
                    // 2. 过滤空值
                    .filter(Objects::nonNull)
                    // 3. 拆分逗号分隔的字符串为数组
                    .flatMap(contract -> Arrays.stream(contract.split(",")))
                    // 4. 去除空格并过滤空字符串
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    // 5. 去重并保持顺序
                    .collect(Collectors.toCollection(LinkedHashSet::new));

            // 6. 拼接为逗号分隔的字符串
            notifyHead.setContractNo(String.join(",", uniqueContracts));
            notifyHead.setOrderNumber(String.join(",", uniqueOrderNumbers));
        }else {
            // 6. 拼接为逗号分隔的字符串
            notifyHead.setContractNo(null);
            notifyHead.setOrderNumber(null);
        }

        BigDecimal totalPayAmt = BigDecimal.ZERO;
        BigDecimal totalPayAmtRmb = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(notifyLists)){
            totalPayAmt =  notifyLists.stream()
                    .map(NotifyList::getPayAmt)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            totalPayAmtRmb =  notifyLists.stream()
                    .map(NotifyList::getPayAmtRmb)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }


        if (notifyHead!= null){
            notifyHead.setPayAmt(totalPayAmt);
            notifyHead.setPayAmtRmb(totalPayAmtRmb);
        }

        notifyHeadMapper.updateByPrimaryKey(notifyHead);


        return notifyHead;


    }

    @Transactional(rollbackFor = Exception.class)
    public NotifyHead insertOrder(@Valid CostIShippingOrderParam costIShippingOrderParam, UserInfoToken userInfo) {

        //获取合同信息
        List<CostIShippingOrderParam> shippingOrderList = expenseIListMapper.getShippingOrderList(costIShippingOrderParam);

        List<NotifyList> insertNotifyList  = new ArrayList<>();

        NotifyHead notifyHead = notifyHeadMapper.selectByPrimaryKey(costIShippingOrderParam.getHeadId());



        for (CostIShippingOrderParam iShippingOrderParam : shippingOrderList) {

            NotifyList notifyList = new NotifyList();
            String sid = UUID.randomUUID().toString();
            notifyList.setSid(sid);
            notifyList.setHeadId(costIShippingOrderParam.getHeadId());
            notifyList.setInsertUser(userInfo.getUserNo());
            notifyList.setInsertTime(new Date());
            notifyList.setTradeCode(userInfo.getCompany());
            //货单号
            notifyList.setOrderNumber(iShippingOrderParam.getPurchaseOrderNo());
            //合同号
            notifyList.setContractNo(iShippingOrderParam.getContractNo());
            //订单号
            notifyList.setOrderNo(iShippingOrderParam.getOrderNo());
            //金额
            notifyList.setPayAmt(iShippingOrderParam.getDecTotal());
            //数量
            notifyList.setQty(iShippingOrderParam.getQty());
            //单位
            notifyList.setUnit(iShippingOrderParam.getUnit());
            //rmb金额
            if (notifyHead!=null && notifyHead.getRate()!=null && iShippingOrderParam.getDecTotal()!=null){
                notifyList.setPayAmtRmb(notifyHead.getRate().multiply(iShippingOrderParam.getDecTotal()));
            }else {
                notifyList.setPayAmtRmb(BigDecimal.ZERO);
            }
            //发票号
            notifyList.setInvoiceNumber(iShippingOrderParam.getInvoiceNumber());
            //商品名称
            notifyList.setGoodsName(iShippingOrderParam.getProductGrade());
            notifyList.setTurnoverSid(iShippingOrderParam.getSid());



            insertNotifyList.add(notifyList);
        }
        if (CollectionUtils.isNotEmpty(insertNotifyList)) {
            BulkSqlOpt.batchInsertForErp(insertNotifyList, NotifyListMapper.class);
        }


        List<NotifyList> notifyLists = notifyListMapper.selectByHeadId(costIShippingOrderParam.getHeadId());

        if (CollectionUtils.isNotEmpty(notifyLists)){
            Set<String> uniqueContracts = notifyLists.stream()
                    // 1. 提取合同号字段
                    .map(NotifyList::getContractNo)
                    // 2. 过滤空值
                    .filter(Objects::nonNull)
                    // 3. 拆分逗号分隔的字符串为数组
                    .flatMap(contract -> Arrays.stream(contract.split(",")))
                    // 4. 去除空格并过滤空字符串
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    // 5. 去重并保持顺序
                    .collect(Collectors.toCollection(LinkedHashSet::new));

            Set<String> uniqueOrderNumbers = notifyLists.stream()
                    // 1. 提取合同号字段
                    .map(NotifyList::getOrderNumber)
                    // 2. 过滤空值
                    .filter(Objects::nonNull)
                    // 3. 拆分逗号分隔的字符串为数组
                    .flatMap(contract -> Arrays.stream(contract.split(",")))
                    // 4. 去除空格并过滤空字符串
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    // 5. 去重并保持顺序
                    .collect(Collectors.toCollection(LinkedHashSet::new));

            // 6. 拼接为逗号分隔的字符串
            notifyHead.setContractNo(String.join(",", uniqueContracts));
            notifyHead.setOrderNumber(String.join(",", uniqueOrderNumbers));
        }else {
            // 6. 拼接为逗号分隔的字符串
            notifyHead.setContractNo(null);
            notifyHead.setOrderNumber(null);
        }

        BigDecimal totalPayAmt = BigDecimal.ZERO;
        BigDecimal totalPayAmtRmb = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(notifyLists)){
            totalPayAmt =  notifyLists.stream()
                    .map(NotifyList::getPayAmt)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            totalPayAmtRmb =  notifyLists.stream()
                    .map(NotifyList::getPayAmtRmb)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }


        if (notifyHead!= null){
            notifyHead.setPayAmt(totalPayAmt);
            notifyHead.setPayAmtRmb(totalPayAmtRmb);
        }

        notifyHeadMapper.updateByPrimaryKey(notifyHead);


        return notifyHead;
    }
    /**
     * 功能描述:修改
     *
     * @param notifyListParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public NotifyHead update(NotifyListParam notifyListParam, UserInfoToken userInfo) {
        NotifyList notifyList = notifyListMapper.selectByPrimaryKey(notifyListParam.getSid());
        NotifyHead notifyHead = notifyHeadMapper.selectByPrimaryKey(notifyList.getHeadId());

        notifyListDtoMapper.updatePo(notifyListParam, notifyList);
        notifyList.setUpdateUser(userInfo.getUserNo());
        notifyList.setUpdateTime(new Date());
        notifyList.setUpdateUserName(userInfo.getLoginName());



        //通过商品名称获取单价
        if (StringUtils.isNotBlank(notifyList.getGoodsName())){
            CostIShippingOrderParam iShippingOrderParam = new CostIShippingOrderParam();
            iShippingOrderParam.setTradeCode(userInfo.getCompany());
            iShippingOrderParam.setProductGrade(notifyList.getGoodsName());

            List<CostIShippingOrderParam> shippingOrderList = expenseIListMapper.getShippingOrderList(iShippingOrderParam);
            if (CollectionUtils.isNotEmpty(shippingOrderList)){
                CostIShippingOrderParam iShippingOrderParam1 = shippingOrderList.get(0);
                if (iShippingOrderParam1.getDecPrice()!=null && notifyList.getQty()!= null){
                    notifyList.setPayAmt(iShippingOrderParam1.getDecPrice().multiply(notifyList.getQty()));
                    notifyList.setPayAmtRmb(notifyHead.getRate().multiply(notifyList.getPayAmt()));
                }
            }

        }

        // 更新数据
        int update = notifyListMapper.updateByPrimaryKey(notifyList);







        //汇总表体金额
        BigDecimal totalPayAmt = new BigDecimal(0);
        BigDecimal totalPayAmtRmb = new BigDecimal(0);



        List<NotifyList> notifyLists = notifyListMapper.selectByHeadId(notifyList.getHeadId());

        for (NotifyList list : notifyLists) {
            if (notifyHead!=null && notifyHead.getRate()!=null && list.getPayAmt()!=null){
                totalPayAmt = totalPayAmt.add(list.getPayAmt());
                totalPayAmtRmb = totalPayAmtRmb.add(list.getPayAmtRmb());
            }
        }

        if (notifyHead!= null){
            notifyHead.setPayAmt(totalPayAmt);
            notifyHead.setPayAmtRmb(totalPayAmtRmb);
        }

        notifyHeadMapper.updateByPrimaryKey(notifyHead);


        return notifyHead;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public NotifyHead delete(List<String> sids,String headId , UserInfoToken userInfo) {
        notifyListMapper.deleteBySids(sids);


        NotifyHead notifyHead = notifyHeadMapper.selectByPrimaryKey(headId);
        List<NotifyList> notifyLists = notifyListMapper.selectByHeadId(headId);
        if (CollectionUtils.isNotEmpty(notifyLists)){
            Set<String> uniqueContracts = notifyLists.stream()
                    // 1. 提取合同号字段
                    .map(NotifyList::getContractNo)
                    // 2. 过滤空值
                    .filter(Objects::nonNull)
                    // 3. 拆分逗号分隔的字符串为数组
                    .flatMap(contract -> Arrays.stream(contract.split(",")))
                    // 4. 去除空格并过滤空字符串
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    // 5. 去重并保持顺序
                    .collect(Collectors.toCollection(LinkedHashSet::new));

            Set<String> uniqueOrderNumbers = notifyLists.stream()
                    // 1. 提取合同号字段
                    .map(NotifyList::getOrderNumber)
                    // 2. 过滤空值
                    .filter(Objects::nonNull)
                    // 3. 拆分逗号分隔的字符串为数组
                    .flatMap(contract -> Arrays.stream(contract.split(",")))
                    // 4. 去除空格并过滤空字符串
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    // 5. 去重并保持顺序
                    .collect(Collectors.toCollection(LinkedHashSet::new));

            // 6. 拼接为逗号分隔的字符串
            notifyHead.setContractNo(String.join(",", uniqueContracts));
            notifyHead.setOrderNumber(String.join(",", uniqueOrderNumbers));
        }else {
            // 6. 拼接为逗号分隔的字符串
            notifyHead.setContractNo(null);
            notifyHead.setOrderNumber(null);
        }


        BigDecimal totalPayAmt = BigDecimal.ZERO;
        BigDecimal totalPayAmtRmb = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(notifyLists)){
            totalPayAmt =  notifyLists.stream()
                    .map(NotifyList::getPayAmt)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            totalPayAmtRmb =  notifyLists.stream()
                    .map(NotifyList::getPayAmtRmb)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }


        if (notifyHead!= null){
            notifyHead.setPayAmt(totalPayAmt);
            notifyHead.setPayAmtRmb(totalPayAmtRmb);
        }

        notifyHeadMapper.updateByPrimaryKey(notifyHead);

        return notifyHead;
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<NotifyListDto> selectAll(NotifyListParam exportParam, UserInfoToken userInfo) {
        NotifyList notifyList = notifyListDtoMapper.toPo(exportParam);
        notifyList.setTradeCode(userInfo.getCompany());
        List<NotifyListDto> notifyListDtos = new ArrayList<>();
        List<NotifyList> notifyLists = notifyListMapper.getList(notifyList);
        if (CollectionUtils.isNotEmpty(notifyLists)) {
            notifyListDtos = notifyLists.stream().map(head -> {
                NotifyListDto dto = notifyListDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return notifyListDtos;
    }

}
