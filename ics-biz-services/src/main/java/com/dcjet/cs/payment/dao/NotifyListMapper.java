package com.dcjet.cs.payment.dao;



import com.dcjet.cs.payment.model.NotifyList;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
* generated by Generate 神码
* NotifyList
* <AUTHOR>
* @date: 2025-3-11
*/
public interface NotifyListMapper extends Mapper<NotifyList> {
    /**
     * 查询获取数据
     * @param notifyHead
     * @return
     */
    List<NotifyList> getList(NotifyList notifyHead);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);


    List<NotifyList> selectByHeadId(String sid);
}
