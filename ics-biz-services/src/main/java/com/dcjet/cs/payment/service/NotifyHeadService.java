package com.dcjet.cs.payment.service;



import com.dcjet.cs.bi.dao.BiClientInformationMapper;
import com.dcjet.cs.dto.payment.NotifyHeadDto;
import com.dcjet.cs.dto.payment.NotifyHeadParam;
import com.dcjet.cs.importedCigarettes.dao.BizIContractHeadMapper;
import com.dcjet.cs.payment.dao.BizPaymentSettlementMapper;
import com.dcjet.cs.payment.dao.NotifyHeadMapper;
import com.dcjet.cs.payment.dao.NotifyListMapper;
import com.dcjet.cs.payment.mapper.NotifyHeadDtoMapper;
import com.dcjet.cs.payment.model.BizPaymentSettlement;
import com.dcjet.cs.payment.model.NotifyHead;
import com.dcjet.cs.payment.model.NotifyList;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.bulkSql.BulkSqlOpt;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Service
public class NotifyHeadService extends BaseService<NotifyHead> {
    @Resource
    private NotifyHeadMapper notifyHeadMapper;
    @Resource
    private BizIContractHeadMapper bizIContractHeadMapper;
    @Resource
    private BizPaymentSettlementMapper bizPaymentSettlementMapper;
    @Resource
    private NotifyHeadDtoMapper notifyHeadDtoMapper;
    @Resource
    private NotifyListMapper notifyListMapper;
    @Override
    public Mapper<NotifyHead> getMapper() {
        return notifyHeadMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param notifyHeadParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<NotifyHeadDto>> getListPaged(NotifyHeadParam notifyHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        NotifyHead notifyHead = notifyHeadDtoMapper.toPo(notifyHeadParam);
        notifyHead.setTradeCode(userInfo.getCompany());
        Page<NotifyHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> notifyHeadMapper.getList(notifyHead));
        List<NotifyHeadDto> notifyHeadDtos = page.getResult().stream().map(head -> {
            NotifyHeadDto dto = notifyHeadDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<NotifyHeadDto>> paged = ResultObject.createInstance(notifyHeadDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param notifyHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public NotifyHeadDto insert(NotifyHeadParam notifyHeadParam, UserInfoToken userInfo) {
        NotifyHead notifyHead = notifyHeadDtoMapper.toPo(notifyHeadParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        notifyHead.setSid(sid);
        notifyHead.setInsertUser(userInfo.getUserNo());
        notifyHead.setInsertTime(new Date());
        notifyHead.setTradeCode(userInfo.getCompany());
        // 新增数据
        int insertStatus = notifyHeadMapper.insert(notifyHead);
        return  insertStatus > 0 ? notifyHeadDtoMapper.toDto(notifyHead) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param notifyHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public NotifyHeadDto update(NotifyHeadParam notifyHeadParam, UserInfoToken userInfo) {
        NotifyHead notifyHead = notifyHeadMapper.selectByPrimaryKey(notifyHeadParam.getSid());

        String contractNo = notifyHead.getContractNo();
        String orderNumber = notifyHead.getOrderNumber();


        notifyHeadDtoMapper.updatePo(notifyHeadParam, notifyHead);
        notifyHead.setUpdateUser(userInfo.getUserNo());
        notifyHead.setUpdateTime(new Date());
        notifyHead.setTradeCode(userInfo.getCompany());
        notifyHead.setUpdateUserName(userInfo.getUserName());
        notifyHead.setContractNo(contractNo);
        notifyHead.setOrderNumber(orderNumber);
        // 更新数据
        int update = notifyHeadMapper.updateByPrimaryKey(notifyHead);
        //保存后更新表体
        List<NotifyList> select = notifyListMapper.select(new NotifyList() {{
            setHeadId(notifyHead.getSid());
        }});
        for (NotifyList notifyList : select) {
            notifyList.setPayAmtRmb(notifyList.getPayAmt().multiply(notifyHead.getRate()));
            notifyListMapper.updateByPrimaryKey(notifyList);
        }
        return update > 0 ? notifyHeadDtoMapper.toDto(notifyHead) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		notifyHeadMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<NotifyHeadDto> selectAll(NotifyHeadParam exportParam, UserInfoToken userInfo) {
        NotifyHead notifyHead = notifyHeadDtoMapper.toPo(exportParam);
        notifyHead.setTradeCode(userInfo.getCompany());
        List<NotifyHeadDto> notifyHeadDtos = new ArrayList<>();
        List<NotifyHead> notifyHeads = notifyHeadMapper.getList(notifyHead);
        if (CollectionUtils.isNotEmpty(notifyHeads)) {
            notifyHeadDtos = notifyHeads.stream().map(head -> {
                NotifyHeadDto dto = notifyHeadDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return notifyHeadDtos;
    }

    public String getDocNo(UserInfoToken userInfo) {
        // 1. 获取当前年月（格式：yyyyMM）
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        String currentYm = sdf.format(new Date());  // 示例输出：202310（2023年10月）

        // 2. 从数据库查询当前最大流水号（最后三位）
        String code = notifyHeadMapper.getDocNo(userInfo.getCompany());

        // 3. 处理流水号逻辑
        if (StringUtils.isBlank(code)) {
            code = "001";  // 如果无记录，从001开始
        } else {
            int nextNumber = Integer.parseInt(code) + 1;
            if (nextNumber > 999) {
                nextNumber = 1;  // 超过999则重置为1（如：999 → 001）
            }
            code = String.format("%03d", nextNumber);  // 格式化为3位，不足补零
        }

        // 4. 拼接完整 doc_no
        return "JYFKTZ" + currentYm + code;  // 示例输出：JYFKTZ202310001
    }


    @Transactional(rollbackFor = Exception.class)
    public ResultObject confirmDataStatus(NotifyHeadParam notifyHeadParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("确认成功"));
        NotifyHead notifyHead = notifyHeadMapper.selectByPrimaryKey(notifyHeadParam.getSid());
        if (notifyHead == null) {
            throw new ErrorException(400, "付款通知表头数据不存在，请刷新");
        }
        if ("1".equals(notifyHead.getDocStatus())){
            throw new ErrorException(400, "该数据已经确认，无需重复操作");
        }
        notifyHead.setDocStatus(CommonEnum.OrderStatusEnum.CONFIRMED.getValue());
        notifyHead.setUpdateUser(userInfo.getUserNo());
        notifyHead.setUpdateTime(new Date());
        notifyHead.setUpdateUserName(userInfo.getUserName());
        notifyHead.setTradeCode(userInfo.getCompany());
        notifyHead.setCfmTime(new Date());


        //流转货款结算
        String errorMsg = bizPaymentSettlementMapper.checkTransferData(notifyHead.getSid(),userInfo);
        if (StringUtils.isNotBlank(errorMsg)){
            throw new RuntimeException(errorMsg);
        }
        bizPaymentSettlementMapper.transferToPaymentSettlement(notifyHead.getSid(),userInfo);



        //todo 发送用友


        notifyHeadMapper.updateByPrimaryKey(notifyHead);
        return result;
    }

    public ResultObject cancelDataStatus(NotifyHeadParam notifyHeadParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("作废成功"));
        NotifyHead notifyHead = notifyHeadMapper.selectByPrimaryKey(notifyHeadParam.getSid());
        if (notifyHead == null) {
            throw new ErrorException(400, "付款通知表头数据不存在，请刷新");
        }
        //校验合同号是否在下游数据使用
        if (bizPaymentSettlementMapper.checkContractIsUsed(notifyHead.getContractNo()) > 0){
            throw new ErrorException(400, "货款结算数据存在此合同号,不允许作废");
        }
        notifyHead.setDocStatus(CommonEnum.OrderStatusEnum.CANCELLED.getValue());
        notifyHead.setUpdateUser(userInfo.getUserNo());
        notifyHead.setUpdateTime(new Date());
        notifyHead.setUpdateUserName(userInfo.getUserName());
        notifyHead.setTradeCode(userInfo.getCompany());
        notifyHead.setCfmTime(null);
        notifyHeadMapper.updateByPrimaryKey(notifyHead);

        return result;
    }

    public ResultObject backDataStatus(NotifyHeadParam notifyHeadParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("退单成功"));
        NotifyHead notifyHead = notifyHeadMapper.selectByPrimaryKey(notifyHeadParam.getSid());
        if (notifyHead == null) {
            throw new ErrorException(400, "付款通知表头数据不存在，请刷新");
        }
        notifyHead.setDocStatus(CommonEnum.OrderStatusEnum.DRAFT.getValue());
        notifyHead.setUpdateUser(userInfo.getUserNo());
        notifyHead.setUpdateTime(new Date());
        notifyHead.setUpdateUserName(userInfo.getUserName());
        notifyHead.setTradeCode(userInfo.getCompany());
        notifyHead.setCfmTime(null);
        notifyHeadMapper.updateByPrimaryKey(notifyHead);
        return result;
    }

    public ResultObject copy(NotifyHeadParam notifyHeadParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("复制成功"));
        NotifyHead notifyHead = notifyHeadMapper.selectByPrimaryKey(notifyHeadParam.getSid());
        if (notifyHead == null) {
            throw new ErrorException(400, "付款通知表头数据不存在，请刷新");
        }

        //表头
        NotifyHead newNotifyHead = new NotifyHead();
        BeanUtils.copyProperties(notifyHead, newNotifyHead);  // 复制所有属性
        String sid = UUID.randomUUID().toString();
        newNotifyHead.setDocStatus(CommonEnum.OrderStatusEnum.DRAFT.getValue());
        newNotifyHead.setDocNo(getDocNo(userInfo));
        newNotifyHead.setSid(sid);
        newNotifyHead.setInsertTime(new Date());
        newNotifyHead.setBizDate(new Date());
        newNotifyHead.setUpdateUser(userInfo.getUserNo());
        newNotifyHead.setUpdateTime(new Date());
        newNotifyHead.setUpdateUserName(userInfo.getUserName());
        newNotifyHead.setInsertUser(userInfo.getUserNo());
        newNotifyHead.setTradeCode(userInfo.getCompany());
        newNotifyHead.setCfmTime(null);
        notifyHeadMapper.insert(newNotifyHead);


        //表体
        List<NotifyList>  notifyLists =  notifyListMapper.selectByHeadId(notifyHeadParam.getSid());
        List<NotifyList> insertNotifyList = new ArrayList<>();
        for (NotifyList notifyList : notifyLists) {
            NotifyList newNotifyList = new NotifyList();
            BeanUtils.copyProperties(notifyList, newNotifyList);  // 复制所有属性
            String sid2 = UUID.randomUUID().toString();
            newNotifyList.setSid(sid2);
            newNotifyList.setHeadId(sid);
            newNotifyList.setInsertTime(new Date());
            newNotifyList.setInsertUser(userInfo.getUserNo());
            insertNotifyList.add(newNotifyList);
        }
        if (CollectionUtils.isNotEmpty(insertNotifyList)) {
            BulkSqlOpt.batchInsertForErp(insertNotifyList, NotifyListMapper.class);
        }



        return result;
    }
}
