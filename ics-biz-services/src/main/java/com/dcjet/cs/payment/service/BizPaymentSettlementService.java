package com.dcjet.cs.payment.service;
import com.dcjet.cs.importedCigarettes.model.BizIPlan;
import com.dcjet.cs.payment.model.NotifyHead;
import com.dcjet.cs.util.CommonEnum;
import com.xdo.common.exception.ErrorException;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.payment.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.payment.dao.BizPaymentSettlementMapper;
import com.dcjet.cs.payment.mapper.BizPaymentSettlementDtoMapper;
import com.dcjet.cs.payment.model.BizPaymentSettlement;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.util.*;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-3-26
 */
@Service
public class BizPaymentSettlementService extends BaseService<BizPaymentSettlement> {
    @Resource
    private BizPaymentSettlementMapper bizPaymentSettlementMapper;
    @Resource
    private BizPaymentSettlementDtoMapper bizPaymentSettlementDtoMapper;
    @Override
    public Mapper<BizPaymentSettlement> getMapper() {
        return bizPaymentSettlementMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizPaymentSettlementParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizPaymentSettlementDto>> getListPaged(BizPaymentSettlementParam bizPaymentSettlementParam, PageParam pageParam) {
        // 启用分页查询
        BizPaymentSettlement bizPaymentSettlement = bizPaymentSettlementDtoMapper.toPo(bizPaymentSettlementParam);
        Page<BizPaymentSettlement> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizPaymentSettlementMapper.getList(bizPaymentSettlement));
        List<BizPaymentSettlementDto> bizPaymentSettlementDtos = page.getResult().stream().map(head -> {
            BizPaymentSettlementDto dto = bizPaymentSettlementDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizPaymentSettlementDto>> paged = ResultObject.createInstance(bizPaymentSettlementDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizPaymentSettlementParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizPaymentSettlementDto insert(BizPaymentSettlementParam bizPaymentSettlementParam, UserInfoToken userInfo) {
        BizPaymentSettlement bizPaymentSettlement = bizPaymentSettlementDtoMapper.toPo(bizPaymentSettlementParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizPaymentSettlement.setSid(sid);
        bizPaymentSettlement.setInsertUser(userInfo.getUserNo());
        bizPaymentSettlement.setInsertTime(new Date());
        bizPaymentSettlement.setTradeCode(userInfo.getCompany());
        // 新增数据
        int insertStatus = bizPaymentSettlementMapper.insert(bizPaymentSettlement);
        return  insertStatus > 0 ? bizPaymentSettlementDtoMapper.toDto(bizPaymentSettlement) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizPaymentSettlementParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizPaymentSettlementDto update(BizPaymentSettlementParam bizPaymentSettlementParam, UserInfoToken userInfo) {
        BizPaymentSettlement bizPaymentSettlement = bizPaymentSettlementMapper.selectByPrimaryKey(bizPaymentSettlementParam.getSid());
        bizPaymentSettlementDtoMapper.updatePo(bizPaymentSettlementParam, bizPaymentSettlement);
        bizPaymentSettlement.setUpdateUser(userInfo.getUserNo());
        bizPaymentSettlement.setUpdateUserName(userInfo.getUserName());
        bizPaymentSettlement.setTradeCode(userInfo.getCompany());
        bizPaymentSettlement.setUpdateTime(new Date());
        // 更新数据
        int update = bizPaymentSettlementMapper.updateByPrimaryKey(bizPaymentSettlement);
        return update > 0 ? bizPaymentSettlementDtoMapper.toDto(bizPaymentSettlement) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		bizPaymentSettlementMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizPaymentSettlementDto> selectAll(BizPaymentSettlementParam exportParam, UserInfoToken userInfo) {
        BizPaymentSettlement bizPaymentSettlement = bizPaymentSettlementDtoMapper.toPo(exportParam);
        // bizPaymentSettlement.setTradeCode(userInfo.getCompany());
        List<BizPaymentSettlementDto> bizPaymentSettlementDtos = new ArrayList<>();
        List<BizPaymentSettlement> bizPaymentSettlements = bizPaymentSettlementMapper.getList(bizPaymentSettlement);
        if (CollectionUtils.isNotEmpty(bizPaymentSettlements)) {
            bizPaymentSettlementDtos = bizPaymentSettlements.stream().map(head -> {
                BizPaymentSettlementDto dto = bizPaymentSettlementDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizPaymentSettlementDtos;
    }

    public ResultObject confirmStatus(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("确认成功"));

        BizPaymentSettlement bizPaymentSettlement = bizPaymentSettlementMapper.selectByPrimaryKey(sid);
        if (bizPaymentSettlement == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }

        // 更新状态为1 确认
        bizPaymentSettlement.setStatus(CommonEnum.OrderStatusEnum.CONFIRMED.getValue());
        bizPaymentSettlement.setConfirmTime(new Date());
        updateStatus(bizPaymentSettlement); // 调用更新状态的方法
        resultObject.setData(bizPaymentSettlement);

        return resultObject;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(BizPaymentSettlement bizPaymentSettlement) {
        bizPaymentSettlementMapper.updateStatus(bizPaymentSettlement); // 调用更新状态的方法
    }

    public ResultObject invalidate(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("作废成功"));

        BizPaymentSettlement bizPaymentSettlement = bizPaymentSettlementMapper.selectByPrimaryKey(sid);
        if (bizPaymentSettlement == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }

        // 更新状态为2（作废）
        bizPaymentSettlement.setStatus(CommonEnum.OrderStatusEnum.CANCELLED.getValue());
        bizPaymentSettlement.setConfirmTime(null);
        updateStatus(bizPaymentSettlement); // 调用更新状态的方法

        return resultObject;
    }


    public ResultObject backDataStatus(BizPaymentSettlementParam bizPaymentSettlementParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("退单成功"));
        BizPaymentSettlement bizPaymentSettlement = bizPaymentSettlementMapper.selectByPrimaryKey(bizPaymentSettlementParam.getSid());
        if (bizPaymentSettlement == null) {
            throw new ErrorException(400, "货款结算表头数据不存在，请刷新");
        }
        bizPaymentSettlement.setStatus(CommonEnum.OrderStatusEnum.DRAFT.getValue());
        bizPaymentSettlement.setConfirmTime(null);
        bizPaymentSettlementMapper.updateByPrimaryKey(bizPaymentSettlement);
        return result;
    }

}
