package com.dcjet.cs.payment.model;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2025-3-26
 */
@Setter
@Getter
@Table(name = "t_biz_payment_settlement")
public class BizPaymentSettlement implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 
     */
	@Id
	@Column(name = "sid")
	private  String sid;
	/**
     * 上游SID
     */
	@Column(name = "head_id")
	private  String headId;
	/**
     * 企业编码
     */
	@Column(name = "trade_code")
	private  String tradeCode;
	/**
     * 业务类型
     */
	@Column(name = "business_type")
	private  String businessType;
	/**
     * 进货单号
     */
	@Column(name = "purchase_order_no")
	private  String purchaseOrderNo;
	/**
     * 合同号
     */
	@Column(name = "contract_no")
	private  String contractNo;
	/**
     * 客户
     */
	@Column(name = "customer")
	private  String customer;
	/**
     * 付款单位
     */
	@Column(name = "payer")
	private  String payer;
	/**
     * 外商发票号
     */
	@Column(name = "foreign_invoice_no")
	private  String foreignInvoiceNo;
	/**
     * 币种
     */
	@Column(name = "curr")
	private  String curr;
	/**
     * 汇率
     */
	@Column(name = "exchange_rate")
	private  BigDecimal exchangeRate;
	/**
     * 原货币价
     */
	@Column(name = "original_amount")
	private  BigDecimal originalAmount;
	/**
     * 人民币金额
     */
	@Column(name = "rmb_amount")
	private  BigDecimal rmbAmount;
	/**
     * 口岸调拨总价
     */
	@Column(name = "port_transfer_amount")
	private  BigDecimal portTransferAmount;
	/**
     * 代理费(不含税金额)
     */
	@Column(name = "agency_fee")
	private  BigDecimal agencyFee;
	/**
     * 代理费税额
     */
	@Column(name = "agency_fee_tax")
	private  BigDecimal agencyFeeTax;
	/**
     * 合计值
     */
	@Column(name = "total_amount")
	private  BigDecimal totalAmount;
	/**
     * 代理费率
     */
	@Column(name = "agency_fee_rate")
	private  BigDecimal agencyFeeRate;
	/**
     * 代理费(价税合计)
     */
	@Column(name = "agency_fee_total")
	private  BigDecimal agencyFeeTotal;
	/**
     * 商品类别
     */
	@Column(name = "product_category")
	private  String productCategory;
	/**
     * 数量
     */
	@Column(name = "qty")
	private  BigDecimal qty;
	/**
     * 发送用友
     */
	@Column(name = "send_to_uf")
	private  String sendToUf;
	/**
     * 备注
     */
	@Column(name = "remark")
	private  String remark;
	/**
     * 单据状态
     */
	@Column(name = "status")
	private  String status;
	/**
     * 确认时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "confirm_time")
	private  Date confirmTime;
	/**
     * 创建人
     */
	@Column(name = "insert_user")
	private  String insertUser;
	/**
     * 创建人名称
     */
	@Column(name = "insert_user_name")
	private  String insertUserName;
	/**
     * 创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "insert_time")
	private  Date insertTime;
	/**
     * 创建时间-开始
     */
	@Transient
	private String insertTimeFrom;
	/**
     * 创建时间-结束
     */
	@Transient
    private String insertTimeTo;
	/**
     * 更新人
     */
	@Column(name = "update_user")
	private  String updateUser;
	/**
     * 修改人姓名
     */
	@Column(name = "update_user_name")
	private  String updateUserName;
	/**
     * 更新时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "update_time")
	private  Date updateTime;
	/**
     * 
     */
	@Column(name = "extend1")
	private  String extend1;
	/**
     * 
     */
	@Column(name = "extend2")
	private  String extend2;
	/**
     * 
     */
	@Column(name = "extend3")
	private  String extend3;
	/**
     * 
     */
	@Column(name = "extend4")
	private  String extend4;
	/**
     * 
     */
	@Column(name = "extend5")
	private  String extend5;
	/**
     * 
     */
	@Column(name = "extend6")
	private  String extend6;
	/**
     * 
     */
	@Column(name = "extend7")
	private  String extend7;
	/**
     * 
     */
	@Column(name = "extend8")
	private  String extend8;
	/**
     * 
     */
	@Column(name = "extend9")
	private  String extend9;
	/**
     * 
     */
	@Column(name = "extend10")
	private  String extend10;

	@Transient
	private String createrUser;

	@Transient
	private String createrUserName;

	@Transient
	private Date createrTime;

	@Column(name = "business_date")
	private  Date businessDate;
}
