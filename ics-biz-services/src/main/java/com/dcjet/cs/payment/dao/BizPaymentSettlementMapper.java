package com.dcjet.cs.payment.dao;
import com.dcjet.cs.importedCigarettes.model.BizIPlan;
import com.dcjet.cs.payment.model.BizPaymentSettlement;
import com.xdo.common.token.UserInfoToken;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
/**
* generated by Generate 神码
* BizPaymentSettlement
* <AUTHOR>
* @date: 2025-3-26
*/
public interface BizPaymentSettlementMapper extends Mapper<BizPaymentSettlement> {
    /**
     * 查询获取数据
     * @param bizPaymentSettlement
     * @return
     */
    List<BizPaymentSettlement> getList(BizPaymentSettlement bizPaymentSettlement);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    void transferToPaymentSettlement(@Param("sid") String sid,@Param("userInfo") UserInfoToken userInfo);

    int updateStatus(BizPaymentSettlement bizIPlan);

    String checkTransferData(@Param("sid") String sid,@Param("userInfo") UserInfoToken userInfo);

    int checkContractIsUsed(@Param("contractNo") String contractNo);
}
