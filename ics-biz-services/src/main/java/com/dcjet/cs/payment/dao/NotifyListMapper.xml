<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.payment.dao.NotifyListMapper">
    <resultMap id="bizMerchantResultMap" type="com.dcjet.cs.payment.model.NotifyList">
        <id column="SID" property="sid" jdbcType="VARCHAR"/>

		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
		<result column="head_id" property="headId" jdbcType="VARCHAR" />

		<result column="insert_user" property="insertUser" jdbcType="VARCHAR" />
		<result column="insert_time" property="insertTime" jdbcType="TIMESTAMP" />
		<result column="update_user" property="updateUser" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR" />
		<result column="update_user_name" property="updateUserName" jdbcType="VARCHAR" />
		<result column="extend1" property="extend1" jdbcType="VARCHAR" />
		<result column="extend2" property="extend2" jdbcType="VARCHAR" />
		<result column="extend3" property="extend3" jdbcType="VARCHAR" />
		<result column="extend4" property="extend4" jdbcType="VARCHAR" />
		<result column="extend5" property="extend5" jdbcType="VARCHAR" />
		<result column="extend6" property="extend6" jdbcType="VARCHAR" />
		<result column="extend7" property="extend7" jdbcType="VARCHAR" />
		<result column="extend8" property="extend8" jdbcType="VARCHAR" />
		<result column="extend9" property="extend9" jdbcType="VARCHAR" />
		<result column="extend10" property="extend10" jdbcType="VARCHAR" />
        <result column="CONTRACT_NO" property="contractNo" jdbcType="VARCHAR"/>
        <result column="ORDER_NUMBER" property="orderNumber" jdbcType="VARCHAR"/>
        <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR"/>
        <result column="GOODS_NAME" property="goodsName" jdbcType="VARCHAR"/>
        <result column="INVOICE_NUMBER" property="invoiceNumber" jdbcType="VARCHAR"/>
        <result column="QTY" property="qty" jdbcType="NUMERIC"/>
        <result column="UNIT" property="unit" jdbcType="VARCHAR"/>
        <result column="PAY_AMT" property="payAmt" jdbcType="NUMERIC"/>
        <result column="PAY_AMT_RMB" property="payAmtRmb" jdbcType="NUMERIC"/>
	</resultMap>
	<sql id="Base_Column_List" >
     sid
     ,trade_code

     ,insert_user
     ,insert_time
     ,update_user
     ,update_time
     ,insert_user_name
     ,update_user_name
     ,extend1
     ,extend2
     ,extend3
     ,extend4
     ,extend5
     ,extend6
     ,extend7
     ,extend8
     ,extend9
     ,extend10
     ,CONTRACT_NO
     ,ORDER_NUMBER
     ,ORDER_NO
     ,GOODS_NAME
     ,INVOICE_NUMBER
     ,QTY
     ,UNIT
     ,PAY_AMT
     ,PAY_AMT_RMB
     ,head_id

    </sql>
    <sql id="condition">
         and trade_code = #{tradeCode}
        <if test="headId != null and headId != ''"> and head_id = #{headId} </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="bizMerchantResultMap" parameterType="com.dcjet.cs.payment.model.NotifyList">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_BIZ_PAYMENT_NOTIFY_LIST t
        <where>
            <include refid="condition"></include>
        </where>
        order by COALESCE(update_time, insert_time) DESC
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_BIZ_PAYMENT_NOTIFY_LIST l
        where l.ORDER_NO in (select l.ORDER_NO
        from T_BIZ_PAYMENT_NOTIFY_LIST l
        where l.sid in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>)
        or l.ORDER_NUMBER in (select l.ORDER_NUMBER
        from T_BIZ_PAYMENT_NOTIFY_LIST l
        where l.sid in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>)
    </delete>

    <select id="selectByHeadId" resultMap="bizMerchantResultMap">
        select * from T_BIZ_PAYMENT_NOTIFY_LIST where head_id = #{sid}
    </select>
</mapper>
