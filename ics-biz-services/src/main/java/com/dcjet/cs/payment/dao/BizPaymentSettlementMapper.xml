<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.payment.dao.BizPaymentSettlementMapper">
    <resultMap id="bizPaymentSettlementResultMap" type="com.dcjet.cs.payment.model.BizPaymentSettlement">
		<result column="sid" property="sid" jdbcType="VARCHAR" />
		<result column="head_id" property="headId" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
		<result column="business_type" property="businessType" jdbcType="VARCHAR" />
		<result column="purchase_order_no" property="purchaseOrderNo" jdbcType="VARCHAR" />
		<result column="contract_no" property="contractNo" jdbcType="VARCHAR" />
		<result column="customer" property="customer" jdbcType="VARCHAR" />
		<result column="payer" property="payer" jdbcType="VARCHAR" />
		<result column="foreign_invoice_no" property="foreignInvoiceNo" jdbcType="VARCHAR" />
		<result column="curr" property="curr" jdbcType="VARCHAR" />
		<result column="exchange_rate" property="exchangeRate" jdbcType="NUMERIC" />
		<result column="original_amount" property="originalAmount" jdbcType="NUMERIC" />
		<result column="rmb_amount" property="rmbAmount" jdbcType="NUMERIC" />
		<result column="port_transfer_amount" property="portTransferAmount" jdbcType="NUMERIC" />
		<result column="agency_fee" property="agencyFee" jdbcType="NUMERIC" />
		<result column="agency_fee_tax" property="agencyFeeTax" jdbcType="NUMERIC" />
		<result column="total_amount" property="totalAmount" jdbcType="NUMERIC" />
		<result column="agency_fee_rate" property="agencyFeeRate" jdbcType="NUMERIC" />
		<result column="agency_fee_total" property="agencyFeeTotal" jdbcType="NUMERIC" />
		<result column="product_category" property="productCategory" jdbcType="VARCHAR" />
		<result column="qty" property="qty" jdbcType="NUMERIC" />
		<result column="send_to_uf" property="sendToUf" jdbcType="VARCHAR" />
		<result column="remark" property="remark" jdbcType="VARCHAR" />
		<result column="status" property="status" jdbcType="VARCHAR" />
		<result column="confirm_time" property="confirmTime" jdbcType="TIMESTAMP" />
		<result column="insert_user" property="insertUser" jdbcType="VARCHAR" />
		<result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR" />
		<result column="insert_time" property="insertTime" jdbcType="TIMESTAMP" />
		<result column="update_user" property="updateUser" jdbcType="VARCHAR" />
		<result column="update_user_name" property="updateUserName" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="extend1" property="extend1" jdbcType="VARCHAR" />
		<result column="extend2" property="extend2" jdbcType="VARCHAR" />
		<result column="extend3" property="extend3" jdbcType="VARCHAR" />
		<result column="extend4" property="extend4" jdbcType="VARCHAR" />
		<result column="extend5" property="extend5" jdbcType="VARCHAR" />
		<result column="extend6" property="extend6" jdbcType="VARCHAR" />
		<result column="extend7" property="extend7" jdbcType="VARCHAR" />
		<result column="extend8" property="extend8" jdbcType="VARCHAR" />
		<result column="extend9" property="extend9" jdbcType="VARCHAR" />
		<result column="extend10" property="extend10" jdbcType="VARCHAR" />
		<result column="business_date" property="businessDate" jdbcType="TIMESTAMP" />
	</resultMap>
	<sql id="Base_Column_List" >
     sid
     ,head_id
     ,trade_code
     ,business_type
     ,purchase_order_no
     ,contract_no
     ,customer
     ,payer
     ,foreign_invoice_no
     ,curr
     ,exchange_rate
     ,original_amount
     ,rmb_amount
     ,port_transfer_amount
     ,agency_fee
     ,agency_fee_tax
     ,total_amount
     ,agency_fee_rate
     ,agency_fee_total
     ,product_category
     ,qty
     ,send_to_uf
     ,remark
     ,status
     ,confirm_time
     ,insert_user
     ,insert_user_name
     ,insert_time
     ,update_user
     ,update_user_name
     ,update_time
     ,extend1
     ,extend2
     ,extend3
     ,extend4
     ,extend5
     ,extend6
     ,extend7
     ,extend8
     ,extend9
     ,extend10
     ,COALESCE(UPDATE_USER,INSERT_USER) as createrUser
     ,COALESCE(UPDATE_USER_NAME,INSERT_USER_NAME) as createrUserName
     ,COALESCE(UPDATE_TIME,INSERT_TIME) as createrTime
     ,business_date
    </sql>
    <sql id="condition">
    <if test="businessType != null and businessType != ''">
		and business_type = #{businessType}
	</if>
    <if test="purchaseOrderNo != null and purchaseOrderNo != ''">
	  and purchase_order_no like '%'|| #{purchaseOrderNo} || '%'
	</if>
    <if test="contractNo != null and contractNo != ''">
	  and contract_no like '%'|| #{contractNo} || '%'
	</if>
    <if test="status != null and status != ''">
        and STATUS = #{status}
    </if>
    <if test="status == null or status == ''">
        and STATUS !='2'
    </if>
    <if test="insertUser != null and insertUser != ''">
	  and insert_user like '%'|| #{insertUser} || '%'
	</if>
    <if test="insertUserName != null and insertUserName != ''">
        and (insert_user_name like '%'|| #{insertUserName} || '%' or update_user_name like '%'|| #{insertUserName} || '%')
    </if>
        <if test="insertTimeFrom != null and insertTimeFrom != ''">
            <![CDATA[ and coalesce(update_time,insert_time) >= to_date(#{insertTimeFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="insertTimeTo != null and insertTimeTo != ''">
            <![CDATA[ and coalesce(update_time,insert_time) <= DATEADD(DAY, 1, to_date(#{insertTimeTo}, 'yyyy-mm-dd hh24:mi:ss')) ]]>
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="bizPaymentSettlementResultMap" parameterType="com.dcjet.cs.payment.model.BizPaymentSettlement">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        t_biz_payment_settlement t
        <where>
            <include refid="condition"></include>
        </where>
        order by COALESCE(UPDATE_TIME,INSERT_TIME) desc
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from t_biz_payment_settlement t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>

    <select id="transferToPaymentSettlement">
        insert into t_biz_payment_settlement(BUSINESS_TYPE, PURCHASE_ORDER_NO, CONTRACT_NO, CUSTOMER, PAYER,
        FOREIGN_INVOICE_NO, CURR, EXCHANGE_RATE, ORIGINAL_AMOUNT, RMB_AMOUNT,
        PORT_TRANSFER_AMOUNT, AGENCY_FEE, AGENCY_FEE_TAX, TOTAL_AMOUNT, AGENCY_FEE_RATE,
        AGENCY_FEE_TOTAL, PRODUCT_CATEGORY, QTY, SEND_TO_UF, STATUS, INSERT_USER,
        INSERT_TIME, SID, TRADE_CODE,INSERT_USER_NAME,HEAD_ID,BUSINESS_DATE)
        select '1',
        l.ORDER_NUMBER,
        LISTAGG(distinct l.CONTRACT_NO,','),
        max(h.PAYEE),
        '中国烟草上海进出口有限责任公司',
        max(tmp.INVOICE_NO),
        max(h.CURR),
        max(h.rate),
        sum(l.PAY_AMT),
        sum(l.PAY_AMT) * max(h.rate),
        sum(l.QTY * coalesce(mat.INCLUDING_TAX,0)),
        sum(l.QTY * coalesce(mat.INCLUDING_TAX,0) * 4),
        sum(l.QTY * coalesce(mat.INCLUDING_TAX,0) * 4 * 0.06),
        sum(l.PAY_AMT_RMB) + sum(l.QTY * coalesce(mat.INCLUDING_TAX,0) * 4) + sum(l.QTY * coalesce(mat.INCLUDING_TAX,0) * 4 * 0.06),
        4,
        sum(l.QTY * coalesce(mat.INCLUDING_TAX,0) * 4) + sum(l.QTY * coalesce(mat.INCLUDING_TAX,0) * 4 * 0.06),
        max(mat.MERCHANDISE_CATEGORIES),
        sum(l.QTY),
        '0',
        '0',
        #{userInfo.userNo},
        now(),
        sys_guid(),
        #{userInfo.company},
        #{userInfo.userName},
        #{sid},
        now()
        from T_BIZ_PAYMENT_NOTIFY_HEAD h
        left join T_BIZ_PAYMENT_NOTIFY_LIST l on h.sid = l.HEAD_ID
        left join (select ph.PURCHASE_ORDER_NO,
        ph.trade_code,
        coalesce(LISTAGG(distinct pl.INVOICE_NO,','), '') as INVOICE_NO
        from T_BIZ_I_PURCHASE_HEAD ph
        left join T_BIZ_I_PURCHASE_LIST pl
        on pl.HEAD_ID = ph.SID
        where ph.data_status != '2'
        group by ph.PURCHASE_ORDER_NO, ph.trade_code) tmp
        on l.ORDER_NUMBER = tmp.PURCHASE_ORDER_NO and tmp.TRADE_CODE = l.TRADE_CODE
        left join T_BIZ_MATERIAL_INFORMATION mat on mat.G_NAME = l.GOODS_NAME and mat.TRADE_CODE = l.TRADE_CODE
        where h.sid = #{sid}
        and l.ORDER_NUMBER is not null
        group by l.ORDER_NUMBER
    </select>


    <update id="updateStatus">
        UPDATE t_biz_payment_settlement
        SET status = #{status}
        <if test="confirmTime !=null">
            ,confirm_time =#{confirmTime}
        </if>
        WHERE sid = #{sid}
    </update>

    <select id="checkTransferData" resultType="java.lang.String">
        SELECT max(concat(
        CASE
        WHEN LISTAGG(distinct l.CONTRACT_NO,',') is null then '表体合同号为空|'
        END,
        CASE
        WHEN max(h.PAYEE) is null then '客户为空|'
        END,
        CASE
        WHEN LISTAGG(distinct pl.INVOICE_NO,',') is null then '外商发票号为空|'
        END,
        CASE
        WHEN max(h.CURR) is null then '币种为空|'
        END,
        CASE
        WHEN max(h.rate) is null then '汇率为空|'
        END,
        CASE
        WHEN sum(l.PAY_AMT) is null then '原货币价为空|'
        END,
        CASE
        WHEN sum(l.PAY_AMT_RMB) is null then '人民币金额为空|'
        END,
        CASE
        WHEN sum(mat.INCLUDING_TAX) is null then '物料对应含税单价为空|'
        END,
        CASE
        WHEN max(mat.MERCHANDISE_CATEGORIES) is null then '商品类别为空|'
        END,
        CASE
        WHEN sum(l.QTY) is null then '数量为空|'
        END
        )) as errorMsg
        FROM T_BIZ_PAYMENT_NOTIFY_HEAD h
        LEFT JOIN T_BIZ_PAYMENT_NOTIFY_LIST l
        ON h.sid = l.HEAD_ID
        LEFT JOIN T_BIZ_I_PURCHASE_HEAD ph ON ph.PURCHASE_ORDER_NO = l.ORDER_NUMBER
        LEFT JOIN T_BIZ_I_PURCHASE_LIST pl ON pl.HEAD_ID = ph.SID
        LEFT JOIN T_BIZ_MATERIAL_INFORMATION mat ON mat.G_NAME = l.GOODS_NAME
        WHERE h.sid = #{sid}
        AND l.ORDER_NUMBER IS NOT NULL
        GROUP BY l.ORDER_NUMBER
    </select>

    <select id="checkContractIsUsed" resultType="int">
        select count(1) from  t_biz_payment_settlement h
        where h.status != '2'
        and h.contract_no = #{contractNo}
    </select>
</mapper>
