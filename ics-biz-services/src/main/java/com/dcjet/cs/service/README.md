# 第三方数据库服务

本服务提供了向第三方数据库插入数据的功能，支持达梦数据库和Oracle数据库。

## 配置说明

数据库连接信息可以在`application.properties`文件中配置：

### 达梦数据库配置

```properties
# 第三方数据库配置
# 数据库类型，支持DM和ORACLE
thirdparty.db.type=DM
# 达梦数据库配置
thirdparty.db.host=**************
thirdparty.db.port=5247
thirdparty.db.username=SYSDBA
thirdparty.db.password=Dces@888
thirdparty.db.database=
```

### Oracle数据库配置

```properties
# 第三方数据库配置
# 数据库类型，支持DM和ORACLE
thirdparty.db.type=ORACLE
# Oracle数据库配置
thirdparty.db.host=**************
thirdparty.db.port=1521
thirdparty.db.username=system
thirdparty.db.password=oracle
thirdparty.db.database=ORCL
```

如果没有在配置文件中指定，将使用默认值。

## API使用说明

### 1. 使用Map方式插入数据

```json
POST /v1/thirdparty/db/insert
{
  "tableName": "YOUR_TABLE_NAME",
  "data": [
    {
      "COLUMN1": "value1",
      "COLUMN2": 123,
      "COLUMN3": "2023-05-01"
    },
    {
      "COLUMN1": "value2",
      "COLUMN2": 456,
      "COLUMN3": "2023-05-02"
    }
  ]
}
```

### 2. 使用泛型对象方式插入数据

```json
POST /v1/thirdparty/db/insert/generic
{
  "tableName": "YOUR_TABLE_NAME",
  "data": [
    {
      "property1": "value1",
      "property2": 123,
      "property3": "2023-05-01"
    },
    {
      "property1": "value2",
      "property2": 456,
      "property3": "2023-05-02"
    }
  ]
}
```

## 注意事项

1. 对于泛型对象，属性名会直接映射到数据库列名。如果对象属性名与数据库列名不一致，需要进行适当的转换。

2. 当使用泛型接口时，传入的对象应该有合适的getter方法，以便能够正确提取属性值。

3. 如果有复杂的嵌套对象或集合属性，可能需要进一步扩展功能。
