package com.dcjet.cs.service;

import com.dcjet.cs.util.ThirdPartyDbUtil;
import com.dcjet.cs.util.ThirdPartyDbUtilFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.persistence.Column;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 第三方数据库服务
 * 用于向第三方数据库插入数据
 */
@Slf4j
@Service
public class ThirdPartyDbService {

    @Autowired
    private ThirdPartyDbUtilFactory dbUtilFactory;

    private ThirdPartyDbUtil dbUtil;

    @Value("${thirdparty.db.type:ORACLE}")
    private String dbType;

    @Value("${thirdparty.db.host:************}")
    private String host;

    @Value("${thirdparty.db.port:1521}")
    private String port;

    @Value("${thirdparty.db.username:gwstd_xmdy}")
    private String username;

    @Value("${thirdparty.db.password:dcjet}")
    private String password;

    @Value("${thirdparty.db.database:ora8new}")
    private String database;

    /**
     * 初始化数据库连接
     */
    @PostConstruct
    public void init() {
        dbUtil = dbUtilFactory.getDbUtil(dbType);
        dbUtil.init(host, port, username, password, database);
        log.info("ThirdPartyDbService initialized with {} connection to {}:{}", dbType, host, port);
    }

    /**
     * 向指定表插入数据
     *
     * @param tableName 表名
     * @param data 要插入的数据
     * @return 影响的行数
     */
    public int insertData(String tableName, Map<String, Object> data) {
        log.info("Inserting data into table: {}", tableName);
        return dbUtil.insert(tableName, data);
    }

    /**
     * 批量向指定表插入数据
     *
     * @param tableName 表名
     * @param dataList 要插入的数据列表
     * @return 影响的行数
     */
    public int batchInsertData(String tableName, List<Map<String, Object>> dataList) {
        log.info("Batch inserting {} records into table: {}", dataList.size(), tableName);
        return dbUtil.batchInsert(tableName, dataList);
    }

    /**
     * 使用泛型对象向指定表插入数据
     *
     * @param tableName 表名
     * @param entity 要插入的实体对象
     * @param <T> 实体类型
     * @return 影响的行数
     */
    public <T> int insertEntity(String tableName, T entity) {
        log.info("Inserting entity into table: {}", tableName);
        return dbUtil.insertEntity(tableName, entity);
    }

    /**
     * 使用泛型对象批量向指定表插入数据
     *
     * @param tableName 表名
     * @param entities 要插入的实体对象列表
     * @param <T> 实体类型
     * @return 影响的行数
     */
    public <T> int batchInsertEntities(String tableName, List<T> entities) {
        log.info("Batch inserting {} entities into table: {}", entities.size(), tableName);
        return dbUtil.batchInsertEntities(tableName, entities);
    }

    /**
     * 执行自定义SQL
     *
     * @param sql SQL语句
     * @param params SQL参数
     * @return 影响的行数
     */
    public int executeUpdate(String sql, Object... params) {
        log.info("Executing SQL: {}", sql);
        return dbUtil.executeUpdate(sql, params);
    }

    /**
     * 获取下一个序列值
     *
     * @param sequenceName 序列名称
     * @param frequency    频次
     * @return 序列值
     */
    public long[] getSequenceNextValue(String sequenceName, int frequency) {
        log.info("Getting sequence {} nextValue at {} times", sequenceName, frequency);
        return dbUtil.getSequenceNextValue(sequenceName, frequency);
    }

    /**
     * 将任意对象转换为 Map<String, Object>，其中 key 是 @Column(name) 的值，value 是字段值
     *
     * @param obj 要转换的对象
     * @return Map<String, Object>，如果 obj 为 null 则返回 null
     */
    public Map<String, Object> convertToMap(Object obj) {
        if (obj == null) {
            return null;
        }

        Map<String, Object> resultMap = new HashMap<>();
        Class<?> clazz = obj.getClass();

        try {
            for (Field field : clazz.getDeclaredFields()) {
                processField(obj, field, resultMap);
            }
        } catch (IllegalAccessException e) {
            throw new RuntimeException("Failed to convert object to Map", e);
        }

        return resultMap;
    }

    private void processField(Object obj, Field field, Map<String, Object> resultMap)
            throws IllegalAccessException {
        Column columnAnnotation = field.getAnnotation(Column.class);
        if (columnAnnotation == null) {
            return; // 如果没有 @Column 注解，跳过
        }

        field.setAccessible(true); // 允许访问私有字段
        String columnName = columnAnnotation.name();
        Object value = field.get(obj);

        // 特殊处理 BigDecimal，避免精度丢失
        if (value instanceof BigDecimal) {
            value = value.toString(); // 转为字符串
        }

        resultMap.put(columnName, value);
    }

    public List<Map<String, Object>> convertListToMapList(List<?> list) throws IllegalAccessException {
        List<Map<String, Object>> result = new ArrayList<>();
        for (Object obj : list) {
            result.add(convertToMap(obj));
        }
        return result;
    }
}