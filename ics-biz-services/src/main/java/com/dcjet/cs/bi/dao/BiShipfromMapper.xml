<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.bi.dao.BiShipfromMapper">
    <resultMap id="BaseResultMap" type="com.dcjet.cs.bi.model.BiShipfrom">
        <id column="sid" property="sid" jdbcType="VARCHAR"/>
        <result column="ship_from_code" property="shipFromCode" jdbcType="VARCHAR"/>
        <result column="ship_from_name" property="shipFromName" jdbcType="VARCHAR"/>
        <result column="ship_from_address" property="shipFromAddress" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="insert_user" property="insertUser" jdbcType="VARCHAR"/>
        <result column="insert_time" property="insertTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="trade_code" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="head_id" property="headId" jdbcType="VARCHAR"/>
        <result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="update_user_name" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="supplier_code" property="supplierCode" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
            t.sid, 
            t.ship_from_code, 
            t.ship_from_name, 
            t.ship_from_address, 
            t.remark, 
            t.insert_user, 
            t.insert_time, 
            t.update_user, 
            t.update_time, 
            t.trade_code, 
            t.head_id, 
            t.insert_user_name, 
            t.update_user_name, 
            t.supplier_code
    </sql>

    <sql id="condition">
        t.trade_code = #{tradeCode}
        and
        t.head_id = #{headId}
        <if test="shipFromCode != null and shipFromCode != ''"> AND t.ship_from_code LIKE '%' || #{shipFromCode} || '%' </if>
        <if test="shipFromName != null and shipFromName != ''"> AND t.ship_from_name LIKE '%' || #{shipFromName} || '%' </if>
        <if test="shipFromAddress != null and shipFromAddress != ''"> AND t.ship_from_address LIKE '%' || #{shipFromAddress} || '%' </if>
        <if test="remark != null and remark != ''"> AND t.remark LIKE '%' || #{remark} || '%' </if>
        <if test="insertTimeFrom != null and insertTimeFrom != ''">
            <![CDATA[ and t.insert_time >= to_timestamp(#{insertTimeFrom},'yyyy-MM-dd hh24:mi:ss') ]]>
        </if>
        <if test="insertTimeTo != null and insertTimeTo != ''">
            <![CDATA[ and t.insert_time < to_timestamp(#{insertTimeTo},'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day' ]]>
        </if>
        <if test="updateTimeFrom != null and updateTimeFrom != ''">
            <![CDATA[ and t.update_time >= to_timestamp(#{updateTimeFrom},'yyyy-MM-dd hh24:mi:ss') ]]>
        </if>
        <if test="updateTimeTo != null and updateTimeTo != ''">
            <![CDATA[ and t.update_time < to_timestamp(#{updateTimeTo},'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day' ]]>
        </if>
        <if test="supplierCode != null and supplierCode != ''"> AND t.supplier_code LIKE '%' || #{supplierCode} || '%' </if>
    </sql>

    <select id="getList" resultMap="BaseResultMap" parameterType="com.dcjet.cs.bi.model.BiShipfrom">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            t_bi_shipfrom t
        <where>
            <include refid="condition"></include>
        </where>
    </select>


    <delete id="deleteBySids" parameterType="java.util.List">
        delete from  t_bi_shipfrom t where t.sid in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
</mapper>