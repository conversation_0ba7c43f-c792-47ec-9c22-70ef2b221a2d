package com.dcjet.cs.bi.mapper;

import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.dto.bi.BizMerchantDto;
import com.dcjet.cs.dto.bi.BizMerchantParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizMerchantDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizMerchantDto toDto(BizMerchant po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizMerchant toPo(BizMerchantParam param);
    /**
     * 数据库原始数据更新
     * @param bizMerchantParam
     * @param bizMerchant
     */
    void updatePo(BizMerchantParam bizMerchantParam, @MappingTarget BizMerchant bizMerchant);
    default void patchPo(BizMerchantParam bizMerchantParam, BizMerchant bizMerchant) {
        // TODO 自行实现局部更新
    }
}
