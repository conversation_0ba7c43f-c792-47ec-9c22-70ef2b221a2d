package com.dcjet.cs.bi.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;

@Setter
@Getter
@Table(name = "T_BIZ_EXPENSE_I_LIST")
public class ExpenseIList {
    @Id
    @Column(name = "SID", nullable = false, length = 50)
    private String sid;
    @Column(name = "head_id", length = 50)
    private String headId;

    //合同号
    @Column(name = "CONTRACT_NUMBER", length = 60)
    private String contractNumber;
    //进货单号
    @Column(name = "purchase_number", length = 60)
    private String purchaseNumber;
    //商品名称
    @Column(name = "PRODUCT_NAME", length = 80)
    private String productName;
    //发票号
    @Column(name = "INVOICE_NUMBER", length = 60)
    private String invoiceNumber;
    //费用类型
    @Column(name = "EXPENSE_TYPE", length = 100)
    private String expenseType;
    //数量
    @Column(name = "QUANTITY", precision = 19, scale = 6)
    private BigDecimal quantity;
    //税额
    @Column(name = "TAX_AMOUNT", precision = 19, scale = 2)
    private BigDecimal taxAmount;
    //无税金额
    @Column(name = "NO_TAX_AMOUNT", precision = 19, scale = 2)
    private BigDecimal noTaxAmount;
    //费用金额
    @Column(name = "EXPENSE_AMOUNT", precision = 19, scale = 2)
    private BigDecimal expenseAmount;

    @Column(name = "turnOVER_SID", length = 50)
    private String turnover_sid;

    @Column(name = "TRADE_CODE", length = 10)
    private String tradeCode;

    @Column(name = "INSERT_USER", length = 50)
    private String insertUser;

    @Column(name = "INSERT_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;

    @Column(name = "UPDATE_USER", length = 50)
    private String updateUser;

    @Column(name = "UPDATE_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;


    @Column(name = "STATE")
    private String state;

    @Transient
    private String barCode;

    @Transient
    private String costName;
}
