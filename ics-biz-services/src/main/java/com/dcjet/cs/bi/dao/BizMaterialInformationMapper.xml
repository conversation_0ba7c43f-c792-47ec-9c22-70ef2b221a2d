<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.bi.dao.BizMaterialInformationMapper">
    <resultMap id="bizMaterialInformationResultMap" type="com.dcjet.cs.bi.model.BizMaterialInformation">
		<id column="sid" property="sid" jdbcType="VARCHAR" />
		<result column="business_type" property="businessType" jdbcType="VARCHAR" />
		<result column="data_status" property="dataStatus" jdbcType="VARCHAR" />
		<result column="version_no" property="versionNo" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
		<result column="parent_id" property="parentId" jdbcType="VARCHAR" />
		<result column="insert_user" property="insertUser" jdbcType="VARCHAR" />
		<result column="insert_time" property="insertTime" jdbcType="TIMESTAMP" />
		<result column="update_user" property="updateUser" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR" />
		<result column="update_user_name" property="updateUserName" jdbcType="VARCHAR" />
		<result column="extend1" property="extend1" jdbcType="VARCHAR" />
		<result column="extend2" property="extend2" jdbcType="VARCHAR" />
		<result column="extend3" property="extend3" jdbcType="VARCHAR" />
		<result column="extend4" property="extend4" jdbcType="VARCHAR" />
		<result column="extend5" property="extend5" jdbcType="VARCHAR" />
		<result column="extend6" property="extend6" jdbcType="VARCHAR" />
		<result column="extend7" property="extend7" jdbcType="VARCHAR" />
		<result column="extend8" property="extend8" jdbcType="VARCHAR" />
		<result column="extend9" property="extend9" jdbcType="VARCHAR" />
		<result column="extend10" property="extend10" jdbcType="VARCHAR" />
		<result column="g_name" property="gname" jdbcType="VARCHAR" />
		<result column="short_cn" property="shortCn" jdbcType="VARCHAR" />
		<result column="billing_name" property="billingName" jdbcType="VARCHAR" />
		<result column="full_en_name" property="fullEnName" jdbcType="VARCHAR" />
		<result column="short_en_name" property="shortEnName" jdbcType="VARCHAR" />
		<result column="merchandise_categories" property="merchandiseCategories" jdbcType="VARCHAR" />
		<result column="national_product_catalogue" property="nationalProductCatalogue" jdbcType="VARCHAR" />
		<result column="bar_code" property="barCode" jdbcType="VARCHAR" />
		<result column="common_mark" property="commonMark" jdbcType="VARCHAR" />
		<result column="packaging_information" property="packagingInformation" jdbcType="VARCHAR" />
		<result column="mis_code" property="misCode" jdbcType="VARCHAR" />
		<result column="statistical_name" property="statisticalName" jdbcType="VARCHAR" />
		<result column="name_method" property="nameMethod" jdbcType="VARCHAR" />
		<result column="tax_exclusive" property="taxExclusive" jdbcType="NUMERIC" />
		<result column="including_tax" property="includingTax" jdbcType="NUMERIC" />
		<result column="tax_rate" property="taxRate" jdbcType="NUMERIC" />
		<result column="price_excluding_tax" property="priceExcludingTax" jdbcType="NUMERIC" />
		<result column="note" property="note" jdbcType="VARCHAR" />
		<result column="data_state" property="dataState" jdbcType="VARCHAR" />
		<result column="supplier_code" property="supplierCode" jdbcType="VARCHAR" />
		<result column="supplier_discount_rate" property="supplierDiscountRate" jdbcType="NUMERIC" />
		<result column="import_unit_price" property="importUnitPrice" jdbcType="NUMERIC" />
		<result column="curr" property="curr" jdbcType="VARCHAR" />
		<result column="merchant_name_cn" property="merchantNameCn" jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List" >
     id as sid
     ,business_type
     ,validate_status as data_status
     ,version_no
     ,trade_code
     ,parent_id
     ,create_by as insert_user
     ,create_time as insert_time
     ,update_by as update_user
     ,update_time
     ,insert_user_name
     ,update_user_name
     ,extend1
     ,extend2
     ,extend3
     ,extend4
     ,extend5
     ,extend6
     ,extend7
     ,extend8
     ,extend9
     ,extend10
     ,g_name
     ,short_cn
     ,billing_name
     ,full_en_name
     ,short_en_name
     ,merchandise_categories
     ,national_product_catalogue
     ,bar_code
     ,common_mark
     ,packaging_information
     ,mis_code
     ,statistical_name
     ,name_method
     ,tax_exclusive
     ,including_tax
     ,tax_rate
     ,price_excluding_tax
     ,note
     ,data_state
     ,supplier_code
     ,supplier_discount_rate
     ,import_unit_price
     ,curr
    </sql>
    <sql id="condition">
    <if test="gname != null and gname != ''">
	  and g_name like '%'|| #{gname} || '%'
	</if>
    <if test="merchandiseCategories != null and merchandiseCategories != ''">
		and merchandise_categories = #{merchandiseCategories}
	</if>
        <if test="commonMark != null and commonMark != ''">
            and common_mark like '%'|| #{commonMark} || '%'
        </if>
        <if test="dataState != null and dataState != ''">
            and data_state = #{dataState}
        </if>
        and trade_code = #{tradeCode}
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="bizMaterialInformationResultMap" parameterType="com.dcjet.cs.bi.model.BizMaterialInformation">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        t_biz_material_information t
        <where>
            <include refid="condition"></include>
        </where>
        order by t.create_time desc
    </select>
    <select id="checkGName" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM t_biz_material_information WHERE g_name = #{gname} and trade_code = #{tradeCode}
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from t_biz_material_information t where t.id in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>

    <select id="getMatForPlan" resultMap="bizMaterialInformationResultMap">
        select mat.id as sid, G_NAME, SUPPLIER_CODE, mer.MERCHANT_NAME_CN, mat.full_en_name, mat.import_unit_price, mat.data_state, mat.supplier_discount_rate
        from T_BIZ_MATERIAL_INFORMATION mat
                 left join T_BIZ_MERCHANT mer on mer.MERCHANT_CODE = mat.SUPPLIER_CODE
        where COMMON_MARK like '%1%'
          and mat.trade_code = #{tradeCode}
          and mat.data_state = '0'
          <if test="gname != null and gname != ''">
              and mat.g_name like '%'|| #{gname} || '%'
          </if>
    </select>
    <select id="checkGNameReturn" resultMap="bizMaterialInformationResultMap">
        SELECT <include refid="Base_Column_List" /> FROM t_biz_material_information WHERE g_name = #{gname} and trade_code = #{tradeCode}
    </select>
</mapper>
