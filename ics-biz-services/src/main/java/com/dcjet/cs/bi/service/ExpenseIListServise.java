package com.dcjet.cs.bi.service;

import com.dcjet.cs.bi.dao.ExpenseIHeadMapper;
import com.dcjet.cs.bi.dao.ExpenseIListMapper;
import com.dcjet.cs.bi.mapper.ExpenseIHeadDtoMapper;
import com.dcjet.cs.bi.mapper.ExpenseIListDtoMapper;
import com.dcjet.cs.bi.model.BizTaxGoodsDetails;
import com.dcjet.cs.bi.model.ExpenseIHead;
import com.dcjet.cs.bi.model.ExpenseIList;
import com.dcjet.cs.config.BulkInsertConfig;
import com.dcjet.cs.dec.dao.BizIPurchaseHeadMapper;
import com.dcjet.cs.dec.dao.BizIPurchaseListMapper;
import com.dcjet.cs.dec.model.BizIPurchaseHead;
import com.dcjet.cs.dec.model.BizIPurchaseList;
import com.dcjet.cs.dec.model.BizISellList;
import com.dcjet.cs.dto.bi.*;
import com.dcjet.cs.dto.params.CostTypeDto;
import com.dcjet.cs.params.dao.CostTypeMapper;
import com.dcjet.cs.params.model.CostType;
import com.dcjet.cs.util.TaxPdfUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.bulkinsert.BulkInsertException;
import com.xdo.bulkinsert.BulkInsertFactory;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.json.JsonObjectMapper;
import com.xdo.common.token.UserInfoToken;
import com.xdo.dataimport.utils.XdoImportLogger;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.i.IBulkInsert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import tk.mybatis.mapper.common.Mapper;
import xdoi18n.XdoI18nUtil;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ExpenseIListServise extends BaseService<ExpenseIList> {
    @Resource
    private BizIPurchaseHeadMapper bizIPurchaseHeadMapper;
    @Resource
    private ExpenseIHeadMapper expenseIHeadMapper;
    @Resource
    private BizIPurchaseListMapper bizIPurchaseListMapper;
    @Resource
    private ExpenseIListMapper expenseIListMapper;
    @Resource
    private ExpenseIListDtoMapper expenseIListDtoMapper;
    @Resource
    private BulkInsertConfig bulkInsertConfig;
    @Resource
    private CostTypeMapper costTypeMapper;
    public IBulkInsert getBulkInsertInstance() throws Exception{
        return BulkInsertFactory.createInstance(bulkInsertConfig);
    }

    @Override
    public Mapper<ExpenseIList> getMapper() {
        return expenseIListMapper;
    }

    public ResultObject<List<ExpenseIListDto>> getListPaged(ExpenseIListParam expenseIListParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        ExpenseIList po = expenseIListDtoMapper.toPo(expenseIListParam);
        po.setTradeCode(userInfo.getCompany());
        Page<ExpenseIList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> expenseIListMapper.getList(po));
        List<ExpenseIListDto> biClientInformationDtos = page.getResult().stream().map(head -> {
            ExpenseIListDto dto = expenseIListDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<ExpenseIListDto>> paged = ResultObject.createInstance(biClientInformationDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    public void delete(List<String> sids, UserInfoToken userInfo) {
        expenseIListMapper.deleteBySids(sids);
    }

    @Transactional(rollbackFor = Exception.class)
    public ExpenseIListDto update( ExpenseIListParam expenseIListParam, UserInfoToken userInfo) {
        ExpenseIList expenseIList = expenseIListMapper.selectByPrimaryKey(expenseIListParam.getSid());
        expenseIListDtoMapper.updatePo(expenseIListParam, expenseIList);
        if(expenseIList.getState().equals("1")){
            throw new RuntimeException("确认状态，无法修改！");
        }
        expenseIList.setUpdateUser(userInfo.getUserNo());
        expenseIList.setUpdateTime(new Date());
        //如果税额金额和无税金额有值则汇总到费用金额
        expenseIList.setExpenseAmount(expenseIList.getTaxAmount() != null ? expenseIList.getTaxAmount().add(expenseIList.getNoTaxAmount() != null ? expenseIList.getNoTaxAmount():BigDecimal.ZERO):BigDecimal.ZERO.add(expenseIList.getNoTaxAmount() != null ? expenseIList.getNoTaxAmount():BigDecimal.ZERO));

        // 更新数据
        int update = expenseIListMapper.updateByPrimaryKey(expenseIList);
        //更新状态
//        ExpenseIHead expenseIHead = expenseIHeadMapper.selectByPrimaryKey(expenseIList.getHeadId());
//        expenseIHead.setUpdateUser(userInfo.getUserNo());
//        expenseIHead.setUpdateTime(new Date());
//        expenseIHeadMapper.updateByPrimaryKey(expenseIHead);
        return update > 0 ? expenseIListDtoMapper.toDto(expenseIList) : null;
    }
    public List<ExpenseIListDto> selectAll(ExpenseIListParam exportParam, UserInfoToken userInfo) {
        ExpenseIList po = expenseIListDtoMapper.toPo(exportParam);
        po.setTradeCode(userInfo.getCompany());
        List<ExpenseIListDto> expenseIListDtos = new ArrayList<>();
        List<ExpenseIList> expenseILists = expenseIListMapper.getList(po);
        if (CollectionUtils.isNotEmpty(expenseILists)) {
            expenseIListDtos = expenseILists.stream().map(List -> {
                ExpenseIListDto dto = expenseIListDtoMapper.toDto(List);
                return dto;
            }).collect(Collectors.toList());
        }
        return expenseIListDtos;
    }

    public ResultObject<List<CostIContractParam>> listContract(CostIContractParam costIContractParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        costIContractParam.setTradeCode(userInfo.getCompany());
        Page<CostIContractParam> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> expenseIListMapper.getContractHead(costIContractParam));
        List<CostIContractParam> collect = page.getResult().stream().map(head -> {
            CostIContractParam dto = head;
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<CostIContractParam>> paged = ResultObject.createInstance(collect, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    @Transactional(rollbackFor = Exception.class)
    public void insertContractDetail(CostIContractParam costIContractParam, UserInfoToken userInfo) {
        //获取合同信息
        List<CostIContractParam> contractList = expenseIListMapper.getContractList(costIContractParam);
        //合同校验
        contractCheck(contractList);
        //准备新增
        List<ExpenseIList> expenseILists = new ArrayList<>();
        for (int i = 0; i < contractList.size(); i++) {
            CostIContractParam iContractParam = contractList.get(i);

            ExpenseIList expenseIList = new ExpenseIList();
            expenseIList.setSid(UUID.randomUUID().toString());
            expenseIList.setHeadId(costIContractParam.getHeadId());
            expenseIList.setContractNumber(iContractParam.getOrderNo());
            expenseIList.setPurchaseNumber(iContractParam.getPurchaseOrderNo());
            expenseIList.setProductName(iContractParam.getProductGrade());
            expenseIList.setInvoiceNumber(iContractParam.getInvoiceNumber());
            expenseIList.setExpenseType(costIContractParam.getExpenseType());
            expenseIList.setQuantity(iContractParam.getQty());
            boolean isLastItem = (i == contractList.size() - 1);
            expenseIList.setTaxAmount(calculateTaxAndNoTax(contractList, costIContractParam, costIContractParam.getTaxAmount(), iContractParam.getQty(), iContractParam.getDecTotal(),isLastItem));
            expenseIList.setNoTaxAmount(calculateTaxAndNoTax(contractList, costIContractParam, costIContractParam.getNoTaxAmount(), iContractParam.getQty(), iContractParam.getDecTotal(),isLastItem));
            expenseIList.setExpenseAmount(expenseIList.getTaxAmount().add(expenseIList.getNoTaxAmount()));
            expenseIList.setTradeCode(userInfo.getCompany());
            expenseIList.setState("0");
            expenseIList.setInsertUser(userInfo.getUserNo());
            expenseIList.setInsertTime(new Date());
            expenseIList.setTurnover_sid(iContractParam.getSid());
            expenseILists.add(expenseIList);
        }
        for (ExpenseIList expenseIList : expenseILists) {
            expenseIListMapper.insert(expenseIList);
        }
        //更新状态
        ExpenseIHead expenseIHead = expenseIHeadMapper.selectByPrimaryKey(costIContractParam.getHeadId());
        expenseIHead.setUpdateUser(userInfo.getUserNo());
        expenseIHead.setUpdateTime(new Date());
        expenseIHeadMapper.updateByPrimaryKey(expenseIHead);
        //批量插入
//        int intEffectCount = 0;
//        try{
//            IBulkInsert iBulkInsert = getBulkInsertInstance();
//            intEffectCount = iBulkInsert.fastImport(expenseILists);
//        }
//        catch (BulkInsertException ex){
//            intEffectCount = ex.getCommitRowCount();
//            XdoImportLogger.log("执行数据批量入库时,已插入" + intEffectCount + "条，出错：" + ex);
//            XdoImportLogger.log(ex);
//        }
//        catch (Exception ex){
//            XdoImportLogger.log("执行数据批量入库时,已插入" + intEffectCount + "条，出错：" + ex);
//            XdoImportLogger.log(ex);
//        }
    }
    //合同校验
    public void contractCheck(List<CostIContractParam> contractList) {
        //合同校验
    }

    //计算税额和无税额
    public BigDecimal calculateTaxAndNoTax(List<CostIContractParam> contractList, CostIContractParam costIContractParam,BigDecimal sum,BigDecimal qty,BigDecimal decTotal,boolean isLastItem) {
        if (costIContractParam.getApportionment().equals("0")) {
            if (isLastItem) {
                // 如果是最后一条，则用总额减去前n-1条分摊金额的汇总
                BigDecimal allocatedSum = contractList.stream()
                        .limit(contractList.size() - 1) // 取前n-1条
                        .map(item -> calculateTaxAndNoTax(
                                contractList, costIContractParam, sum, item.getQty(), item.getDecTotal(), false))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                return sum.subtract(allocatedSum).setScale(2, RoundingMode.HALF_UP);
            }

            // 前n-1条按比例分摊
            if (costIContractParam.getMethodAllocation().equals("0")) {
                // 从量分摊
                BigDecimal totalQty = contractList.stream()
                        .map(CostIContractParam::getQty)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                return sum.divide(totalQty, 7, RoundingMode.HALF_UP)
                        .multiply(qty)
                        .setScale(2, RoundingMode.HALF_UP);
            } else if (costIContractParam.getMethodAllocation().equals("1")) {
                // 从价分摊
                BigDecimal totalAmount = contractList.stream()
                        .map(CostIContractParam::getDecTotal)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                return sum.divide(totalAmount, 7, RoundingMode.HALF_UP)
                        .multiply(decTotal)
                        .setScale(2, RoundingMode.HALF_UP);
            }
        } else {
            return sum.setScale(2, RoundingMode.HALF_UP);
        }
        return BigDecimal.ZERO;
    }

    public BigDecimal calculateTaxAndNoTaxShippingOrder(List<CostIShippingOrderParam> contractList, CostIShippingOrderParam costIContractParam,BigDecimal sum,BigDecimal qty,BigDecimal decTotal,boolean isLastItem) {
        if (costIContractParam.getApportionment().equals("0")) {
            if (isLastItem) {
                // 如果是最后一条，则用总额减去前n-1条分摊金额的汇总
                BigDecimal allocatedSum = contractList.stream()
                        .limit(contractList.size() - 1) // 取前n-1条
                        .map(item -> calculateTaxAndNoTaxShippingOrder(
                                contractList, costIContractParam, sum, item.getQty(), item.getDecTotal(), false))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                return sum.subtract(allocatedSum).setScale(2, RoundingMode.HALF_UP);
            }

            // 前n-1条按比例分摊
            if (costIContractParam.getMethodAllocation().equals("0")) {
                // 从量分摊
                BigDecimal totalQty = contractList.stream()
                        .map(CostIShippingOrderParam::getQty)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                return sum.divide(totalQty, 7, RoundingMode.HALF_UP)
                        .multiply(qty)
                        .setScale(2, RoundingMode.HALF_UP);
            } else if (costIContractParam.getMethodAllocation().equals("1")) {
                // 从价分摊
                BigDecimal totalAmount = contractList.stream()
                        .map(CostIShippingOrderParam::getDecTotal)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                return sum.divide(totalAmount, 7, RoundingMode.HALF_UP)
                        .multiply(decTotal)
                        .setScale(2, RoundingMode.HALF_UP);
            }
        } else {
            return sum.setScale(2, RoundingMode.HALF_UP);
        }
        return BigDecimal.ZERO;
    }

    public ResultObject<List<CostIShippingOrderParam>> listShippingOrder(CostIShippingOrderParam costIShippingOrderParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        costIShippingOrderParam.setTradeCode(userInfo.getCompany());
        Page<CostIShippingOrderParam> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> expenseIListMapper.getShippingOrderHead(costIShippingOrderParam));
        List<CostIShippingOrderParam> collect = page.getResult().stream().map(head -> {
            CostIShippingOrderParam dto = head;
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<CostIShippingOrderParam>> paged = ResultObject.createInstance(collect, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    @Transactional(rollbackFor = Exception.class)
    public void insertShippingOrder(CostIShippingOrderParam costIShippingOrderParam, UserInfoToken userInfo) {

        //获取合同信息
        List<CostIShippingOrderParam> shippingOrderList = expenseIListMapper.getShippingOrderList(costIShippingOrderParam);
        //准备新增
        List<ExpenseIList> expenseILists = new ArrayList<>();
        for (int i = 0; i < shippingOrderList.size(); i++) {
            CostIShippingOrderParam iShippingOrderParam = shippingOrderList.get(i);

            ExpenseIList expenseIList = new ExpenseIList();
            expenseIList.setSid(UUID.randomUUID().toString());
            expenseIList.setHeadId(costIShippingOrderParam.getHeadId());
            expenseIList.setContractNumber(iShippingOrderParam.getOrderNo());
            expenseIList.setPurchaseNumber(iShippingOrderParam.getPurchaseOrderNo());
            expenseIList.setProductName(iShippingOrderParam.getProductGrade());
            expenseIList.setInvoiceNumber(iShippingOrderParam.getInvoiceNumber());
            expenseIList.setExpenseType(costIShippingOrderParam.getExpenseType());
            expenseIList.setQuantity(iShippingOrderParam.getQty());
            boolean isLastItem = (i == shippingOrderList.size() - 1);
            expenseIList.setTaxAmount(calculateTaxAndNoTaxShippingOrder(shippingOrderList, costIShippingOrderParam, costIShippingOrderParam.getTaxAmount(), iShippingOrderParam.getQty(), iShippingOrderParam.getDecTotal(), isLastItem));
            expenseIList.setNoTaxAmount(calculateTaxAndNoTaxShippingOrder(shippingOrderList, costIShippingOrderParam, costIShippingOrderParam.getNoTaxAmount(), iShippingOrderParam.getQty(), iShippingOrderParam.getDecTotal(), isLastItem));
            expenseIList.setExpenseAmount(expenseIList.getTaxAmount().add(expenseIList.getNoTaxAmount()));
            expenseIList.setTradeCode(userInfo.getCompany());
            expenseIList.setInsertUser(userInfo.getUserNo());
            expenseIList.setInsertTime(new Date());
            expenseIList.setState("0");
            expenseIList.setTurnover_sid(iShippingOrderParam.getSid());
            expenseILists.add(expenseIList);
        }
        for (ExpenseIList expenseIList : expenseILists) {
            expenseIListMapper.insert(expenseIList);
        }
        //更新状态
        ExpenseIHead expenseIHead = expenseIHeadMapper.selectByPrimaryKey(costIShippingOrderParam.getHeadId());
        expenseIHead.setUpdateUser(userInfo.getUserNo());
        expenseIHead.setUpdateTime(new Date());
        expenseIHeadMapper.updateByPrimaryKey(expenseIHead);
    }

    public ResultObject getSumDataByInvoiceSellSummary(CostIShippingOrderParam costIShippingOrderParam, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取汇总数据成功！");
        // Assert.hasLength(param.getHeadId(), "headId不能为空!");
        if (StringUtils.isBlank(costIShippingOrderParam.getHeadId())) {
            throw new ErrorException(400, XdoI18nUtil.t("headId不能为空!"));
        }
        // 获取汇总数据
        ExpenseIList sumData = expenseIListMapper.getSumDataByInvoiceSell(costIShippingOrderParam.getHeadId());
        resultObject.setData(sumData);
        return resultObject;

    }

    public ResultObject<List<CostIContractParam>> listContractPayment(CostIContractParam costIContractParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        costIContractParam.setTradeCode(userInfo.getCompany());
        Page<CostIContractParam> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> expenseIListMapper.getContractHeadPayment(costIContractParam));
        List<CostIContractParam> collect = page.getResult().stream().map(head -> {
            CostIContractParam dto = head;
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<CostIContractParam>> paged = ResultObject.createInstance(collect, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    public ResultObject<List<CostIShippingOrderParam>> listShippingOrderPayment(CostIShippingOrderParam costIShippingOrderParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        costIShippingOrderParam.setTradeCode(userInfo.getCompany());
        Page<CostIShippingOrderParam> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> expenseIListMapper.getShippingOrderHeadPayment(costIShippingOrderParam));
        List<CostIShippingOrderParam> collect = page.getResult().stream().map(head -> {
            CostIShippingOrderParam dto = head;
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<CostIShippingOrderParam>> paged = ResultObject.createInstance(collect, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    public ResultObject selectCostType(CostIShippingOrderParam costIShippingOrderParam, UserInfoToken userInfo) {
        //获取进货单号
        //sql意思：通过进货单号获取所有费用表体状态为0,1的费用类型,在将查询的费用类型进行过滤
        costIShippingOrderParam.setTradeCode(userInfo.getCompany());
        List<CostTypeDto> expenseTypeList = expenseIListMapper.getCostType(costIShippingOrderParam);
        ResultObject resultObject = ResultObject.createInstance(true, "查询费用类型成功！");
        resultObject.setData(expenseTypeList);
        return resultObject;
    }

    /**
     * 读取税金
     * @param file
     * @param sids
     * @param headId
     * @param userInfo
     */
    @Transactional(rollbackFor = Exception.class)
    public void uploadTaxPdf(MultipartFile[] file, List<String> sids, String headId, UserInfoToken userInfo) {
        if (file == null || file.length == 0) {
            throw new ErrorException(400, "税金附件未上传！");
        }
        String json = "";
        try {
            json = TaxPdfUtils.parseTaxInvoice(file[0]);
        } catch (Exception e) {
            log.error("税金附件解析异常: %s", e);
            throw new ErrorException(400, "税金附件解析异常，请联系管理员！");
        }
        log.error("税金附件解析结果：{}", json);

        JsonObjectMapper jsonObjectMapper = JsonObjectMapper.getInstance();
        List<BizTaxGoodsDetails> resultTaxPdf = jsonObjectMapper.fromJson(json, new TypeReference<List<BizTaxGoodsDetails>>() {});

        CostIShippingOrderParam costIShippingOrderParam = new CostIShippingOrderParam();
        costIShippingOrderParam.setSids(sids);
        costIShippingOrderParam.setHeadId(headId);
        costIShippingOrderParam.setTradeCode(userInfo.getCompany());
        costIShippingOrderParam.setMethodAllocation("1");
        costIShippingOrderParam.setApportionment("0");

        for (BizTaxGoodsDetails taxGoodsDetails : resultTaxPdf) {
            //费用类型转换 税种A(进口关税) 费用类型为“海关关税”
            //税种L(进口增值税) 费用类型为”海关增值税”
            //税种Y(进口消费税) 费用类型为”海关消费税”
            String taxType = taxGoodsDetails.getTaxType();
            String expenseTypeCode = taxType; // 默认使用原始taxType

            // 根据税种类型查询对应的费用类型
            if (taxType != null) {
                String costName = null;
                if (taxType.startsWith("A")) {
                    // A开头的，使用海关关税去找code
                    costName = "海关关税";
                } else if (taxType.startsWith("L")) {
                    // L开头的，使用海关增值税去找code
                    costName = "海关增值税";
                } else if (taxType.startsWith("Y")) {
                    // Y开头的，使用海关消费税去找code
                    costName = "海关消费税";
                }
                // 如果找到了对应的费用名称，则查询对应的paramCode
                if (costName != null) {
                    CostType costTypeQuery = new CostType();
                    costTypeQuery.setCostName(costName);
                    costTypeQuery.setTradeCode(userInfo.getCompany());
                    List<CostType> costTypes = costTypeMapper.select(costTypeQuery);
                    if (costTypes != null && !costTypes.isEmpty()) {
                        expenseTypeCode = costTypes.get(0).getParamCode();
                        log.info("税种 {} 转换为费用类型 {}, 费用代码 {}", taxType, costName, expenseTypeCode);
                    } else {
                        log.error("未找到费用名称为 {} 的费用类型，将使用原始税种 {}", costName, taxType);
                    }
                }
            }
            costIShippingOrderParam.setExpenseType(expenseTypeCode);
            costIShippingOrderParam.setTaxAmount(BigDecimal.ZERO);
            costIShippingOrderParam.setNoTaxAmount((new BigDecimal(taxGoodsDetails.getTaxAmount())));

            //执行“从价分摊”原则，计算赋值至费用表体的“无税金额“的行栏位，并计算“费用金额”栏位值。
            insertShippingOrder(costIShippingOrderParam, userInfo);
        }
    }
}
