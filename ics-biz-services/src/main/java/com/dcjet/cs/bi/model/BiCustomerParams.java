package com.dcjet.cs.bi.model;
import com.dcjet.cs.base.model.BasicModel;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2019-6-6
 */
@Setter
@Getter
@Table(name = "T_BI_CUSTOMER_PARAMS")
public class BiCustomerParams extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 自定义参数类型
     */
	@Column(name = "PARAMS_TYPE")
	private  String paramsType;
	/**
     * 自定义参数编码
     */
	@Column(name = "PARAMS_CODE")
	private  String paramsCode;
	/**
     * 自定义参数名称
     */
	@Column(name = "PARAMS_NAME")
	private  String paramsName;
	/**
     * 备注
     */
	@Column(name = "NOTE")
	private  String note;
	/**
     * 对应海关参数编码
     */
	@Column(name = "CUSTOM_PARAM_CODE")
	private  String customParamCode;
	/**
     * 对应海关参数名称
     */
	@Column(name = "CUSTOM_PARAM_NAME")
	private  String customParamName;
	/**
     * 所属企业编码
     */
	@Column(name = "TRADE_CODE")
	private  String tradeCode;

	/**
	 * 查询类型
	 */
	@Transient
	private  String inType;
}
