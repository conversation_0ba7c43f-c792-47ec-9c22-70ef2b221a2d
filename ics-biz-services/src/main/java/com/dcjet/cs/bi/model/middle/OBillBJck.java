package com.dcjet.cs.bi.model.middle;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * 业务单据：付费用通知子表
 */
@Table(name = "O_BILL_B_JCK")
@Data
public class OBillBJck {
    /**
     * 关联主表外键
     */
    @Column(name = "BILLID")
    private String billid;

    /**
     * 存货编码
     */
    @Column(name = "INVENTORY")
    private String inventory;

    /**
     * 数量
     */
    @Column(name = "TNUMBER")
    private BigDecimal tnumber;

    /**
     * 计量单位
     */
    @Column(name = "MAINUNIT")
    private String mainunit;

    /**
     * 计量单位名称
     */
    @Column(name = "NAME_MAINUNIT")
    private String nameMainunit;

    /**
     * 单价
     */
    @Column(name = "PRICE")
    private BigDecimal price = new BigDecimal("0");

    /**
     * 不含税金额
     */
    @Column(name = "NOTAXMONEY")
    private BigDecimal notaxmoney;

    /**
     * 金额
     */
    @Column(name = "TOTALMONEY")
    private BigDecimal totalmoney;

    /**
     * 税额
     */
    @Column(name = "TAXMONEY")
    private BigDecimal taxmoney;

    @Column(name = "OLDPRICE")
    private BigDecimal oldprice;
    @Column(name = "NEWPRICE")
    private BigDecimal newprice;

    /**
     * 货价
     */
    @Column(name = "HJ")
    private BigDecimal hj;

    /**
     * 关税
     */
    @Column(name = "GS")
    private BigDecimal gs;

    /**
     * 消费税
     */
    @Column(name = "XFS")
    private BigDecimal xfs;

    /**
     * 海运费
     */
    @Column(name = "HYF")
    private BigDecimal hyf;

    /**
     * 其他费用
     */
    @Column(name = "QTFY")
    private BigDecimal qtfy;

    /**
     * 收支项目编码
     */
    @Column(name = "COSTSUBJ")
    private String costsubj;

    /**
     * 收支项目名称
     */
    @Column(name = "NAME_COSTSUBJ")
    private String nameCostsubj;

    @Column(name = "OLDMIDDLEPRICE")
    private BigDecimal oldmiddleprice;
    @Column(name = "NEWMIDDLEPRICE")
    private BigDecimal newmiddleprice;
    @Column(name = "OLDBPRICE")
    private BigDecimal oldbprice;
    @Column(name = "NEWBPRICE")
    private BigDecimal newbprice;
    @Column(name = "OLDJPRICE")
    private BigDecimal oldjprice;
    @Column(name = "NEWJPRICE")
    private BigDecimal newjprice;
    @Column(name = "PROJECT")
    private String project;
    @Column(name = "JTSERIOR")
    private String jtserior;

    /**
     * 存货名称
     */
    @Column(name = "NAME_INVMANDOC")
    private String nameInvmandoc;

    @Column(name = "NAME_JOBMNGFIL")
    private String nameJobmngfil;
    @Column(name = "TRADEMARKID")
    private String trademarkid;
    @Column(name = "TRADEMARKNAME")
    private String trademarkname;

    /**
     * 序列
     */
    @Column(name = "MQ_LSH")
    private Integer mqLsh;

    @Column(name = "MQ_OP")
    private String mqOp = "i";
    @Column(name = "MQ_ST")
    private String mqSt = "0";
    @Column(name = "MQ_COUNT")
    private Integer mqCount = 1;
    @Column(name = "PK_CORP")
    private String pkCorp = "1022";
    @Column(name = "FFACTPRICE")
    private BigDecimal ffactprice;

    /**
     * 系统时间
     */
    @Column(name = "TS")
    private String ts;

    @Column(name = "DR")
    private Integer dr = 0;
    @Column(name = "PK_UNIT")
    private String pkUnit;
    @Column(name = "SHIPNOTENO")
    private String shipnoteno;

    /**
     * 客商编码
     */
    @Column(name = "SHIPCUSTNAME")
    private String shipcustname;

    /**
     * 订单号
     */
    @Column(name = "ORDERNO")
    private String orderno;

    @Column(name = "IROWPOS")
    private Integer irowpos;

    /**
     * 发票号
     */
    @Column(name = "BILLNO_BT")
    private String billnoBt;

    @Column(name = "BUDGETNOTE")
    private String budgetnote;
    @Column(name = "BOVERBUDGET")
    private String boverbudget;

    /**
     * 客商编码
     */
    @Column(name = "PK_SHIPCUST")
    private String pkShipcust;

    /**
     * 汇率
     */
    @Column(name = "CURRENTRATE")
    private BigDecimal currentrate = new BigDecimal("1");

    /**
     * 本币金额
     */
    @Column(name = "BBJE")
    private BigDecimal bbje;
}