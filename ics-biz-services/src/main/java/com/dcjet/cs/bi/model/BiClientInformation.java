package com.dcjet.cs.bi.model;

import com.dcjet.cs.base.model.BasicModel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2019-4-18
 */
@Setter
@Getter
@Table(name = "T_BI_CLIENT_INFORMATION")
public class BiClientInformation extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;

    /***
     * 客户CLI
     */
    public static final String CUSTOMER_TYPE_CLI = "CLI";

    /***
     * 供应商
     */
    public static final String CUSTOMER_TYPE_PRD = "PRD";

    /***
     * 货代FOD
     */
    public static final String CUSTOMER_TYPE_FOD = "FOD";

    /***
     * 报关行CUT
     */
    public static final String CUSTOMER_TYPE_CUT = "CUT";

    /***
     * 企业COM
     */
    public static final String CUSTOMER_TYPE_COM = "COM";

    /**
     * 客户类型(供应商 PRD、客户CLI、货代FOD、报关行CUT、企业COM)
     */
    @Column(name = "CUSTOMER_TYPE")
    private String customerType;

    /**
     * 客户代码
     */
    @Column(name = "CUSTOMER_CODE")
    private String customerCode;

    /**
     * 客户中文名称
     */
    @Column(name = "COMPANY_NAME")
    private String companyName;

    /**
     * 海关信用等级
     */
    @Column(name = "CUSTOMS_CREDIT_RATING")
    private String customsCreditRating;

    /**
     * 商检代码
     */
    @Column(name = "INSPECTION_CODE")
    private String inspectionCode;

    /**
     * 社会信用代码
     */
    @Column(name = "CREDIT_CODE")
    private String creditCode;

    /**
     * 海关注册编码
     */
    @Column(name = "DECLARE_CODE")
    private String declareCode;

    /**
     * 企业名称缩写
     */
    @Column(name = "COMPANY_NAME_SHORT")
    private String companyNameShort;

    /**
     * 客户电话
     */
    @Column(name = "TELEPHONE_NO")
    private String telephoneNo;

    /**
     * 客户联系人
     */
    @Column(name = "LINKMAN_NAME")
    private String linkmanName;

    /**
     * 联系人职务
     */
    @Column(name = "LINKMAN_DUTY")
    private String linkmanDuty;

    /**
     * 联系人电话
     */
    @Column(name = "MOBILE_PHONE")
    private String mobilePhone;

    /**
     * 联系人邮箱
     */
    @Column(name = "E_MAIL")
    private String EMail;

    /**
     * 企业中文地址
     */
    @Column(name = "ADDRESS")
    private String address;

    /**
     * 企业英文名称
     */
    @Column(name = "COMPANY_NAME_EN")
    private String companyNameEn;

    /**
     * 英文国家
     */
    @Column(name = "COUNTRY_EN")
    private String countryEn;

    /**
     * 英文地区
     */
    @Column(name = "AREA_EN")
    private String areaEn;

    /**
     * 英文城市
     */
    @Column(name = "CITY_EN")
    private String cityEn;

    /**
     * 英文地址
     */
    @Column(name = "ADDRESS_EN")
    private String addressEn;

    /**
     * 客户电话(英文)
     */
    @Column(name = "TELEPHONE_NO_EN")
    private String telephoneNoEn;

    /**
     * 客户联系人(英文)
     */
    @Column(name = "LINKMAN_NAME_EN")
    private String linkmanNameEn;

    /**
     * 联系人电话(英文)
     */
    @Column(name = "MOBILE_PHONE_EN")
    private String mobilePhoneEn;

    /**
     * 联系人邮箱(英文)
     */
    @Column(name = "E_MAIL_EN")
    private String EMailEn;

    /**
     * 备注
     */
    @Column(name = "NOTE")
    private String note;

    /**
     * 创建日期-开始
     */
    @Transient
    private String insertTimeFrom;

    /**
     * 创建日期-结束
     */
    @Transient
    private String insertTimeTo;

    /**
     * 修改时间-开始
     */
    @Transient
    private String updateTimeFrom;

    /**
     * 修改时间-结束
     */
    @Transient
    private String updateTimeTo;

    /**
     * 所属企业编码
     */
    @Column(name = "TRADE_CODE")
    private String tradeCode;

    /**
     * 联系人职务(英文)
     */
    @Column(name = "LINKMAN_DUTY_EN")
    private String linkmanDutyEn;

    @Transient
    private String customsCreditRatingName;

    /**
     * AEO代码
     */
    @Column(name = "AEO_CODE")
    private String aeoCode;

    /**
     * AEO代码
     */
    @Column(name = "MASTER_CUSTOMS")
    private String masterCustoms;

    /**
     * 传真
     */
    @Column(name = "FAX")
    private String fax;

    /**
     * 邮编
     */
    @Column(name = "POSTAL")
    private String postal;

    //add by zhangjl 2019-09-12
    /**
     * 中文国家
     */
    @Column(name = "COUNTRY")
    private String country;

    /**
     * 中文地区
     */
    @Column(name = "AREA")
    private String area;

    /**
     * 中文城市
     */
    @Column(name = "CITY")
    private String city;

    /**
     * 发票中文地址
     */
    @Column(name = "INVOICE_ADDRESS")
    private String invoiceAddress;

    /**
     * 发票英文地址
     */
    @Column(name = "INVOICE_ADDRESS_EN")
    private String invoiceAddressEn;

    /**
     * 送货中文地址
     */
    @Column(name = "DELIVER_ADDRESS")
    private String deliverAddress;

    /**
     * 送货英文地址
     */
    @Column(name = "DELIVER_ADDRESS_EN")
    private String deliverAddressEn;

    /**
     * 状态(0:启用，1：停用)
     */
    @Column(name = "STATUS")
    private String status;

    @Transient
    private String shipToName;
    @Transient
    private String shipToCode;
    @Transient
    private String shipToAddress;

    /**
     * 是否授权(0:未授权; 1:已授权)
     */
    @Column(name = "AUTHORIZE")
    private String authorize;

    /**
     * 授权截止日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "AUTHORIZE_DEADLINE")
    private Date authorizeDeadline;

    /**
     * 授权截止日期-开始
     */
    @Transient
    private String authorizeDeadlineFrom;

    /**
     * 授权截止日期-结束
     */
    @Transient
    private String authorizeDeadlineTo;

    /**
     * 减免税货物使用地点
     */
    @Column(name = "free_address")
    private String freeAddress;

    /**
     * 减免税物资属性
     */
    @Column(name = "FREE_PROPERTIES")
    private String freeProperties;

    /**
     * 成本中心
     */
    @Column(name = "COST_CENTER")
    private String costCenter;

    /**
     * 报关人员
     */
    @Column(name = "DEC_PERSONNEL")
    private String decPersonnel;

    /**
     * 报关人员电话
     */
    @Column(name = "DEC_PERSONNEL_TEL")
    private String decPersonnelTel;
}
