package com.dcjet.cs.bi.dao;

import com.dcjet.cs.bi.model.BiShipfrom;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import tk.mybatis.mapper.common.Mapper;

/**
 * shipfromMapper
 */
public interface BiShipfromMapper extends Mapper<BiShipfrom>{

    /**
     * 查询获取数据
     * @param biShipfrom
     * @return
     */
    List<BiShipfrom> getList(BiShipfrom biShipfrom);

    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(@Param("list")List<String> sids);
}