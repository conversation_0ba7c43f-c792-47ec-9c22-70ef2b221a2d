package com.dcjet.cs.bi.mapper;

import com.dcjet.cs.bi.model.ExpenseIList;
import com.dcjet.cs.dto.bi.ExpenseIListDto;
import com.dcjet.cs.dto.bi.ExpenseIListParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2019-4-18
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ExpenseIListDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    ExpenseIListDto toDto(ExpenseIList po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    ExpenseIList toPo(ExpenseIListParam param);
    /**
     * 数据库原始数据更新
     */
    void updatePo(ExpenseIListParam expenseIListParam, @MappingTarget ExpenseIList expenseIList);
    default void patchPo(ExpenseIListParam expenseIListParam, ExpenseIList expenseIList) {
        // TODO 自行实现局部更新
    }}
