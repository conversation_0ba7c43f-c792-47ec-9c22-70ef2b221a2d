package com.dcjet.cs.bi.dao;

import com.dcjet.cs.bi.model.BiBillto;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

/**
 * generated by Generate 神码
 * BiBillto
 *
 * <AUTHOR>
 * @date: 2021-4-21
 */
public interface BiBilltoMapper extends Mapper<BiBillto> {
    /**
     * 查询获取数据
     *
     * @param biBillto
     * @return
     */
    List<BiBillto> getList(BiBillto biBillto);

    /**
     * 批量删除
     *
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    /**
     *
     * @param param
     * @return
     */
    List<Map<String,Object>> selectComboByCode(Map<String, Object> param);
}
