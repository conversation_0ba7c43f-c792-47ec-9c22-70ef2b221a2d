package com.dcjet.cs.bi.service;

import com.dcjet.cs.bi.dao.BiClientInformationMapper;
import com.dcjet.cs.bi.mapper.BiClientInformationDtoMapper;
import com.dcjet.cs.bi.model.BiClientInformation;
import com.dcjet.cs.bi.model.BiTransform;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.dto.bi.*;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.util.FunctionUtil;
import com.dcjet.cs.util.variable.CommonVariable;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ArgumentException;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.KeyValuePair;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.file.XdoFileHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2019-4-18
 */
@Service
public class BiClientInformationService extends BaseService<BiClientInformation> {
    @Resource
    private BiClientInformationMapper biClientInformationMapper;
    @Resource
    private BiClientInformationDtoMapper biClientInformationDtoMapper;
    @Resource
    private CommonService commonService;
    @Override
    public Mapper<BiClientInformation> getMapper() {
        return biClientInformationMapper;
    }
    @Resource(name = "eternalXdoFileHandler")
    public XdoFileHandler fileHandler;
    /**
     * 获取分页信息
     *
     * @param biClientInformationParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<BiClientInformationDto>> getListPaged(BiClientInformationParam biClientInformationParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BiClientInformation biClientInformation = biClientInformationDtoMapper.toPo(biClientInformationParam);
        biClientInformation.setTradeCode(userInfo.getCompany());
        Page<BiClientInformation> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> biClientInformationMapper.getList(biClientInformation));
        List<BiClientInformationDto> biClientInformationDtos = page.getResult().stream().map(head -> {
            BiClientInformationDto dto = biClientInformationDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<BiClientInformationDto>> paged = ResultObject.createInstance(biClientInformationDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 功能描述:新增
     *
     * @param biClientInformationParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject<BiClientInformationDto> insert(BiClientInformationParam biClientInformationParam, UserInfoToken userInfo) {
        ResultObject<BiClientInformationDto> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        BiClientInformation biClientInformation = biClientInformationDtoMapper.toPo(biClientInformationParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        String tradeCode = userInfo.getCompany();
        String custoerType = biClientInformation.getCustomerType();
        switch (custoerType) {
            case CommonVariable.FOD:
                if (StringUtils.isBlank(biClientInformation.getCustomerCode())) {
                    throw new ErrorException(400,xdoi18n.XdoI18nUtil.t("货代代码不能为空"));
                }
                if (StringUtils.isBlank(biClientInformation.getCompanyName())) {
                    throw new ErrorException(400,xdoi18n.XdoI18nUtil.t("货代名称不能为空"));
                }
                break;
            case CommonVariable.PRD:
                if (StringUtils.isBlank(biClientInformation.getCustomerCode())) {
                throw new ErrorException(400,xdoi18n.XdoI18nUtil.t("供应商代码不能为空"));
                }
                if (StringUtils.isBlank(biClientInformation.getCompanyName())) {
                    throw new ErrorException(400,xdoi18n.XdoI18nUtil.t("供应商名称不能为空"));
                }
                break;
            case CommonVariable.CLI:
                if (StringUtils.isBlank(biClientInformation.getCustomerCode())) {
                throw new ErrorException(400,xdoi18n.XdoI18nUtil.t("客户代码不能为空"));
                }
                if (StringUtils.isBlank(biClientInformation.getCompanyName())) {
                    throw new ErrorException(400,xdoi18n.XdoI18nUtil.t("客户名称不能为空"));
                }
                break;
            case CommonVariable.CUT:
                if (StringUtils.isBlank(biClientInformation.getCustomerCode())) {
                throw new ErrorException(400,xdoi18n.XdoI18nUtil.t("报关行代码不能为空"));
                }
                if (StringUtils.isBlank(biClientInformation.getCompanyName())) {
                    throw new ErrorException(400,xdoi18n.XdoI18nUtil.t("报关行名称不能为空"));
                }
                break;
            /*case "COM":
                if (cot) {
                    resultObject.setSuccess(false);
                    resultObject.setMessage("企业代码已存在");
                    return resultObject;
                }*/
            default:
                break;
        }



        Map<String, Object> map = new HashMap<>(3);
        map.put("sid", sid);
        map.put("customerCode", biClientInformation.getCustomerCode());
        map.put("declareCode", biClientInformation.getDeclareCode());
        map.put("tradeCode", tradeCode);
        map.put("customerType", biClientInformation.getCustomerType());
        map.put("companyName", biClientInformation.getCompanyName());

        boolean cot = isCustomerCode(map);
        switch (custoerType) {
            case CommonVariable.FOD:
                if (cot) {
                    throw new ErrorException(400,xdoi18n.XdoI18nUtil.t("货代代码已存在"));
                }
                break;
            case CommonVariable.PRD:
                if (cot) {
                    throw new ErrorException(400,xdoi18n.XdoI18nUtil.t("供应商代码已存在"));
                }
                break;
            case CommonVariable.CLI:
                if (cot) {
                    throw new ErrorException(400,xdoi18n.XdoI18nUtil.t("客户代码已存在"));
                }
                break;
            case CommonVariable.CUT:
                if (cot) {
                    throw new ErrorException(400,xdoi18n.XdoI18nUtil.t("报关行代码已存在"));
                }
                if (isHSCode(map)) {
                    throw new ErrorException(400,xdoi18n.XdoI18nUtil.t("报关行海关编码已存在"));
                }
                break;
            /*case "COM":
                if (cot) {
                    resultObject.setSuccess(false);
                    resultObject.setMessage("企业代码已存在");
                    return resultObject;
                }*/
            default:
                break;
        }
        if(!"COM".equals(biClientInformation.getCustomerType())) {
            boolean oneName = oneNameByCode(map);
            if (oneName) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("当前中文名称与其他模块不一致，无法保存"));
            }
        }
        biClientInformation.setSid(sid);
        biClientInformation.setInsertUser(userInfo.getUserNo());
        biClientInformation.setInsertUserName(userInfo.getUserName());
        biClientInformation.setTradeCode(tradeCode);
        biClientInformation.setInsertTime(new Date());
        biClientInformation.setStatus(ConstantsStatus.STATUS_0);
        // 新增数据
        int insertStatus = biClientInformationMapper.insert(biClientInformation);
        BiClientInformationDto biClientInformationDto = insertStatus > 0 ? biClientInformationDtoMapper.toDto(biClientInformation) : null;
        if (biClientInformationDto != null) {
            resultObject.setData(biClientInformationDto);
            List<BiClientInformation> biClientInformationList=new ArrayList<>();
            biClientInformationList.add(biClientInformation);
            auditLog(biClientInformationList,userInfo, CommonEnum.operationsEnum.INSERT.getValue());
        } else {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("新增失败"));
        }
        return resultObject;
    }

    private void auditLog(List<BiClientInformation> biClientInformation, UserInfoToken userInfo, String operations)
    {
        String typeName="";
        switch (biClientInformation.get(0).getCustomerType()) {
            case CommonVariable.FOD:
                typeName=xdoi18n.XdoI18nUtil.t("货代基础信息");
                break;
            case CommonVariable.PRD:
                typeName=xdoi18n.XdoI18nUtil.t("供应商基础信息");
                break;
            case CommonVariable.CLI:
                typeName=xdoi18n.XdoI18nUtil.t("客户基础信息");
                break;
            case CommonVariable.CUT:
                typeName=xdoi18n.XdoI18nUtil.t("报关行基础信息");
                break;
            case CommonVariable.COM:
                typeName=xdoi18n.XdoI18nUtil.t("企业基础信息");
                break;
            default:
                break;
        }
   }
    /**
     * 功能描述:修改
     *
     * @param biClientInformationParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject<BiClientInformationDto> update(BiClientInformationParam biClientInformationParam, UserInfoToken userInfo) {
        ResultObject<BiClientInformationDto> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("修改成功"));
        BiClientInformation biClientInformation = biClientInformationMapper.selectByPrimaryKey(biClientInformationParam.getSid());
        biClientInformationDtoMapper.updatePo(biClientInformationParam, biClientInformation);
        Map<String, Object> map = new HashMap<>(3);
        map.put("sid", biClientInformation.getSid());
        map.put("customerCode", biClientInformation.getCustomerCode());
        map.put("declareCode", biClientInformation.getDeclareCode());
        map.put("tradeCode", userInfo.getCompany());
        map.put("customerType", biClientInformation.getCustomerType());
        map.put("companyName", biClientInformation.getCompanyName());
        String custoerType = biClientInformation.getCustomerType();

        switch (custoerType) {
            case CommonVariable.FOD:
                if (StringUtils.isBlank(biClientInformation.getCustomerCode())) {
                    throw new ErrorException(400,xdoi18n.XdoI18nUtil.t("货代代码不能为空"));
                }
                if (StringUtils.isBlank(biClientInformation.getCompanyName())) {
                    throw new ErrorException(400,xdoi18n.XdoI18nUtil.t("货代名称不能为空"));
                }
                break;
            case CommonVariable.PRD:
                if (StringUtils.isBlank(biClientInformation.getCustomerCode())) {
                    throw new ErrorException(400,xdoi18n.XdoI18nUtil.t("供应商代码不能为空"));
                }
                if (StringUtils.isBlank(biClientInformation.getCompanyName())) {
                    throw new ErrorException(400,xdoi18n.XdoI18nUtil.t("供应商名称不能为空"));
                }
                break;
            case CommonVariable.CLI:
                if (StringUtils.isBlank(biClientInformation.getCustomerCode())) {
                    throw new ErrorException(400,xdoi18n.XdoI18nUtil.t("客户代码不能为空"));
                }
                if (StringUtils.isBlank(biClientInformation.getCompanyName())) {
                    throw new ErrorException(400,xdoi18n.XdoI18nUtil.t("客户名称不能为空"));
                }
                break;
            case CommonVariable.CUT:
                if (StringUtils.isBlank(biClientInformation.getCustomerCode())) {
                    throw new ErrorException(400,xdoi18n.XdoI18nUtil.t("报关行代码不能为空"));
                }
                if (StringUtils.isBlank(biClientInformation.getCompanyName())) {
                    throw new ErrorException(400,xdoi18n.XdoI18nUtil.t("报关行名称不能为空"));
                }
                break;
            /*case "COM":
                if (cot) {
                    resultObject.setSuccess(false);
                    resultObject.setMessage("企业代码已存在");
                    return resultObject;
                }*/
            default:
                break;
        }

        switch (custoerType) {
            case "CUT":
                if (isHSCode(map)) {
                    throw new ErrorException(400,xdoi18n.XdoI18nUtil.t("报关行海关编码已存在"));
                }
                break;
            default:
                break;
        }
        if(!"COM".equals(biClientInformation.getCustomerType())) {
            boolean oneName = oneNameByCode(map);
            if (oneName) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("当前中文名称与其他模块不一致，无法保存"));
            }
        }
        biClientInformation.setUpdateUser(userInfo.getUserNo());
        biClientInformation.setUpdateUserName(userInfo.getUserName());
        biClientInformation.setUpdateTime(new Date());
        // 新增数据
        int update = biClientInformationMapper.updateByPrimaryKey(biClientInformation);
        BiClientInformationDto biClientInformationDto = update > 0 ? biClientInformationDtoMapper.toDto(biClientInformation) : null;
        if (biClientInformationDto != null) {
            resultObject.setData(biClientInformationDto);
            List<BiClientInformation> biClientInformationList=new ArrayList<>();
            biClientInformationList.add(biClientInformation);
            auditLog(biClientInformationList,userInfo,CommonEnum.operationsEnum.UPDATE.getValue());
        } else {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("修改失败"));
        }
        return resultObject;
    }

    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<String> sids,UserInfoToken userInfo) throws Exception {
        Example exampleList = new Example(BiClientInformation.class);
        Example.Criteria criteriaList = exampleList.createCriteria();
        criteriaList.andIn("sid", sids);
        List<BiClientInformation> biClientInformationList = biClientInformationMapper.selectByExample(exampleList);
        biClientInformationMapper.deleteBySids(sids);
        auditLog(biClientInformationList, userInfo, CommonEnum.operationsEnum.DELETE.getValue());

    }

    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BiClientInformationDto> selectAll(BiClientInformationParam exportParam, UserInfoToken userInfo) {
        BiClientInformation biClientInformation = biClientInformationDtoMapper.toPo(exportParam);
        biClientInformation.setTradeCode(userInfo.getCompany());
        List<BiClientInformationDto> biClientInformationDtos = new ArrayList<>();
        List<BiClientInformation> biClientInformations = biClientInformationMapper.getList(biClientInformation);
        if (CollectionUtils.isNotEmpty(biClientInformations)) {
            biClientInformationDtos = biClientInformations.stream().map(head -> {
                BiClientInformationDto dto = biClientInformationDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return biClientInformationDtos;
    }
     /**
     * 功能描述: 根据客户类型企业下的数据
     *
     * @auther: zhuhui
     * @version :  1.0
     * @date: 2019/8/14
     * @param: customerType 客户类型
     * @return:
     */
    public ResultObject<List<BiClientInformationDto>> getListByCustomerType(String customerType, UserInfoToken userInfo) {
        ResultObject<List<BiClientInformationDto>> resultObject = ResultObject.createInstance(true);
        BiClientInformation biClientInformation = new BiClientInformation() {{
            setTradeCode(userInfo.getCompany());
            setCustomerType(customerType);
        }};
        List<BiClientInformation> biClientInformationList = biClientInformationMapper.getList(biClientInformation);
        List<BiClientInformationDto> biClientInformationDtoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(biClientInformationList)) {
            biClientInformationDtoList = biClientInformationList.parallelStream().map(x -> biClientInformationDtoMapper.toDto(x)).collect(Collectors.toList());
        }
        resultObject.setData(biClientInformationDtoList);
        return resultObject;
    }

    public ResultObject selectComboxByCode(List<String> customerTypes, UserInfoToken userInfo) {
        return selectComboxByCode(customerTypes,false,userInfo);
    }
    /**
     * 功能描述 下拉框-查询基础企业信息 只查询当前可用
     * <AUTHOR>
     * @date 2020/4/18
     * @version 1.0
     * @param customerTypes 企业类型(可多个) 供应商 PRD、客户CLI、货代FOD、报关行CUT、企业COM
     * @param userInfo 当前用户信息
     * @return com.xdo.domain.ResultObject 三个值
    */
    public ResultObject selectComboxByCode(List<String> customerTypes,Boolean isLoadStopData, UserInfoToken userInfo) {
        if (customerTypes == null || customerTypes.size() == 0) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("查询参数不能为空"));
        }

        // 供应商 PRD、客户CLI、货代FOD、报关行CUT、企业COM
        Map<String, String> typeMap = new HashMap<String, String>(5){{
            put("PRD", "供应商");
            put("CLI", "客户");
            put("FOD", "货代");
            put("CUT", "报关行");
            put("COM", "企业");
        }};
        List<BiClientInformation> list = new ArrayList<>();
        if(!isLoadStopData) {
            list = biClientInformationMapper.findByCodeAndType(userInfo.getCompany(), null, customerTypes);
        } else {
            list = biClientInformationMapper.findByCodeAndType2(userInfo.getCompany(), null, customerTypes);
        }
        List<Map<String, String>> mapList = list.stream().map(it -> {
            Map<String, String> map = new HashMap<>(5);
            map.put("VALUE", it.getCustomerCode());
            if(isLoadStopData && it.getStatus().equals("1")) {
                map.put("LABEL", it.getCompanyName().concat("(停用)"));
            } else {
                map.put("LABEL", it.getCompanyName());
            }
            map.put("CODE", it.getDeclareCode());
            map.put("TYPE", it.getCustomerType());
            map.put("TYPE_NAME", typeMap.get(it.getCustomerType()));
            map.put("AEO_CODE", it.getAeoCode());
            return map;
        }).filter(FunctionUtil.distinctByKey(p -> p.get("TYPE") + p.get("VALUE") + p.get("LABEL") + p.get("CODE")+ p.get("AEO_CODE"))).collect(Collectors.toList());

        return ResultObject.createInstance(true, "", mapList);
    }
    /**
     * 功能描述: 查询数据字典所以数据
     *
     * @auther: 沈振宇
     * @date: 2019-06-02
     * @return:
     */
    public ResultObject getComboxByCode(String customerType, UserInfoToken userInfo) {
        return selectComboxByCode(Arrays.asList(customerType), userInfo);

    }
    /**
     * 功能描述: 查询当前企业的维护的报关行数据及自身企业数据
     *
     * <AUTHOR> 张军林
     * @version :   1.0
     * @date：2019-07-22
     * @param: session
     * @return: ResultObject
     */
    public ResultObject selectAllCombox(UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("查询成功"));
        Map<String, Object> param = new HashMap<>(1);
        param.put("tradeCode", userInfo.getCompany());
        param.put("isAll", ConstantsStatus.STATUS_1);
        param.put("customerType", CommonVariable.CUT);
        List<Map<String, Object>> listResult = biClientInformationMapper.selectAllCombox(param);
        List<KeyValuePair> keyValuePairList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(listResult)) {
            for (Map<String, Object> map : listResult) {
                keyValuePairList.add(new KeyValuePair(map.get("VALUE"), map.get("LABEL")));
            }
        }
        //带上当前企业自身的企业海关代码和名称
        keyValuePairList.add(new KeyValuePair(userInfo.getCompany(), userInfo.getCompanyName()));
        result.setData(keyValuePairList);
        return result;
    }
    /**
     * 功能描述: 根据申报企业海关代码获取申报企业信用代码
     *
     * @auther: 张军林
     * @date: 2019/07/19 14:20
     * @param: tradeCode
     * @param: session
     * @return: com.dcits.base.model.ResultObject
     */
    public BiClientInformationDto getCreditCode(String customerCode, UserInfoToken userInfo) {
        BiClientInformation biClientInformation = new BiClientInformation();
        biClientInformation.setTradeCode(userInfo.getCompany());
        biClientInformation.setCustomerType(CommonVariable.CUT);
        biClientInformation.setDeclareCode(customerCode);
        //biClientInformation = biClientInformationMapper.selectOne(biClientInformation);
        List<BiClientInformation> biClientInformationList = biClientInformationMapper.getList(biClientInformation);
        if (!biClientInformationList.isEmpty()) {
            biClientInformation = biClientInformationList.get(0);
        } else {
            biClientInformation = null;
        }
        return biClientInformationDtoMapper.toDto(biClientInformation);
    }

    /**
     * 功能描述: 复制
     *
     * <AUTHOR> 沈振宇
     * @version :   1.0
     * @date： 2019-4-9
     * @param: model 实体(List)
     * @param: optUserNo 当前用户
     * @param: tradeCode 当前企业code
     * @return:
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject copyprd(List<String> model, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("复制成功"));

        for (String aModel : model) {
            String tradeCode = userInfo.getCompany();
            BiClientInformation copyModel = biClientInformationMapper.selectByPrimaryKey(aModel);

            Map<String, Object> map = new HashMap<>(4);
            map.put("customerCode", copyModel.getCustomerCode());
            map.put("declareCode", copyModel.getDeclareCode());
            map.put("tradeCode", tradeCode);
            map.put("customerType", CommonVariable.PRD);

            boolean cot = isCustomerCode(map);
            if (cot) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("代码：") + copyModel.getCustomerCode() + xdoi18n.XdoI18nUtil.t("已存在,复制失败"));
            }
        }

        for (String aModel : model) {
            //copyprd(aModel, userInfo);

            String sysid = UUID.randomUUID().toString();
            String loginUserNo = userInfo.getUserNo();
            String tradeCode = userInfo.getCompany();
            BiClientInformation copyModel = biClientInformationMapper.selectByPrimaryKey(aModel);

            if (aModel != null) {
                copyModel.setSid(sysid);
                copyModel.setCustomerType(CommonVariable.PRD);
                copyModel.setInsertUser(loginUserNo);
                copyModel.setInsertUserName(userInfo.getUserName());
                copyModel.setInsertTime(new Date());
                copyModel.setTradeCode(tradeCode);
                biClientInformationMapper.insertSelective(copyModel);
            }
        }
        return result;
    }

    /**
     * 功能描述: 复制
     *
     * <AUTHOR> 沈振宇
     * @version :   1.0
     * @date： 2019-4-9
     * @param: model 实体(List)
     * @param: optUserNo 当前用户
     * @param: tradeCode 当前企业code
     * @return:
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject copycli(List<String> model, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true,xdoi18n.XdoI18nUtil.t("复制成功"));

        for (String aModel : model) {
            String tradeCode = userInfo.getCompany();
            BiClientInformation copyModel = biClientInformationMapper.selectByPrimaryKey(aModel);

            Map<String, Object> map = new HashMap<>(3);
            map.put("customerCode", copyModel.getCustomerCode());
            map.put("declareCode", copyModel.getDeclareCode());
            map.put("tradeCode", tradeCode);
            map.put("customerType", CommonVariable.CLI);

            boolean cot = isCustomerCode(map);
            if (cot) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("代码：") + copyModel.getCustomerCode() + xdoi18n.XdoI18nUtil.t("已存在,复制失败"));
            }
        }

        for (String aModel : model) {
            String sysid = UUID.randomUUID().toString();
            String loginUserNo = userInfo.getUserNo();
            String tradeCode = userInfo.getCompany();
            BiClientInformation copyModel = biClientInformationMapper.selectByPrimaryKey(aModel);

            if (aModel != null) {
                copyModel.setSid(sysid);
                copyModel.setCustomerType(CommonVariable.CLI);
                copyModel.setInsertUser(loginUserNo);
                copyModel.setInsertUserName(userInfo.getUserName());
                copyModel.setInsertTime(new Date());
                copyModel.setTradeCode(tradeCode);
                biClientInformationMapper.insertSelective(copyModel);
            }
        }
        return result;
    }


    public boolean isCustomerCode(Map<String, Object> param) {
        return biClientInformationMapper.isCustomerCode(param) > 0;
    }

    public boolean oneNameByCode(Map<String, Object> param) {
        return biClientInformationMapper.oneNameByCode(param) > 0;
    }

    /**
     * 获取报关行代码
     * @author: 沈振宇
     * @date: 2019-5-21
     * @param param 参数
     * @return
     */
    public boolean isHSCode(Map<String, Object> param) {
        return biClientInformationMapper.isHSCode(param) > 0;
    }


    /**
     * 功能描述:根据客户类型、客户编码获取当前企业下的数据
     *
     * @auther: zhuhui
     * @version :  1.0
     * @date: 2019/8/14
     * @param: customerType 客户类型
     * @param: customerCode 客户代码
     * @return:
     */
    public ResultObject<BiClientInformationDto> selectByCustomerTypeAndCode(String customerType, String customerCode, UserInfoToken userInfo) {
        ResultObject<BiClientInformationDto> resultObject = ResultObject.createInstance(true);
        BiClientInformation biClientInformation = new BiClientInformation() {{
            setTradeCode(userInfo.getCompany());
            setCustomerType(customerType);
            setCustomerCode(customerCode);
        }};
        BiClientInformation rtBiClientInfo = biClientInformationMapper.selectOne(biClientInformation);
        if (null != rtBiClientInfo) {
            resultObject.setData(biClientInformationDtoMapper.toDto(rtBiClientInfo));
        } else {
            resultObject.setData(biClientInformationDtoMapper.toDto(rtBiClientInfo));
        }
        return resultObject;
    }

    public ResultObject getTransformInfo(BiTransformParam transformParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("查询成功"));
        BiTransform biTransform = biClientInformationDtoMapper.toPo(transformParam);
        biTransform.setTradeCode(userInfo.getCompany());
        List<BiTransform> biTransforms = biClientInformationMapper.getTransformInfo(biTransform);
        if (CollectionUtils.isNotEmpty(biTransforms)) {
            BiTransformDto biTransformDto = biClientInformationDtoMapper.toDto(biTransforms.get(0));
            result.setData(biTransformDto);
        } else {
            result.setMessage(xdoi18n.XdoI18nUtil.t("没有相关转换信息"));
        }
        return result;
    }

    /**
     * 功能描述:启用/停用
     *
     * @param status
     * @param sids
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject setMatStatus(String status, List<String> sids, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("状态设置成功"));
        for (String sid : sids) {
            BiClientInformation biClientInformation = biClientInformationMapper.selectByPrimaryKey(sid);
            if (biClientInformation.getStatus() == null || biClientInformation.getStatus() == "") {
                biClientInformation.setStatus(status);
            } else if (biClientInformation.getStatus().equals(status)) {
                switch (biClientInformation.getStatus()) {
                    case ConstantsStatus.STATUS_0:
                        throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("此数据已是启用，无需再设为启用"));
                    case ConstantsStatus.STATUS_1:
                        throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("此数据已是停用，无需再设为停用"));
                }
            } else {
                biClientInformation.setStatus(status);
            }
            biClientInformation.setUpdateUser(userInfo.getUserNo());
            biClientInformation.setUpdateUserName(userInfo.getUserName());
            biClientInformation.setUpdateTime(new Date());
            biClientInformationMapper.updateByPrimaryKey(biClientInformation);
        }

        return result;
    }

    public List<BiAeoDto> selectCutAndFodInfo (String tradeCode){
        return biClientInformationMapper.selectCutAndFodInfo(tradeCode);
    }
}
