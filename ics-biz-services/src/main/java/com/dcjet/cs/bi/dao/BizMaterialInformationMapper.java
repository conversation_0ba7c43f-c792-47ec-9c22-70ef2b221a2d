package com.dcjet.cs.bi.dao;

import com.dcjet.cs.bi.model.BizMaterialInformation;
import com.dcjet.cs.dto.bi.BizMaterialInformationDto;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
/**
* generated by Generate 神码
* BizMaterialInformation
* <AUTHOR>
* @date: 2025-3-12
*/
public interface BizMaterialInformationMapper extends Mapper<BizMaterialInformation> {
    /**
     * 查询获取数据
     * @param bizMaterialInformation
     * @return
     */
    List<BizMaterialInformation> getList(BizMaterialInformation bizMaterialInformation);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
    int checkGName(BizMaterialInformation bizMaterialInformation);
    List<BizMaterialInformation> checkGNameReturn(BizMaterialInformation bizMaterialInformation);

    /**
     * 获取物料信息列表
     * @param mat
     * @return 物料信息列表
     */
    List<BizMaterialInformation> getMatForPlan(BizMaterialInformationDto mat);
}
