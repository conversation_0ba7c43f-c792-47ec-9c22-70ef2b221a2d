package com.dcjet.cs.bi.service;

import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.dao.ExpenseIHeadMapper;
import com.dcjet.cs.bi.dao.ExpenseIListMapper;
import com.dcjet.cs.bi.mapper.ExpenseIHeadDtoMapper;
import com.dcjet.cs.bi.model.BiClientInformation;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.bi.model.ExpenseIHead;
import com.dcjet.cs.bi.model.ExpenseIList;
import com.dcjet.cs.bi.model.middle.OBillBJck;
import com.dcjet.cs.bi.model.middle.OBillJck;
import com.dcjet.cs.dto.bi.BiClientInformationDto;
import com.dcjet.cs.dto.bi.BiClientInformationParam;
import com.dcjet.cs.dto.bi.ExpenseIHeadDto;
import com.dcjet.cs.dto.bi.ExpenseIHeadParam;
import com.dcjet.cs.service.ThirdPartyDbService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.common.util.DateUtils;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import javax.persistence.Table;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ExpenseIHeadServise extends BaseService<ExpenseIHead> {
    @Resource
    private ExpenseIHeadMapper expenseIHeadMapper;
    @Resource
    private ExpenseIListMapper expenseIListMapper;
    @Resource
    private ExpenseIHeadDtoMapper expenseIHeadDtoMapper;
    @Resource
    private BizMerchantMapper bizMerchantMapper;
    @Resource
    private ThirdPartyDbService thirdPartyDbService;

    @Override
    public Mapper<ExpenseIHead> getMapper() {
        return expenseIHeadMapper;
    }

    public ResultObject<List<ExpenseIHeadDto>> getListPaged(ExpenseIHeadParam expenseIHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        ExpenseIHead po = expenseIHeadDtoMapper.toPo(expenseIHeadParam);
        po.setTradeCode(userInfo.getCompany());
        Page<ExpenseIHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> expenseIHeadMapper.getList(po));
        List<ExpenseIHeadDto> biClientInformationDtos = page.getResult().stream().map(head -> {
            ExpenseIHeadDto dto = expenseIHeadDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<ExpenseIHeadDto>> paged = ResultObject.createInstance(biClientInformationDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    @Transactional(rollbackFor = Exception.class)
    public ResultObject<ExpenseIHeadDto> insert(@Valid ExpenseIHeadParam expenseIHeadParam, UserInfoToken userInfo) {
        ResultObject<ExpenseIHeadDto> resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        ExpenseIHead expenseIHead = expenseIHeadDtoMapper.toPo(expenseIHeadParam);
        //系统自动生成，首字母JYFFTZ+年份4码+月份2码+流水3码
        expenseIHead.setDocumentNumber("JYFFTZ"+generateDocumentNumber(userInfo));
        // 新增数据
        // 规范固定字段
        String sid = UUID.randomUUID().toString();
        expenseIHead.setSid(sid);
        expenseIHead.setCreateUser(userInfo.getUserName());
        expenseIHead.setCreateUserTime(new Date());
        expenseIHead.setInsertUser(userInfo.getUserNo());
        expenseIHead.setInsertTime(new Date());
        expenseIHead.setTradeCode(userInfo.getCompany());
        expenseIHead.setUpdateUser(userInfo.getUserNo());
        expenseIHead.setUpdateTime(new Date());

        // 新增数据
        int insertStatus = expenseIHeadMapper.insert(expenseIHead);
        resultObject.setData(expenseIHeadDtoMapper.toDto(expenseIHead));

        return resultObject;
    }

    public String generateDocumentNumber(UserInfoToken userInfo) {
        //系统自动生成，首字母JYFFTZ+年份4码+月份2码+流水3码
        // 获取当前年月
        String currentDate = new SimpleDateFormat("yyyyMM").format(new Date());
        // 查询当月的最后一笔单号
        ExpenseIHead po = new ExpenseIHead();
        po.setTradeCode(userInfo.getCompany());
        po.setDocumentNumber(currentDate);
        List<ExpenseIHead> expenseIHeads = expenseIHeadMapper.selectByDocumentNumber(po);
        if (expenseIHeads == null || expenseIHeads.isEmpty()) {
            return currentDate + "001";
        }
        // 获取流水号
        int maxNumber = expenseIHeads.stream().mapToInt(expenseIHead -> Integer.parseInt(expenseIHead.getDocumentNumber().substring(12))).max().orElse(0);
        return currentDate + String.format("%03d", maxNumber + 1);
    }

    public void delete(List<String> sids, UserInfoToken userInfo) {
        expenseIHeadMapper.deleteBySids(sids);
    }

    @Transactional(rollbackFor = Exception.class)
    public ExpenseIHeadDto update(@Valid ExpenseIHeadParam expenseIHeadParam, UserInfoToken userInfo) {
        ExpenseIHead expenseIHead = expenseIHeadMapper.selectByPrimaryKey(expenseIHeadParam.getSid());
        expenseIHeadDtoMapper.updatePo(expenseIHeadParam, expenseIHead);
        expenseIHead.setUpdateUser(userInfo.getUserNo());
        expenseIHead.setUpdateTime(new Date());
        expenseIHead.setCreateUser(userInfo.getUserName());
//        expenseIHead.setCreateUserTime(new Date());

        // 更新数据
        int update = expenseIHeadMapper.updateByPrimaryKey(expenseIHead);
        return update > 0 ? expenseIHeadDtoMapper.toDto(expenseIHead) : null;
    }

    public void chargeback(List<String> sids, UserInfoToken userInfo) {
        expenseIHeadMapper.chargebackBySids(sids);
        //更新状态
        for (String sid : sids) {
            ExpenseIHead expenseIHead = expenseIHeadMapper.selectByPrimaryKey(sid);
            expenseIHead.setUpdateUser(userInfo.getUserNo());
            expenseIHead.setUpdateTime(new Date());
            expenseIHeadMapper.updateByPrimaryKey(expenseIHead);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void affirm(List<String> sids, UserInfoToken userInfo) {
        expenseIHeadMapper.chargeAffirmBySids(sids);
        List<ExpenseIHead> expenseIHeads = new ArrayList<>();
        //更新状态
        for (String sid : sids) {
            ExpenseIHead expenseIHead = expenseIHeadMapper.selectByPrimaryKey(sid);
            expenseIHeads.add(expenseIHead);
            expenseIHead.setUpdateUser(userInfo.getUserNo());
            expenseIHead.setUpdateTime(new Date());
            expenseIHeadMapper.updateByPrimaryKey(expenseIHead);
            //根据进货单号 回刷税金
            expenseIHeadMapper.updateTaxReceiptListBySid(sid, userInfo.getUserNo(), userInfo.getCompany());
        }
//        this.pushDataToUfida(expenseIHeads, userInfo);
    }

    public void cancellation(List<String> sids, UserInfoToken userInfo) {
        expenseIHeadMapper.cancellationBySids(sids);
        //更新状态
        for (String sid : sids) {
            ExpenseIHead expenseIHead = expenseIHeadMapper.selectByPrimaryKey(sid);
            expenseIHead.setUpdateUser(userInfo.getUserNo());
            expenseIHead.setUpdateTime(new Date());
            expenseIHeadMapper.updateByPrimaryKey(expenseIHead);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void copy(List<String> sids, UserInfoToken userInfo) {
        //获取表头+表体+附件信息
        String s = sids.get(0);
        ExpenseIHead expenseIHead = expenseIHeadMapper.selectByPrimaryKey(s);
        List<ExpenseIList> select = expenseIListMapper.select(new ExpenseIList() {{
            setHeadId(s);
        }});
        String string = UUID.randomUUID().toString();
        expenseIHead.setSid(string);
        //系统自动生成，首字母JYFFTZ+年份4码+月份2码+流水3码
        expenseIHead.setDocumentNumber("JYFFTZ"+generateDocumentNumber(userInfo));
        expenseIHead.setCreateUser(userInfo.getUserNo());
        expenseIHead.setCreateUserTime(new Date());
        expenseIHead.setInsertUser(userInfo.getUserNo());
        expenseIHead.setState("0");
        expenseIHead.setConfirmationTime(null);
        expenseIHead.setInsertTime(new Date());
        expenseIHead.setUpdateTime(new Date());
        expenseIHead.setUpdateUser(userInfo.getUserNo());
        expenseIHeadMapper.insert(expenseIHead);
        if(CollectionUtils.isNotEmpty(select)){
            for (ExpenseIList expenseIList : select) {
                expenseIList.setSid(UUID.randomUUID().toString());
                expenseIList.setHeadId(string);
                expenseIList.setInsertUser(userInfo.getUserNo());
                expenseIList.setInsertTime(new Date());
                expenseIList.setUpdateTime(null);
                expenseIList.setUpdateUser(null);
                expenseIList.setState("0");
                expenseIListMapper.insert(expenseIList);
            }
        }
    }

    public List<ExpenseIHeadDto> selectAll(ExpenseIHeadParam exportParam, UserInfoToken userInfo) {
        ExpenseIHead po = expenseIHeadDtoMapper.toPo(exportParam);
        po.setTradeCode(userInfo.getCompany());
        List<ExpenseIHeadDto> expenseIHeadDtos = new ArrayList<>();
        List<ExpenseIHead> expenseIHeads = expenseIHeadMapper.getList(po);
        if (CollectionUtils.isNotEmpty(expenseIHeads)) {
            expenseIHeadDtos = expenseIHeads.stream().map(head -> {
                ExpenseIHeadDto dto = expenseIHeadDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return expenseIHeadDtos;
    }

    private void pushDataToUfida(List<ExpenseIHead> expenseIHeads, UserInfoToken<?> userInfo) {
        if (CollectionUtils.isEmpty(expenseIHeads)) {
            return;
        }
        BizMerchant bizMerchantQuery = new BizMerchant();
        bizMerchantQuery.setTradeCode(userInfo.getCompany());
        List<BizMerchant> bizMerchants = this.bizMerchantMapper.getList(bizMerchantQuery);
        List<OBillJck> middleHeads = new ArrayList<>(expenseIHeads.size());
        List<OBillBJck> middleLists = new ArrayList<>();
        expenseIHeads.forEach(expenseIHead -> {
            List<ExpenseIList> expenseILists = this.expenseIListMapper.getByHeadId(expenseIHead.getSid(), expenseIHead.getTradeCode());
            OBillJck oBillJck = new OBillJck();
            oBillJck.setBillid(UUID.randomUUID().toString().replace("-", StringUtils.EMPTY));
            oBillJck.setBilldate(DateUtils.dateToString(expenseIHead.getConfirmationTime(), "yyyy-MM-dd"));
            oBillJck.setBillcode(expenseIHead.getDocumentNumber());
            oBillJck.setPerson(expenseIHead.getInsertUser());
            oBillJck.setMaker(expenseIHead.getInsertUser());
            oBillJck.setCust(expenseIHead.getPayee());
            oBillJck.setNamePsndoc(expenseIHead.getCreateUser());
            oBillJck.setNameOperator(expenseIHead.getCreateUser());
            oBillJck.setNameCumandoc(bizMerchants.stream()
                    .filter(bizMerchant -> Objects.equals(bizMerchant.getMerchantCode(), expenseIHead.getPayee()))
                    .map(BizMerchant::getMerchantNameCn)
                    .filter(StringUtils::isNotBlank)
                    .findAny().orElse(null));
            oBillJck.setTs(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            BigDecimal totalExpenseAmount = expenseILists.stream()
                    .map(expenseIList -> expenseIList.getExpenseAmount() != null
                            ? expenseIList.getExpenseAmount() : BigDecimal.ZERO)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            oBillJck.setTotalmoney(totalExpenseAmount);
            oBillJck.setRowCount(CollectionUtils.isEmpty(expenseILists) ? 0 : expenseILists.size());
            oBillJck.setBbje(totalExpenseAmount);
            oBillJck.setMqLsh((int) this.thirdPartyDbService.getSequenceNextValue("S_DEC_I_SEQ_NO", 1)[0]);
            middleHeads.add(oBillJck);

            long[] seqNos = this.thirdPartyDbService.getSequenceNextValue("S_DEC_I_SEQ_NO", expenseILists.size());
            for (int i = 0; i < expenseILists.size(); i++) {
                ExpenseIList expenseIList = expenseILists.get(i);
                OBillBJck oBillBJck = new OBillBJck();
                oBillBJck.setBillid(oBillJck.getBillid());
                oBillBJck.setInventory(expenseIList.getBarCode());
                oBillBJck.setTnumber(expenseIList.getQuantity());
                oBillBJck.setNotaxmoney(expenseIList.getNoTaxAmount());
                oBillBJck.setTotalmoney(expenseIList.getExpenseAmount());
                oBillBJck.setTaxmoney(expenseIList.getTaxAmount());
                oBillBJck.setCostsubj(expenseIList.getExpenseType());
                oBillBJck.setNameCostsubj(expenseIList.getCostName());
                oBillBJck.setNameInvmandoc(expenseIList.getProductName());
                oBillBJck.setTs(oBillJck.getTs());
                oBillBJck.setShipcustname(expenseIHead.getPayee());
                oBillBJck.setOrderno(expenseIList.getPurchaseNumber());
                oBillBJck.setBillnoBt(expenseIList.getInvoiceNumber());
                oBillBJck.setPkShipcust(expenseIHead.getPayee());
                oBillBJck.setBbje(expenseIList.getExpenseAmount());
                oBillBJck.setMqLsh((int) seqNos[i]);
                middleLists.add(oBillBJck);
            }
        });
        this.thirdPartyDbService.batchInsertEntities(OBillJck.class.getAnnotation(Table.class).name(), middleHeads);
        this.thirdPartyDbService.batchInsertEntities(OBillBJck.class.getAnnotation(Table.class).name(), middleLists);
    }
}
