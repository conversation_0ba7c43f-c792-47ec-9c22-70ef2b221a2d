package com.dcjet.cs.bi.mapper;

import com.dcjet.cs.bi.model.ExpenseIHead;
import com.dcjet.cs.dto.bi.*;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2019-4-18
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ExpenseIHeadDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    ExpenseIHeadDto toDto(ExpenseIHead po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    ExpenseIHead toPo(ExpenseIHeadParam param);
    /**
     * 数据库原始数据更新
     */
    void updatePo(ExpenseIHeadParam expenseIHeadParam, @MappingTarget ExpenseIHead expenseIHead);
    default void patchPo(ExpenseIHeadParam expenseIHeadParam, ExpenseIHead expenseIHead) {
        // TODO 自行实现局部更新
    }}
