<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.bi.dao.BiClientInformationMapper">
    <sql id="Base_Column_List">
     t.CUSTOMER_TYPE
     ,t.CUSTOMER_CODE
     ,t.COMPANY_NAME
     ,t.CUSTOMS_CREDIT_RATING
     ,t.INSPECTION_CODE
     ,t.CREDIT_CODE
     ,t.DECLARE_CODE
     ,t.COMPANY_NAME_SHORT
     ,t.TELEPHONE_NO
     ,t.LINKMAN_NAME
     ,t.LINKMAN_DUTY
     ,t.MOBILE_PHONE
     ,t.E_MAIL
     ,t.ADDRESS
     ,t.COMPANY_NAME_EN
     ,t.COUNTRY_E<PERSON>
     ,t.AREA_EN
     ,t.<PERSON>_<PERSON>
     ,t.<PERSON><PERSON>_<PERSON>
     ,t.T<PERSON><PERSON>_NO_E<PERSON>
     ,t.<PERSON>_NAME_EN
     ,t.<PERSON>_PHONE_EN
     ,t.E_MAIL_EN
     ,t.NOTE
     ,t.INSERT_USER
     ,t.INSERT_TIME
     ,t.UPDATE_USER
     ,t.UPDATE_TIME
     ,t.TRADE_CODE
     ,t.SID
     ,t.LINKMAN_DUTY_EN
	 ,b.PARAMS_NAME as CUSTOMS_CREDIT_RATING_NAME
	 ,AEO_CODE
	 ,t.MASTER_CUSTOMS
	 ,t.FAX
	 ,t.POSTAL
	 ,t.COUNTRY
	 ,t.AREA
	 ,t.CITY
	 ,t.INVOICE_ADDRESS
	 ,t.INVOICE_ADDRESS_EN
	 ,t.DELIVER_ADDRESS
	 ,t.DELIVER_ADDRESS_EN
	 ,t.STATUS
	 ,t.INSERT_USER_NAME
     ,t.UPDATE_USER_NAME
     ,t.AUTHORIZE
     ,t.AUTHORIZE_DEADLINE
     ,t.free_address
     ,t.FREE_PROPERTIES
     ,t.COST_CENTER
     ,t.DEC_PERSONNEL
     ,t.DEC_PERSONNEL_TEL
    </sql>
    <sql id="conditionORACLE">
        <if test="authorizeDeadlineFrom != null and authorizeDeadlineFrom != ''">
            <![CDATA[ and t.AUTHORIZE_DEADLINE >= to_date(#{authorizeDeadlineFrom},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="authorizeDeadlineTo != null and authorizeDeadlineTo != ''">
            <![CDATA[ and t.AUTHORIZE_DEADLINE < to_date(#{authorizeDeadlineTo},'yyyy-MM-dd hh24:mi:ss')+1 ]]>
        </if>
    </sql>
    <sql id="conditionPG">
        <if test="authorizeDeadlineFrom != null and authorizeDeadlineFrom != ''">
            <![CDATA[ and t.AUTHORIZE_DEADLINE >= to_timestamp(#{authorizeDeadlineFrom},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="authorizeDeadlineTo != null and authorizeDeadlineTo != ''">
            <![CDATA[ and t.AUTHORIZE_DEADLINE < to_timestamp(#{authorizeDeadlineTo},'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day']]>
        </if>
    </sql>
    <sql id="condition">
        <if test="sid != null and sid != ''">
            and SID = #{sid}
        </if>
        <if test="customerCode != null and customerCode != ''">
            and t.CUSTOMER_CODE = #{customerCode}
        </if>
        <if test="declareCode != null and declareCode != ''">
            and t.DECLARE_CODE = #{declareCode}
        </if>
        <if test="companyName != null and companyName != ''">
            and t.COMPANY_NAME like concat(concat('%',#{companyName,jdbcType=VARCHAR}),'%')
        </if>
        <if test="customerType != null and customerType != ''">
            and t.CUSTOMER_TYPE = #{customerType}
        </if>
        <if test="authorize != null and authorize != ''">
            and t.AUTHORIZE = #{authorize}
        </if>
        <if test="companyNameShort != null and companyNameShort != ''">
            and t.COMPANY_NAME_SHORT like concat(concat('%',#{companyNameShort,jdbcType=VARCHAR}),'%')
        </if>
        and t.TRADE_CODE = #{tradeCode}
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultType="com.dcjet.cs.bi.model.BiClientInformation"
            parameterType="com.dcjet.cs.bi.model.BiClientInformation">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_bi_client_information t
        left join T_BI_CUSTOMER_PARAMS b on t.CUSTOMS_CREDIT_RATING=b.PARAMS_CODE and b.PARAMS_TYPE='CREDIT_RATING'
        <where>
            <include refid="condition"></include>
            <if test='_databaseId == "postgresql" '>
                <include refid="conditionPG"></include>
            </if>
            <if test='_databaseId != "postgresql" '>
                <include refid="conditionORACLE"></include>
            </if>
        </where>
        order by t.CUSTOMER_CODE desc, t.sid
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_BI_CLIENT_INFORMATION t where t.SID in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>;
        delete from t_bi_shipfrom t where t.head_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>;
    </delete>
    <select id="findByCodeAndType" resultType="com.dcjet.cs.bi.model.BiClientInformation">
        SELECT t.* FROM t_bi_client_information t
        WHERE t.TRADE_CODE = #{tradeCode}
        <if test="customerCode != null and customerCode != ''">
            AND t.CUSTOMER_CODE = #{customerCode}
        </if>
        and t.STATUS!='1'
        AND t.CUSTOMER_TYPE IN
        <foreach collection="type" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        ORDER BY t.CUSTOMER_CODE
    </select>

    <select id="findByCodeAndType2" resultType="com.dcjet.cs.bi.model.BiClientInformation">
        SELECT t.* FROM t_bi_client_information t
        WHERE t.TRADE_CODE = #{tradeCode}
        <if test="customerCode != null and customerCode != ''">
            AND t.CUSTOMER_CODE = #{customerCode}
        </if>
        AND t.CUSTOMER_TYPE IN
        <foreach collection="type" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        ORDER BY STATUS,t.CUSTOMER_CODE
    </select>

    <!--获取出方海关十位代码下拉-->
    <select id="selectAllCombox" resultType="map" parameterType="map">
        select distinct t.DECLARE_CODE as "VALUE", t.COMPANY_NAME as "LABEL"
        from T_BI_CLIENT_INFORMATION t
        <where>
            <!-- 用户Grid查询 and 条件-->
            <if test="customerType != null and customerType!=''">
                t.CUSTOMER_TYPE = #{customerType,jdbcType=VARCHAR}
            </if>
            <if test="isAll != null and isAll!=''">
                AND t.DECLARE_CODE is not null
            </if>
            and t.TRADE_CODE = #{tradeCode,jdbcType=VARCHAR}
            and t.STATUS!='1'
        </where>
        order BY t.DECLARE_CODE
    </select>
    <select id="isCustomerCode" resultType="int" parameterType="map">
        SELECT COUNT(*) FROM T_BI_CLIENT_INFORMATION t
        <where>
            <if test="customerCode != null and customerCode !=''">
                and t.CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
            </if>
            and t.TRADE_CODE = #{tradeCode,jdbcType=VARCHAR}
            <if test="customerType != null and customerType != ''">
                and t.CUSTOMER_TYPE = #{customerType,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
    <select id="oneNameByCode" resultType="int" parameterType="map">
        SELECT COUNT(*) FROM (select SID,CUSTOMER_CODE,COMPANY_NAME,TRADE_CODE from T_BI_CLIENT_INFORMATION where
        customer_type!='COM' union all select SID,company_code,COMPANY_NAME,TRADE_CODE from t_bi_out_enterprise ) t
        <where>
            <if test="customerCode != null and customerCode !=''">
                and t.CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
            </if>
            <if test="sid != null and sid !=''">
                and t.SID != #{sid,jdbcType=VARCHAR}
            </if>
            and t.TRADE_CODE = #{tradeCode,jdbcType=VARCHAR}
            and t.COMPANY_NAME != #{companyName,jdbcType=VARCHAR}
        </where>
    </select>
    <select id="isHSCode" resultType="int" parameterType="map">
        SELECT COUNT(*) FROM T_BI_CLIENT_INFORMATION t
        <where>
            <if test="sid != null and sid !=''">
                and t.SID != #{sid,jdbcType=VARCHAR}
            </if>
            <if test="declareCode != null and declareCode !=''">
                and t.DECLARE_CODE = #{declareCode,jdbcType=VARCHAR}
            </if>
            and t.TRADE_CODE = #{tradeCode,jdbcType=VARCHAR}
            <if test="customerType != null and customerType != ''">
                and t.CUSTOMER_TYPE = #{customerType,jdbcType=VARCHAR}
            </if>
            <if test="customerCode != null and customerCode !=''">
                and t.CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <resultMap id="biTransformResultMap" type="com.dcjet.cs.bi.model.BiTransform">
        <id column="SID" property="sid" jdbcType="VARCHAR"/>
        <result column="ORIGIN" property="origin" jdbcType="VARCHAR"/>
        <result column="DEST" property="dest" jdbcType="VARCHAR"/>
        <result column="RATE" property="rate" jdbcType="VARCHAR"/>
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="INSERT_USER" property="insertUser" jdbcType="VARCHAR"/>
        <result column="INSERT_TIME" property="insertTime" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="VARCHAR"/>
        <result column="TYPE" property="type" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Transform_Column_List">
     t.SID
     ,t.ORIGIN
     ,t.DEST
     ,t.RATE
     ,t.TRADE_CODE
     ,t.INSERT_USER
     ,t.INSERT_TIME
     ,t.UPDATE_USER
     ,t.UPDATE_TIME
     ,t.TYPE
    </sql>
    <sql id="transform_condition">
        <if test="origin != null and origin != ''">
            and ORIGIN = #{origin}
        </if>
        <if test="dest != null and dest != ''">
            and t.DEST = #{dest}
        </if>
        <if test="type != null and type != ''">
            and t.TYPE = #{type}
        </if>
        and (t.TRADE_CODE = #{tradeCode} or t.TRADE_CODE = '9999999999')
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getTransformInfo" resultMap="biTransformResultMap"
            parameterType="com.dcjet.cs.bi.model.BiTransform">
        SELECT
        <include refid="Transform_Column_List"/>
        FROM T_BI_TRANSFORM t
        <where>
            <include refid="transform_condition"></include>
        </where>
    </select>

    <select id="selectCutAndFodInfo" resultType="com.dcjet.cs.dto.bi.BiAeoDto">
        select
            CUSTOMER_CODE || '|' || CUSTOMER_TYPE as CUSTOMER_CODE,
            case when CUSTOMER_TYPE = 'CUT' then COMPANY_NAME || '(报关行)'
            when CUSTOMER_TYPE = 'FOD' then COMPANY_NAME || '(货代)'
            else COMPANY_NAME end COMPANY_NAME,
            CUSTOMER_CODE as ACTUAL_CUSTOMER_CODE,
            COMPANY_NAME as ACTUAL_COMPANY_NAME,
            DECLARE_CODE as CUSTOMER_TRADE_CODE,
            CUSTOMER_TYPE
        from T_BI_CLIENT_INFORMATION
        where TRADE_CODE = #{tradeCode}
        and CUSTOMER_TYPE in ('CUT','FOD')
        order by CUSTOMER_TYPE,CUSTOMER_CODE
    </select>

    <select id="getlistBiClient" resultType="com.dcjet.cs.bi.model.BiClientInformation">
        SELECT t.*
        FROM t_bi_client_information t
        WHERE t.TRADE_CODE = #{tradeCode}
          and t.STATUS!='1'
          AND t.CUSTOMER_TYPE = #{customerType}
          AND DECLARE_CODE = #{tradeCode}
    </select>
    <select id="getListbiCli" resultType="com.dcjet.cs.bi.model.BiClientInformation">
        SELECT t.*
        FROM t_bi_client_information t
        WHERE t.TRADE_CODE = #{tradeCode}
        and t.STATUS!='1'
        AND t.CUSTOMER_TYPE = #{customerType}
        <if test="customerType=='COM'.toString()">
            AND DECLARE_CODE = #{declareCode}
        </if>
        <if test="customerType=='CLI'.toString()">
            AND customer_code = #{declareCode}
        </if>
    </select>
    <select id="selectBiClientCom" resultType="com.dcjet.cs.bi.model.BiClientInformation">
        select *
        FROM t_bi_client_information t
        where customer_type = 'COM'
          and trade_code = #{tradeCode}
          and declare_code = #{tradeCode}
    </select>

    <select id="selectBiClientCom2" resultType="com.dcjet.cs.bi.model.BiClientInformation">
        select *
        FROM t_bi_client_information t
        where customer_type = 'COM'
          and trade_code = #{tradeCode}
          and declare_code = #{declareCode}
    </select>

    <select id="selectBiClientCuts" resultType="com.dcjet.cs.bi.model.BiClientInformation">
        select *
        FROM t_bi_client_information
        where customer_type = 'CUT'
          and trade_code = #{tradeCode}
          and status = '0'
          and customer_code = #{declareCodeCustoms}
    </select>
    <select id="selectBiClients" resultType="com.dcjet.cs.bi.model.BiClientInformation">
        select *
        FROM t_bi_client_information
        where customer_type = 'CUT'
          and trade_code = #{tradeCode}
          and status = '0'
          and credit_code = #{entryDeclareScc}
    </select>
</mapper>
