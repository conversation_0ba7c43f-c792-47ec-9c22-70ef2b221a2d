<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.bi.dao.BiBilltoMapper">
    <sql id="Base_Column_List">
        SID
        ,BILL_TO_CODE
     ,BILL_TO_NAME
     ,BILL_TO_ADDRESS
     ,REMARK
     ,INSERT_USER
     ,INSERT_TIME
     ,UPDATE_USER
     ,UPDATE_TIME
     ,TRADE_CODE
     ,HEAD_ID
     ,INSERT_USER_NAME
     ,UPDATE_USER_NAME
     ,CLIENT_CODE
    </sql>
    <sql id="condition">
        <if test="billToCode != null and billToCode != ''">
            and BILL_TO_CODE = #{billToCode}
        </if>
        <if test="billToName != null and billToName != ''">
            and BILL_TO_NAME = #{billToName}
        </if>
        <if test="billToAddress != null and billToAddress != ''">
            and BILL_TO_ADDRESS = #{billToAddress}
        </if>
        <if test="remark != null and remark != ''">
            and REMARK = #{remark}
        </if>
        <if test="tradeCode != null and tradeCode != ''">
            and TRADE_CODE = #{tradeCode}
        </if>
        <if test="headId != null and headId != ''">
            and HEAD_ID = #{headId}
        </if>
        <if test="insertUserName != null and insertUserName != ''">
            and INSERT_USER_NAME = #{insertUserName}
        </if>
        <if test="updateUserName != null and updateUserName != ''">
            and UPDATE_USER_NAME = #{updateUserName}
        </if>
        <if test="clientCode != null and clientCode != ''">
            and CLIENT_CODE = #{clientCode}
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList"  resultType="com.dcjet.cs.bi.model.BiBillto" parameterType="com.dcjet.cs.bi.model.BiBillto">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        T_BI_BILLTO t
        <where>
            <include refid="condition"></include>
        </where>
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_BI_BILLTO t where t.SID in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
    <select id="selectComboByCode" resultType="map" parameterType="map">
        select distinct t.BILL_TO_CODE as "VALUE", t.BILL_TO_NAME as "LABEL"
        from T_BI_BILLTO t
        left join T_BI_CLIENT_INFORMATION B on t.head_ID=B.SID
        <where>
            and B.CUSTOMER_CODE = #{customerCode}
            and B.TRADE_CODE = #{tradeCode}
        </where>
        order BY t.BILL_TO_CODE
    </select>
</mapper>
