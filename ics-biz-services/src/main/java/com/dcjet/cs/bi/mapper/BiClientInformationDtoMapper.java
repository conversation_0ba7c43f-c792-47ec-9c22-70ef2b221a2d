package com.dcjet.cs.bi.mapper;

import com.dcjet.cs.bi.model.BiClientInformation;
import com.dcjet.cs.bi.model.BiTransform;
import com.dcjet.cs.dto.bi.BiClientInformationDto;
import com.dcjet.cs.dto.bi.BiClientInformationParam;
import com.dcjet.cs.dto.bi.BiTransformDto;
import com.dcjet.cs.dto.bi.BiTransformParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2019-4-18
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BiClientInformationDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BiClientInformationDto toDto(BiClientInformation po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BiClientInformation toPo(BiClientInformationParam param);
    /**
     * 数据库原始数据更新
     * @param biClientInformationParam
     * @param biClientInformation
     */
    void updatePo(BiClientInformationParam biClientInformationParam, @MappingTarget BiClientInformation biClientInformation);
    default void patchPo(BiClientInformationParam biClientInformationParam, BiClientInformation biClientInformation) {
        // TODO 自行实现局部更新
    }

    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BiTransformDto toDto(BiTransform po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BiTransform toPo(BiTransformParam param);

}
