package com.dcjet.cs.bi.dao;

import com.dcjet.cs.bi.model.BiClientInformation;
import com.dcjet.cs.bi.model.BiTransform;
import com.dcjet.cs.dto.bi.BiAeoDto;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

/**
 * generated by Generate 神码
 * BiClientInformation
 *
 * <AUTHOR>
 * @date: 2019-4-18
 */
public interface BiClientInformationMapper extends Mapper<BiClientInformation> {

    /**
     * 查询获取数据
     *
     * @param biClientInformation
     * @return
     */
    List<BiClientInformation> getList(BiClientInformation biClientInformation);

    /***
     *
     */
    List<BiClientInformation> findByCodeAndType(@Param("tradeCode") String tradeCode, @Param("customerCode") String customerCode, @Param("type") List<String> type);

    List<BiClientInformation> findByCodeAndType2(@Param("tradeCode") String tradeCode
            , @Param("customerCode") String customerCode
            , @Param("type") List<String> type);


    /**
     * 批量删除
     *
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    /**
     * 获取报关行代码
     *
     * @param param 参数
     * @return
     * @author: 沈振宇
     * @date: 2019-5-21
     */
    Integer isCustomerCode(Map<String, Object> param);

    Integer oneNameByCode(Map<String, Object> param);

    /**
     * 获取报关行代码
     *
     * @param param 参数
     * @return
     * @author: 沈振宇
     * @date: 2019-5-21
     */
    Integer isHSCode(Map<String, Object> param);

    /**
     * 查询当前企业的维护的报关行数据及自身企业数据
     *
     * @param param 传入参数
     * @return list数据
     * <AUTHOR>
     */
    List<Map<String, Object>> selectAllCombox(Map<String, Object> param);

    List<BiTransform> getTransformInfo(BiTransform biTransform);

    List<BiAeoDto> selectCutAndFodInfo(String tradeCode);

    BiClientInformation getlistBiClient(@Param("customerType") String customerType, @Param("tradeCode") String tradeCode);

    BiClientInformation getListbiCli(@Param("customerType") String customerType, @Param("declareCode") String declareCode, @Param("tradeCode") String company);

    BiClientInformation selectBiClientCom(@Param("tradeCode") String tradeCode);

    BiClientInformation selectBiClientCom2(@Param("tradeCode") String tradeCode,@Param("declareCode") String declareCode);

    List<BiClientInformation> selectBiClientCuts(@Param("declareCodeCustoms") String declareCodeCustoms, @Param("tradeCode") String tradeCod);

    List<BiClientInformation> selectBiClients(@Param("entryDeclareScc") String entryDeclareScc, @Param("tradeCode") String tradeCod);
}
