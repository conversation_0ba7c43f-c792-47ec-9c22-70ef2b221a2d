package com.dcjet.cs.bi.model;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Id;
import lombok.Getter;
import lombok.Setter;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import javax.persistence.Transient;
import java.io.Serializable;


/**
 * shipfrom
 *
 * <AUTHOR>
 * @date 2025-03-01 09:59:28
 */
@Getter
@Setter
@Table(name = "t_bi_shipfrom")
public class BiShipfrom implements Serializable{
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     * 字符类型(40)
     * 必填
     */
    @Id
    @Column(name = "sid")
    private String sid;

    /**
     * shipFrom代码
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "ship_from_code")
    private String shipFromCode;

    /**
     * shipFrom名称
     * 字符类型(255)
     * 非必填
     */
    @Column(name = "ship_from_name")
    private String shipFromName;

    /**
     * shipFrom地址
     * 字符类型(512)
     * 非必填
     */
    @Column(name = "ship_from_address")
    private String shipFromAddress;

    /**
     * 备注
     * 字符类型(255)
     * 非必填
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 创建人
     * 字符类型(50)
     * 必填
     */
    @Column(name = "insert_user")
    private String insertUser;

    /**
     * 创建日期
     * timestamp
     * 必填
     */
    @Column(name = "insert_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;

    /**
     * 创建日期-开始时间
     */
    @Transient
    private String insertTimeFrom;

    /**
     * 创建日期-结束时间
     */
    @Transient
    private String insertTimeTo;

    /**
     * 修改人
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 修改时间
     * timestamp
     * 非必填
     */
    @Column(name = "update_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 修改时间-开始时间
     */
    @Transient
    private String updateTimeFrom;

    /**
     * 修改时间-结束时间
     */
    @Transient
    private String updateTimeTo;

    /**
     * 所属企业编码
     * 字符类型(20)
     * 非必填
     */
    @Column(name = "trade_code")
    private String tradeCode;

    /**
     * 供应商SID
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "head_id")
    private String headId;

    /**
     * 制单人姓名
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "insert_user_name")
    private String insertUserName;

    /**
     * 修改人姓名
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "update_user_name")
    private String updateUserName;

    /**
     * 供应商CODE
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "supplier_code")
    private String supplierCode;


}