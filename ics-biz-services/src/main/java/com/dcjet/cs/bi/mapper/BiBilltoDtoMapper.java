package com.dcjet.cs.bi.mapper;

import com.dcjet.cs.bi.model.BiBillto;
import com.dcjet.cs.dto.bi.BiBilltoDto;
import com.dcjet.cs.dto.bi.BiBilltoParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2021-4-21
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BiBilltoDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BiBilltoDto toDto(BiBillto po);

    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BiBillto toPo(BiBilltoParam param);

    /**
     * 数据库原始数据更新
     *
     * @param biBilltoParam
     * @param biBillto
     */
    void updatePo(BiBilltoParam biBilltoParam, @MappingTarget BiBillto biBillto);

    default void patchPo(BiBilltoParam biBilltoParam, BiBillto biBillto) {
        // TODO 自行实现局部更新
    }
}
