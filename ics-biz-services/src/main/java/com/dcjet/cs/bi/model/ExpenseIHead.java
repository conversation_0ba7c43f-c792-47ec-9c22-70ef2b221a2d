package com.dcjet.cs.bi.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;

@Setter
@Getter
@Table(name = "T_BIZ_EXPENSE_I_HEAD")
public class ExpenseIHead {
    @Id
    @Column(name = "SID")
    private String sid;
    //单据号
    @Column(name = "DOCUMENT_NUMBER")
    private String documentNumber;
    //业务类型
    @Column(name = "BUSINESS_TYPE")
    private String businessType;
    //预付标志
    @Column(name = "ADVANCE_FLAG")
    private String advanceFlag;
    //部门
    @Column(name = "DEPT_NAME")
    private String deptName;
    //收款方
    @Column(name = "PAYEE")
    private String payee;
    //费用类型
    @Column(name = "EXPENSE_TYPE")
    private String expenseType;
    //合同号
    @Column(name = "CONTRACT_NUMBER")
    private String contractNumber;
    //进/出货单号
    @Column(name = "ORDER_NUMBER")
    private String orderNumber;
    //币种
    @Column(name = "CURR")
    private String curr;
    //预付标志
    @Column(name = "ADVANCE_MARK")
    private String advanceMark;
    //发送用友
    @Column(name = "SEND_UFIDA")
    private String sendUfida;
    //备注
    @Column(name = "REMARK")
    private String remark;
    //单据状态
    @Column(name = "STATE")
    private String state;
    //制单人
    @Column(name = "CREATE_USER")
    private String createUser;
    //制单日期
    @Column(name = "CREATE_USER_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createUserTime;
    //确认时间
    @Column(name = "CONFIRMATION_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date confirmationTime;

    @Column(name = "TRADE_CODE")
    private String tradeCode;

    @Column(name = "INSERT_USER")
    private String insertUser;

    @Column(name = "INSERT_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;

    @Column(name = "UPDATE_USER")
    private String updateUser;

    @Column(name = "UPDATE_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    @Transient
    private String createUserTimeFrom;
    @Transient
    private String createUserTimeTo;
    //总金额
    @Transient
    private BigDecimal totalAmount;
}
