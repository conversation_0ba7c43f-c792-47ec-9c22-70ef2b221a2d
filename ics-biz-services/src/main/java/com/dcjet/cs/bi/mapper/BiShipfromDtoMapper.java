package com.dcjet.cs.bi.mapper;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
import com.dcjet.cs.bi.model.BiShipfrom;
import com.dcjet.cs.dto.bi.BiShipfromParam;
import com.dcjet.cs.dto.bi.BiShipfromDto;

/**
 * BiShipfromDto
 *
 * <AUTHOR>
 * @date 2025-03-01 09:59:28
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BiShipfromDtoMapper {

    /**
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BiShipfromDto toDto(BiShipfrom po);

    /**
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BiShipfrom toPo(BiShipfromParam param);

    /**
     * 数据库原始数据更新
     * @param biShipfromParam
     * @param BiShipfrom
     */
    void updatePo(BiShipfromParam biShipfromParam, @MappingTarget BiShipfrom biShipfrom);

    default void patchPo(BiShipfromParam biShipfromParam , BiShipfrom biShipfrom) {
        // TODO 自行实现局部更新
    }
}