package com.dcjet.cs.bi.mapper;

import com.dcjet.cs.bi.model.BizMaterialInformation;
import com.dcjet.cs.dto.bi.BizMaterialInformationDto;
import com.dcjet.cs.dto.bi.BizMaterialInformationParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-3-12
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizMaterialInformationDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizMaterialInformationDto toDto(BizMaterialInformation po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizMaterialInformation toPo(BizMaterialInformationParam param);
    /**
     * 数据库原始数据更新
     * @param bizMaterialInformationParam
     * @param bizMaterialInformation
     */
    void updatePo(BizMaterialInformationParam bizMaterialInformationParam, @MappingTarget BizMaterialInformation bizMaterialInformation);
    default void patchPo(BizMaterialInformationParam bizMaterialInformationParam, BizMaterialInformation bizMaterialInformation) {
        // TODO 自行实现局部更新
    }
}
