package com.dcjet.cs.bi.model.middle;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * 业务单据：付费用通知
 */
@Table(name = "O_BILL_JCK")
@Data
public class OBillJck {
    /**
     * 单据类型
     */
    @Column(name = "RD_TYPE")
    private String rdType = "RR";

    /**
     * 单据主键
     */
    @Column(name = "BILLID")
    private String billid;

    /**
     * 单据编号
     */
    @Column(name = "BILLCODE")
    private String billcode;

    /**
     * 单据日期
     */
    @Column(name = "BILLDATE")
    private String billdate;

    /**
     * 库存组织
     */
    @Column(name = "RDCENTER")
    private String rdcenter = "JCK01";

    /**
     * 公司名称
     */
    @Column(name = "COMPANY")
    private String company = "中国烟草上海进出口有限责任公司";

    /**
     * 部门编码
     */
    @Column(name = "DEPTDOC")
    private String deptdoc = "04";

    /**
     * 业务人员
     */
    @Column(name = "PERSON")
    private String person;

    /**
     * 制单人员
     */
    @Column(name = "MAKER")
    private String maker;

    /**
     * 客商编码
     */
    @Column(name = "CUST")
    private String cust;

    /**
     * 库存组织名称
     */
    @Column(name = "NAME_CALBODY")
    private String nameCalbody = "个别计价";

    /**
     * 部门名称
     */
    @Column(name = "NAME_DEPTDOC")
    private String nameDeptdoc = "业务一部";

    /**
     * 业务员名称
     */
    @Column(name = "NAME_PSNDOC")
    private String namePsndoc;

    /**
     * 操作员名称
     */
    @Column(name = "NAME_OPERATOR")
    private String nameOperator;

    /**
     * 客商名称
     */
    @Column(name = "NAME_CUMANDOC")
    private String nameCumandoc;

    /**
     * 序列
     */
    @Column(name = "MQ_LSH")
    private Integer mqLsh;

    @Column(name = "MQ_OP")
    private String mqOp = "i";
    @Column(name = "MQ_ST")
    private String mqSt = "0";
    @Column(name = "MQ_COUNT")
    private Integer mqCount = 1;
    @Column(name = "PK_CORP")
    private String pkCorp = "1022";

    /**
     * 系统时间
     */
    @Column(name = "TS")
    private String ts;

    @Column(name = "DR")
    private Integer dr = 0;

    /**
     * 订单号
     */
    @Column(name = "HTH")
    private String hth;

    @Column(name = "FILENO")
    private String fileno;

    /**
     * 仓库代码
     */
    @Column(name = "STORAGE")
    private String storage;

    /**
     * 仓库名称
     */
    @Column(name = "NAME_STORAGE")
    private String nameStorage;

    @Column(name = "INRDCENTER")
    private String inrdcenter;
    @Column(name = "NAME_INCALBODY")
    private String nameIncalbody;
    @Column(name = "INSTORAGE")
    private String instorage;
    @Column(name = "NAME_INSTORAGE")
    private String nameInstorage;
    @Column(name = "ISADVANCE")
    private String isadvance;
    @Column(name = "ACCBANKNO")
    private String accbankno;
    @Column(name = "NAME_ACCBANK")
    private String nameAccbank;

    /**
     * 币种编码
     */
    @Column(name = "CURRTYPECODE")
    private String currtypecode = "CNY";

    /**
     * 币种名称
     */
    @Column(name = "CURRTYPENAME")
    private String currtypename = "人民币";

    @Column(name = "JSCODE")
    private String jscode;
    @Column(name = "JSNAME")
    private String jsname;
    @Column(name = "BILLNUM")
    private String billnum;
    @Column(name = "PAYNOTENO")
    private String paynoteno;
    @Column(name = "FEETYPE")
    private String feetype;

    /**
     * 总费用
     */
    @Column(name = "TOTALMONEY")
    private BigDecimal totalmoney;

    /**
     * 发票号
     */
    @Column(name = "BILLNO")
    private String billno;

    @Column(name = "ADJUSTDATE")
    private String adjustdate;

    /**
     * 表体行数
     */
    @Column(name = "ROW_COUNT")
    private Integer rowCount;

    /**
     * 汇率
     */
    @Column(name = "CURRENTRATE")
    private BigDecimal currentrate = new BigDecimal("1");

    /**
     * 本币金额
     */
    @Column(name = "BBJE")
    private BigDecimal bbje;
}