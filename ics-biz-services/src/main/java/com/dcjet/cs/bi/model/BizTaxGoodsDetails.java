package com.dcjet.cs.bi.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
public class BizTaxGoodsDetails {
    @JsonProperty("taxType")
    private String taxType;

    @JsonProperty("taxAmount")
    private String taxAmount;

    @JsonProperty("list")
    private List<ListDTO> list;


    @NoArgsConstructor
    @Data
    public static class ListDTO {
        @JsonProperty("codeTs")
        private String codeTs;
        @JsonProperty("gName")
        private String gName;
        @JsonProperty("quantity")
        private String quantity;
        @JsonProperty("unit")
        private String unit;
        @JsonProperty("curr")
        private String curr;
        @JsonProperty("exchangeRate")
        private String exchangeRate;
        @JsonProperty("dutyValue")
        private String dutyValue;
        @JsonProperty("valoremRate")
        private String valoremRate;
        @JsonProperty("qtyRate")
        private String qtyRate;
        @JsonProperty("taxAmount")
        private String taxAmount;
    }
}
