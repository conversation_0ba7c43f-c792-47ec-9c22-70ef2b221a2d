package com.dcjet.cs.bi.service;

import com.dcjet.cs.bi.dao.BiShipfromMapper;
import com.dcjet.cs.bi.mapper.BiShipfromDtoMapper;
import com.dcjet.cs.bi.model.BiShipfrom;
import com.dcjet.cs.dto.bi.BiShipfromDto;
import com.dcjet.cs.dto.bi.BiShipfromParam;

import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;

import java.util.*;
import java.util.stream.Collectors;

import xdoi18n.XdoI18nUtil;

/**
 * BiShipfrom业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2025-03-01 09:59:28
 * 翻译使用：throw new ErrorException(400, XdoI18nUtil.t("xxxxxxxxxx"));
 */
@Service
public class BiShipfromService extends BaseService<BiShipfrom> {

    private static final Logger log = LoggerFactory.getLogger(BiShipfromService.class);

    @Resource
    private BiShipfromMapper biShipfromMapper;

    @Resource
    private BiShipfromDtoMapper biShipfromDtoMapper;

    @Override
    public Mapper<BiShipfrom> getMapper() {
        return biShipfromMapper;
    }



    /**
     * 获取分页信息
     *
     * @param biShipfromParam 查询参数
     * @param pageParam               分页参数
     * @return 分页结果
     */
    public ResultObject<List<BiShipfromDto>> getListPaged(BiShipfromParam biShipfromParam, PageParam pageParam,UserInfoToken userInfo) {
        // 启用分页查询
        BiShipfrom biShipfrom = biShipfromDtoMapper.toPo(biShipfromParam);
        biShipfrom.setTradeCode(userInfo.getCompany());
        Page<BiShipfrom> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
            .doSelectPage(() -> biShipfromMapper.getList( biShipfrom));
        // 将PO转为DTO返回给前端
        List<BiShipfromDto> biShipfromDtoList = page.getResult().stream()
            .map(biShipfromDtoMapper::toDto)
            .collect(Collectors.toList());

        return ResultObject.createInstance(biShipfromDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 新增记录
     *
     * @param biShipfromParam 插入参数
     * @param userInfo                用户信息
     * @return 新增的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BiShipfromDto insert(BiShipfromParam biShipfromParam, UserInfoToken userInfo) {
        BiShipfrom biShipfrom = biShipfromDtoMapper.toPo(biShipfromParam);
        
        // 规范固定字段
        String sid = UUID.randomUUID().toString();
        biShipfrom.setSid(sid);
        biShipfrom.setInsertUser(userInfo.getUserNo());
        biShipfrom.setInsertTime(new Date());
        biShipfrom.setTradeCode(userInfo.getCompany());

        // 新增数据
        int insertStatus = biShipfromMapper.insert(biShipfrom);

        // 新增完成后 将数据转为DTO返回给前端
        return insertStatus > 0 ? biShipfromDtoMapper.toDto(biShipfrom) : null;
    }

    /**
     * 修改记录
     *
     * @param biShipfromParam 更新参数
     * @param userInfo                用户信息
     * @return 更新后的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BiShipfromDto update(BiShipfromParam biShipfromParam, UserInfoToken userInfo) {
        BiShipfrom biShipfrom = biShipfromMapper.selectByPrimaryKey(biShipfromParam.getSid());
        biShipfromDtoMapper.updatePo(biShipfromParam, biShipfrom);
        biShipfrom.setUpdateUser(userInfo.getUserNo());
        biShipfrom.setUpdateTime(new Date());

        // 更新数据
        int update = biShipfromMapper.updateByPrimaryKey(biShipfrom);
        return update > 0 ? biShipfromDtoMapper.toDto(biShipfrom) : null;
    }

    /**
     * 批量删除记录
     *
     * @param sids     要删除的SID列表
     * @param userInfo 用户信息
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
       biShipfromMapper.deleteBySids(sids);
    }



    /**
     * 功能描述:查询所有数据(导出查询)
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BiShipfromDto> selectAll(BiShipfromParam exportParam, UserInfoToken userInfo) {
        BiShipfrom biShipfrom = biShipfromDtoMapper.toPo(exportParam);
        biShipfrom.setTradeCode(userInfo.getCompany());
        List<BiShipfromDto> biShipfromDtos = new ArrayList<>();
        List<BiShipfrom> biShipfromLists = biShipfromMapper.getList(biShipfrom);
        if (CollectionUtils.isNotEmpty(biShipfromLists)) {
           biShipfromDtos = biShipfromLists.stream().map(head -> {
                    BiShipfromDto dto =  biShipfromDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return biShipfromDtos;
    }

}