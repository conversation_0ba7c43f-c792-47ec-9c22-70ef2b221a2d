package com.dcjet.cs.bi.mapper;

import com.dcjet.cs.bi.model.BiCustomerParams;
import com.dcjet.cs.dto.bi.BiCustomerParamsDto;
import com.dcjet.cs.dto.bi.BiCustomerParamsParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2019-6-6
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BiCustomerParamsDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BiCustomerParamsDto toDto(BiCustomerParams po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BiCustomerParams toPo(BiCustomerParamsParam param);
    /**
     * 数据库原始数据更新
     * @param biCustomerParamsParam
     * @param biCustomerParams
     */
    void updatePo(BiCustomerParamsParam biCustomerParamsParam, @MappingTarget BiCustomerParams biCustomerParams);
    default void patchPo(BiCustomerParamsParam biCustomerParamsParam, BiCustomerParams biCustomerParams) {
        // TODO 自行实现局部更新
    }
}
