package com.dcjet.cs.bi.model;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2025-3-12
 */
@Setter
@Getter
@Table(name = "t_biz_material_information")
public class BizMaterialInformation implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 
     */
	 @Id
	@Column(name = "id")
	private  String sid;
	/**
     * 
     */
	@Column(name = "business_type")
	private  String businessType;
	/**
     * 
     */
	@Column(name = "validate_status")
	private  String dataStatus;
	/**
     * 
     */
	@Column(name = "version_no")
	private  String versionNo;
	/**
     * 
     */
	@Column(name = "trade_code")
	private  String tradeCode;
	/**
     * 
     */
	@Column(name = "parent_id")
	private  String parentId;
	/**
     * 创建人
     */
	@Column(name = "create_by")
	private  String insertUser;
	/**
     * 创建日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "create_time")
	private  Date insertTime;
	/**
     * 
     */
	@Column(name = "update_by")
	private  String updateUser;
	/**
     * 
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "update_time")
	private  Date updateTime;
	/**
     * 
     */
	@Column(name = "insert_user_name")
	private  String insertUserName;
	/**
     * 
     */
	@Column(name = "update_user_name")
	private  String updateUserName;
	/**
     * 
     */
	@Column(name = "extend1")
	private  String extend1;
	/**
     * 
     */
	@Column(name = "extend2")
	private  String extend2;
	/**
     * 
     */
	@Column(name = "extend3")
	private  String extend3;
	/**
     * 
     */
	@Column(name = "extend4")
	private  String extend4;
	/**
     * 
     */
	@Column(name = "extend5")
	private  String extend5;
	/**
     * 
     */
	@Column(name = "extend6")
	private  String extend6;
	/**
     * 
     */
	@Column(name = "extend7")
	private  String extend7;
	/**
     * 
     */
	@Column(name = "extend8")
	private  String extend8;
	/**
     * 
     */
	@Column(name = "extend9")
	private  String extend9;
	/**
     * 
     */
	@Column(name = "extend10")
	private  String extend10;
	/**
     * 商品名称
     */
	@Column(name = "g_name")
	@JsonProperty("gName")
	private  String gname;
	/**
     * 中文简称
     */
	@Column(name = "short_cn")
	private  String shortCn;
	/**
     * 开票名称
     */
	@Column(name = "billing_name")
	private  String billingName;
	/**
     * 英文全称
     */
	@Column(name = "full_en_name")
	private  String fullEnName;
	/**
     * 英文简称
     */
	@Column(name = "short_en_name")
	private  String shortEnName;
	/**
     * 商品类别
     */
	@Column(name = "merchandise_categories")
	private  String merchandiseCategories;
	/**
     * 国家产品目录
     */
	@Column(name = "national_product_catalogue")
	private  String nationalProductCatalogue;
	/**
     * 条形码
     */
	@Column(name = "bar_code")
	private  String barCode;
	/**
     * 常用标志
     */
	@Column(name = "common_mark")
	private  String commonMark;
	@Transient
	private List<String> commonMarkList;
	/**
     * 包装信息
     */
	@Column(name = "packaging_information")
	private  String packagingInformation;
	/**
     * 中烟MIS编码
     */
	@Column(name = "mis_code")
	private  String misCode;
	/**
     * 统计名称
     */
	@Column(name = "statistical_name")
	private  String statisticalName;
	/**
     * 报送税务总局牌号名称方式
     */
	@Column(name = "name_method")
	private  String nameMethod;
	/**
     * 国内不含税调拨价（RMB）
     */
	@Column(name = "tax_exclusive")
	private  BigDecimal taxExclusive;
	/**
     * 含税单价
     */
	@Column(name = "including_tax")
	private  BigDecimal includingTax;
	/**
     * 税率
     */
	@Column(name = "tax_rate")
	private  BigDecimal taxRate;
	/**
     * 不含税单价
     */
	@Column(name = "price_excluding_tax")
	private  BigDecimal priceExcludingTax;
	/**
     * 备注
     */
	@Column(name = "note")
	private  String note;
	/**
     * 数据状态
     */
	@Column(name = "data_state")
	private  String dataState;
	/**
     * 供应商
     */
	@Column(name = "supplier_code")
	private  String supplierCode;
	/**
     * 供应商折扣率
     */
	@Column(name = "supplier_discount_rate")
	private  BigDecimal supplierDiscountRate;
	/**
     * 进口单价
     */
	@Column(name = "import_unit_price")
	private  BigDecimal importUnitPrice;
	/**
     * 币种
     */
	@Column(name = "curr")
	private  String curr;

	@Transient
	private String merchantNameCn;



	/**
	 * 创建人部门编码
	 */
	@Column(name = "sys_org_code")
	private  String sysOrgCode;
}
