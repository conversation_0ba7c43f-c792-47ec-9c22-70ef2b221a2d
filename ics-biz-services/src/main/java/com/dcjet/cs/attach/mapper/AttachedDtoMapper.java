package com.dcjet.cs.attach.mapper;


import com.dcjet.cs.attach.model.Attached;
import com.dcjet.cs.dto.attach.AttachedDto;
import com.dcjet.cs.dto.attach.AttachedLicenceDto;
import com.dcjet.cs.dto.attach.AttachedParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2019-4-25
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AttachedDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    AttachedDto toDto(Attached po);
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    AttachedLicenceDto toLicenceDto(Attached po);

    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    Attached toPo(AttachedParam param);
    /**
     * 数据库原始数据更新
     * @param attachedParam
     * @param attached
     */
    void updatePo(AttachedParam attachedParam, @MappingTarget Attached attached);
    default void patchPo(AttachedParam attachedParam, Attached attached) {
        // TODO 自行实现局部更新
    }
}
