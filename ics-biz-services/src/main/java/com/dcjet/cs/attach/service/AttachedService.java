package com.dcjet.cs.attach.service;

import com.dcjet.cs.attach.dao.AttachedMapper;
import com.dcjet.cs.attach.mapper.AttachedDtoMapper;
import com.dcjet.cs.attach.model.Attached;
import com.dcjet.cs.bi.model.BiCustomerParams;
import com.dcjet.cs.dto.attach.AttachedDto;
import com.dcjet.cs.dto.attach.AttachedParam;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.util.variable.CommonVariable;
import com.dcjet.cs.util.variable.DecVariable;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.json.JsonObjectMapper;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.KeyValuePair;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.file.XdoFileHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.*;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2019-4-25
 */
@Service
public class AttachedService extends BaseService<Attached> {
    @Resource
    private AttachedMapper attachedMapper;
    @Resource
    private AttachedDtoMapper attachedDtoMapper;
    @Override
    public Mapper<Attached> getMapper() {
        return attachedMapper;
    }
    @Resource(name = "eternalXdoFileHandler")
    public XdoFileHandler fileHandler;





    /**
     * 获取随附单据分页信息
     *
     * @param attachedParam
     * @param pageParam
     * @return
     * <AUTHOR>
     */
    public ResultObject<List<AttachedDto>> getListPaged(AttachedParam attachedParam, PageParam pageParam, UserInfoToken userInfo) {

        // 启用分页查询
        Attached attached = attachedDtoMapper.toPo(attachedParam);
        attached.setTradeCode(userInfo.getCompany());
        Page<Attached> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> attachedMapper.getList(attached));
        List<AttachedDto> attachedDtos = page.getResult().stream().map(head -> {
            AttachedDto dto = attachedDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<AttachedDto>> paged = ResultObject.createInstance(attachedDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }


    /**
     * 功能描述:修改
     *
     * @param attachedParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public AttachedDto update(AttachedParam attachedParam, UserInfoToken userInfo) throws Exception {
        Attached attached = attachedMapper.selectByPrimaryKey(attachedParam.getSid());
        // 删除旧文件
        fileHandler.deleteFile(attached.getFileName());

        attachedDtoMapper.updatePo(attachedParam, attached);
        attached.setUpdateUser(userInfo.getUserNo());
        attached.setUpdateTime(new Date());
        // 更新数据
        int update = attachedMapper.updateByPrimaryKey(attached);
        return update > 0 ? attachedDtoMapper.toDto(attached) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) throws Exception {
        // 删除文件
        Example example = new Example(Attached.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("sid", sids);
        criteria.andEqualTo("tradeCode", userInfo.getCompany());
        List<Attached> list = attachedMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(list)) {
            for (Attached attached : list) {
                fileHandler.deleteFile(attached.getFileName());
            }
        }
		attachedMapper.deleteBySids(sids);
    }

    public void deleteByBillSids(List<String> businessSid, UserInfoToken userInfo)
    {
        List<String> attachSids=attachedMapper.getSidList(userInfo.getCompany(),businessSid);
        if(attachSids.size()>0) {
            try {
                delete(attachSids, userInfo);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<AttachedDto> selectAll(AttachedParam exportParam, UserInfoToken userInfo) {
        Attached attached = attachedDtoMapper.toPo(exportParam);
        attached.setTradeCode(userInfo.getCompany());
        List<AttachedDto> attachedDtos = new ArrayList<>();
        List<Attached> attacheds = attachedMapper.getList(attached);
        if (CollectionUtils.isNotEmpty(attacheds)) {
            attachedDtos = attacheds.stream().map(head -> {
                AttachedDto dto = attachedDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return attachedDtos;
    }




    /**
     * 根据业务sid查询附件列表
     * @param sid 业主主键sid
     * @param userInfo 用户信息
     * @return 附件下拉列表
     */
    public ResultObject<List<AttachedDto>> getAttechedList(String sid, UserInfoToken userInfo) {
        return getAttechedList("",sid,userInfo);
    }

    /**
     * 根据业务sid查询附件列表
     * @param sid 业主主键sid
     * @param userInfo 用户信息
     * @param businessType  业务类型
     * @return 附件下拉列表
     */
    public ResultObject<List<AttachedDto>> getAttechedList(String businessType,String sid, UserInfoToken userInfo) {
        if(sid == null || "".equals(sid)) {
            throw new ErrorException(400, "业务主键不能为空");
        }
        ResultObject resultObject = ResultObject.createInstance(true,"查询成功！");
        try {
            Example example = new Example(Attached.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("businessSid", sid);
            criteria.andEqualTo("tradeCode", userInfo.getCompany());
            if(StringUtils.isNotBlank(businessType)) {
                criteria.andEqualTo("businessType", businessType);
            }
            List<Attached> attacheds = attachedMapper.selectByExample(example);
            List<AttachedDto> attachedDtos = attacheds.stream().map(head -> {
                AttachedDto dto = attachedDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
            resultObject.setMessage("查询成功！");
            resultObject.setData(attachedDtos);
        } catch (Exception e) {
            resultObject.setSuccess(false);
            resultObject.setMessage("查询成功！");
            e.printStackTrace();
        }
        return resultObject;
    }
    public List<Attached> getAttechedListToCopy(String businessType,String sid, UserInfoToken userInfo){
        Example example = new Example(Attached.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("businessSid", sid);
        criteria.andEqualTo("tradeCode", userInfo.getCompany());
        if(StringUtils.isNotBlank(businessType)) {
            criteria.andEqualTo("businessType", businessType);
        }
        List<Attached> attacheds = attachedMapper.selectByExample(example);
        return attacheds;
    }

    /**
     * 根据业务sid查询附件列表
     * @param sids
     * @param userInfo
     * @return
     */
    public ResultObject<List<AttachedDto>> getListByBusinessSids(List<String> sids, UserInfoToken userInfo) {
        if(CollectionUtils.isEmpty(sids)) {
            throw new ErrorException(400, "业务主键不能为空");
        }
        ResultObject resultObject = ResultObject.createInstance(true,"查询成功！");
        try {
            Example example = new Example(Attached.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andIn("businessSid", sids);
            criteria.andEqualTo("tradeCode", userInfo.getCompany());
            List<Attached> attacheds = attachedMapper.selectByExample(example);
            List<AttachedDto> attachedDtos = attacheds.stream().map(head -> {
                AttachedDto dto = attachedDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
            resultObject.setMessage("查询成功！");
            resultObject.setData(attachedDtos);
        } catch (Exception e) {
            resultObject.setSuccess(false);
            resultObject.setMessage("查询成功！");
            e.printStackTrace();
        }
        return resultObject;
    }



    /**
     * 功能描述:新增
     *
     * @param attached
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public AttachedDto insert(Attached attached, UserInfoToken userInfo) {
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        attached.setSid(sid);
        attached.setInsertUser(userInfo.getUserNo());
        attached.setInsertTime(new Date());
        attached.setTradeCode(userInfo.getCompany());
        // 新增数据
        int insertStatus = attachedMapper.insert(attached);
        return  insertStatus > 0 ? attachedDtoMapper.toDto(attached) : null;
    }



    public Attached selectByPrimarykey(String sId) {
        return attachedMapper.selectByPrimaryKey(sId);
    }


}
