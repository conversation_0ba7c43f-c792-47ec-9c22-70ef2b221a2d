package com.dcjet.cs.attach.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2019-4-25
 */
@Setter
@Getter
@Table(name = "T_ATTACHED")
public class Attached implements Serializable {
    private static final long serialVersionUID = 1L;

	/***
	 * 提单进口
	 */
	public static final String DEC_IM = "IM";

	/**
	 * 主键
	 */
	@Id
	@Column(name = "SID")
	private  String sid;
	/**
	 * 海关10位编码
	 */
	@Column(name = "TRADE_CODE")
	private  String tradeCode;
	/**
	 * 外键，关联手册/账册表头ID，或者清单、报核表头ID
	 */
	@Column(name = "HEAD_ID")
	private  String businessSid;
	/**
	 * 业务单证类型
	 */
	@Column(name = "BUSINESS_TYPE")
	private  String businessType;
	/**
	 * 随附单据类型
	 */
	@Column(name = "ACMP_TYPE")
	private  String acmpType;
	/**
	 * 随附单据编号
	 */
	@Column(name = "ACMP_NO")
	private  String acmpNo;
	/**
	 * 随附单据文件名称 企业端上传的文件名，含扩展名(非结构化必填)
	 */
	@Column(name = "FILE_NAME")
	private  String fileName;
	/**
	 * 备注
	 */
	@Column(name = "NOTE")
	private  String note;
	/**
	 * 导入文件名称
	 */
	@Column(name = "ORIGIN_FILE_NAME")
	private  String originFileName;
	/**
	 * 文件服务器返回链接地址
	 */
	@Column(name = "FDFS_ID")
	private  String fdfsId;
	/**
	 * 创建人
	 */
	@Column(name = "INSERT_USER")
	private  String insertUser;
	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "INSERT_TIME")
	private  Date insertTime;
	/**
	 * 创建时间-开始
	 */
	@Transient
	private String insertTimeFrom;
	/**
	 * 创建时间-结束
	 */
	@Transient
	private String insertTimeTo;
	/**
	 * 更新人
	 */
	@Column(name = "UPDATE_USER")
	private  String updateUser;
	/**
	 * 更新时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "UPDATE_TIME")
	private  Date updateTime;
	/**
	 * 更新时间-开始
	 */
	@Transient
	private String updateTimeFrom;
	/**
	 * 更新时间-结束
	 */
	@Transient
	private String updateTimeTo;

	/**
	 * 文件大小（KB）
	 */
	@Column(name = "file_size")
	private BigDecimal fileSize;

	/**
	 * 数据来源(0:手动上传，1:自动上传)
	 */
	@Column(name = "data_source")
	private String dataSource = "0";
}
