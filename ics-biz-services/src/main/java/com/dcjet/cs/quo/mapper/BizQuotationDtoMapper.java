package com.dcjet.cs.quo.mapper;
import com.dcjet.cs.dto.quo.*;
import com.dcjet.cs.quo.model.BizQuotation;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-5-20
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizQuotationDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizQuotationDto toDto(BizQuotation po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizQuotation toPo(BizQuotationParam param);
    /**
     * 数据库原始数据更新
     * @param bizQuotationParam
     * @param bizQuotation
     */
    void updatePo(BizQuotationParam bizQuotationParam, @MappingTarget BizQuotation bizQuotation);
    default void patchPo(BizQuotationParam bizQuotationParam, BizQuotation bizQuotation) {
        // TODO 自行实现局部更新
    }
}
