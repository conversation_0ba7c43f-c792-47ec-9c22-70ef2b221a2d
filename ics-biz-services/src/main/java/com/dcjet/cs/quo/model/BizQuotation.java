package com.dcjet.cs.quo.model;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2025-5-20
 */
@Setter
@Getter
@Table(name = "t_biz_quotation")
public class BizQuotation implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 主键
     */
	 @Id
	@Column(name = "sid")
	private  String sid;
	/**
     * 创建人
     */
	@Column(name = "create_by")
	private  String createBy;
	/**
     * 创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "create_time")
	private  Date createTime;
	/**
     * 创建人名称
     */
	@Column(name = "create_user_name")
	private  String createUserName;
	/**
     * 最后修改人
     */
	@Column(name = "update_by")
	private  String updateBy;
	/**
     * 最后修改时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "update_time")
	private  Date updateTime;
	/**
     * 最后修改人名称
     */
	@Column(name = "update_user_name")
	private  String updateUserName;
	/**
     * 企业编码
     */
	@Column(name = "trade_code")
	private  String tradeCode;
	/**
     * 创建人部门编码
     */
	@Column(name = "sys_org_code")
	private  String sysOrgCode;
	/**
     * 拓展字段1
     */
	@Column(name = "extend1")
	private  String extend1;
	/**
     * 拓展字段2
     */
	@Column(name = "extend2")
	private  String extend2;
	/**
     * 拓展字段3
     */
	@Column(name = "extend3")
	private  String extend3;
	/**
     * 拓展字段4
     */
	@Column(name = "extend4")
	private  String extend4;
	/**
     * 拓展字段5
     */
	@Column(name = "extend5")
	private  String extend5;
	/**
     * 拓展字段6
     */
	@Column(name = "extend6")
	private  String extend6;
	/**
     * 拓展字段7
     */
	@Column(name = "extend7")
	private  String extend7;
	/**
     * 拓展字段8
     */
	@Column(name = "extend8")
	private  String extend8;
	/**
     * 拓展字段9
     */
	@Column(name = "extend9")
	private  String extend9;
	/**
     * 拓展字段10
     */
	@Column(name = "extend10")
	private  String extend10;
	/**
     * 业务类型
     */
	@Column(name = "business_type")
	private  String businessType;
	/**
     * 商品名称
     */
	@Column(name = "g_name")
	@JsonProperty("gName")
	private  String gName;
	/**
     * 商品类别
     */
	@Column(name = "merchandise_categories")
	private  String merchandiseCategories;
	/**
     * 产品型号
     */
	@Column(name = "product_model")
	private  String productModel;
	/**
     * 规格
     */
	@Column(name = "specifications")
	private  String specifications;
	/**
     * 克重
     */
	@Column(name = "grammage")
	private  BigDecimal grammage;
	/**
     * 材料编号
     */
	@Column(name = "material_no")
	private  String materialNo;
	/**
     * 计量单位
     */
	@Column(name = "unit")
	private  String unit;
	/**
     * 进口计量单位
     */
	@Column(name = "unit_i")
	private  String unitI;
	/**
     * 供应商
     */
	@Column(name = "merchant_code")
	private  String merchantCode;
	/**
     * 价格条款
     */
	@Column(name = "price_term")
	private  String priceTerm;
	/**
     * 指运港
     */
	@Column(name = "destination_port")
	private  String destinationPort;
	/**
     * 进口单价
     */
	@Column(name = "import_unit_price")
	private  BigDecimal importUnitPrice;
	/**
     * 单价/盘（USD）
     */
	@Column(name = "unit_tray_price")
	private  BigDecimal unitTrayPrice;
	/**
     * 币种
     */
	@Column(name = "curr")
	private  String curr;
	/**
     * 汇率
     */
	@Column(name = "exchange_rate")
	private  BigDecimal exchangeRate;
	/**
     * 关税率%
     */
	@Column(name = "tariff_rate")
	private  BigDecimal tariffRate;
	/**
     * 关税金额
     */
	@Column(name = "tariff_price")
	private  BigDecimal tariffPrice;
	/**
     * 增值税率%
     */
	@Column(name = "vat")
	private  BigDecimal vat;
	/**
     * 总公司代理费率%
     */
	@Column(name = "head_agency_fee_rate")
	private  BigDecimal headAgencyFeeRate;
	/**
     * 总公司代理费
     */
	@Column(name = "head_agency_fee_price")
	private  BigDecimal headAgencyFeePrice;
	/**
     * 货代费单价
     */
	@Column(name = "freight_forwarding_fee_price")
	private  BigDecimal freightForwardingFeePrice;
	/**
     * 货代费用
     */
	@Column(name = "freight_forwarding_fee")
	private  BigDecimal freightForwardingFee;
	/**
     * 保险费
     */
	@Column(name = "insurance_fee")
	private  BigDecimal insuranceFee;
	/**
     * 购进成本
     */
	@Column(name = "purchase_cost")
	private  BigDecimal purchaseCost;
	/**
     * 仓储运输及税额
     */
	@Column(name = "storage_transport_tax")
	private  BigDecimal storageTransportTax;
	/**
     * 毛利
     */
	@Column(name = "gross_margin")
	private  BigDecimal grossMargin;
	/**
     * 不含税单价
     */
	@Column(name = "price_excluding_tax")
	private  BigDecimal priceExcludingTax;
	/**
     * 人民币单价（含税）
     */
	@Column(name = "price_rmb")
	private  BigDecimal priceRmb;
	/**
     * 备注
     */
	@Column(name = "note")
	private  String note;
	/**
     * 数据状态
     */
	@Column(name = "status")
	private  String status;

	@Transient
	private String insertTimeFrom;
	@Transient
	private String insertTimeTo;

	@Transient
	private String createrBy;

	@Transient
	private String createrUserName;

	@Transient
	private Date createrTime;
}
