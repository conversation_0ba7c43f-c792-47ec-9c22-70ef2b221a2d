package com.dcjet.cs.quo.dao;
import com.dcjet.cs.quo.model.BizQuotation;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
/**
* generated by Generate 神码
* BizQuotation
* <AUTHOR>
* @date: 2025-5-20
*/
public interface BizQuotationMapper extends Mapper<BizQuotation> {
    /**
     * 查询获取数据
     * @param bizQuotation
     * @return
     */
    List<BizQuotation> getList(BizQuotation bizQuotation);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    int checkKey(BizQuotation bizQuotation);
}
