package com.dcjet.cs.quo.service;
import com.dcjet.cs.bi.model.BizMaterialInformation;
import com.xdo.common.exception.ErrorException;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.quo.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.quo.dao.BizQuotationMapper;
import com.dcjet.cs.quo.mapper.BizQuotationDtoMapper;
import com.dcjet.cs.quo.model.BizQuotation;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-5-20
 */
@Service
public class BizQuotationService extends BaseService<BizQuotation> {
    @Resource
    private BizQuotationMapper bizQuotationMapper;
    @Resource
    private BizQuotationDtoMapper bizQuotationDtoMapper;
    @Override
    public Mapper<BizQuotation> getMapper() {
        return bizQuotationMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizQuotationParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizQuotationDto>> getListPaged(BizQuotationParam bizQuotationParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizQuotation bizQuotation = bizQuotationDtoMapper.toPo(bizQuotationParam);
        bizQuotation.setTradeCode(userInfo.getCompany());
        Page<BizQuotation> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizQuotationMapper.getList(bizQuotation));
        List<BizQuotationDto> bizQuotationDtos = page.getResult().stream().map(head -> {
            BizQuotationDto dto = bizQuotationDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizQuotationDto>> paged = ResultObject.createInstance(bizQuotationDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizQuotationParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizQuotationDto insert(BizQuotationParam bizQuotationParam, UserInfoToken userInfo) {
        BizQuotation bizQuotation = bizQuotationDtoMapper.toPo(bizQuotationParam);
        bizQuotation.setTradeCode(userInfo.getCompany());
        int check = bizQuotationMapper.checkKey(bizQuotation);
        if(check>0){
            throw new ErrorException(400, "商品名称+规格已经存在！");
        }
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizQuotation.setSid(sid);
        bizQuotation.setCreateBy(userInfo.getUserNo());
        bizQuotation.setCreateTime(new Date());
        bizQuotation.setCreateUserName(userInfo.getUserName());

        // 新增数据
        int insertStatus = bizQuotationMapper.insert(bizQuotation);
        return  insertStatus > 0 ? bizQuotationDtoMapper.toDto(bizQuotation) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizQuotationParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizQuotationDto update(BizQuotationParam bizQuotationParam, UserInfoToken userInfo) {
        BizQuotation bizQuotation = bizQuotationMapper.selectByPrimaryKey(bizQuotationParam.getSid());
        bizQuotationDtoMapper.updatePo(bizQuotationParam, bizQuotation);
        bizQuotation.setUpdateBy(userInfo.getUserNo());
        bizQuotation.setUpdateUserName(userInfo.getUserName());
        bizQuotation.setUpdateTime(new Date());

        int check = bizQuotationMapper.checkKey(bizQuotation);
        if(check>0){
            throw new ErrorException(400, "商品名称+规格已经存在！");
        }
        // 更新数据
        int update = bizQuotationMapper.updateByPrimaryKey(bizQuotation);
        return update > 0 ? bizQuotationDtoMapper.toDto(bizQuotation) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		bizQuotationMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizQuotationDto> selectAll(BizQuotationParam exportParam, UserInfoToken userInfo) {
        BizQuotation bizQuotation = bizQuotationDtoMapper.toPo(exportParam);
         bizQuotation.setTradeCode(userInfo.getCompany());
        List<BizQuotationDto> bizQuotationDtos = new ArrayList<>();
        List<BizQuotation> bizQuotations = bizQuotationMapper.getList(bizQuotation);
        if (CollectionUtils.isNotEmpty(bizQuotations)) {
            bizQuotationDtos = bizQuotations.stream().map(head -> {
                BizQuotationDto dto = bizQuotationDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizQuotationDtos;
    }

    public Boolean enable(List<String> sids, String enable, UserInfoToken userInfo) {
        if(ObjectUtils.isEmpty(sids)){
            if(enable.equals("1")){
                throw new ErrorException(400,"作废数据不存在！");
            }
            if(enable.equals("0")){
                throw new ErrorException(400,"重新启用数据不存在！");
            }
        }
        for (String sid : sids) {
            BizQuotation bizQuotation = bizQuotationMapper.selectByPrimaryKey(sid);
            if(!ObjectUtils.isEmpty(bizQuotation)){
                BizQuotation update = new BizQuotation();
                update.setSid(sid);
                update.setStatus(enable);
//                update.setUpdateBy(userInfo.getUserNo());
//                update.setUpdateTime(new Date());
                bizQuotationMapper.updateByPrimaryKeySelective(update);
            }
        }
        return true;
    }
}
