package com.dcjet.cs.dec.service;

import com.dcjet.cs.dec.dao.BizIOrderDocumentMapper;
import com.dcjet.cs.dec.dao.BizIOrderHeadMapper;
import com.dcjet.cs.dec.mapper.BizIOrderDocumentDtoMapper;
import com.dcjet.cs.dec.mapper.BizIOrderHeadDtoMapper;
import com.dcjet.cs.dec.model.BizIOrderDocument;
import com.dcjet.cs.dec.model.BizIOrderHead;
import com.dcjet.cs.dto.dec.*;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tk.mybatis.mapper.common.Mapper;
import xdoi18n.XdoI18nUtil;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 *
 *
 * <AUTHOR>
 * @date 2025-03-07 15:37:18
 * 翻译使用：throw new ErrorException(400, XdoI18nUtil.t("xxxxxxxxxx"));
 */
@Service
public class BizIOrderDocumentService extends BaseService<BizIOrderDocument> {

    private static final Logger log = LoggerFactory.getLogger(BizIOrderDocumentService.class);

    @Resource
    private BizIOrderDocumentMapper mapper;

    @Resource
    private BizIOrderDocumentDtoMapper dtoMapper;

    @Override
    public Mapper<BizIOrderDocument> getMapper() {
        return mapper;
    }



    /**
     * 获取分页信息
     *
     * @param param 查询参数
     * @param pageParam               分页参数
     * @return 分页结果
     */
    public ResultObject<BizIOrderDocumentDto> getListPaged(BizIOrderDocumentParam param, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizIOrderDocument bizIOrderDocument = dtoMapper.toPo(param);
        bizIOrderDocument.setTradeCode(userInfo.getCompany());
        BizIOrderDocument iOrderDocument = mapper.getList(bizIOrderDocument);

        return ResultObject.createInstance(true,null,dtoMapper.toDto(iOrderDocument));
    }


    /**
     * 修改记录
     *
     * @param param 更新参数
     * @param userInfo                用户信息
     * @return 更新后的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIOrderDocumentDto update(BizIOrderDocumentParam param, UserInfoToken userInfo) {
        BizIOrderDocument document = mapper.selectByPrimaryKey(param.getSid());
        if (null!=document){
            //进行更新操作
            dtoMapper.updatePo(param, document);
            document.setUpdateUserName(userInfo.getUserName());
            document.setUpdateUser(userInfo.getUserNo());
            document.setUpdateTime(new Date());
            document.setTradeCode(userInfo.getCompany());
            // 更新数据
            int update = mapper.updateByPrimaryKey(document);
            mapper.updateHead(param.getParentId(),param.getLicenseNumber(),param.getPermitNumber());
            // 将数据插入到进货信息
            return update > 0 ? dtoMapper.toDto(document) : null;
        }else {
            //进行插入操作
            BizIOrderDocument documentInsert = dtoMapper.toPo(param);

            // 规范固定字段
            String sid = UUID.randomUUID().toString();
            documentInsert.setSid(sid);
            documentInsert.setInsertUser(userInfo.getUserNo());
            documentInsert.setInsertUserName(userInfo.getUserName());
            documentInsert.setInsertTime(new Date());
            documentInsert.setTradeCode(userInfo.getCompany());

            // 新增数据
            int insertStatus = mapper.insert(documentInsert);
            mapper.updateHead(param.getParentId(),param.getLicenseNumber(),param.getPermitNumber());
            // 新增完成后 将数据转为DTO返回给前端
            return insertStatus > 0 ? dtoMapper.toDto(documentInsert) : null;
        }


    }

}