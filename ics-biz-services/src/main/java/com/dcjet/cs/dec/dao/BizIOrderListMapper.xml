<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.dec.dao.BizIOrderListMapper">
    <resultMap id="BaseResultMap" type="com.dcjet.cs.dec.model.BizIOrderList">
        <id column="sid" property="sid" jdbcType="VARCHAR"/>
        <result column="insert_user" property="insertUser" jdbcType="VARCHAR"/>
        <result column="insert_time" property="insertTime" jdbcType="TIMESTAMP"/>
        <result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="update_user_name" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="trade_code" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="product_grade" property="productGrade" jdbcType="VARCHAR"/>
        <result column="unit" property="unit" jdbcType="VARCHAR"/>
        <result column="qty" property="qty" jdbcType="NUMERIC"/>
        <result column="curr" property="curr" jdbcType="VARCHAR"/>
        <result column="dec_price" property="decPrice" jdbcType="NUMERIC"/>
        <result column="dec_total" property="decTotal" jdbcType="NUMERIC"/>
        <result column="head_id" property="headId" jdbcType="VARCHAR"/>
        <result column="product_type" property="productType" jdbcType="VARCHAR"/>
        <result column="version_no" property="versionNo" jdbcType="VARCHAR"/>
        <result column="data_status" property="dataStatus" jdbcType="VARCHAR"/>
        <result column="extend1" property="extend1" jdbcType="VARCHAR"/>
        <result column="extend2" property="extend2" jdbcType="VARCHAR"/>
        <result column="extend3" property="extend3" jdbcType="VARCHAR"/>
        <result column="extend4" property="extend4" jdbcType="VARCHAR"/>
        <result column="extend5" property="extend5" jdbcType="VARCHAR"/>
        <result column="extend6" property="extend6" jdbcType="VARCHAR"/>
        <result column="extend7" property="extend7" jdbcType="VARCHAR"/>
        <result column="extend8" property="extend8" jdbcType="VARCHAR"/>
        <result column="extend9" property="extend9" jdbcType="VARCHAR"/>
        <result column="extend10" property="extend10" jdbcType="VARCHAR"/>
        <result column="contract_head_id" property="contractHeadId" jdbcType="VARCHAR"/>
        <result column="contract_no" property="contractNo" jdbcType="VARCHAR"/>
        <result column="contract_list_id" property="contractListId" jdbcType="VARCHAR"/>
        <result column="last_copy_list_id" property="lastCopyListId" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
            t.sid, 
            coalesce(t.update_user,t.insert_user) as insert_user,
            coalesce(t.update_time,t.insert_time) as insert_time,
            coalesce(t.update_user_name,t.insert_user_name) as insert_user_name,
            t.update_user, 
            t.update_time, 
            t.update_user_name, 
            t.trade_code, 
            t.product_grade, 
            t.unit, 
            t.qty, 
            t.curr, 
            t.dec_price, 
            t.dec_total, 
            t.head_id, 
            t.product_type, 
            t.version_no, 
            t.data_status, 
            t.extend1, 
            t.extend2, 
            t.extend3, 
            t.extend4, 
            t.extend5, 
            t.extend6, 
            t.extend7, 
            t.extend8, 
            t.extend9, 
            t.extend10,
            t.contract_no,
            t.contract_head_id,
            t.contract_list_id,
            t.last_copy_list_id
    </sql>

    <sql id="condition">
        <if test="sid != null and sid  != ''">
            AND t.sid LIKE '%' || #{sid} || '%'
        </if>
        <if test="insertUser != null and insertUser  != ''">
            AND t.insert_user LIKE '%' || #{insertUser} || '%'
        </if>
        <if test="insertTimeFrom != null and insertTimeFrom != ''">
            <![CDATA[ and t.insert_time >= to_timestamp(insertTimeFrom,'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="insertTimeTo != null and insertTimeTo != ''">
            <![CDATA[ and t.insert_time < to_timestamp(insertTimeTo,'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day']]>
        </if>
        <if test="insertUserName != null and insertUserName  != ''">
            AND t.insert_user_name LIKE '%' || #{insertUserName} || '%'
        </if>
        <if test="updateUser != null and updateUser  != ''">
            AND t.update_user LIKE '%' || #{updateUser} || '%'
        </if>
        <if test="updateTimeFrom != null and updateTimeFrom != ''">
            <![CDATA[ and t.update_time >= to_timestamp(updateTimeFrom,'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="updateTimeTo != null and updateTimeTo != ''">
            <![CDATA[ and t.update_time < to_timestamp(updateTimeTo,'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day']]>
        </if>
        <if test="updateUserName != null and updateUserName  != ''">
            AND t.update_user_name LIKE '%' || #{updateUserName} || '%'
        </if>
        <if test="tradeCode != null and tradeCode  != ''">
            AND t.trade_code LIKE '%' || #{tradeCode} || '%'
        </if>
        <if test="productGrade != null and productGrade  != ''">
            AND t.product_grade LIKE '%' || #{productGrade} || '%'
        </if>
        <if test="unit != null and unit  != ''">
            AND t.unit LIKE '%' || #{unit} || '%'
        </if>
        <if test="curr != null and curr  != ''">
            AND t.curr LIKE '%' || #{curr} || '%'
        </if>
        <if test="headId != null and headId  != ''">
            AND t.head_id LIKE '%' || #{headId} || '%'
        </if>
        <if test="productType != null and productType  != ''">
            AND t.product_type LIKE '%' || #{productType} || '%'
        </if>
        <if test="versionNo != null and versionNo  != ''">
            AND t.version_no LIKE '%' || #{versionNo} || '%'
        </if>
        <if test="dataStatus != null and dataStatus  != ''">
            AND t.data_status LIKE '%' || #{dataStatus} || '%'
        </if>
        <if test="extend1 != null and extend1  != ''">
            AND t.extend1 LIKE '%' || #{extend1} || '%'
        </if>
        <if test="extend2 != null and extend2  != ''">
            AND t.extend2 LIKE '%' || #{extend2} || '%'
        </if>
        <if test="extend3 != null and extend3  != ''">
            AND t.extend3 LIKE '%' || #{extend3} || '%'
        </if>
        <if test="extend4 != null and extend4  != ''">
            AND t.extend4 LIKE '%' || #{extend4} || '%'
        </if>
        <if test="extend5 != null and extend5  != ''">
            AND t.extend5 LIKE '%' || #{extend5} || '%'
        </if>
        <if test="extend6 != null and extend6  != ''">
            AND t.extend6 LIKE '%' || #{extend6} || '%'
        </if>
        <if test="extend7 != null and extend7  != ''">
            AND t.extend7 LIKE '%' || #{extend7} || '%'
        </if>
        <if test="extend8 != null and extend8  != ''">
            AND t.extend8 LIKE '%' || #{extend8} || '%'
        </if>
        <if test="extend9 != null and extend9  != ''">
            AND t.extend9 LIKE '%' || #{extend9} || '%'
        </if>
        <if test="extend10 != null and extend10  != ''">
            AND t.extend10 LIKE '%' || #{extend10} || '%'
        </if>
        <if test="contractNo!= null and contractNo != ''">
            AND t.contract_no LIKE '%' || #{contractNo} || '%'
        </if>
        <if test="contractHeadId!= null and contractHeadId!= ''">
            AND t.contract_head_id LIKE '%' || #{contractHeadId} || '%'
        </if>
        <if test="contractListId!= null and contractListId!= ''">
            AND t.contract_list_id LIKE '%' || #{contractListId} || '%'
        </if>
        <if test="lastCopyListId!= null and lastCopyListId!= ''">
            AND t.last_copy_list_id LIKE '%' || #{lastCopyListId} || '%'
        </if>
    </sql>

    <select id="getList" resultMap="BaseResultMap" parameterType="com.dcjet.cs.dec.model.BizIOrderList">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            t_biz_i_order_list t
        <where>
            <include refid="condition"></include>
        </where>
        order by t.product_grade
    </select>
    <select id="getITotal" resultType="com.dcjet.cs.dto.dec.BizIListTotal">
        SELECT
            sum(t.qty) as qtyTotal,
            sum(t.dec_total) as decTotal
        FROM
        t_biz_i_order_list t
        <where>
            <include refid="condition"></include>
        </where>
    </select>

    <!-- 获取订单表体数据 -->
    <select id="getOrderListByHeadId" resultType="com.dcjet.cs.dec.model.BizIOrderList">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            t_biz_i_order_list t
        WHERE
            t.head_id = #{oldHeadSid}
    </select>
    <select id="getDeleteStatus" resultType="java.lang.Integer">
        select
            count(1)
        from
            t_biz_i_order_head h
        where head_id in (
            select
                distinct t.head_id
            from
                t_biz_i_order_list t
            where sid in
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        ) and h.data_status != '0';
    </select>
    <select id="getContractOriginalQty" resultType="java.math.BigDecimal">
        select
            l.CONTRACT_QUANTITY
        from
            T_BIZ_I_CONTRACT_LIST  l
        left join T_BIZ_I_CONTRACT_HEAD h on l.HEAD_ID  = h.sid
        where l.GOODS_BRAND = #{productGrade} and h.CONTRACT_NO = #{contractNo} and l.TRADE_CODE = #{tradeCode}  and h.DATA_STATUS != '2' limit 1;
    </select>
    <select id="getOrderOriginalQty" resultType="java.math.BigDecimal">
        select
            sum(l.QTY)
        from
            T_BIZ_I_ORDER_LIST l
                left join T_BIZ_I_ORDER_HEAD h on l.HEAD_ID = h.sid
        where
              l.PRODUCT_GRADE = #{productGrade}
          and l.CONTRACT_NO = #{contractNo}
          and l.TRADE_CODE = #{tradeCode}
          and h.DATA_STATUS ! = '2'
          and l.SID ! = #{sid};
    </select>


    <delete id="deleteBySids" parameterType="java.util.List">
        delete from  t_biz_i_order_list t where t.sid in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <update id="updateCorrelationID">
        update T_BIZ_I_ORDER_HEAD set head_id = #{headId} where head_id = #{oldHeadId};
        update T_BIZ_I_ORDER_LIST set CONTRACT_LIST_ID = #{newListId}, CONTRACT_HEAD_ID = #{headId} where CONTRACT_LIST_ID = #{oldSid} and CONTRACT_HEAD_ID = #{oldHeadId};
        update T_BIZ_I_PURCHASE_LIST set CONTRACT_LIST_ID = #{newListId}, CONTRACT_HEAD_ID = #{headId} where CONTRACT_LIST_ID = #{oldSid} and CONTRACT_HEAD_ID = #{oldHeadId};
    </update>

</mapper>