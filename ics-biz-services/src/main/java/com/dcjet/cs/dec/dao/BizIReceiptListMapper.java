package com.dcjet.cs.dec.dao;

import com.dcjet.cs.dec.model.BizIReceiptList;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * 进口管理-进货信息表头Mapper
 */
public interface BizIReceiptListMapper extends Mapper<BizIReceiptList>{

    /**
     * 查询获取数据
     * @return
     */
    List<BizIReceiptList> getList(BizIReceiptList BizIReceiptList);

    BizIReceiptList getEditDataByHeadId(@Param("headId") String headId);

    BizIReceiptList getSumDataByInvoiceSellSummary(String headId);
}