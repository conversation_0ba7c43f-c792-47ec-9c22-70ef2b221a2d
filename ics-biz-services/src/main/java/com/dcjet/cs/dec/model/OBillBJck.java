package com.dcjet.cs.dec.model;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
@Getter
@Setter
public class OBillBJck {
    /**
     * 单据ID
     */
    private String billid;

    /**
     * 存货编码
     */
    private String inventory;

    /**
     * 数量
     */
    private BigDecimal tnumber;

    /**
     * 主计量单位编码
     */
    private String mainunit;

    /**
     * 主计量单位名称
     */
    private String nameMainunit;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 无税金额
     */
    private BigDecimal notaxmoney;

    /**
     * 金额
     */
    private BigDecimal totalmoney;

    /**
     * 税额
     */
    private BigDecimal taxmoney;

    /**
     * 原单价
     */
    private BigDecimal oldprice;

    /**
     * 新单价
     */
    private BigDecimal newprice;

    /**
     * 原中间价
     */
    private BigDecimal oldmiddleprice;

    /**
     * 新中间价
     */
    private BigDecimal newmiddleprice;

    /**
     * 原B价
     */
    private BigDecimal oldbprice;

    /**
     * 新B价
     */
    private BigDecimal newbprice;

    /**
     * 原J价
     */
    private BigDecimal oldjprice;

    /**
     * 新J价
     */
    private BigDecimal newjprice;

    /**
     * 项目
     */
    private String project;

    /**
     * 具体序列号
     */
    private String jtserior;

    /**
     * 存货档案名称
     */
    private String nameInvmandoc;

    /**
     * 作业管理文件名称
     */
    private String nameJobmngfil;

    /**
     * 商标ID
     */
    private String trademarkid;

    /**
     * 商标名称
     */
    private String trademarkname;

    /**
     * 流水号(主键)
     */
    private Integer mqLsh;

    /**
     * 操作类型
     */
    private String mqOp;

    /**
     * 状态
     */
    private String mqSt;

    /**
     * 计数
     */
    private Integer mqCount;

    /**
     * 公司主键
     */
    private String pkCorp;

    /**
     * 时间戳
     */
    private String ts;

    /**
     * 删除标记
     */
    private Integer dr;

    /**
     * 发货通知单号
     */
    private String shipnoteno;

    /**
     * 发货客户名称
     */
    private String shipcustname;

    /**
     * 订单号
     */
    private String orderno;

    /**
     * 行位置
     */
    private Integer irowpos;

    /**
     * 实际价格
     */
    private BigDecimal ffactprice;

    /**
     * 是否超预算
     */
    private String boverbudget;

    /**
     * 预算说明
     */
    private String budgetnote;

    /**
     * 关联单据编号
     */
    private String billnoBt;

    /**
     * 合计
     */
    private BigDecimal hj;

    /**
     * 工时
     */
    private BigDecimal gs;

    /**
     * 消费税
     */
    private BigDecimal xfs;

    /**
     * 行业费
     */
    private BigDecimal hyf;

    /**
     * 其他费用
     */
    private BigDecimal qtfy;

    /**
     * 成本科目
     */
    private String costsubj;

    /**
     * 成本科目名称
     */
    private String nameCostsubj;

    /**
     * 单位主键
     */
    private String pkUnit;

    /**
     * 发货客户主键
     */
    private String pkShipcust;

    /**
     * 当前汇率
     */
    private BigDecimal currentrate;

    /**
     * 报表金额
     */
    private BigDecimal bbje;
}
