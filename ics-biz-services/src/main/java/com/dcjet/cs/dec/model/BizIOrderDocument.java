package com.dcjet.cs.dec.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 进口管理-订单信息表头
 *
 * <AUTHOR>
 * @date 2025-03-07 15:37:18
 */
@Getter
@Setter
@Table(name = "t_biz_i_order_document")
public class BizIOrderDocument implements Serializable{
    private static final long serialVersionUID = 1L;
    /**
     * 主建sid
     * 字符类型(50)
     * 非必填
     */
    @Id
    @Column(name = "sid")
    private String sid;

    /**
     * 制单人
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "insert_user")
    private String insertUser;

    /**
     * 订单制单时间
     * timestamp
     * 非必填
     */
    @Column(name = "insert_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;

    /**
     * 订单制单时间-开始时间
     */
    @Transient
    private String insertTimeFrom;

    /**
     * 订单制单时间-结束时间
     */
    @Transient
    private String insertTimeTo;

    /**
     * 创建人姓名
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "insert_user_name")
    private String insertUserName;

    /**
     * 更新人
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 更新时间
     * timestamp
     * 非必填
     */
    @Column(name = "update_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 更新时间-开始时间
     */
    @Transient
    private String updateTimeFrom;

    /**
     * 更新时间-结束时间
     */
    @Transient
    private String updateTimeTo;

    /**
     * 更新人姓名
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "update_user_name")
    private String updateUserName;

    /**
     * 企业代码
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "trade_code")
    private String tradeCode;

    /**
     * 业务类型
     * 字符类型(60)
     * 非必填
     */
    @Column(name = "business_type")
    private String businessType;

    /**
     * 版本号
     * 字符类型(10)
     * 非必填
     */
    @Column(name = "version_no")
    private String versionNo;

    /**
     * 数据状态
     * 字符类型(10)
     * 非必填
     */
    @Column(name = "data_status")
    private String dataStatus;

    /**
     * 拓展字段1
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend1")
    private String extend1;

    /**
     * 拓展字段2
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend2")
    private String extend2;

    /**
     * 拓展字段3
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend3")
    private String extend3;

    /**
     * 拓展字段4
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend4")
    private String extend4;

    /**
     * 拓展字段5
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend5")
    private String extend5;

    /**
     * 拓展字段6
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend6")
    private String extend6;

    /**
     * 拓展字段7
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend7")
    private String extend7;

    /**
     * 拓展字段8
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend8")
    private String extend8;

    /**
     * 拓展字段9
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend9")
    private String extend9;

    /**
     * 拓展字段10
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend10")
    private String extend10;






    /**
     * 进口合同表头SID
     * 数据库字段：parent_id
     * 字符类型(50)
     */
    @Column(name = "parent_id")
    @ApiModelProperty("进口合同表头SID")
    private String parentId;

    /**
     * 许可证号
     */
    @Column(name = "license_number")
    @ApiModelProperty("许可证号")
    private String licenseNumber;


    /**
     * 许可证申请日期
     * timestamp
     */
    @Column(name = "application_date")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date applicationDate;



    /**
     * 许可证有效日期
     * timestamp
     */
    @Column(name = "effective_date")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date effectiveDate;

    /**
     * 准运证编号
     */
    @Column(name = "permit_number")
    @ApiModelProperty("准运证编号")
    private String permitNumber;
    /**
     * 准运证申办日期
     * timestamp
     */
    @Column(name = "permit_application_date")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date permitApplicationDate;

    /**
     * 备注
     */
    @Column(name = "note")
    @ApiModelProperty("备注")
    private String note;


}