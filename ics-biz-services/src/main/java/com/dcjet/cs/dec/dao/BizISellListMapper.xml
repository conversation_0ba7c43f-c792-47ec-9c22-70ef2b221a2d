<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.dec.dao.BizISellListMapper">
    <sql id="Base_Column_List">
        t.sid,
        t.head_id,
        t.sales_contract_number,
        t.sales_invoice_number,
        t.trade_name,
        t.unit,
        t.quantity,
        t.unit_price_excluding_tax,
        t.amount_of_tax,
        t.tax_not_included,
        t.total_value_tax,
        t.insert_user,
        t.insert_time,
        t.insert_user_name,
        t.update_user,
        t.update_time,
        t.update_user_name,
        t.trade_code
    </sql>

    <sql id="condition">

    </sql>

    <select id="getList" resultType="com.dcjet.cs.dec.model.BizISellList" parameterType="com.dcjet.cs.dec.model.BizISellList">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
        T_BIZ_I_SELL_LIST t
        <where>
            t.head_id = #{headId}
        </where>
    </select>
    <select id="getEditDataByHeadId" resultType="com.dcjet.cs.dec.model.BizISellList">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        T_BIZ_I_SELL_LIST t
        <where>
            t.head_id = #{headId}
        </where>
    </select>
    <select id="getListBySids" resultType="com.dcjet.cs.dec.model.BizISellList">
        select * from T_BIZ_I_SELL_LIST t
        where t.sid in
        <foreach collection="sids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="getSumDataByInvoiceSell" resultType="com.dcjet.cs.dec.model.BizISellList">
        select
               sum(amount_Of_Tax)       as amount_Of_Tax,
               sum(tax_Not_Included) as tax_Not_Included,
               sum(total_Value_Tax)  as total_Value_Tax
        from T_BIZ_I_SELL_LIST
        where HEAD_ID = #{headId};
    </select>

    <select id="getSumDataByInvoiceSellSummary" resultType="com.dcjet.cs.dec.model.BizISellList">
        select sales_Invoice_Number           as sales_Invoice_Number,
               sum(amount_Of_Tax)       as amount_Of_Tax,
               sum(tax_Not_Included) as tax_Not_Included,
               sum(total_Value_Tax)  as total_Value_Tax
        from T_BIZ_I_SELL_LIST
        where HEAD_ID = #{headId}
        group by sales_Invoice_Number;
    </select>
</mapper>