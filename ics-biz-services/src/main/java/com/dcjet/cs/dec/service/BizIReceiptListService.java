package com.dcjet.cs.dec.service;

import com.dcjet.cs.dec.dao.BizIPurchaseListMapper;
import com.dcjet.cs.dec.dao.BizIReceiptHeadMapper;
import com.dcjet.cs.dec.dao.BizIReceiptListMapper;
import com.dcjet.cs.dec.mapper.BizIReceiptListDtoMapper;
import com.dcjet.cs.dec.model.BizIPurchaseList;
import com.dcjet.cs.dec.model.BizIReceiptHead;
import com.dcjet.cs.dec.model.BizIReceiptList;
import com.dcjet.cs.dec.model.BizISellList;
import com.dcjet.cs.dto.dec.BizIReceiptListDto;
import com.dcjet.cs.dto.dec.BizIReceiptListParam;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import xdoi18n.XdoI18nUtil;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * BizIReceiptList业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2025-03-07 15:37:58
 * 翻译使用：throw new ErrorException(400, XdoI18nUtil.t("xxxxxxxxxx"));
 */
@Service
public class BizIReceiptListService extends BaseService<BizIReceiptList> {

    private static final Logger log = LoggerFactory.getLogger(BizIReceiptListService.class);

    @Resource
    private BizIReceiptListMapper BizIReceiptListMapper;

    @Resource
    private BizIReceiptHeadMapper bizIReceiptHeadMapper;

    @Resource
    private BizIReceiptListDtoMapper BizIReceiptListDtoMapper;

    @Override
    public Mapper<BizIReceiptList> getMapper() {
        return BizIReceiptListMapper;
    }

    @Resource
    private BizIPurchaseListMapper bizIPurchaseListMapper;

    /**
     * 获取分页信息
     *
     * @param BizIReceiptListParam 查询参数
     * @param pageParam               分页参数
     * @return 分页结果
     */
    public ResultObject<List<BizIReceiptListDto>> getListPaged(BizIReceiptListParam BizIReceiptListParam, PageParam pageParam,UserInfoToken userInfo) {
        // 启用分页查询
        BizIReceiptList BizIReceiptList = BizIReceiptListDtoMapper.toPo(BizIReceiptListParam);
        BizIReceiptList.setTradeCode(userInfo.getCompany());
        Page<BizIReceiptList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
            .doSelectPage(() -> BizIReceiptListMapper.getList( BizIReceiptList));
        // 将PO转为DTO返回给前端
        List<BizIReceiptListDto> BizIReceiptListDtoList = page.getResult().stream()
            .map(BizIReceiptListDtoMapper::toDto)
            .collect(Collectors.toList());

        return ResultObject.createInstance(BizIReceiptListDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 新增记录
     *
     * @param BizIReceiptListParam 插入参数
     * @param userInfo                用户信息
     * @return 新增的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIReceiptListDto insert(BizIReceiptListParam BizIReceiptListParam, UserInfoToken userInfo) {
        BizIReceiptList BizIReceiptList = BizIReceiptListDtoMapper.toPo(BizIReceiptListParam);
        
        // 规范固定字段
        String sid = UUID.randomUUID().toString();
        BizIReceiptList.setSid(sid);
        BizIReceiptList.setInsertUser(userInfo.getUserNo());
        BizIReceiptList.setInsertTime(new Date());
        BizIReceiptList.setTradeCode(userInfo.getCompany());
        BizIReceiptList.setInsertUserName(userInfo.getUserName());

        // 新增数据
        int insertStatus = BizIReceiptListMapper.insert(BizIReceiptList);

        // 新增完成后 将数据转为DTO返回给前端
        return insertStatus > 0 ? BizIReceiptListDtoMapper.toDto(BizIReceiptList) : null;
    }

    /**
     * 修改记录
     *
     * @param bizIReceiptListParam 更新参数
     * @param userInfo                用户信息
     * @return 更新后的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIReceiptListDto update(BizIReceiptListParam bizIReceiptListParam, UserInfoToken userInfo) {
        BizIReceiptList BizIReceiptList = BizIReceiptListMapper.selectByPrimaryKey(bizIReceiptListParam.getSid());
        BizIReceiptListDtoMapper.updatePo(bizIReceiptListParam, BizIReceiptList);

        BizIReceiptHead bizIReceiptHead = bizIReceiptHeadMapper.selectByPrimaryKey(bizIReceiptListParam.getHeadId());
        if(bizIReceiptHead.getOutstockDocumentStatus().equals("1")){
            throw new ErrorException(400, XdoI18nUtil.t("确认状态无法编辑!"));
        }
        if(bizIReceiptHead.getOutstockDocumentStatus().equals("2")){
            throw new ErrorException(400, XdoI18nUtil.t("作废状态无法编辑!"));
        }


        BizIReceiptList.setUpdateUser(userInfo.getUserNo());
        BizIReceiptList.setUpdateTime(new Date());
        BizIReceiptList.setUpdateUserName(userInfo.getUserName());
        //数量卡控
        BizIPurchaseList bizIPurchaseList = bizIPurchaseListMapper.selectByPrimaryKey(bizIReceiptListParam.getSid());
        if(bizIPurchaseList.getQty().compareTo(BizIReceiptList.getShipmentQuantity())<0){
            throw new ErrorException(400, XdoI18nUtil.t("出货数量不可大于进货信息表体数量"));
        }

        if(bizIPurchaseList.getQty().compareTo(BizIReceiptList.getActualQuantityIssued())<0){
            throw new ErrorException(400, XdoI18nUtil.t("实发数量不可大于进货信息表体数量"));
        }

        //计算单价
        //1.通过金额/出货数量
        if(BizIReceiptList.getAmount() != null && BizIReceiptList.getShipmentQuantity() != null){
            BizIReceiptList.setDecPrice(BizIReceiptList.getAmount().divide(BizIReceiptList.getShipmentQuantity(), 8, RoundingMode.HALF_UP));
        }

        // 更新数据
        int update = BizIReceiptListMapper.updateByPrimaryKey(BizIReceiptList);
        return update > 0 ? BizIReceiptListDtoMapper.toDto(BizIReceiptList) : null;
    }



    /**
     * 获取进货信息表头数据 根据订单表头sid
     * @param BizIReceiptListParam 订单表头sid
     * @param userInfo 用户信息
     * @return 结果
     */
    public ResultObject<BizIReceiptListDto> getPurchaseHeadByOrderSid(BizIReceiptListParam BizIReceiptListParam, UserInfoToken userInfo) {
        ResultObject<BizIReceiptListDto> resultObject = ResultObject.createInstance(true,"获取成功！");
        BizIReceiptList BizIReceiptListList = BizIReceiptListMapper.getEditDataByHeadId(BizIReceiptListParam.getHeadId());
        if (BizIReceiptListList != null) {
            BizIReceiptListDto BizIReceiptListDto = BizIReceiptListDtoMapper.toDto(BizIReceiptListList);
            resultObject.setData(BizIReceiptListDto);
        }
        return resultObject;
    }

    public ResultObject getSumDataByInvoiceSellSummary(BizIReceiptListParam param, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取汇总数据成功！");
        // Assert.hasLength(param.getHeadId(), "headId不能为空!");
        if (StringUtils.isBlank(param.getHeadId())) {
            throw new ErrorException(400, XdoI18nUtil.t("headId不能为空!"));
        }
        // 获取汇总数据
        BizIReceiptList sumData = BizIReceiptListMapper.getSumDataByInvoiceSellSummary(param.getHeadId());
        resultObject.setData(sumData);
        return resultObject;
    }
}