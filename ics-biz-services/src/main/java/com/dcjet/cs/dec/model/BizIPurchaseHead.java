package com.dcjet.cs.dec.model;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Id;

import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import javax.persistence.Transient;
import javax.validation.constraints.Digits;
import java.io.Serializable;


/**
 * 进口管理-进货信息表头
 *
 * <AUTHOR>
 * @date 2025-03-07 15:37:58
 */
@Getter
@Setter
@Table(name = "t_biz_i_purchase_head")
public class BizIPurchaseHead implements Serializable{
    /**
     * 主建SID
     * 字符类型(50)
     * 非必填
     */
    @Id
    @Column(name = "sid")
    private String sid;

    /**
     * 制单人
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "insert_user")
    private String insertUser;

    /**
     * 订单制单时间
     * 日期类型(6)
     * 非必填
     */
    @Column(name = "insert_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;

    /**
     * 订单制单时间-开始时间
     */
    @Transient
    private String insertTimeFrom;

    /**
     * 订单制单时间-结束时间
     */
    @Transient
    private String insertTimeTo;

    /**
     * 创建人姓名
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "insert_user_name")
    private String insertUserName;

    /**
     * 更新人
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 更新时间
     * 日期类型(6)
     * 非必填
     */
    @Column(name = "update_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 更新时间-开始时间
     */
    @Transient
    private String updateTimeFrom;

    /**
     * 更新时间-结束时间
     */
    @Transient
    private String updateTimeTo;

    /**
     * 更新人姓名
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "update_user_name")
    private String updateUserName;

    /**
     * 企业代码
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "trade_code")
    private String tradeCode;

    /**
     * 进货单号
     * 字符类型(60)
     * 非必填
     */
    @Column(name = "purchase_order_no")
    private String purchaseOrderNo;

    /**
     * 表头HEAD_ID
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "head_id")
    private String headId;

    /**
     * 船名航次
     * 字符类型(60)
     * 非必填
     */
    @Column(name = "vessel_voyage")
    private String vesselVoyage;

    /**
     * 开航日期
     * 日期类型(6)
     * 非必填
     */
    @Column(name = "sailing_date")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sailingDate;

    /**
     * 开航日期-开始时间
     */
    @Transient
    private String sailingDateFrom;

    /**
     * 开航日期-结束时间
     */
    @Transient
    private String sailingDateTo;

    /**
     * 预计抵达日期
     * 日期类型(6)
     * 非必填
     */
    @Column(name = "expected_arrival_date")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expectedArrivalDate;

    /**
     * 预计抵达日期-开始时间
     */
    @Transient
    private String expectedArrivalDateFrom;

    /**
     * 预计抵达日期-结束时间
     */
    @Transient
    private String expectedArrivalDateTo;

    /**
     * 报关单号
     * 字符类型(18)
     * 非必填
     */
    @Column(name = "entry_no")
    private String entryNo;

    /**
     * 报关日期
     * 日期类型(6)
     * 非必填
     */
    @Column(name = "entry_date")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date entryDate;

    /**
     * 报关日期-开始时间
     */
    @Transient
    private String entryDateFrom;

    /**
     * 报关日期-结束时间
     */
    @Transient
    private String entryDateTo;

    /**
     * 进货数据状态
     * 字符类型(10)
     * 非必填
     */
    @Column(name = "purchasing_data_status")
    private String purchasingDataStatus;

    /**
     * 进货确认时间
     * 日期类型(6)
     * 非必填
     */
    @Column(name = "purchase_confirmation_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date purchaseConfirmationTime;

    /**
     * 进货确认时间-开始时间
     */
    @Transient
    private String purchaseConfirmationTimeFrom;

    /**
     * 进货确认时间-结束时间
     */
    @Transient
    private String purchaseConfirmationTimeTo;

    /**
     * 版本号
     * 字符类型(10)
     * 非必填
     */
    @Column(name = "version_no")
    private String versionNo;

    /**
     * 数据状态
     * 字符类型(10)
     * 非必填
     */
    @Column(name = "data_status")
    private String dataStatus;

    /**
     * 拓展字段1
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend1")
    private String extend1;

    /**
     * 拓展字段2
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend2")
    private String extend2;

    /**
     * 拓展字段3
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend3")
    private String extend3;

    /**
     * 拓展字段4
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend4")
    private String extend4;

    /**
     * 拓展字段5
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend5")
    private String extend5;

    /**
     * 拓展字段6
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend6")
    private String extend6;

    /**
     * 拓展字段7
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend7")
    private String extend7;

    /**
     * 拓展字段8
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend8")
    private String extend8;

    /**
     * 拓展字段9
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend9")
    private String extend9;

    /**
     * 拓展字段10
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend10")
    private String extend10;

    /**
     * 备注
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "note")
    private String note;

    /**
     * 计划编号
     * 字符类型(60)
     * 非必填
     */
    @Column(name = "plan_no")
    private String planNo;

    /**
     * 序号
     * 数值类型(19,0)
     * 非必填
     */
    @Column(name = "serial_no")
    private BigDecimal serialNo;

    /**
     * 订单信息表头-订单号
     * 字符类型(60)
     * 非必填
     */
    @Column(name = "order_no")
    private String orderNo;


    /**
     * 业务类型
     * 字符类型(60)
     * 非必填
     */
    @Column(name = "business_type")
    private String businessType;


    /**
     * 是否流入下一个节点
     */
    @Column(name = "is_next")
    private String isNext;


    @Transient
    private BigDecimal sumQty;
    @Transient
    private String sumQtyStr;

}