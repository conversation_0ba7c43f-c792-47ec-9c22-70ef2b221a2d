<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.dec.dao.BizIncomingGoodsInsureMapper">
    <resultMap id="BaseResultMap" type="com.dcjet.cs.dec.model.BizIncomingGoodsInsure">
        <result column="id" property="id" jdbcType="VARCHAR"/>
        <result column="business_type" property="businessType" jdbcType="VARCHAR"/>
        <result column="data_state" property="dataState" jdbcType="VARCHAR"/>
        <result column="version_no" property="versionNo" jdbcType="VARCHAR"/>
        <result column="trade_code" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="sys_org_code" property="sysOrgCode" jdbcType="VARCHAR"/>
        <result column="parent_id" property="parentId" jdbcType="VARCHAR"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="update_user_name" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="extend1" property="extend1" jdbcType="VARCHAR"/>
        <result column="extend2" property="extend2" jdbcType="VARCHAR"/>
        <result column="extend3" property="extend3" jdbcType="VARCHAR"/>
        <result column="extend4" property="extend4" jdbcType="VARCHAR"/>
        <result column="extend5" property="extend5" jdbcType="VARCHAR"/>
        <result column="extend6" property="extend6" jdbcType="VARCHAR"/>
        <result column="extend7" property="extend7" jdbcType="VARCHAR"/>
        <result column="extend8" property="extend8" jdbcType="VARCHAR"/>
        <result column="extend9" property="extend9" jdbcType="VARCHAR"/>
        <result column="extend10" property="extend10" jdbcType="VARCHAR"/>
        <result column="permit_no" property="permitNo" jdbcType="VARCHAR"/>
        <result column="permit_apply_date" property="permitApplyDate" jdbcType="DATE"/>
        <result column="customs_declaration_no" property="customsDeclarationNo" jdbcType="VARCHAR"/>
        <result column="declaration_date" property="declarationDate" jdbcType="DATE"/>
        <result column="release_date" property="releaseDate" jdbcType="DATE"/>
        <result column="code_no" property="codeNo" jdbcType="VARCHAR"/>
        <result column="insurance_company" property="insuranceCompany" jdbcType="VARCHAR"/>
        <result column="insured_person" property="insuredPerson" jdbcType="VARCHAR"/>
        <result column="insurance_type" property="insuranceType" jdbcType="VARCHAR"/>
        <result column="currency" property="currency" jdbcType="VARCHAR"/>
        <result column="insurance_amount" property="insuranceAmount" jdbcType="NUMERIC"/>
        <result column="insurance_rate" property="insuranceRate" jdbcType="NUMERIC"/>
        <result column="premium" property="premium" jdbcType="NUMERIC"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="head_id" property="headId" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
            t.id, 
            t.business_type, 
            t.data_state, 
            t.version_no, 
            t.trade_code, 
            t.sys_org_code, 
            t.parent_id, 
            t.create_by, 
            t.create_time, 
            t.update_by, 
            t.update_time, 
            t.insert_user_name, 
            t.update_user_name, 
            t.extend1, 
            t.extend2, 
            t.extend3, 
            t.extend4, 
            t.extend5, 
            t.extend6, 
            t.extend7, 
            t.extend8, 
            t.extend9, 
            t.extend10, 
            t.permit_no, 
            t.permit_apply_date, 
            t.customs_declaration_no, 
            t.declaration_date, 
            t.release_date, 
            t.code_no, 
            t.insurance_company, 
            t.insured_person, 
            t.insurance_type, 
            t.currency, 
            t.insurance_amount, 
            t.insurance_rate, 
            t.premium, 
            t.remark, 
            t.head_id
    </sql>

    <sql id="condition">
    </sql>

    <select id="getList" resultMap="BaseResultMap" parameterType="com.dcjet.cs.dec.model.BizIncomingGoodsInsure">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            t_biz_incoming_goods_insure t
        <where>
            <include refid="condition"></include>
        </where>
    </select>
    <select id="getDocumentByHeadId" resultType="com.dcjet.cs.dec.model.BizIncomingGoodsInsure">
        select
            <include refid="Base_Column_List"></include>
        from
            t_biz_incoming_goods_insure t
        WHERE t.HEAD_ID = #{headId}
    </select>


    <delete id="deleteBySids" parameterType="java.util.List">
        delete from  t_biz_incoming_goods_insure t where t.sid in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
</mapper>