package com.dcjet.cs.dec.service;

import com.dcjet.cs.Utils.NumberFormatterUtils;
import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.bi.service.BizMerchantService;
import com.dcjet.cs.dec.dao.BizIOrderHeadMapper;
import com.dcjet.cs.dec.dao.BizIReceiptHeadMapper;
import com.dcjet.cs.dec.dao.BizIReceiptListMapper;
import com.dcjet.cs.dec.mapper.BizIReceiptHeadDtoMapper;
import com.dcjet.cs.dec.model.BizIOrderHead;
import com.dcjet.cs.dec.model.BizIReceiptHead;
import com.dcjet.cs.dec.model.BizIReceiptList;
import com.dcjet.cs.dec.model.BizISellHead;
import com.dcjet.cs.dto.dec.BizIReceiptExportDto;
import com.dcjet.cs.dto.dec.BizIReceiptHeadDto;
import com.dcjet.cs.dto.dec.BizIReceiptHeadParam;
import com.dcjet.cs.dto.dec.BizIReceiptListDto;
import com.dcjet.cs.params.dao.StorehouseMapper;
import com.dcjet.cs.params.model.Storehouse;
import com.dcjet.cs.util.CommonEnum;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import tk.mybatis.mapper.common.Mapper;
import xdoi18n.XdoI18nUtil;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * BizIReceiptHead业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2025-03-07 15:37:58
 * 翻译使用：throw new ErrorException(400, XdoI18nUtil.t("xxxxxxxxxx"));
 */
@Service
public class BizIReceiptHeadService extends BaseService<BizIReceiptHead> {

    private static final Logger log = LoggerFactory.getLogger(BizIReceiptHeadService.class);

    @Resource
    private BizIReceiptHeadMapper BizIReceiptHeadMapper;

    @Resource
    private BizMerchantMapper bizMerchantMapper;

    @Resource
    private StorehouseMapper storehouseMapper;


    @Resource
    private BizIReceiptHeadDtoMapper BizIReceiptHeadDtoMapper;

    @Override
    public Mapper<BizIReceiptHead> getMapper() {
        return BizIReceiptHeadMapper;
    }

    @Resource
    private BizIReceiptListMapper receiptListMapper;


    /**
     * 获取分页信息
     *
     * @param BizIReceiptHeadParam 查询参数
     * @param pageParam               分页参数
     * @return 分页结果
     */
    public ResultObject<List<BizIReceiptHeadDto>> getListPaged(BizIReceiptHeadParam BizIReceiptHeadParam, PageParam pageParam,UserInfoToken userInfo) {
        // 启用分页查询
        BizIReceiptHead BizIReceiptHead = BizIReceiptHeadDtoMapper.toPo(BizIReceiptHeadParam);
        BizIReceiptHead.setTradeCode(userInfo.getCompany());
        Page<BizIReceiptHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
            .doSelectPage(() -> BizIReceiptHeadMapper.getList( BizIReceiptHead));
        // 将PO转为DTO返回给前端
        List<BizIReceiptHeadDto> BizIReceiptHeadDtoList = page.getResult().stream()
            .map(BizIReceiptHeadDtoMapper::toDto)
            .collect(Collectors.toList());

        return ResultObject.createInstance(BizIReceiptHeadDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 新增记录
     *
     * @param BizIReceiptHeadParam 插入参数
     * @param userInfo                用户信息
     * @return 新增的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIReceiptHeadDto insert(BizIReceiptHeadParam BizIReceiptHeadParam, UserInfoToken userInfo) {
        BizIReceiptHead BizIReceiptHead = BizIReceiptHeadDtoMapper.toPo(BizIReceiptHeadParam);
        
        // 规范固定字段
        String sid = UUID.randomUUID().toString();
        BizIReceiptHead.setSid(sid);
        BizIReceiptHead.setInsertUser(userInfo.getUserNo());
        BizIReceiptHead.setInsertTime(new Date());
        BizIReceiptHead.setTradeCode(userInfo.getCompany());
        BizIReceiptHead.setInsertUserName(userInfo.getUserName());

        // 新增数据
        int insertStatus = BizIReceiptHeadMapper.insert(BizIReceiptHead);

        // 新增完成后 将数据转为DTO返回给前端
        return insertStatus > 0 ? BizIReceiptHeadDtoMapper.toDto(BizIReceiptHead) : null;
    }

    /**
     * 修改记录
     *
     * @param BizIReceiptHeadParam 更新参数
     * @param userInfo                用户信息
     * @return 更新后的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIReceiptHeadDto update(BizIReceiptHeadParam BizIReceiptHeadParam, UserInfoToken userInfo) {
        BizIReceiptHead BizIReceiptHead = BizIReceiptHeadMapper.selectByPrimaryKey(BizIReceiptHeadParam.getSid());
        BizIReceiptHeadDtoMapper.updatePo(BizIReceiptHeadParam, BizIReceiptHead);
        BizIReceiptHead.setCreateBy(userInfo.getUserName());
        BizIReceiptHead.setCreateDate(new Date());
        BizIReceiptHead.setUpdateUser(userInfo.getUserNo());
        BizIReceiptHead.setUpdateTime(new Date());
        BizIReceiptHead.setUpdateUserName(userInfo.getUserName());

        // 更新数据
        int update = BizIReceiptHeadMapper.updateByPrimaryKey(BizIReceiptHead);
        return update > 0 ? BizIReceiptHeadDtoMapper.toDto(BizIReceiptHead) : null;
    }



    /**
     * 获取进货信息表头数据 根据订单表头sid
     * @param BizIReceiptHeadParam 订单表头sid
     * @param userInfo 用户信息
     * @return 结果
     */
    public ResultObject<BizIReceiptHeadDto> getPurchaseHeadByOrderSid(BizIReceiptHeadParam BizIReceiptHeadParam, UserInfoToken userInfo) {
        ResultObject<BizIReceiptHeadDto> resultObject = ResultObject.createInstance(true,"获取成功！");
        BizIReceiptHead BizIReceiptHeadList = BizIReceiptHeadMapper.getEditDataByHeadId(BizIReceiptHeadParam.getHeadId());
        if (BizIReceiptHeadList != null) {
            BizIReceiptHeadDto BizIReceiptHeadDto = BizIReceiptHeadDtoMapper.toDto(BizIReceiptHeadList);
            resultObject.setData(BizIReceiptHeadDto);
        }
        return resultObject;
    }

    public BizIReceiptExportDto selectExportMessage(String sid, UserInfoToken userInfoToken) {
        //通过sid获取出库头体
        BizIReceiptHead bizIReceiptHead = BizIReceiptHeadMapper.selectByPrimaryKey(sid);
        List<BizIReceiptList> select = receiptListMapper.select(new BizIReceiptList() {{
            setHeadId(bizIReceiptHead.getSid());
        }});

        BizIReceiptExportDto bizIReceiptExportDto = new BizIReceiptExportDto();
        bizIReceiptExportDto.setReceiptNumber(bizIReceiptHead.getReceiptNumber());
        //转换提货人:
        List<BizMerchant> select1 = bizMerchantMapper.select(new BizMerchant() {{
            setMerchantCode(bizIReceiptHead.getConsignee());
            setTradeCode(userInfoToken.getCompany());
        }});
        if(!CollectionUtils.isEmpty(select1)){
            bizIReceiptExportDto.setConsignee(select1.get(0).getMerchantNameCn());
        }

        if(bizIReceiptHead.getDeliveryDate() != null){
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String formattedDate = sdf.format(bizIReceiptHead.getDeliveryDate());
            bizIReceiptExportDto.setDeliveryDate(formattedDate);
        }
        //转换仓库:
        List<Storehouse> select2 = storehouseMapper.select(new Storehouse() {{
            setParamCode(bizIReceiptHead.getWarehouse());
            setTradeCode(userInfoToken.getCompany());
        }});
        if(!CollectionUtils.isEmpty(select2)){
            bizIReceiptExportDto.setWarehouse(select2.get(0).getStorehouseName());
        }

        //转换供应商
        List<BizMerchant> select3 = bizMerchantMapper.select(new BizMerchant() {{
            setMerchantCode(bizIReceiptHead.getSupplier());
            setTradeCode(userInfoToken.getCompany());
        }});
        if(!CollectionUtils.isEmpty(select3)){
            bizIReceiptExportDto.setSupplier(select3.get(0).getMerchantNameCn());
        }


        bizIReceiptExportDto.setRemark(bizIReceiptHead.getRemark());
        bizIReceiptExportDto.setCreateBy(bizIReceiptHead.getCreateBy());
        List<BizIReceiptListDto> bizIReceiptListDtoList = new ArrayList<>();
        if(select!= null &&!select.isEmpty()){
            for (BizIReceiptList bizIReceiptList : select) {
                BizIReceiptListDto bizIReceiptListDto = new BizIReceiptListDto();
                bizIReceiptListDto.setTradeName(bizIReceiptList.getTradeName());
                bizIReceiptListDto.setUnit(bizIReceiptList.getUnit());
                bizIReceiptListDto.setShipmentQuantity(NumberFormatterUtils.formatNumber(bizIReceiptList.getShipmentQuantity()));
                bizIReceiptListDto.setActualQuantityIssued(NumberFormatterUtils.formatNumber(bizIReceiptList.getActualQuantityIssued()));
                bizIReceiptListDto.setDecPrice(bizIReceiptList.getDecPrice());
                bizIReceiptListDto.setDecPriceStr(NumberFormatterUtils.formatNumber(bizIReceiptList.getDecPrice()));
                bizIReceiptListDto.setAmount(bizIReceiptList.getAmount());
                bizIReceiptListDto.setAmountStr(NumberFormatterUtils.formatNumber(bizIReceiptList.getAmount()));
                bizIReceiptListDtoList.add(bizIReceiptListDto);
            }
            String num1 = NumberFormatterUtils.formatNumber(select.stream()
                    .map(BizIReceiptList::getShipmentQuantity)
                    .filter(Objects::nonNull) // 过滤 null
                    .reduce(BigDecimal::add)
                    .orElse(BigDecimal.ZERO));

            String num2 = NumberFormatterUtils.formatNumber( select.stream()
                    .map(BizIReceiptList::getActualQuantityIssued)
                    .filter(Objects::nonNull) // 过滤 null
                    .reduce(BigDecimal::add)
                    .orElse(BigDecimal.ZERO));
            String num3 = NumberFormatterUtils.formatNumber(select.stream()
                            .map(BizIReceiptList::getAmount)
                            .filter(Objects::nonNull) // 过滤 null
                            .reduce(BigDecimal::add)
                            .orElse(BigDecimal.ZERO));
            bizIReceiptExportDto.setTotalShipmentQuantity(num1);
            bizIReceiptExportDto.setTotalActualQuantityIssued(num2);
            bizIReceiptExportDto.setTotalAmount(num3);
            bizIReceiptExportDto.setBizIReceiptListDtos(bizIReceiptListDtoList);
        }
        return bizIReceiptExportDto;
    }
}