package com.dcjet.cs.dec.mapper;

import com.dcjet.cs.dec.model.BizIOrderDocument;
import com.dcjet.cs.dec.model.BizIOrderHead;
import com.dcjet.cs.dto.dec.BizIOrderDocumentDto;
import com.dcjet.cs.dto.dec.BizIOrderDocumentParam;
import com.dcjet.cs.dto.dec.BizIOrderHeadDto;
import com.dcjet.cs.dto.dec.BizIOrderHeadParam;
import jdk.nashorn.internal.ir.annotations.Ignore;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * BizIOrderHeadDto
 *
 * <AUTHOR>
 * @date 2025-03-07 15:37:18
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizIOrderDocumentDtoMapper {

    /**
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizIOrderDocumentDto toDto(BizIOrderDocument po);

    /**
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizIOrderDocument toPo(BizIOrderDocumentParam param);

    /**
     * 数据库原始数据更新
     * @param bizIOrderDocumentParam
     * @param bizIOrderDocument
     */
    @Mapping(target = "insertTime", ignore = true)
    @Mapping(target = "insertUserName", ignore = true)
    @Mapping(target = "insertUser", ignore = true)
    void updatePo(BizIOrderDocumentParam bizIOrderDocumentParam, @MappingTarget BizIOrderDocument bizIOrderDocument);

    default void patchPo(BizIOrderDocumentParam bizIOrderDocumentParam , BizIOrderDocument bizIOrderDocument) {
        // TODO 自行实现局部更新
    }
}