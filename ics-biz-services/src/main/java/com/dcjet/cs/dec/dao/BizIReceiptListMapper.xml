<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.dec.dao.BizIReceiptListMapper">
    <sql id="Base_Column_List">
        t.sid,
        t.head_id,
        t.trade_Name,
        t.shipment_Quantity,
        t.actual_Quantity_Issued,
        t.unit,
        t.dec_price,
        t.curr,
        t.amount,
        t.insert_user,
        t.insert_time,
        t.insert_user_name,
        t.update_user,
        t.update_time,
        t.update_user_name,
        t.trade_code
    </sql>

    <sql id="condition">

    </sql>

    <select id="getList" resultType="com.dcjet.cs.dec.model.BizIReceiptList" parameterType="com.dcjet.cs.dec.model.BizIReceiptList">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
        T_BIZ_I_RECEIPT_LIST t
        <where>
            t.head_id = #{headId}
        </where>
    </select>
    <select id="getEditDataByHeadId" resultType="com.dcjet.cs.dec.model.BizIReceiptList">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        T_BIZ_I_RECEIPT_LIST t
        <where>
            t.head_id = #{headId}
        </where>
    </select>
    <select id="getSumDataByInvoiceSellSummary" resultType="com.dcjet.cs.dec.model.BizIReceiptList">
        select
            sum(shipment_Quantity)       as shipment_Quantity,
            sum(actual_Quantity_Issued) as actual_Quantity_Issued,
            sum(amount)  as amount
        from T_BIZ_I_RECEIPT_LIST
        where HEAD_ID = #{headId};
    </select>
</mapper>