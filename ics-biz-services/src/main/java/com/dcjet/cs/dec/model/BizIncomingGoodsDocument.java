package com.dcjet.cs.dec.model;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Id;
import lombok.Getter;
import lombok.Setter;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import javax.persistence.Transient;
import java.io.Serializable;


/**
 * 基础字段参考表
 *
 * <AUTHOR>
 * @date 2025-05-24 11:01:44
 */
@Getter
@Setter
@Table(name = "t_biz_incoming_goods_document")
public class BizIncomingGoodsDocument implements Serializable{
    private static final long serialVersionUID = 1L;
    /**
     * 主键ID，系统自动生成
     * 字符类型(40)
     * 必填
     */
    @Column(name = "id")
    @Id
    private String id;

    /**
     * 业务类型
     * 字符类型(60)
     * 非必填
     */
    @Column(name = "business_type")
    private String businessType;

    /**
     * 数据状态
     * 字符类型(10)
     * 非必填
     */
    @Column(name = "data_state")
    private String dataState;

    /**
     * 版本号
     * 字符类型(10)
     * 非必填
     */
    @Column(name = "version_no")
    private String versionNo;

    /**
     * 交易代码
     * 字符类型(10)
     * 非必填
     */
    @Column(name = "trade_code")
    private String tradeCode;

    /**
     * 系统机构代码
     * 字符类型(10)
     * 非必填
     */
    @Column(name = "sys_org_code")
    private String sysOrgCode;

    /**
     * 父级ID
     * 字符类型(40)
     * 非必填
     */
    @Column(name = "parent_id")
    private String parentId;

    /**
     * 创建人
     * 字符类型(50)
     * 必填
     */
    @Column(name = "create_by")
    private String createBy;

    /**
     * 创建时间
     * 日期类型(6)
     * 必填
     */
    @Column(name = "create_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 创建时间-开始时间
     */
    @Transient
    private Date createTimeFrom;

    /**
     * 创建时间-结束时间
     */
    @Transient
    private Date createTimeTo;

    /**
     * 更新人
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "update_by")
    private String updateBy;

    /**
     * 更新时间
     * 日期类型(6)
     * 非必填
     */
    @Column(name = "update_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 更新时间-开始时间
     */
    @Transient
    private Date updateTimeFrom;

    /**
     * 更新时间-结束时间
     */
    @Transient
    private Date updateTimeTo;

    /**
     * 插入用户名
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "insert_user_name")
    private String insertUserName;

    /**
     * 更新用户名
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "update_user_name")
    private String updateUserName;

    /**
     * 扩展字段1
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend1")
    private String extend1;

    /**
     * 扩展字段2
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend2")
    private String extend2;

    /**
     * 扩展字段3
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend3")
    private String extend3;

    /**
     * 扩展字段4
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend4")
    private String extend4;

    /**
     * 扩展字段5
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend5")
    private String extend5;

    /**
     * 扩展字段6
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend6")
    private String extend6;

    /**
     * 扩展字段7
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend7")
    private String extend7;

    /**
     * 扩展字段8
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend8")
    private String extend8;

    /**
     * 扩展字段9
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend9")
    private String extend9;

    /**
     * 扩展字段10
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend10")
    private String extend10;


    /**
     * 许可证号
     * 字符类型(120)
     * 非必填
     */
    @Column(name = "license_number")
    private String licenseNumber;

    /**
     * 许可证申请日期
     * 日期类型(0)
     * 非必填
     */
    @Column(name = "license_application_date")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date licenseApplicationDate;

    /**
     * 许可证申请日期-开始时间
     */
    @Transient
    private Date licenseApplicationDateFrom;

    /**
     * 许可证申请日期-结束时间
     */
    @Transient
    private Date licenseApplicationDateTo;

    /**
     * 许可证有效日期
     * 日期类型(0)
     * 非必填
     */
    @Column(name = "license_validity_date")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date licenseValidityDate;

    /**
     * 许可证有效日期-开始时间
     */
    @Transient
    private Date licenseValidityDateFrom;

    /**
     * 许可证有效日期-结束时间
     */
    @Transient
    private Date licenseValidityDateTo;

    /**
     * 准运证编号
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "permit_number")
    private String permitNumber;

    /**
     * 准运证申办日期
     * 日期类型(0)
     * 非必填
     */
    @Column(name = "application_date_for_transportation_permit")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date applicationDateForTransportationPermit;

    /**
     * 准运证申办日期-开始时间
     */
    @Transient
    private Date applicationDateForTransportationPermitFrom;

    /**
     * 准运证申办日期-结束时间
     */
    @Transient
    private Date applicationDateForTransportationPermitTo;

    /**
     * 表头head_id
     * 字符类型(40)
     * 非必填
     */
    @Column(name = "head_id")
    private String headId;

    /**
     * 备注
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "note")
    private String note;


}