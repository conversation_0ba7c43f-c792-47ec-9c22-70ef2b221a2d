package com.dcjet.cs.dec.dao;


import com.dcjet.cs.dec.model.BizIncomingGoodsDocument;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import tk.mybatis.mapper.common.Mapper;

/**
 * 进过管理-表体列表Mapper
 */
public interface BizIncomingGoodsDocumentMapper extends Mapper<BizIncomingGoodsDocument>{

    /**
     * 查询获取数据
     * @param bizIncomingGoodsDocument
     * @return
     */
    List<BizIncomingGoodsDocument> getList(BizIncomingGoodsDocument bizIncomingGoodsDocument);

    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(@Param("list")List<String> sids);


    /**
     * 根据单据头ID获取单据体
     * @param headId 单据头ID
     * @return 返回单据体
     */
    BizIncomingGoodsDocument getDocumentByHeadId(@Param("headId") String headId);
}