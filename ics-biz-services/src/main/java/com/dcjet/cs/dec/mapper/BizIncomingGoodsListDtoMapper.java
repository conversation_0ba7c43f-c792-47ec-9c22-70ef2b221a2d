package com.dcjet.cs.dec.mapper;
import com.dcjet.cs.dec.model.BizIncomingGoodsList;
import com.dcjet.cs.dto.dec.BizIncomingGoodsListDto;
import com.dcjet.cs.dto.dec.BizIncomingGoodsListParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;


/**
 * BizIncomingGoodsListDto
 *
 * <AUTHOR>
 * @date 2025-05-23 13:32:21
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizIncomingGoodsListDtoMapper {

    /**
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizIncomingGoodsListDto toDto(BizIncomingGoodsList po);

    /**
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizIncomingGoodsList toPo(BizIncomingGoodsListParam param);

    /**
     * 数据库原始数据更新
     * @param bizIncomingGoodsListParam
     * @param BizIncomingGoodsList
     */
    void updatePo(BizIncomingGoodsListParam bizIncomingGoodsListParam, @MappingTarget BizIncomingGoodsList bizIncomingGoodsList);

    default void patchPo(BizIncomingGoodsListParam bizIncomingGoodsListParam , BizIncomingGoodsList bizIncomingGoodsList) {
        // TODO 自行实现局部更新
    }
}