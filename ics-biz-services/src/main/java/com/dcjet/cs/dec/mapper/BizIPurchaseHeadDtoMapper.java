package com.dcjet.cs.dec.mapper;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
import com.dcjet.cs.dec.model.BizIPurchaseHead;
import com.dcjet.cs.dto.dec.BizIPurchaseHeadParam;
import com.dcjet.cs.dto.dec.BizIPurchaseHeadDto;

/**
 * BizIPurchaseHeadDto
 *
 * <AUTHOR>
 * @date 2025-03-07 15:37:58
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizIPurchaseHeadDtoMapper {

    /**
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizIPurchaseHeadDto toDto(BizIPurchaseHead po);

    /**
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizIPurchaseHead toPo(BizIPurchaseHeadParam param);

    /**
     * 数据库原始数据更新
     * @param bizIPurchaseHeadParam
     * @param BizIPurchaseHead
     */
    void updatePo(BizIPurchaseHeadParam bizIPurchaseHeadParam, @MappingTarget BizIPurchaseHead bizIPurchaseHead);

    default void patchPo(BizIPurchaseHeadParam bizIPurchaseHeadParam , BizIPurchaseHead bizIPurchaseHead) {
        // TODO 自行实现局部更新
    }
}