<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.dec.dao.BizIWarehouseReceiptHeadMapper">



    <select id="getList" resultType="com.dcjet.cs.dec.model.BizIWarehouseReceiptHead" parameterType="com.dcjet.cs.dec.model.BizIWarehouseReceiptHead">
        SELECT
            *
        FROM
        t_biz_warehouse_receipt_head t
        <where>
            t.parent_id = #{parentId}
        </where>
    </select>

    <select id="getListBySid" resultType="com.dcjet.cs.dec.model.BizIWarehouseReceiptHead" parameterType="com.dcjet.cs.dec.model.BizIWarehouseReceiptHead">
        SELECT
        t.*,mer.merchant_name_cn,st.STOREHOUSE_NAME
        FROM
        t_biz_warehouse_receipt_head t left join  t_biz_merchant mer on
        t.lading_department=mer.merchant_code
        left join  T_BIZ_STOREHOUSE  st on t.warehouse=st.PARAM_CODE
        <where>
            t.sid = #{sid}
        </where>
    </select>


    <select id="selectWarehouseEntryNumber" resultType="string">
        SELECT
        distinct warehouse_entry_number
        FROM
        t_biz_warehouse_receipt_head t
        <where>
            t.status = #{status} and t.trade_code=#{tradeCode} and extend1 is null
        </where>
        order by warehouse_entry_number
    </select>

    <select id="selectladingNumber" resultType="string">
        SELECT
        distinct LADING_NUMBER
        FROM
        t_biz_warehouse_receipt_head t
        <where>
            t.status = #{status} and t.trade_code=#{tradeCode} and extend2 is null
        </where>
        order by LADING_NUMBER
    </select>

    <delete id="deleteHeadAndList">
        delete from t_biz_warehouse_receipt_head  where parent_id=#{parentId};

       delete from t_biz_warehouse_receipt_list  where parent_id=#{listParentId}

    </delete>

    <select id="selectDiscountRate" resultType="decimal">
                select discount_rate  from t_biz_i_plan_list
         a left join  t_biz_i_plan as head on a.head_id=head.sid  where a.product_name=#{productGrade} and head.plan_id=#{planNo}
          and head.trade_code=#{tradeCode} and head.STATUS !='2'
          limit 1
    </select>

    <select id="selectMaxWarehouseEntryNumber" resultType="string">
SELECT MAX(warehouse_entry_number) AS max_entry_number
                    FROM t_biz_warehouse_receipt_head
                    WHERE warehouse_entry_number like '%'|| #{warehouseEntryNumber} || '%'
    </select>


    <select id="selectMaxLadingNumber" resultType="string">
SELECT MAX(lading_number) AS lading_number
                    FROM t_biz_warehouse_receipt_head
                    WHERE lading_number like '%'|| #{ladingNumber} || '%'
    </select>

    <update id="updateByWarehouseEntryNumber">
        update t_biz_warehouse_receipt_head set extend1='1'  WHERE warehouse_entry_number=#{warehouseEntryNumber}  and status = '2'
    </update>

    <update id="updateByLadingNumber">
        update t_biz_warehouse_receipt_head set extend2='1'  WHERE lading_number=#{ladingNumber} and status = '2'
    </update>

    <select id="checkExists" resultType="com.dcjet.cs.dec.model.BizIWarehouseReceiptHead">
        SELECT
        *
        FROM
        t_biz_warehouse_receipt_head t
        <where>
            t.sid != #{sid} and warehouse_entry_number=#{warehouseEntryNumber}  and extend1!='1'
        </where>
    </select>

    <select id="getHeadForFile" resultType="com.dcjet.cs.dec.model.BizIWarehouseReceiptHeadToFile">
        select warehouse_receipt_number,
               COALESCE(supplier,'') as supplier,
               '订单号：' || COALESCE(order_no,'') as order_no,
               COALESCE(selling_rate,0) as selling_rate,
               COALESCE(rate,0) as rate,
               COALESCE(discount_rate,0) as discount_rate,
               '制单：' || COALESCE(insert_user_name,'') as insert_user_name,
               '制单日期：' || COALESCE(TO_CHAR(insert_time, 'YYYY/MM/DD'),'') AS insert_time
        from T_BIZ_WAREHOUSE_RECEIPT_HEAD
        where sid=#{sid}
    </select>


    <select id="selectContract" resultType="string">
        SELECT LISTAGG(contract_no, ',') WITHIN GROUP (ORDER BY sid)
FROM T_BIZ_I_ORDER_LIST
WHERE head_id = #{sid};
    </select>
</mapper>