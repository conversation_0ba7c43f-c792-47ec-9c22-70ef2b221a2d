package com.dcjet.cs.dec.dao;

import com.dcjet.cs.dec.model.BizIReceiptHead;
import com.xdo.common.token.UserInfoToken;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * 进口管理-进货信息表头Mapper
 */
public interface BizIReceiptHeadMapper extends Mapper<BizIReceiptHead>{

    /**
     * 查询获取数据
     * @return
     */
    List<BizIReceiptHead> getList(BizIReceiptHead bizIReceiptHead);

    BizIReceiptHead getEditDataByHeadId(@Param("headId") String headId);

    void insertHeadList(@Param("receiptSid")String receiptSid, @Param("sid")String sid, @Param("userInfo")UserInfoToken userInfo);

    String selectUserMessage(@Param("insertUser")String insertUser,@Param("tradeCode") String company);
}