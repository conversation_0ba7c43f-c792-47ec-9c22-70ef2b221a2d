package com.dcjet.cs.dec.mapper;

import com.dcjet.cs.dec.model.BizISellHead;
import com.dcjet.cs.dto.dec.BizISellHeadDto;
import com.dcjet.cs.dto.dec.BizISellHeadParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * BizISellHeadDto
 *
 * <AUTHOR>
 * @date 2025-03-07 15:37:58
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizISellHeadDtoMapper {

    /**
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizISellHeadDto toDto(BizISellHead po);

    /**
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizISellHead toPo(BizISellHeadParam param);

    /**
     * 数据库原始数据更新
     * @param BizISellHeadParam
     * @param BizISellHead
     */
    void updatePo(BizISellHeadParam BizISellHeadParam, @MappingTarget BizISellHead BizISellHead);

    default void patchPo(BizISellHeadParam BizISellHeadParam , BizISellHead BizISellHead) {
        // TODO 自行实现局部更新
    }
}