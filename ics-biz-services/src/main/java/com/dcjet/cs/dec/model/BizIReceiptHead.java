package com.dcjet.cs.dec.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 进口管理-进货信息表头
 *
 * <AUTHOR>
 * @date 2025-03-07 15:37:58
 */
@Getter
@Setter
@Table(name = "T_BIZ_I_RECEIPT_HEAD")
public class BizIReceiptHead implements Serializable{
    /**
     * 主建SID
     * 字符类型(50)
     * 非必填
     */
    @Id
    @Column(name = "sid")
    private String sid;

    @Column(name = "head_id")
    private String headId;

    @Column(name = "receipt_Number")
    private String receiptNumber; // 出库回单编号
    @Column(name = "contract_Number")
    private String contractNumber; // 合同号
    @Column(name = "order_Number")
    private String orderNumber; // 订单号
    @Column(name = "delivery_Number")
    private String deliveryNumber; // 交货单号
    @Column(name = "consignee")
    private String consignee; // 提货人
    @Column(name = "warehouse")
    private String warehouse; // 仓库
    @Column(name = "delivery_Date")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryDate; // 出库日期
    @Column(name = "supplier")
    private String supplier; // 供应商
    @Column(name = "send_Ufida")
    private String sendUfida; // 发送用友
    @Column(name = "inspection_Outstock")
    private String inspectionOutstock; // 抽检出库
    @Column(name = "remark")
    private String remark; // 备注
    @Column(name = "create_By")
    private String createBy; // 制单人
    @Column(name = "create_Date")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate; // 制单时间
    @Column(name = "outstock_Document_Status")
    private String outstockDocumentStatus; // 出库单据状态

    /**
     * 制单人
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "insert_user")
    private String insertUser;

    /**
     * 制单时间
     * 日期类型(6)
     * 非必填
     */
    @Column(name = "insert_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;


    /**
     * 创建人姓名
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "insert_user_name")
    private String insertUserName;

    /**
     * 更新人
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 更新时间
     * 日期类型(6)
     * 非必填
     */
    @Column(name = "update_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 更新人姓名
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "update_user_name")
    private String updateUserName;

    /**
     * 企业代码
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "trade_code")
    private String tradeCode;
}