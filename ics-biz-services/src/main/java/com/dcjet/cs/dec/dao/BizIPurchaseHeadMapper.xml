<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.dec.dao.BizIPurchaseHeadMapper">
    <resultMap id="BaseResultMap" type="com.dcjet.cs.dec.model.BizIPurchaseHead">
        <id column="sid" property="sid" jdbcType="VARCHAR"/>
        <result column="insert_user" property="insertUser" jdbcType="VARCHAR"/>
        <result column="insert_time" property="insertTime" jdbcType="TIMESTAMP"/>
        <result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="update_user_name" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="trade_code" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="purchase_order_no" property="purchaseOrderNo" jdbcType="VARCHAR"/>
        <result column="head_id" property="headId" jdbcType="VARCHAR"/>
        <result column="vessel_voyage" property="vesselVoyage" jdbcType="VARCHAR"/>
        <result column="sailing_date" property="sailingDate" jdbcType="TIMESTAMP"/>
        <result column="expected_arrival_date" property="expectedArrivalDate" jdbcType="TIMESTAMP"/>
        <result column="entry_no" property="entryNo" jdbcType="VARCHAR"/>
        <result column="entry_date" property="entryDate" jdbcType="TIMESTAMP"/>
        <result column="purchasing_data_status" property="purchasingDataStatus" jdbcType="VARCHAR"/>
        <result column="purchase_confirmation_time" property="purchaseConfirmationTime" jdbcType="TIMESTAMP"/>
        <result column="version_no" property="versionNo" jdbcType="VARCHAR"/>
        <result column="data_status" property="dataStatus" jdbcType="VARCHAR"/>
        <result column="extend1" property="extend1" jdbcType="VARCHAR"/>
        <result column="extend2" property="extend2" jdbcType="VARCHAR"/>
        <result column="extend3" property="extend3" jdbcType="VARCHAR"/>
        <result column="extend4" property="extend4" jdbcType="VARCHAR"/>
        <result column="extend5" property="extend5" jdbcType="VARCHAR"/>
        <result column="extend6" property="extend6" jdbcType="VARCHAR"/>
        <result column="extend7" property="extend7" jdbcType="VARCHAR"/>
        <result column="extend8" property="extend8" jdbcType="VARCHAR"/>
        <result column="extend9" property="extend9" jdbcType="VARCHAR"/>
        <result column="extend10" property="extend10" jdbcType="VARCHAR"/>
        <result column="note" property="note" jdbcType="VARCHAR"/>
        <result column="plan_no" property="planNo" jdbcType="VARCHAR"/>
        <result column="serial_no" property="serialNo" jdbcType="NUMERIC"/>
        <result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
        <result column="business_type" property="businessType" jdbcType="VARCHAR"/>
        <result column="is_next" property="isNext" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
            t.sid,
            coalesce(t.update_user,t.insert_user) as insert_user,
            coalesce(t.update_time,t.insert_time) as insert_time,
            coalesce(t.update_user_name,t.insert_user_name) as insert_user_name,
            t.update_user, 
            t.update_time, 
            t.update_user_name, 
            t.trade_code, 
            t.purchase_order_no, 
            t.head_id, 
            t.vessel_voyage, 
            t.sailing_date, 
            t.expected_arrival_date, 
            t.entry_no, 
            t.entry_date, 
            t.purchasing_data_status, 
            t.purchase_confirmation_time, 
            t.version_no, 
            t.data_status, 
            t.extend1, 
            t.extend2, 
            t.extend3, 
            t.extend4, 
            t.extend5, 
            t.extend6, 
            t.extend7, 
            t.extend8, 
            t.extend9, 
            t.extend10,
            t.note,
            t.plan_no,
            t.serial_no,
            t.order_no,
            t.business_type,
            t.is_next
    </sql>

    <sql id="condition">
        <if test="isNext!= null and isNext!= ''">
            AND t.is_next LIKE '%' || #{isNext} || '%'
        </if>
        <if test="businessType!= null and businessType!= ''">
            AND t.business_type LIKE '%' || #{businessType} || '%'
        </if>
        <if test="sid != null and sid  != ''">
            AND t.sid LIKE '%' || #{sid} || '%'
        </if>
        <if test="insertUser != null and insertUser  != ''">
            AND t.insert_user LIKE '%' || #{insertUser} || '%'
        </if>
        <if test="insertTimeFrom != null and insertTimeFrom != ''">
            <![CDATA[ and t.insert_time >= to_timestamp(insertTimeFrom, 'yyyy-MM-dd hh24:mi:ss')::timestamp]]>
        </if>
        <if test="insertTimeTo != null and insertTimeTo != ''">
            <![CDATA[ and t.insert_time <= to_timestamp(insertTimeTo, 'yyyy-MM-dd hh24:mi:ss')::timestamp]]>
        </if>
        <if test="insertUserName != null and insertUserName  != ''">
            AND t.insert_user_name LIKE '%' || #{insertUserName} || '%'
        </if>
        <if test="updateUser != null and updateUser  != ''">
            AND t.update_user LIKE '%' || #{updateUser} || '%'
        </if>
        <if test="updateTimeFrom != null and updateTimeFrom != ''">
            <![CDATA[ and t.update_time >= to_timestamp(updateTimeFrom, 'yyyy-MM-dd hh24:mi:ss')::timestamp]]>
        </if>
        <if test="updateTimeTo != null and updateTimeTo != ''">
            <![CDATA[ and t.update_time <= to_timestamp(updateTimeTo, 'yyyy-MM-dd hh24:mi:ss')::timestamp]]>
        </if>
        <if test="updateUserName != null and updateUserName  != ''">
            AND t.update_user_name LIKE '%' || #{updateUserName} || '%'
        </if>
        <if test="tradeCode != null and tradeCode  != ''">
            AND t.trade_code LIKE '%' || #{tradeCode} || '%'
        </if>
        <if test="purchaseOrderNo != null and purchaseOrderNo  != ''">
            AND t.purchase_order_no LIKE '%' || #{purchaseOrderNo} || '%'
        </if>
        <if test="headId != null and headId  != ''">
            AND t.head_id LIKE '%' || #{headId} || '%'
        </if>
        <if test="vesselVoyage != null and vesselVoyage  != ''">
            AND t.vessel_voyage LIKE '%' || #{vesselVoyage} || '%'
        </if>
        <if test="sailingDateFrom != null and sailingDateFrom != ''">
            <![CDATA[ and t.sailing_date >= to_timestamp(sailingDateFrom, 'yyyy-MM-dd hh24:mi:ss')::timestamp]]>
        </if>
        <if test="sailingDateTo != null and sailingDateTo != ''">
            <![CDATA[ and t.sailing_date <= to_timestamp(sailingDateTo, 'yyyy-MM-dd hh24:mi:ss')::timestamp]]>
        </if>
        <if test="expectedArrivalDateFrom != null and expectedArrivalDateFrom != ''">
            <![CDATA[ and t.expected_arrival_date >= to_timestamp(expectedArrivalDateFrom, 'yyyy-MM-dd hh24:mi:ss')::timestamp]]>
        </if>
        <if test="expectedArrivalDateTo != null and expectedArrivalDateTo != ''">
            <![CDATA[ and t.expected_arrival_date <= to_timestamp(expectedArrivalDateTo, 'yyyy-MM-dd hh24:mi:ss')::timestamp]]>
        </if>
        <if test="entryNo != null and entryNo  != ''">
            AND t.entry_no LIKE '%' || #{entryNo} || '%'
        </if>
        <if test="entryDateFrom != null and entryDateFrom != ''">
            <![CDATA[ and t.entry_date >= to_timestamp(entryDateFrom, 'yyyy-MM-dd hh24:mi:ss')::timestamp]]>
        </if>
        <if test="entryDateTo != null and entryDateTo != ''">
            <![CDATA[ and t.entry_date <= to_timestamp(entryDateTo, 'yyyy-MM-dd hh24:mi:ss')::timestamp]]>
        </if>
        <if test="purchasingDataStatus != null and purchasingDataStatus  != ''">
            AND t.purchasing_data_status LIKE '%' || #{purchasingDataStatus} || '%'
        </if>
        <if test="purchaseConfirmationTimeFrom != null and purchaseConfirmationTimeFrom != ''">
            <![CDATA[ and t.purchase_confirmation_time >= to_timestamp(purchaseConfirmationTimeFrom, 'yyyy-MM-dd hh24:mi:ss')::timestamp]]>
        </if>
        <if test="purchaseConfirmationTimeTo != null and purchaseConfirmationTimeTo != ''">
            <![CDATA[ and t.purchase_confirmation_time <= to_timestamp(purchaseConfirmationTimeTo, 'yyyy-MM-dd hh24:mi:ss')::timestamp]]>
        </if>
        <if test="versionNo != null and versionNo  != ''">
            AND t.version_no LIKE '%' || #{versionNo} || '%'
        </if>
        <if test="dataStatus != null and dataStatus  != ''">
            AND t.data_status LIKE '%' || #{dataStatus} || '%'
        </if>
        <if test="extend1 != null and extend1  != ''">
            AND t.extend1 LIKE '%' || #{extend1} || '%'
        </if>
        <if test="extend2 != null and extend2  != ''">
            AND t.extend2 LIKE '%' || #{extend2} || '%'
        </if>
        <if test="extend3 != null and extend3  != ''">
            AND t.extend3 LIKE '%' || #{extend3} || '%'
        </if>
        <if test="extend4 != null and extend4  != ''">
            AND t.extend4 LIKE '%' || #{extend4} || '%'
        </if>
        <if test="extend5 != null and extend5  != ''">
            AND t.extend5 LIKE '%' || #{extend5} || '%'
        </if>
        <if test="extend6 != null and extend6  != ''">
            AND t.extend6 LIKE '%' || #{extend6} || '%'
        </if>
        <if test="extend7 != null and extend7  != ''">
            AND t.extend7 LIKE '%' || #{extend7} || '%'
        </if>
        <if test="extend8 != null and extend8  != ''">
            AND t.extend8 LIKE '%' || #{extend8} || '%'
        </if>
        <if test="extend9 != null and extend9  != ''">
            AND t.extend9 LIKE '%' || #{extend9} || '%'
        </if>
        <if test="extend10 != null and extend10  != ''">
            AND t.extend10 LIKE '%' || #{extend10} || '%'
        </if>
        <if test="note != null and note  != ''">
            AND t.note LIKE '%' || #{note} || '%'
        </if>
        <if test="planNo != null and planNo  != ''">
            AND t.plan_no LIKE '%' || #{planNo} || '%'
        </if>
        <if test="orderNo != null and orderNo  != ''">
            AND t.order_no LIKE '%' || #{orderNo} || '%'
        </if>
    </sql>

    <update id="updateOderHeadPurchaseNo">
        update T_BIZ_I_ORDER_HEAD t set PURCHASE_ORDER_NO = #{purchaseOrderNo} where t.sid = #{headId};
    </update>

    <select id="getList" resultMap="BaseResultMap" parameterType="com.dcjet.cs.dec.model.BizIPurchaseHead">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            t_biz_i_purchase_head t
        <where>
            <include refid="condition"></include>
        </where>
    </select>



    <select id="getEditDataByHeadId" resultType="com.dcjet.cs.dec.model.BizIPurchaseHead">
        select <include refid="Base_Column_List"/> from T_BIZ_I_PURCHASE_HEAD t where t.HEAD_ID = #{headId};
    </select>


    <!-- 用于校验进货单号是否已经存在 -->
    <select id="getOrderNoIsExists" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM T_BIZ_I_PURCHASE_HEAD
        WHERE PURCHASE_ORDER_NO = #{purchaseOrderNo}
          AND SID != #{sid} AND TRADE_CODE = #{tradeCode} and DATA_STATUS != '2';
    </select>
    <select id="getDeleteHeadId" resultType="java.lang.String">
        select distinct t.list_head_sid from  T_BIZ_I_PURCHASE_LIST_BOX t where t.sid in
        <foreach collection="sids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>;
    </select>


    <delete id="deleteBySids" parameterType="java.util.List">
        delete from  t_biz_i_purchase_head t where t.sid in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>


    <!-- 删除装箱子表  -->
    <delete id="deletePurchaseList">
        delete from  T_BIZ_I_PURCHASE_LIST_BOX t where t.sid in
        <foreach collection="sids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>;
    </delete>
</mapper>