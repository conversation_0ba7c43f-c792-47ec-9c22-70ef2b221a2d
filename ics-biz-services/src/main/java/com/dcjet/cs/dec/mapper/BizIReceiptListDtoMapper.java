package com.dcjet.cs.dec.mapper;

import com.dcjet.cs.dec.model.BizIReceiptList;
import com.dcjet.cs.dto.dec.BizIReceiptListDto;
import com.dcjet.cs.dto.dec.BizIReceiptListParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * BizIReceiptListDto
 *
 * <AUTHOR>
 * @date 2025-03-07 15:37:58
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizIReceiptListDtoMapper {

    /**
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizIReceiptListDto toDto(BizIReceiptList po);

    /**
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizIReceiptList toPo(BizIReceiptListParam param);

    /**
     * 数据库原始数据更新
     * @param BizIReceiptListParam
     * @param BizIReceiptList
     */
    void updatePo(BizIReceiptListParam BizIReceiptListParam, @MappingTarget BizIReceiptList BizIReceiptList);

    default void patchPo(BizIReceiptListParam BizIReceiptListParam , BizIReceiptList BizIReceiptList) {
        // TODO 自行实现局部更新
    }
}