<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.dec.dao.BizISellHeadMapper">
    <sql id="Base_Column_List">
        t.sid,
        t.head_id,
        t.purchase_order_number,
        t.purchasing_unit,
        t.selling_unit,
        t.tax_rate,
        t.date_of_sale,
        t.remark,
        t.sales_document_status,
        t.sales_data_confirmation_time,
        t.send_ufida,
        t.drawer,
        t.business_date,
        t.insert_user,
        t.insert_time,
        t.insert_user_name,
        t.update_user,
        t.update_time,
        t.update_user_name,
        t.trade_code
    </sql>

    <sql id="condition">

    </sql>
    <insert id="insertHeadList">
        insert into T_BIZ_I_SELL_HEAD(
                                      SID,
                                      HEAD_ID,
                                      PURCHASE_ORDER_NUMBER,
                                      PURCHASING_UNIT,
                                      SELLING_UNIT,
                                      date_of_sale,
                                      TAX_RATE,
                                      SALES_DOCUMENT_STATUS,
                                      SEND_UFIDA,
                                      DRAWER,
                                      BUSINESS_DATE,
                                      INSERT_USER,
                                      INSERT_USER_NAME,
                                      INSERT_TIME,
                                      TRADE_CODE)
        select
            #{sellSid},
            #{sid},
            h.order_no,
            wh.LADING_DEPARTMENT,
            '中国烟草上海进出口有限责任公司',
            now(),
            (select mi.TAX_RATE
             from T_BIZ_WAREHOUSE_RECEIPT_LIST wl
                      left join T_BIZ_MATERIAL_INFORMATION mi on mi.G_NAME = wl.GOODS_NAME
             where wl.parent_Id = wh.sid
            limit 1),
            '0',
            '0',
            #{userInfo.userName},
            now(),
            #{userInfo.userNo},
            #{userInfo.userName},
            now(),
            #{userInfo.company}
            from t_biz_i_order_head h
            left join T_BIZ_WAREHOUSE_RECEIPT_HEAD wh on wh.PARENT_ID = #{sid}
        where h.sid = #{sid};
        insert into T_BIZ_I_SELL_LIST(
                                      SID,
                                      HEAD_ID,
                                      TRADE_NAME,
                                      UNIT,
                                      QUANTITY,
                                      UNIT_PRICE_EXCLUDING_TAX,
                                      AMOUNT_OF_TAX,
                                      TAX_NOT_INCLUDED,
                                      TOTAL_VALUE_TAX,
                                      INSERT_USER,
                                      INSERT_USER_NAME,
                                      INSERT_TIME,
                                      TRADE_CODE)
        select
            sys_guid(),
            #{sellSid},
            lb.PRODUCT_GRADE,
            lb.UNIT,
            lb.quantity,
            mi.PRICE_EXCLUDING_TAX,
            lb.quantity*COALESCE(mi.TAX_RATE, 0),
            lb.quantity*COALESCE(mi.PRICE_EXCLUDING_TAX, 0),
            lb.quantity * (COALESCE(mi.PRICE_EXCLUDING_TAX, 0) + COALESCE(mi.TAX_RATE, 0)),
            #{userInfo.userNo},
            #{userInfo.userName},
            now(),
            #{userInfo.company}
        from T_BIZ_I_PURCHASE_LIST_BOX lb
        left join T_BIZ_I_PURCHASE_HEAD ph on lb.HEAD_ID = ph.sid
        left join T_BIZ_MATERIAL_INFORMATION mi on mi.G_NAME = lb.PRODUCT_GRADE
        where ph.HEAD_ID = #{sid};
    </insert>

    <select id="getList" resultType="com.dcjet.cs.dec.model.BizISellHead" parameterType="com.dcjet.cs.dec.model.BizISellHead">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
        T_BIZ_I_SELL_HEAD t
        <where>
            t.trade_code = #{tradeCode}
        </where>
    </select>
    <select id="getEditDataByHeadId" resultType="com.dcjet.cs.dec.model.BizISellHead">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        T_BIZ_I_SELL_HEAD t
        <where>
            t.head_id = #{headId}
        </where>
    </select>
</mapper>