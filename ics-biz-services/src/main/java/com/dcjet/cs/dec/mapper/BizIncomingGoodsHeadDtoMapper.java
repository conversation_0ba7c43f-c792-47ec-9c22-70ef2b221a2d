package com.dcjet.cs.dec.mapper;
import com.dcjet.cs.dec.model.BizIncomingGoodsHead;
import com.dcjet.cs.dto.dec.BizIncomingGoodsHeadDto;
import com.dcjet.cs.dto.dec.BizIncomingGoodsHeadParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;


/**
 * TBizIncomingGoodsHeadDto
 *
 * <AUTHOR>
 * @date 2025-05-22 15:21:28
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizIncomingGoodsHeadDtoMapper {

    /**
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizIncomingGoodsHeadDto toDto(BizIncomingGoodsHead po);

    /**
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizIncomingGoodsHead toPo(BizIncomingGoodsHeadParam param);

    /**
     * 数据库原始数据更新
     * @param tBizIncomingGoodsHeadParam
     * @param TBizIncomingGoodsHead
     */
    void updatePo(BizIncomingGoodsHeadParam tBizIncomingGoodsHeadParam, @MappingTarget BizIncomingGoodsHead tBizIncomingGoodsHead);

    default void patchPo(BizIncomingGoodsHeadParam tBizIncomingGoodsHeadParam , BizIncomingGoodsHead tBizIncomingGoodsHead) {
        // TODO 自行实现局部更新
    }
}