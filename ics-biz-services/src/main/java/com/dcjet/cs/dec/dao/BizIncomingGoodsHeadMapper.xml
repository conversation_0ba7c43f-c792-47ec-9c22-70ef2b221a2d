<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.dec.dao.BizIncomingGoodsHeadMapper">
    <resultMap id="BaseResultMap" type="com.dcjet.cs.dec.model.BizIncomingGoodsHead">
        <result column="ID" property="id" jdbcType="VARCHAR"/>
        <result column="BUSINESS_TYPE" property="businessType" jdbcType="VARCHAR"/>
        <result column="DATA_STATE" property="dataState" jdbcType="VARCHAR"/>
        <result column="VERSION_NO" property="versionNo" jdbcType="VARCHAR"/>
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="SYS_ORG_CODE" property="sysOrgCode" jdbcType="VARCHAR"/>
        <result column="PARENT_ID" property="parentId" jdbcType="VARCHAR"/>
        <result column="CREATE_BY" property="createBy" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_BY" property="updateBy" jdbcType="VARCHAR"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="INSERT_USER_NAME" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="EXTEND1" property="extend1" jdbcType="VARCHAR"/>
        <result column="EXTEND2" property="extend2" jdbcType="VARCHAR"/>
        <result column="EXTEND3" property="extend3" jdbcType="VARCHAR"/>
        <result column="EXTEND4" property="extend4" jdbcType="VARCHAR"/>
        <result column="EXTEND5" property="extend5" jdbcType="VARCHAR"/>
        <result column="EXTEND6" property="extend6" jdbcType="VARCHAR"/>
        <result column="EXTEND7" property="extend7" jdbcType="VARCHAR"/>
        <result column="EXTEND8" property="extend8" jdbcType="VARCHAR"/>
        <result column="EXTEND9" property="extend9" jdbcType="VARCHAR"/>
        <result column="EXTEND10" property="extend10" jdbcType="VARCHAR"/>
        <result column="CONTRACT_NO" property="contractNo" jdbcType="VARCHAR"/>
        <result column="PURCHASE_NO" property="purchaseNo" jdbcType="VARCHAR"/>
        <result column="CUSTOMER" property="customer" jdbcType="VARCHAR"/>
        <result column="SUPPLIER" property="supplier" jdbcType="VARCHAR"/>
        <result column="INVOICE_NO" property="invoiceNo" jdbcType="VARCHAR"/>
        <result column="PORT_OF_DEPARTURE" property="portOfDeparture" jdbcType="VARCHAR"/>
        <result column="DESTINATION" property="destination" jdbcType="VARCHAR"/>
        <result column="PAYMENT_METHOD" property="paymentMethod" jdbcType="VARCHAR"/>
        <result column="PRICE_TERM" property="priceTerm" jdbcType="VARCHAR"/>
        <result column="PRICE_TERM_PORT" property="priceTermPort" jdbcType="VARCHAR"/>
        <result column="VESSEL_VOYAGE" property="vesselVoyage" jdbcType="VARCHAR"/>
        <result column="SAILING_DATE" property="sailingDate" jdbcType="TIMESTAMP"/>
        <result column="EXPECTED_ARRIVAL_DATE" property="expectedArrivalDate" jdbcType="TIMESTAMP"/>
        <result column="SALES_DATE" property="salesDate" jdbcType="TIMESTAMP"/>
        <result column="CONTRACT_AMOUNT" property="contractAmount" jdbcType="NUMERIC"/>
        <result column="INSURANCE_RATE" property="insuranceRate" jdbcType="NUMERIC"/>
        <result column="INSURANCE_MARKUP" property="insuranceMarkup" jdbcType="NUMERIC"/>
        <result column="DOCUMENT_CREATOR" property="documentCreator" jdbcType="VARCHAR"/>
        <result column="DOCUMENT_DATE" property="documentDate" jdbcType="TIMESTAMP"/>
        <result column="DOCUMENT_STATUS" property="documentStatus" jdbcType="VARCHAR"/>
        <result column="CONFIRM_TIME" property="confirmTime" jdbcType="TIMESTAMP"/>
        <result column="APPROVAL_STATUS" property="approvalStatus" jdbcType="VARCHAR"/>
        <result column="DATE_OF_CONTRACT" property="dateOfContract" jdbcType="TIMESTAMP"/>
        <result column="IS_NEXT" property="isNext" jdbcType="TIMESTAMP"/>
        <result column="PURCHASE_CONTRACT_NO" property="purchaseContractNo" jdbcType="TIMESTAMP"/>
        <result column="ENTRY_NO" property="entryNo" jdbcType="TIMESTAMP"/>
        <result column="ENTRY_DATE" property="entryDate" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
            t.ID, 
            t.BUSINESS_TYPE, 
            t.DATA_STATE, 
            t.VERSION_NO, 
            t.TRADE_CODE, 
            t.SYS_ORG_CODE, 
            t.PARENT_ID, 
            t.CREATE_BY, 
            t.CREATE_TIME, 
            t.UPDATE_BY, 
            t.UPDATE_TIME, 
            t.INSERT_USER_NAME, 
            t.UPDATE_USER_NAME, 
            t.EXTEND1, 
            t.EXTEND2, 
            t.EXTEND3, 
            t.EXTEND4, 
            t.EXTEND5, 
            t.EXTEND6, 
            t.EXTEND7, 
            t.EXTEND8, 
            t.EXTEND9, 
            t.EXTEND10, 
            t.CONTRACT_NO, 
            t.PURCHASE_NO, 
            t.CUSTOMER, 
            t.SUPPLIER, 
            t.INVOICE_NO, 
            t.PORT_OF_DEPARTURE, 
            t.DESTINATION, 
            t.PAYMENT_METHOD, 
            t.PRICE_TERM, 
            t.PRICE_TERM_PORT, 
            t.VESSEL_VOYAGE, 
            t.SAILING_DATE, 
            t.EXPECTED_ARRIVAL_DATE, 
            t.SALES_DATE, 
            t.CONTRACT_AMOUNT, 
            t.INSURANCE_RATE, 
            t.INSURANCE_MARKUP, 
            t.DOCUMENT_CREATOR, 
            t.DOCUMENT_DATE, 
            t.DOCUMENT_STATUS, 
            t.CONFIRM_TIME, 
            t.APPROVAL_STATUS, 
            t.DATE_OF_CONTRACT,
            t.IS_NEXT,
            t.PURCHASE_CONTRACT_NO,
            t.ENTRY_NO,
            t.ENTRY_DATE
    </sql>

    <sql id="condition">
        <if test="createBy != null and createBy  != ''">
            AND t.CREATE_BY LIKE '%' || #{createBy} || '%'
        </if>
        <if test="createTimeFrom != null and createTimeFrom != ''">
            <![CDATA[ and t.CREATE_TIME >= to_timestamp(#{createTime},'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="createTimeTo != null and createTimeTo != ''">
            <![CDATA[ and t.CREATE_TIME < to_timestamp(#{createTime},'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day']]>
        </if>
        <if test="contractNo != null and contractNo  != ''">
            AND t.CONTRACT_NO LIKE '%' || #{contractNo} || '%'
        </if>
        <if test="purchaseNo != null and purchaseNo  != ''">
            AND t.PURCHASE_NO LIKE '%' || #{purchaseNo} || '%'
        </if>
        <if test="customer != null and customer  != ''">
            AND t.CUSTOMER LIKE '%' || #{customer} || '%'
        </if>
        <if test="supplier != null and supplier  != ''">
            AND t.SUPPLIER LIKE '%' || #{supplier} || '%'
        </if>
    </sql>

    <select id="getList" resultMap="BaseResultMap" parameterType="com.dcjet.cs.dec.model.BizIncomingGoodsHead">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            T_BIZ_INCOMING_GOODS_HEAD t
        <where>
            <include refid="condition"></include>
        </where>
    </select>
    <select id="getOrderSupplierList" resultType="java.util.Map">
        select
            distinct
            merchant_code as "value",
            merchant_name_cn as "label"
        from  T_BIZ_MERCHANT
        where TRADE_CODE = #{tradeCode};
    </select>
    <select id="getPortList" resultType="java.util.Map">
        select
            distinct
            params_code as "value",
            params_name as "label"
        from BIZ_TOBACOO.T_BIZ_CUSTOMS_PARAMS
        where PARAMS_TYPE = 'PORT'
          and TRADE_CODE = #{tradeCode}
    </select>
    <select id="getCurrList" resultType="java.util.Map">
        select
            distinct
            custom_param_code as "value",
            params_name as "label"
        from BIZ_TOBACOO.T_BIZ_CUSTOMS_PARAMS
        where PARAMS_TYPE = 'CURR'
          and TRADE_CODE = #{tradeCode}
    </select>
    <select id="getPriceTermList" resultType="java.util.Map">
        select param_code as "value",
               price_term as "label"
        from T_BIZ_PRICE_TERMS
        where TRADE_CODE = #{tradeCode};
    </select>


    <delete id="deleteBySids" parameterType="java.util.List">
        delete from  T_BIZ_INCOMING_GOODS_HEAD t where t.sid in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
</mapper>