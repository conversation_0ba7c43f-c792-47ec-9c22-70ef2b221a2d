package com.dcjet.cs.dec.dao;

import com.dcjet.cs.dec.model.BizIOrderDocument;
import com.dcjet.cs.dec.model.BizIOrderHead;
import com.dcjet.cs.dto.dec.BizIOrderExtractDto;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * 进口管理-订单信息表头Mapper
 */
public interface BizIOrderDocumentMapper extends Mapper<BizIOrderDocument>{

    /**
     * 查询获取数据
     * @param bizIOrderDocument
     * @return
     */
    BizIOrderDocument getList(BizIOrderDocument bizIOrderDocument);

    void updateHead(@Param("sid") String parentId,@Param("lNo")String lNom,@Param("pNo")String pNo);

}