package com.dcjet.cs.dec.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 进口管理-进货信息表头
 *
 * <AUTHOR>
 * @date 2025-03-07 15:37:58
 */
@Getter
@Setter
@Table(name = "T_BIZ_I_RECEIPT_LIST")
public class BizIReceiptList implements Serializable{
    /**
     * 主建SID
     * 字符类型(50)
     * 非必填
     */
    @Id
    @Column(name = "sid")
    private String sid;

    @Column(name = "head_id")
    private String headId;

    @Column(name = "trade_Name")
    private String tradeName;
    @Column(name = "shipment_Quantity")
    private BigDecimal shipmentQuantity;
    @Column(name = "actual_Quantity_Issued")
    private BigDecimal actualQuantityIssued;
    @Column(name = "unit")
    private String unit;
    @Column(name = "dec_price")
    private BigDecimal decPrice;
    @Column(name = "curr")
    private String curr;
    @Column(name = "amount")
    private BigDecimal amount;


    /**
     * 制单人
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "insert_user")
    private String insertUser;

    /**
     * 制单时间
     * 日期类型(6)
     * 非必填
     */
    @Column(name = "insert_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;


    /**
     * 创建人姓名
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "insert_user_name")
    private String insertUserName;

    /**
     * 更新人
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 更新时间
     * 日期类型(6)
     * 非必填
     */
    @Column(name = "update_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 更新人姓名
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "update_user_name")
    private String updateUserName;

    /**
     * 企业代码
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "trade_code")
    private String tradeCode;

}