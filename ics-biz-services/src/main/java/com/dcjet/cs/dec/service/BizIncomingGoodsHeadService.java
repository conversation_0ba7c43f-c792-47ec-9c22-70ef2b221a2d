package com.dcjet.cs.dec.service;



import com.dcjet.cs.dec.dao.BizIncomingGoodsHeadMapper;
import com.dcjet.cs.dec.mapper.BizIncomingGoodsHeadDtoMapper;
import com.dcjet.cs.dec.model.BizIncomingGoodsHead;
import com.dcjet.cs.dto.dec.BizIOrderHeadParam;
import com.dcjet.cs.dto.dec.BizIncomingGoodsHeadDto;
import com.dcjet.cs.dto.dec.BizIncomingGoodsHeadParam;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;

import java.util.*;
import java.util.stream.Collectors;

/**
 * TBizIncomingGoodsHead业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2025-05-22 15:28:59
 * 翻译使用：throw new ErrorException(400, XdoI18nUtil.t("xxxxxxxxxx"));
 */
@Service
public class BizIncomingGoodsHeadService extends BaseService<BizIncomingGoodsHead> {

    private static final Logger log = LoggerFactory.getLogger(BizIncomingGoodsHeadService.class);

    @Resource
    private BizIncomingGoodsHeadMapper tBizIncomingGoodsHeadMapper;

    @Resource
    private BizIncomingGoodsHeadDtoMapper tBizIncomingGoodsHeadDtoMapper;

    @Override
    public Mapper<BizIncomingGoodsHead> getMapper() {
        return tBizIncomingGoodsHeadMapper;
    }



    /**
     * 获取分页信息
     *
     * @param tBizIncomingGoodsHeadParam 查询参数
     * @param pageParam               分页参数
     * @return 分页结果
     */
    public ResultObject<List<BizIncomingGoodsHeadDto>> getListPaged(BizIncomingGoodsHeadParam tBizIncomingGoodsHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizIncomingGoodsHead tBizIncomingGoodsHead = tBizIncomingGoodsHeadDtoMapper.toPo(tBizIncomingGoodsHeadParam);
        tBizIncomingGoodsHead.setTradeCode(userInfo.getCompany());
        Page<BizIncomingGoodsHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
            .doSelectPage(() -> tBizIncomingGoodsHeadMapper.getList( tBizIncomingGoodsHead));
        // 将PO转为DTO返回给前端
        List<BizIncomingGoodsHeadDto> tBizIncomingGoodsHeadDtoList = page.getResult().stream()
            .map(tBizIncomingGoodsHeadDtoMapper::toDto)
            .collect(Collectors.toList());

        return ResultObject.createInstance(tBizIncomingGoodsHeadDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 新增记录
     *
     * @param tBizIncomingGoodsHeadParam 插入参数
     * @param userInfo                用户信息
     * @return 新增的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIncomingGoodsHeadDto insert(BizIncomingGoodsHeadParam tBizIncomingGoodsHeadParam, UserInfoToken userInfo) {
        BizIncomingGoodsHead tBizIncomingGoodsHead = tBizIncomingGoodsHeadDtoMapper.toPo(tBizIncomingGoodsHeadParam);
        
        // 规范固定字段
        String sid = UUID.randomUUID().toString();
        tBizIncomingGoodsHead.setId(sid);
        tBizIncomingGoodsHead.setCreateBy(userInfo.getUserNo());
        tBizIncomingGoodsHead.setCreateTime(new Date());
        tBizIncomingGoodsHead.setTradeCode(userInfo.getCompany());

        // 新增数据
        int insertStatus = tBizIncomingGoodsHeadMapper.insert(tBizIncomingGoodsHead);

        // 新增完成后 将数据转为DTO返回给前端
        return insertStatus > 0 ? tBizIncomingGoodsHeadDtoMapper.toDto(tBizIncomingGoodsHead) : null;
    }

    /**
     * 修改记录
     *
     * @param tBizIncomingGoodsHeadParam 更新参数
     * @param userInfo                用户信息
     * @return 更新后的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIncomingGoodsHeadDto update(BizIncomingGoodsHeadParam tBizIncomingGoodsHeadParam, UserInfoToken userInfo) {
        BizIncomingGoodsHead tBizIncomingGoodsHead = tBizIncomingGoodsHeadMapper.selectByPrimaryKey(tBizIncomingGoodsHeadParam.getId());
        tBizIncomingGoodsHeadDtoMapper.updatePo(tBizIncomingGoodsHeadParam, tBizIncomingGoodsHead);
        tBizIncomingGoodsHead.setCreateBy(userInfo.getUserNo());
        tBizIncomingGoodsHead.setUpdateTime(new Date());

        // 更新数据
        int update = tBizIncomingGoodsHeadMapper.updateByPrimaryKey(tBizIncomingGoodsHead);
        return update > 0 ? tBizIncomingGoodsHeadDtoMapper.toDto(tBizIncomingGoodsHead) : null;
    }

    /**
     * 批量删除记录
     *
     * @param sids     要删除的SID列表
     * @param userInfo 用户信息
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
       tBizIncomingGoodsHeadMapper.deleteBySids(sids);
    }



    /**
     * 功能描述:查询所有数据(导出查询)
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizIncomingGoodsHeadDto> selectAll(BizIncomingGoodsHeadParam exportParam, UserInfoToken userInfo) {
        BizIncomingGoodsHead tBizIncomingGoodsHead = tBizIncomingGoodsHeadDtoMapper.toPo(exportParam);
        tBizIncomingGoodsHead.setTradeCode(userInfo.getCompany());
        List<BizIncomingGoodsHeadDto> tBizIncomingGoodsHeadDtos = new ArrayList<>();
        List<BizIncomingGoodsHead> tBizIncomingGoodsHeadLists = tBizIncomingGoodsHeadMapper.getList(tBizIncomingGoodsHead);
        if (CollectionUtils.isNotEmpty(tBizIncomingGoodsHeadLists)) {
           tBizIncomingGoodsHeadDtos = tBizIncomingGoodsHeadLists.stream().map(head -> {
                    BizIncomingGoodsHeadDto dto =  tBizIncomingGoodsHeadDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return tBizIncomingGoodsHeadDtos;
    }



    /**
     * 进货管理-获取供应商列表信息
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    public ResultObject getSupplierList(BizIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功！");
        // 获取当前企业的供应商信息
        List<Map<String,String>>  list  = tBizIncomingGoodsHeadMapper.getOrderSupplierList(userInfo.getCompany());
        if (CollectionUtils.isEmpty(list)){
            resultObject.setMessage("未查询到相关供应商信息");
            return resultObject;
        }
        resultObject.setData(list);
        return resultObject;
    }

    /**
     * 进货管理-获取港口列表信息
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    public ResultObject getPortList(BizIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功！");
        // 获取当前企业的供应商信息
        List<Map<String,String>>  list  = tBizIncomingGoodsHeadMapper.getPortList(userInfo.getCompany());
        if (CollectionUtils.isEmpty(list)){
            resultObject.setMessage("未查询到相关港口信息");
            return resultObject;
        }
        resultObject.setData(list);
        return resultObject;
    }

    /**
     * 获取币制信息 (下拉框)
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    public ResultObject getCurrList(BizIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功！");
        // 获取当前企业的供应商信息
        List<Map<String,String>>  list  = tBizIncomingGoodsHeadMapper.getCurrList(userInfo.getCompany());
        if (CollectionUtils.isEmpty(list)){
            resultObject.setMessage("未查询到相关币制信息");
            return resultObject;
        }
        resultObject.setData(list);
        return resultObject;
    }

    /**
     * 进货管理-获取价格条款
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    public ResultObject getPriceTermList(BizIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功！");
        // 获取当前企业的供应商信息
        List<Map<String,String>>  list  = tBizIncomingGoodsHeadMapper.getPriceTermList(userInfo.getCompany());
        if (CollectionUtils.isEmpty(list)){
            resultObject.setMessage("未查询到相关价格条款信息");
            return resultObject;
        }
        resultObject.setData(list);
        return resultObject;
    }


    /**
     * 进货管理 确认
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    public ResultObject<BizIncomingGoodsHeadDto> confirmIncomingGoods(BizIncomingGoodsHeadParam params, UserInfoToken userInfo) {
        ResultObject<BizIncomingGoodsHeadDto> resultObject = ResultObject.createInstance(true, "确认成功！");
        BizIncomingGoodsHead po = tBizIncomingGoodsHeadDtoMapper.toPo(params);

        // 设置确认信息
        po.setConfirmTime(new Date());
        po.setDataState("1");
        po.setDocumentStatus("1");
        po.setIsNext("1");

        // 更新
        int update = tBizIncomingGoodsHeadMapper.updateByPrimaryKeySelective(po);
        if (update <= 0){
            resultObject.setMessage("确认失败！");
            return resultObject;
        }
        BizIncomingGoodsHeadDto dto = tBizIncomingGoodsHeadDtoMapper.toDto(po);
        resultObject.setData(dto);
        return resultObject;
    }
}