package com.dcjet.cs.dec.mapper;

import com.dcjet.cs.dec.model.BizISellList;
import com.dcjet.cs.dto.dec.BizISellListDto;
import com.dcjet.cs.dto.dec.BizISellListParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * BizISellListDto
 *
 * <AUTHOR>
 * @date 2025-03-07 15:37:58
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizISellListDtoMapper {

    /**
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizISellListDto toDto(BizISellList po);

    /**
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizISellList toPo(BizISellListParam param);

    /**
     * 数据库原始数据更新
     * @param BizISellListParam
     * @param BizISellList
     */
    void updatePo(BizISellListParam BizISellListParam, @MappingTarget BizISellList BizISellList);

    default void patchPo(BizISellListParam BizISellListParam , BizISellList BizISellList) {
        // TODO 自行实现局部更新
    }
}