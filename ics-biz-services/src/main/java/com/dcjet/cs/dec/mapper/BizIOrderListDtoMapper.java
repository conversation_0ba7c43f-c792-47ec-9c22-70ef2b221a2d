package com.dcjet.cs.dec.mapper;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
import com.dcjet.cs.dec.model.BizIOrderList;
import com.dcjet.cs.dto.dec.BizIOrderListParam;
import com.dcjet.cs.dto.dec.BizIOrderListDto;

/**
 * BizIOrderListDto
 *
 * <AUTHOR>
 * @date 2025-03-07 15:37:37
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizIOrderListDtoMapper {

    /**
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizIOrderListDto toDto(BizIOrderList po);

    /**
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizIOrderList toPo(BizIOrderListParam param);

    /**
     * 数据库原始数据更新
     * @param bizIOrderListParam
     * @param BizIOrderList
     */
    void updatePo(BizIOrderListParam bizIOrderListParam, @MappingTarget BizIOrderList bizIOrderList);

    default void patchPo(BizIOrderListParam bizIOrderListParam , BizIOrderList bizIOrderList) {
        // TODO 自行实现局部更新
    }
}