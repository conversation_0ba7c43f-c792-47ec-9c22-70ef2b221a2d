package com.dcjet.cs.dec.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 进口管理-订单信息表头
 *
 * <AUTHOR>
 * @date 2025-03-07 15:37:18
 */
@Getter
@Setter
@Table(name = "t_biz_warehouse_receipt_head")
public class BizIWarehouseReceiptHead implements Serializable{
    private static final long serialVersionUID = 1L;
    /**
     * 主建sid
     * 字符类型(50)
     * 非必填
     */
    @Id
    @Column(name = "sid")
    private String sid;

    /**
     * 制单人
     */
    @Column(name = "insert_user")
    private String insertUser;

    /**
     * 订单制单时间
     * timestamp
     * 非必填
     */
    @Column(name = "insert_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;

    @Transient
    private String insertTimeString;

    /**
     * 订单制单时间-开始时间
     */
    @Transient
    private String insertTimeFrom;

    /**
     * 订单制单时间-结束时间
     */
    @Transient
    private String insertTimeTo;

    /**
     * 创建人姓名
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "insert_user_name")
    private String insertUserName;

    /**
     * 更新人
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 更新时间
     * timestamp
     * 非必填
     */
    @Column(name = "update_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 更新时间-开始时间
     */
    @Transient
    private String updateTimeFrom;

    /**
     * 更新时间-结束时间
     */
    @Transient
    private String updateTimeTo;

    /**
     * 更新人姓名
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "update_user_name")
    private String updateUserName;

    /**
     * 企业代码
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "trade_code")
    private String tradeCode;

    /**
     * 业务类型
     * 字符类型(60)
     * 非必填
     */
    @Column(name = "business_type")
    private String businessType;

    /**
     */
    @Column(name = "parent_id")
    private String parentId;

    /**
     * 版本号
     * 字符类型(10)
     * 非必填
     */
    @Column(name = "version_no")
    private String versionNo;

    /**
     * 数据状态
     * 字符类型(10)
     * 非必填
     */
    @Column(name = "data_status")
    private String dataStatus;

    /**
     * 拓展字段1
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend1")
    private String extend1;

    /**
     * 拓展字段2
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend2")
    private String extend2;

    /**
     * 拓展字段3
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend3")
    private String extend3;

    /**
     * 拓展字段4
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend4")
    private String extend4;

    /**
     * 拓展字段5
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend5")
    private String extend5;

    /**
     * 拓展字段6
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend6")
    private String extend6;

    /**
     * 拓展字段7
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend7")
    private String extend7;

    /**
     * 拓展字段8
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend8")
    private String extend8;

    /**
     * 拓展字段9
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend9")
    private String extend9;

    /**
     * 拓展字段10
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend10")
    private String extend10;

    /**
     *入库回单编号
     */
    @Column(name = "warehouse_receipt_number")
    private String warehouseReceiptNumber;
    /**
     *合同号
     */
    @Column(name = "contract_number")
    private String contractNumber;

    /**
     *订单号
     */
    @Column(name = "order_no")
    private String orderNo;

    /**
     *进仓编号
     */
    @Column(name = "warehouse_entry_number")
    private String warehouseEntryNumber;

    /**
     *提货单号
     */
    @Column(name = "lading_number")
    private String ladingNumber;
    /**
     *提货单位
     */
    @Column(name = "lading_department")
    private String ladingDepartment;
    /**
     *进口发票号码
     */
    @Column(name = "invoice_number")
    private String invoiceNumber;
    /**
     *供应商
     */
    @Column(name = "supplier")
    private String supplier;
    /**
     *仓库
     */
    @Column(name = "warehouse")
    private String warehouse;
    /**
     *币种
     */
    @Column(name = "curr")
    private String curr;
    /**
     *卖出价（汇率）
     */
    @Column(name = "selling_rate")
    private BigDecimal sellingRate;

    /**
     *汇率
     */
    @Column(name = "rate")
    private BigDecimal rate;
    /**
     *税单日期
     */
    @Column(name = "tax_invoice_date")
    private Date taxInvoiceDate;
    /**
     *进仓日期
     */
    @Column(name = "entry_date")
    private Date entryDate;

    /**
     *进仓日期
     */
    @Transient
    private String entryDateString;
    /**
     *出库日期
     */
    @Column(name = "outdate")
    private Date outdate;

    @Transient
    private String outdateString;

    /**
     *客户折扣率
     */
    @Column(name = "discount_rate")
    private BigDecimal discountRate;
    /**
     *发送用友
     */
    @Column(name = "send_to_yongyou")
    private String sendToYongyou;
    /**
     *备注
     */
    @Column(name = "note")
    private String note;
    /**
     *入库单据状态
     */
    @Column(name = "status")
    private String status;


    @Transient
    private BigDecimal sumQty;

    @Transient
    private String sumQtyStr;

    /**
     * 仓库名称
     */
    @Transient
    private  String storehouseName;

    /**
     * 客商中文名称
     */
    @Transient
    private  String merchantNameCn;


    /**
     * 是否流入下一个节点
     */
    @Column(name = "is_next")
    private String isNext;

    /**
     * 业务日期
     */
    @Column(name = "business_date")
    private Date businessDate;

}