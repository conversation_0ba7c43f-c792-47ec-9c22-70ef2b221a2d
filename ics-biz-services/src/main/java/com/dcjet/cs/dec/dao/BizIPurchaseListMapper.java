package com.dcjet.cs.dec.dao;

import com.dcjet.cs.dec.model.BizIPurchaseList;
import com.dcjet.cs.dto.dec.BizIPurchaseListInvoiceNoSumData;
import com.dcjet.cs.dto.dec.BizIPurchaseListSumData;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import tk.mybatis.mapper.common.Mapper;

/**
 * 进口管理-进货信息表体Mapper
 */
public interface BizIPurchaseListMapper extends Mapper<BizIPurchaseList>{

    /**
     * 查询获取数据
     * @param bizIPurchaseList
     * @return
     */
    List<BizIPurchaseList> getList(BizIPurchaseList bizIPurchaseList);

    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(@Param("list")List<String> sids);


    /**
     * 进货表体更新发票号 联动 更新装箱子表发票号
     * @param po po
     * @return 返回执行成功行数，0为执行失败
     */
    int updateInvoiceNo(BizIPurchaseList po);


    /**
     * 获取进货信息表体汇总数据
     * @param headId 进货信息表头ID
     * @return 返回汇总数据
     */
    BizIPurchaseListSumData getSumData(@Param("headId") String headId);


    /**
     * (根据发票号)获取汇总数据 数量、总值、折扣金额、货款金额
     * @param headId 表头sid
     * @return 汇总数据
     */
    List<BizIPurchaseListInvoiceNoSumData> getSumDataByInvoice(@Param("headId") String headId);

    /**
     * 更新订单信息表头的 进口发票号 汇总字段
     * @param headId 订单信息表头sid
     * @return 返回执行成功行数，0为执行失败
     */
    int updateIOrderHeadInvoiceNo(@Param("headId") String headId);


    /**
     * 获取进货信息表体数据
     * @param oldHeadSid 订单表头sid
     * @return 返回进货信息表体数据
     */
    List<BizIPurchaseList> getPurchaseListByHeadId(@Param("oldHeadSid") String oldHeadSid);
    List<BizIPurchaseList> getPurchaseListByPHeadId(@Param("pHeadSid") String pHeadSid);


    /**
     * 更新装箱子表的进口发票号
     * @param invoiceNo 进口发票号
     * @param sid 商品子表的sid
     * @return 返回执行成功行数，0为执行失败
     */
    int updateBoxInvoiceNo(@Param("invoiceNo") String invoiceNo, @Param("sid") String sid);
}