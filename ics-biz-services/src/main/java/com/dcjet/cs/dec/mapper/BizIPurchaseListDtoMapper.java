package com.dcjet.cs.dec.mapper;
import com.dcjet.cs.dec.model.BizIPurchaseListBox;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
import com.dcjet.cs.dec.model.BizIPurchaseList;
import com.dcjet.cs.dto.dec.BizIPurchaseListParam;
import com.dcjet.cs.dto.dec.BizIPurchaseListDto;

/**
 * BizIPurchaseListDto
 *
 * <AUTHOR>
 * @date 2025-03-07 15:38:12
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizIPurchaseListDtoMapper {

    /**
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizIPurchaseListDto toDto(BizIPurchaseList po);

    /**
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizIPurchaseList toPo(BizIPurchaseListParam param);


    BizIPurchaseListBox toBoxList(BizIPurchaseListParam param);

    /**
     * 数据库原始数据更新
     * @param bizIPurchaseListParam
     * @param BizIPurchaseList
     */
    void updatePo(BizIPurchaseListParam bizIPurchaseListParam, @MappingTarget BizIPurchaseList bizIPurchaseList);

    default void patchPo(BizIPurchaseListParam bizIPurchaseListParam , BizIPurchaseList bizIPurchaseList) {
        // TODO 自行实现局部更新
    }
}