package com.dcjet.cs.dec.model;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Id;
import lombok.Getter;
import lombok.Setter;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.io.Serializable;


/**
 * 进货管理-投保信息
 *
 * <AUTHOR>
 * @date 2025-05-24 13:25:43
 */
@Getter
@Setter
@Table(name = "t_biz_incoming_goods_insure")
public class BizIncomingGoodsInsure implements Serializable{
    private static final long serialVersionUID = 1L;
    /**
     * 主键ID，系统自动生成
     * 字符类型(40)
     * 必填
     */
    @Column(name = "id")
    @Id
    private String id;

    /**
     * 业务类型
     * 字符类型(60)
     * 非必填
     */
    @Column(name = "business_type")
    private String businessType;

    /**
     * 数据状态
     * 字符类型(10)
     * 非必填
     */
    @Column(name = "data_state")
    private String dataState;

    /**
     * 版本号
     * 字符类型(10)
     * 非必填
     */
    @Column(name = "version_no")
    private String versionNo;

    /**
     * 交易代码
     * 字符类型(10)
     * 非必填
     */
    @Column(name = "trade_code")
    private String tradeCode;

    /**
     * 系统机构代码
     * 字符类型(10)
     * 非必填
     */
    @Column(name = "sys_org_code")
    private String sysOrgCode;

    /**
     * 父级ID
     * 字符类型(40)
     * 非必填
     */
    @Column(name = "parent_id")
    private String parentId;

    /**
     * 创建人
     * 字符类型(50)
     * 必填
     */
    @Column(name = "create_by")
    private String createBy;

    /**
     * 创建时间
     * 日期类型(6)
     * 必填
     */
    @Column(name = "create_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 创建时间-开始时间
     */
    @Transient
    private Date createTimeFrom;

    /**
     * 创建时间-结束时间
     */
    @Transient
    private Date createTimeTo;

    /**
     * 更新人
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "update_by")
    private String updateBy;

    /**
     * 更新时间
     * 日期类型(6)
     * 非必填
     */
    @Column(name = "update_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 更新时间-开始时间
     */
    @Transient
    private Date updateTimeFrom;

    /**
     * 更新时间-结束时间
     */
    @Transient
    private Date updateTimeTo;

    /**
     * 插入用户名
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "insert_user_name")
    private String insertUserName;

    /**
     * 更新用户名
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "update_user_name")
    private String updateUserName;

    /**
     * 扩展字段1
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend1")
    private String extend1;

    /**
     * 扩展字段2
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend2")
    private String extend2;

    /**
     * 扩展字段3
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend3")
    private String extend3;

    /**
     * 扩展字段4
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend4")
    private String extend4;

    /**
     * 扩展字段5
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend5")
    private String extend5;

    /**
     * 扩展字段6
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend6")
    private String extend6;

    /**
     * 扩展字段7
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend7")
    private String extend7;

    /**
     * 扩展字段8
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend8")
    private String extend8;

    /**
     * 扩展字段9
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend9")
    private String extend9;

    /**
     * 扩展字段10
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend10")
    private String extend10;

    /**
     * 准运证编号
     * 字符类型(120)
     * 非必填
     */
    @Column(name = "permit_no")
    private String permitNo;

    /**
     * 准运证申办日期
     * date
     * 非必填
     */
    @Column(name = "permit_apply_date")
    private Date permitApplyDate;

    /**
     * 报关单号
     * 字符类型(36)
     * 非必填
     */
    @Column(name = "customs_declaration_no")
    private String customsDeclarationNo;

    /**
     * 申报日期
     * date
     * 非必填
     */
    @Column(name = "declaration_date")
    private Date declarationDate;

    /**
     * 放行日期
     * date
     * 非必填
     */
    @Column(name = "release_date")
    private Date releaseDate;

    /**
     * 编号
     * 字符类型(120)
     * 非必填
     */
    @Column(name = "code_no")
    private String codeNo;

    /**
     * 保险公司
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "insurance_company")
    private String insuranceCompany;

    /**
     * 被保险人
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "insured_person")
    private String insuredPerson;

    /**
     * 投保险别
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "insurance_type")
    private String insuranceType;

    /**
     * 币种
     * 字符类型(20)
     * 非必填
     */
    @Column(name = "currency")
    private String currency;

    /**
     * 保险金额
     * 数值类型(19,4)
     * 非必填
     */
    @Column(name = "insurance_amount")
    private BigDecimal insuranceAmount;

    /**
     * 保险费率
     * 数值类型(19,4)
     * 非必填
     */
    @Column(name = "insurance_rate")
    private BigDecimal insuranceRate;

    /**
     * 保费
     * 数值类型(19,2)
     * 非必填
     */
    @Column(name = "premium")
    private BigDecimal premium;

    /**
     * 备注
     * 字符类型(400)
     * 非必填
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 表头head_id
     * 字符类型(80)
     * 非必填
     */
    @Column(name = "head_id")
    private String headId;


}