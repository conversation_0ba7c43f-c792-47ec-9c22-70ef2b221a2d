package com.dcjet.cs.dec.mapper;

import com.dcjet.cs.dec.model.BizIReceiptHead;
import com.dcjet.cs.dto.dec.BizIReceiptHeadDto;
import com.dcjet.cs.dto.dec.BizIReceiptHeadParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * BizIReceiptHeadDto
 *
 * <AUTHOR>
 * @date 2025-03-07 15:37:58
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizIReceiptHeadDtoMapper {

    /**
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizIReceiptHeadDto toDto(BizIReceiptHead po);

    /**
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizIReceiptHead toPo(BizIReceiptHeadParam param);

    /**
     * 数据库原始数据更新
     * @param BizIReceiptHeadParam
     * @param BizIReceiptHead
     */
    void updatePo(BizIReceiptHeadParam BizIReceiptHeadParam, @MappingTarget BizIReceiptHead BizIReceiptHead);

    default void patchPo(BizIReceiptHeadParam BizIReceiptHeadParam , BizIReceiptHead BizIReceiptHead) {
        // TODO 自行实现局部更新
    }
}