package com.dcjet.cs.dec.model;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.SequenceGenerator;
import java.math.BigDecimal;
@Getter
@Setter
public class OBillJck {
    /**
     * 单据类型
     */
    @Column(name = "RD_TYPE")
    private String rdType;

    /**
     * 单据ID
     */
    @Column(name = "BILLID")
    private String billid;

    /**
     * 单据编号
     */
    @Column(name = "BILLCODE")
    private String billcode;

    /**
     * 单据日期
     */
    @Column(name = "BILLDATE")
    private String billdate;

    /**
     * 收付中心
     */
    @Column(name = "RDCENTER")
    private String rdcenter;

    /**
     * 公司
     */
    @Column(name = "COMPANY")
    private String company;

    /**
     * 部门档案
     */
    @Column(name = "DEPTDOC")
    private String deptdoc;

    /**
     * 人员
     */
    @Column(name = "PERSON")
    private String person;

    /**
     * 制单人
     */
    @Column(name = "MAKER")
    private String maker;

    /**
     * 客户
     */
    @Column(name = "CUST")
    private String cust;

    /**
     * 结算单位名称
     */
    @Column(name = "NAME_CALBODY")
    private String nameCalbody;

    /**
     * 部门名称
     */
    @Column(name = "NAME_DEPTDOC")
    private String nameDeptdoc;

    /**
     * 人员名称
     */
    @Column(name = "NAME_PSNDOC")
    private String namePsndoc;

    /**
     * 操作员名称
     */
    @Column(name = "NAME_OPERATOR")
    private String nameOperator;

    /**
     * 客户档案名称
     */
    @Column(name = "NAME_CUMANDOC")
    private String nameCumandoc;

    /**
     * 流水号(主键)
     */
    @Column(name = "MQ_LSH")
    private Integer mqLsh;

    /**
     * 操作类型
     */
    @Column(name = "MQ_OP")
    private String mqOp;

    /**
     * 状态
     */
    @Column(name = "MQ_ST")
    private String mqSt;

    /**
     * 计数
     */
    @Column(name = "MQ_COUNT")
    private Integer mqCount;

    /**
     * 公司主键
     */
    @Column(name = "PK_CORP")
    private String pkCorp;

    /**
     * 时间戳
     */
    @Column(name = "TS")
    private String ts;

    /**
     * 删除标记
     */
    @Column(name = "DR")
    private Integer dr;

    /**
     * 合同号
     */
    @Column(name = "HTH")
    private String hth;

    /**
     * 档案号
     */
    @Column(name = "FILENO")
    private String fileno;

    /**
     * 仓库
     */
    @Column(name = "STORAGE")
    private String storage;

    /**
     * 仓库名称
     */
    @Column(name = "NAME_STORAGE")
    private String nameStorage;

    /**
     * 收付中心(内部)
     */
    @Column(name = "INRDCENTER")
    private String inrdcenter;

    /**
     * 结算单位名称(内部)
     */
    @Column(name = "NAME_INCALBODY")
    private String nameIncalbody;

    /**
     * 仓库(内部)
     */
    @Column(name = "INSTORAGE")
    private String instorage;

    /**
     * 仓库名称(内部)
     */
    @Column(name = "NAME_INSTORAGE")
    private String nameInstorage;

    /**
     * 是否预收
     */
    @Column(name = "ISADVANCE")
    private String isadvance;

    /**
     * 银行账号
     */
    @Column(name = "ACCBANKNO")
    private String accbankno;

    /**
     * 银行名称
     */
    @Column(name = "NAME_ACCBANK")
    private String nameAccbank;

    /**
     * 币种编码
     */
    @Column(name = "CURRTYPECODE")
    private String currtypecode;

    /**
     * 币种名称
     */
    @Column(name = "CURRTYPENAME")
    private String currtypename;

    /**
     * 结算方式编码
     */
    @Column(name = "JSCODE")
    private String jscode;

    /**
     * 结算方式名称
     */
    @Column(name = "JSNAME")
    private String jsname;

    /**
     * 单据号码
     */
    @Column(name = "BILLNUM")
    private String billnum;

    /**
     * 付款单号
     */
    @Column(name = "PAYNOTENO")
    private String paynoteno;

    /**
     * 费用类型
     */
    @Column(name = "FEETYPE")
    private String feetype;

    /**
     * 总金额
     */
    @Column(name = "TOTALMONEY")
    private BigDecimal totalmoney;

    /**
     * 单据号
     */
    @Column(name = "BILLNO")
    private String billno;

    /**
     * 调整日期
     */
    @Column(name = "ADJUSTDATE")
    private String adjustdate;

    /**
     * 行数
     */
    @Column(name = "ROW_COUNT")
    private Integer rowCount;

    /**
     * 当前汇率
     */
    @Column(name = "CURRENTRATE")
    private BigDecimal currentrate;

    /**
     * 报表金额
     */
    @Column(name = "BBJE")
    private BigDecimal bbje;
}
