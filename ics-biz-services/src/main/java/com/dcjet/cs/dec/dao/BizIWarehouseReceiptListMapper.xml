<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.dec.dao.BizIWarehouseReceiptListMapper">



    <select id="getList" resultType="com.dcjet.cs.dec.model.BizIWarehouseReceiptList" parameterType="com.dcjet.cs.dec.model.BizIWarehouseReceiptList">
        SELECT
            *
        FROM
        t_biz_warehouse_receipt_list t
        <where>
            t.parent_id = #{parentId}
        </where>
    </select>


    <select id="getSumData" resultType="com.dcjet.cs.dec.model.BizIWarehouseReceiptList">
         select sum(foreign_prices)             as foreign_prices,
               sum(rmb_prices)       as rmb_prices,
               sum(tariff) as tariff,
               sum(consumption_tax)  as consumption_tax,

 sum(value_added_tax)       as value_added_tax,
               sum(tax_amount) as tax_amount,
               sum(cost_amount)  as cost_amount,
                sum(total_amount)  as total_amount

        from t_biz_warehouse_receipt_list
        where parent_id = #{parentId}
    </select>

    <update id="updateListTaxes">
        update t_biz_warehouse_receipt_list as t
        set TARIFF = (select SUM(l.EXPENSE_AMOUNT) from T_BIZ_EXPENSE_I_LIST l
                                                       left join T_BIZ_COST_TYPE ct on ct.PARAM_CODE = l.EXPENSE_TYPE and ct.COMMON_FLAG like '%1%' and ct.TRADE_CODE = l.TRADE_CODE
                                                       left join T_BIZ_EXPENSE_I_HEAD h on h.sid = l.HEAD_ID
                               where ct.COST_NAME in ('海关关税') and l.PURCHASE_NUMBER = #{warehouseReceiptNumber} and l.PRODUCT_NAME = t.GOODS_NAME and l.trade_code = #{tradeCode} and h.STATE != '2'),
            CONSUMPTION_TAX = (select SUM(l.EXPENSE_AMOUNT) from T_BIZ_EXPENSE_I_LIST l
                                                        left join T_BIZ_COST_TYPE ct on ct.PARAM_CODE = l.EXPENSE_TYPE and ct.COMMON_FLAG like '%1%' and ct.TRADE_CODE = l.TRADE_CODE
                                                        left join T_BIZ_EXPENSE_I_HEAD h on h.sid = l.HEAD_ID
                               where ct.COST_NAME in ('海关增值税') and l.PURCHASE_NUMBER = #{warehouseReceiptNumber} and l.PRODUCT_NAME = t.GOODS_NAME and l.trade_code = #{tradeCode} and h.STATE != '2'),
            VALUE_ADDED_TAX = (select SUM(l.EXPENSE_AMOUNT) from T_BIZ_EXPENSE_I_LIST l
                                                        left join T_BIZ_COST_TYPE ct on ct.PARAM_CODE = l.EXPENSE_TYPE and ct.COMMON_FLAG like '%1%' and ct.TRADE_CODE = l.TRADE_CODE
                                                        left join T_BIZ_EXPENSE_I_HEAD h on h.sid = l.HEAD_ID
                               where ct.COST_NAME in ('海关消费税') and l.PURCHASE_NUMBER = #{warehouseReceiptNumber} and l.PRODUCT_NAME = t.GOODS_NAME and l.trade_code = #{tradeCode} and h.STATE != '2'),
            update_user = #{userNo},
            update_time = now(),
            EXTEND10 = '1'
        where t.PARENT_ID = #{parentId} and coalesce(t.EXTEND10, '0') = '0';
    </update>

    <select id="getListForFile" resultType="com.dcjet.cs.dec.model.BizIWarehouseReceiptList">
        select goods_name,qty,invoice_number,foreign_unit_price,rmb_unit_price,
               foreign_prices,rmb_prices,tariff,consumption_tax,value_added_tax,
               coalesce(tariff,0)+coalesce(consumption_tax,0)+coalesce(value_added_tax,0)  as tax_amount,
               coalesce(rmb_prices,0)+coalesce(tariff,0)+coalesce(consumption_tax,0)  as cost_amount,
               coalesce(rmb_prices,0)+coalesce(tariff,0)+coalesce(consumption_tax,0) +coalesce(value_added_tax,0)  as total_amount,
               ROW_NUMBER() OVER (ORDER BY insert_time) AS serialNo
        from T_BIZ_WAREHOUSE_RECEIPT_LIST
        where parent_id =#{parentId}
        order by insert_time
    </select>

    <select id="getPrintOfLadingList" resultType="com.dcjet.cs.dec.model.BizIWarehouseReceiptList">
        SELECT
        t.*,ma.bar_code
        FROM
        t_biz_warehouse_receipt_list t left join  T_BIZ_MATERIAL_INFORMATION ma on
        t.goods_name=ma.g_name
        <where>
            t.parent_id = #{parentId}
        </where>
    </select>
</mapper>