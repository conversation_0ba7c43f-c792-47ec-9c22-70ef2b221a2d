package com.dcjet.cs.dec.dao;


import com.dcjet.cs.dec.model.BizIncomingGoodsHead;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;

import tk.mybatis.mapper.common.Mapper;

/**
 * 进货管理-表头数据Mapper
 */
public interface BizIncomingGoodsHeadMapper extends Mapper<BizIncomingGoodsHead>{

    /**
     * 查询获取数据
     * @param tBizIncomingGoodsHead
     * @return
     */
    List<BizIncomingGoodsHead> getList(BizIncomingGoodsHead tBizIncomingGoodsHead);

    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(@Param("list")List<String> sids);



    /**
     * 进货管理-获取供应商列表信息
     * @param tradeCode 企业代码
     * @return 返回结果
     */
    List<Map<String, String>> getOrderSupplierList(@Param("tradeCode") String tradeCode);

    /**
     * 进货管理-获取港口列表信息
     * @param tradeCode 企业代码
     * @return 返回结果
     */
    List<Map<String, String>> getPortList(@Param("tradeCode") String tradeCode);

    /**
     * 进货管理-获取币别列表信息
     * @param tradeCode 企业代码
     * @return 返回结果
     */
    List<Map<String, String>> getCurrList(@Param("tradeCode") String tradeCode);


    /**
     * 进货管理-获取价格条款
     * @param tradeCode 企业代码
     * @return 返回结果
     */
    List<Map<String, String>> getPriceTermList(@Param("tradeCode") String tradeCode);
}