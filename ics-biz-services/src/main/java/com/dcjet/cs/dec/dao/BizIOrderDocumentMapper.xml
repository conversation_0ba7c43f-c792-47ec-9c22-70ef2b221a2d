<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.dec.dao.BizIOrderDocumentMapper">

    <sql id="condition">
        <if test="parentId != null and parentId  != ''">
            AND t.parent_id LIKE '%' || #{parentId} || '%'
        </if>

        <if test="tradeCode != null and tradeCode  != ''">
            AND t.trade_code LIKE '%' || #{tradeCode} || '%'
        </if>
        limit 1
    </sql>
    <update id="updateHead">
        UPDATE T_BIZ_I_ORDER_HEAD r
        SET
            LICENSE_NO = #{lNo},
            TRANSPORT_PERMIT_NO = #{pNo}
        WHERE r.sid = #{sid};
    </update>

    <select id="getList" resultType="com.dcjet.cs.dec.model.BizIOrderDocument" parameterType="com.dcjet.cs.dec.model.BizIOrderDocument">
        SELECT
            *
        FROM
        t_biz_i_order_document t
        <where>
            <include refid="condition"></include>
        </where>
    </select>
</mapper>