package com.dcjet.cs.dec.mapper;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
import com.dcjet.cs.dec.model.BizIOrderHead;
import com.dcjet.cs.dto.dec.BizIOrderHeadParam;
import com.dcjet.cs.dto.dec.BizIOrderHeadDto;

/**
 * BizIOrderHeadDto
 *
 * <AUTHOR>
 * @date 2025-03-07 15:37:18
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizIOrderHeadDtoMapper {

    /**
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizIOrderHeadDto toDto(BizIOrderHead po);

    /**
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizIOrderHead toPo(BizIOrderHeadParam param);

    /**
     * 数据库原始数据更新
     * @param bizIOrderHeadParam
     * @param BizIOrderHead
     */
    void updatePo(BizIOrderHeadParam bizIOrderHeadParam, @MappingTarget BizIOrderHead bizIOrderHead);

    default void patchPo(BizIOrderHeadParam bizIOrderHeadParam , BizIOrderHead bizIOrderHead) {
        // TODO 自行实现局部更新
    }
}