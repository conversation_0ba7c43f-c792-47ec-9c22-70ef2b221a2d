package com.dcjet.cs.dec.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 进口管理-进货信息表头
 *
 * <AUTHOR>
 * @date 2025-03-07 15:37:58
 */
@Getter
@Setter
@Table(name = "T_BIZ_I_SELL_LIST")
public class BizISellList implements Serializable{
    /**
     * 主建SID
     * 字符类型(50)
     * 非必填
     */
    @Id
    @Column(name = "sid")
    private String sid;

    @Column(name = "head_id")
    private String headId;


    @Column(name = "sales_contract_number")
    private String salesContractNumber;

    @Column(name = "sales_invoice_number")
    private String salesInvoiceNumber;

    @Column(name = "trade_name")
    private String tradeName;

    @Column(name = "unit")
    private String unit;

    @Column(name = "quantity")
    private BigDecimal quantity;

    @Column(name = "unit_price_excluding_tax")
    private BigDecimal unitPriceExcludingTax;

    @Column(name = "amount_of_tax")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal amountOfTax;

    @Column(name = "tax_not_included")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal taxNotIncluded;

    @Column(name = "total_value_tax")
    @JsonSerialize(using = ToStringSerializer.class)
    private BigDecimal totalValueTax;


    /**
     * 制单人
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "insert_user")
    private String insertUser;

    /**
     * 制单时间
     * 日期类型(6)
     * 非必填
     */
    @Column(name = "insert_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;


    /**
     * 创建人姓名
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "insert_user_name")
    private String insertUserName;

    /**
     * 更新人
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 更新时间
     * 日期类型(6)
     * 非必填
     */
    @Column(name = "update_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 更新人姓名
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "update_user_name")
    private String updateUserName;

    /**
     * 企业代码
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "trade_code")
    private String tradeCode;

    @Transient
    private List<String> sids;
}