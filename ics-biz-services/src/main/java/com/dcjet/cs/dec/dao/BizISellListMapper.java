package com.dcjet.cs.dec.dao;

import com.dcjet.cs.dec.model.BizISellList;
import com.dcjet.cs.dto.dec.BizISellListSumData;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * 进口管理-进货信息表头Mapper
 */
public interface BizISellListMapper extends Mapper<BizISellList>{

    /**
     * 查询获取数据
     * @return
     */
    List<BizISellList> getList(BizISellList BizISellList);

    List<BizISellList> getListBySids(BizISellList BizISellList);

    BizISellList getEditDataByHeadId(@Param("headId") String headId);

    BizISellList getSumDataByInvoiceSell(String headId);

    List<BizISellList> getSumDataByInvoiceSellSummary(String headId);
}