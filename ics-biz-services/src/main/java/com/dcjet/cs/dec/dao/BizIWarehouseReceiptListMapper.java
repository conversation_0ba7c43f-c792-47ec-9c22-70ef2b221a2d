package com.dcjet.cs.dec.dao;

import com.dcjet.cs.dec.model.BizIWarehouseReceiptHead;
import com.dcjet.cs.dec.model.BizIWarehouseReceiptList;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * 进口管理-订单信息表头Mapper
 */
public interface BizIWarehouseReceiptListMapper extends Mapper<BizIWarehouseReceiptList>{

    /**
     * 查询获取数据
     * @param head
     * @return
     */
    List<BizIWarehouseReceiptList> getList(BizIWarehouseReceiptList head);

    BizIWarehouseReceiptList getSumData(@Param("parentId") String parentId);

    void updateListTaxes(@Param("warehouseReceiptNumber") String warehouseReceiptNumber, @Param("parentId") String parentId, @Param("userNo") String userNo, @Param("tradeCode") String tradeCode);

    List<BizIWarehouseReceiptList> getListForFile(@Param("parentId") String parentId);


    List<BizIWarehouseReceiptList> getPrintOfLadingList(BizIWarehouseReceiptList listParam);
}