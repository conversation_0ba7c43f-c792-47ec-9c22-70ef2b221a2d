package com.dcjet.cs.dec.service;

import com.dcjet.cs.dec.dao.BizIOrderHeadMapper;
import com.dcjet.cs.dec.dao.BizIPurchaseHeadMapper;
import com.dcjet.cs.dec.dao.BizIPurchaseListBoxMapper;
import com.dcjet.cs.dec.dao.BizIPurchaseListMapper;
import com.dcjet.cs.dec.mapper.BizIPurchaseListDtoMapper;
import com.dcjet.cs.dec.model.BizIOrderHead;
import com.dcjet.cs.dec.model.BizIPurchaseHead;
import com.dcjet.cs.dec.model.BizIPurchaseList;
import com.dcjet.cs.dec.model.BizIPurchaseListBox;
import com.dcjet.cs.dto.dec.*;

import com.dcjet.cs.util.TaskSequencerUtils;
import com.xdo.common.exception.ErrorException;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import xdoi18n.XdoI18nUtil;

/**
 * BizIPurchaseList业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2025-03-07 15:38:12
 * 翻译使用：throw new ErrorException(400, XdoI18nUtil.t("xxxxxxxxxx"));
 */
@Service
public class BizIPurchaseListService extends BaseService<BizIPurchaseList> {

    private static final Logger log = LoggerFactory.getLogger(BizIPurchaseListService.class);

    @Resource
    private BizIPurchaseListMapper bizIPurchaseListMapper;

    @Resource
    private BizIPurchaseListDtoMapper bizIPurchaseListDtoMapper;

    @Override
    public Mapper<BizIPurchaseList> getMapper() {
        return bizIPurchaseListMapper;
    }


    @Resource
    private BizIPurchaseHeadMapper bizIPurchaseHeadMapper;

    @Resource
    private BizIOrderHeadService bIzIOrderHeadService;
    @Resource
    private BizIPurchaseListBoxMapper bizIPurchaseListBoxMapper;
    @Resource
    private BizIOrderHeadMapper bizIOrderHeadMapper;



    /**
     * 获取分页信息
     *
     * @param bizIPurchaseListParam 查询参数
     * @param pageParam               分页参数
     * @return 分页结果
     */
    public ResultObject<List<BizIPurchaseListDto>> getListPaged(BizIPurchaseListParam bizIPurchaseListParam, PageParam pageParam,UserInfoToken userInfo) {
        // 启用分页查询
        BizIPurchaseList bizIPurchaseList = bizIPurchaseListDtoMapper.toPo(bizIPurchaseListParam);
        bizIPurchaseList.setTradeCode(userInfo.getCompany());
        Page<BizIPurchaseList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
            .doSelectPage(() -> bizIPurchaseListMapper.getList( bizIPurchaseList));
        // 将PO转为DTO返回给前端
        List<BizIPurchaseListDto> bizIPurchaseListDtoList = page.getResult().stream()
            .map(bizIPurchaseListDtoMapper::toDto)
            .collect(Collectors.toList());

        return ResultObject.createInstance(bizIPurchaseListDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 新增记录
     *
     * @param bizIPurchaseListParam 插入参数
     * @param userInfo                用户信息
     * @return 新增的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIPurchaseListDto insert(BizIPurchaseListParam bizIPurchaseListParam, UserInfoToken userInfo) {
        BizIPurchaseList bizIPurchaseList = bizIPurchaseListDtoMapper.toPo(bizIPurchaseListParam);
        
        // 规范固定字段
        String sid = UUID.randomUUID().toString();
        bizIPurchaseList.setSid(sid);
        bizIPurchaseList.setInsertUser(userInfo.getUserNo());
        bizIPurchaseList.setInsertTime(new Date());
        bizIPurchaseList.setTradeCode(userInfo.getCompany());
        bizIPurchaseList.setInsertUserName(userInfo.getUserName());

        // 新增数据
        int insertStatus = bizIPurchaseListMapper.insert(bizIPurchaseList);

        // 新增完成后 将数据转为DTO返回给前端
        return insertStatus > 0 ? bizIPurchaseListDtoMapper.toDto(bizIPurchaseList) : null;
    }

    /**
     * 修改记录
     *
     * @param bizIPurchaseListParam 更新参数
     * @param userInfo                用户信息
     * @return 更新后的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIPurchaseListDto update(BizIPurchaseListParam bizIPurchaseListParam, UserInfoToken userInfo) {
        BizIPurchaseList bizIPurchaseList = bizIPurchaseListMapper.selectByPrimaryKey(bizIPurchaseListParam.getSid());
        bizIPurchaseListDtoMapper.updatePo(bizIPurchaseListParam, bizIPurchaseList);
        bizIPurchaseList.setUpdateUser(userInfo.getUserNo());
        bizIPurchaseList.setUpdateTime(new Date());
        bizIPurchaseList.setUpdateUserName(userInfo.getUserName());

        // 更新数据
        int update = bizIPurchaseListMapper.updateByPrimaryKey(bizIPurchaseList);
        return update > 0 ? bizIPurchaseListDtoMapper.toDto(bizIPurchaseList) : null;
    }

    /**
     * 批量删除记录
     *
     * @param sids     要删除的SID列表
     * @param userInfo 用户信息
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
       bizIPurchaseListMapper.deleteBySids(sids);
    }



    /**
     * 功能描述:查询所有数据(导出查询)
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizIPurchaseListDto> selectAll(BizIPurchaseListParam exportParam, UserInfoToken userInfo) {
        BizIPurchaseList bizIPurchaseList = bizIPurchaseListDtoMapper.toPo(exportParam);
        bizIPurchaseList.setTradeCode(userInfo.getCompany());
        List<BizIPurchaseListDto> bizIPurchaseListDtos = new ArrayList<>();
        List<BizIPurchaseList> bizIPurchaseListLists = bizIPurchaseListMapper.getList(bizIPurchaseList);
        if (CollectionUtils.isNotEmpty(bizIPurchaseListLists)) {
           bizIPurchaseListDtos = bizIPurchaseListLists.stream().map(head -> {
                    BizIPurchaseListDto dto =  bizIPurchaseListDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizIPurchaseListDtos;
    }


    /**
     * 进货信息表体-行内编辑更新字段
     * @param param 进口管理-进货信息表体参数
     * @param userInfo 用户信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject innerUpdatePurchaseList(BizIPurchaseListParam param, UserInfoToken userInfo) {
        BizIPurchaseList po = bizIPurchaseListDtoMapper.toPo(param);
        BizIPurchaseHead bizIPurchaseHead = bizIPurchaseHeadMapper.selectByPrimaryKey(po.getHeadId());
        if (bizIPurchaseHead != null) {
            bIzIOrderHeadService.checkData(bizIPurchaseHead.getHeadId(),"所选数据已产生后续单据，请将后续单据作废后再进行编辑!");
        }
        // 数量不能为空
        if (po.getQty() == null || po.getQty().compareTo(BigDecimal.ZERO) == 0) {
            throw new ErrorException(400, XdoI18nUtil.t("数量不能为空或者为0!"));
        }


        po.setUpdateUser(userInfo.getUserNo());
        po.setUpdateTime(new Date());
        po.setUpdateUserName(userInfo.getUserName());
        // 1.修改数量 重新计算总价
        BigDecimal totalPrice = po.getQty().multiply(po.getDecPrice()).setScale(2, BigDecimal.ROUND_HALF_UP);
        log.info("[总价：{}]", totalPrice);
        po.setDecTotal(totalPrice);
        // 2.重新计算折扣金额 总值*折扣率%
        if (po.getDiscountRate() != null) {
            BigDecimal discountRate = po.getDiscountRate();
            if(discountRate.compareTo(BigDecimal.ZERO) == 0) {
                // 折扣率为0是，折扣金额为0
                // discountRate = BigDecimal.ONE;
                discountRate = BigDecimal.ZERO;
                log.info("[折扣率：{}]", discountRate);
                po.setPaymentAmount(totalPrice);
                po.setDiscountAmount(BigDecimal.ZERO);
            }else {
                log.info("[折扣率：{}]", discountRate);
                discountRate = discountRate.divide(BigDecimal.valueOf(100), 8, BigDecimal.ROUND_HALF_UP);
                BigDecimal discountTotal = totalPrice.multiply(discountRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                log.info("[折扣金额：{}]", discountTotal);
                po.setDiscountAmount(discountTotal);
                // 3.重新贷款金额 总值-折扣金额
                BigDecimal loanTotal = totalPrice.subtract(discountTotal).setScale(2, BigDecimal.ROUND_HALF_UP);
                log.info("[贷款金额：{}]", loanTotal);
                po.setPaymentAmount(loanTotal);
            }
        }
        int update = bizIPurchaseListMapper.updateByPrimaryKey(po);
        return update > 0? ResultObject.createInstance(true,"更新成功!") : ResultObject.createInstance(false,"更新失败!");
    }

    @Transactional(rollbackFor = Exception.class)
    public ResultObject innerDecTotalUpdatePurchaseList(BizIPurchaseListParam param, UserInfoToken userInfo) {
        BizIPurchaseList po = bizIPurchaseListDtoMapper.toPo(param);
        BizIPurchaseHead bizIPurchaseHead = bizIPurchaseHeadMapper.selectByPrimaryKey(po.getHeadId());
        if (bizIPurchaseHead != null) {
            bIzIOrderHeadService.checkData(bizIPurchaseHead.getHeadId(),"所选数据已产生后续单据，请将后续单据作废后再进行编辑!");
        }
        po.setUpdateUser(userInfo.getUserNo());
        po.setUpdateTime(new Date());
        po.setUpdateUserName(userInfo.getUserName());
        // 总价值不能为空
        if (po.getDecTotal() == null || po.getDecTotal().compareTo(BigDecimal.ZERO) == 0) {
            throw new ErrorException(400, XdoI18nUtil.t("总价值不能为空或者为0!"));
        }
        // 根据总价重新计算数量
        BigDecimal totalPrice = po.getDecTotal();
        BigDecimal qty = totalPrice.divide(po.getDecPrice(), 6, BigDecimal.ROUND_HALF_UP);
        po.setQty(qty);
        // 重新计算折扣金额
        if (po.getDiscountRate()!= null) {
            BigDecimal discountRate = po.getDiscountRate();
            if(discountRate.compareTo(BigDecimal.ZERO) == 0) {
                discountRate = BigDecimal.ZERO;
                log.info("[折扣率：{}]", discountRate);
                po.setDiscountAmount(BigDecimal.ZERO);
                po.setPaymentAmount(totalPrice);
            }else {
                log.info("[折扣率：{}]", discountRate);
                discountRate = discountRate.divide(BigDecimal.valueOf(100), 8, BigDecimal.ROUND_HALF_UP);
                BigDecimal discountTotal = totalPrice.multiply(discountRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                log.info("[折扣金额：{}]", discountTotal);
                po.setDiscountAmount(discountTotal);
                // 重新计算贷款金额
                BigDecimal loanTotal = totalPrice.subtract(discountTotal).setScale(2, BigDecimal.ROUND_HALF_UP);
                log.info("[贷款金额：{}]", loanTotal);
                po.setPaymentAmount(loanTotal);
            }
        }
        int update = bizIPurchaseListMapper.updateByPrimaryKey(po);
        return update > 0? ResultObject.createInstance(true,"更新成功!") : ResultObject.createInstance(false,"更新失败!");
    }

    /**
     * 进货信息表体-行内编辑更新字段(跟新进口发票号)
     * @param param 进口管理-进货信息表体参数
     * @param userInfo 用户信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject innerUpdatePurchaseListInvoiceNo(BizIPurchaseListParam param, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "更新成功!");
        // 1.转换数据
        BizIPurchaseList po = bizIPurchaseListDtoMapper.toPo(param);

        BizIPurchaseHead head = bizIPurchaseHeadMapper.selectByPrimaryKey(po.getHeadId());
        if (head != null) {
            bIzIOrderHeadService.checkData(po.getHeadId(),"所选数据已产生后续单据，请将后续单据作废后再进行编辑!");
        }

        po.setUpdateUser(userInfo.getUserNo());
        po.setUpdateTime(new Date());
        po.setUpdateUserName(userInfo.getUserName());
        try {
            // 2.更新数据
            TaskSequencerUtils.executeSequentially(()->{
                return bizIPurchaseListMapper.updateByPrimaryKey(po);
            },()->{
                // 3.更新子表的发票号
                bizIPurchaseListMapper.updateInvoiceNo(po);
                // 5.更新订单信息表头的 进口发票号 汇总
                String headId = po.getHeadId();
                BizIPurchaseHead bizIPurchaseHead = bizIPurchaseHeadMapper.selectByPrimaryKey(headId);
                bizIPurchaseListMapper.updateIOrderHeadInvoiceNo(bizIPurchaseHead.getHeadId());
            });
        }catch (Exception e) {
            log.error("更新发票号失败：{}", e.getMessage());
            throw new ErrorException(400,"更新失败!");
        }
        return resultObject;
    }







    /**
     * 获取汇总数据 数量、总值、折扣金额、货款金额
     * @param param headId 表头sid
     * @param userInfo 用户信息
     * @return 汇总数据
     */
    public ResultObject getSumData(BizIPurchaseListParam param, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取汇总数据成功！");
        // Assert.hasLength(param.getHeadId(), "headId不能为空!");
        if (StringUtils.isBlank(param.getHeadId())) {
            throw new ErrorException(400, XdoI18nUtil.t("headId不能为空!"));
        }
        // 获取汇总数据
        BizIPurchaseListSumData sumData = bizIPurchaseListMapper.getSumData(param.getHeadId());
        resultObject.setData(sumData);
        return resultObject;
    }


    /**
     * (根据发票号)获取汇总数据 数量、总值、折扣金额、货款金额
     * @param param headId 表头sid
     * @param userInfo 用户信息
     * @return 汇总数据
     */
    public ResultObject getSumDataByInvoice(BizIPurchaseListParam param, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取汇总数据成功！");
        // Assert.hasLength(param.getHeadId(), "headId不能为空!");
        if (StringUtils.isBlank(param.getHeadId())) {
            throw new ErrorException(400, XdoI18nUtil.t("headId不能为空!"));
        }
        // 获取汇总数据
        List<BizIPurchaseListInvoiceNoSumData> sumData = bizIPurchaseListMapper.getSumDataByInvoice(param.getHeadId());
        resultObject.setData(sumData);
        return resultObject;
    }
    public ResultObject addPushListInvoice(PushListInvoiceParams params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "新增成功！");
        List<BizIPurchaseListParam> bizIPurchaseListInvoiceParams = params.getPurchaseList();
        if (CollectionUtils.isEmpty(bizIPurchaseListInvoiceParams)) {
            throw new ErrorException(400, "装箱子表数据不能为空!");
        }
        if(ObjectUtils.isEmpty(bizIPurchaseListInvoiceParams)){
            throw new ErrorException(400, "装箱子表数据不能为空!");
        }
        List<String> stringList = new ArrayList<>();
        // 处理数据
        for (BizIPurchaseListParam item : bizIPurchaseListInvoiceParams) {
            BizIPurchaseList update = new BizIPurchaseList();
            update.setSid(item.getSid());
            // 设置装箱子表的
            update.setInvoiceNo(params.getInvoiceNo());
            update.setUpdateUser(userInfo.getUserNo());
            update.setUpdateUserName(userInfo.getUserName());
            update.setUpdateTime(new Date());
            bizIPurchaseListMapper.updateByPrimaryKeySelective(update);

            BizIPurchaseListBox paramBox = new BizIPurchaseListBox();
            paramBox.setListHeadSid(item.getSid());
            paramBox.setTradeCode(userInfo.getCompany());
            List<BizIPurchaseListBox> list = bizIPurchaseListBoxMapper.getList(paramBox);
            if(!ObjectUtils.isEmpty(list)){
                for (BizIPurchaseListBox box : list) {
                    BizIPurchaseListBox updateBox = new BizIPurchaseListBox();
                    updateBox.setSid(box.getSid());
                    updateBox.setInvoiceNo(params.getInvoiceNo());
                    bizIPurchaseListBoxMapper.updateByPrimaryKeySelective(updateBox);
                }
            }
            if(StringUtils.isNotBlank(item.getHeadId())){
                stringList.add(item.getHeadId());
            }
        }
        if(stringList.size() > 0){
            for (String s : stringList.stream().distinct().collect(Collectors.toList())) {
                List<BizIPurchaseList> purchaseListByHeadId = bizIPurchaseListMapper.getPurchaseListByPHeadId(s);
                if(!ObjectUtils.isEmpty(purchaseListByHeadId)){
                    BizIPurchaseHead bizIPurchaseHead = bizIPurchaseHeadMapper.selectByPrimaryKey(s);
                    if(!ObjectUtils.isEmpty(bizIPurchaseHead)){
                        String collect = purchaseListByHeadId.stream().map(BizIPurchaseList::getInvoiceNo).distinct().collect(Collectors.joining(","));
                        BizIOrderHead bizIOrderHead = new BizIOrderHead();
                        bizIOrderHead.setSid(bizIPurchaseHead.getHeadId());
                        bizIOrderHead.setImportInvoiceNo(collect);
                        bizIOrderHeadMapper.updateByPrimaryKeySelective(bizIOrderHead);
                    }
                }
            }
        }
        return resultObject;
    }




    /**
     * 功能描述:删除进货信息表体数据
     * @param sids 删除的sids(商品子表的sid)
     * @param userInfo 用户信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject deletePurchaseList(List<String> sids, UserInfoToken userInfo) {
        Assert.notEmpty(sids, "请选择需要删除装箱子表！");
        List<String> headIds =   bizIPurchaseHeadMapper.getDeleteHeadId(sids);
        // 删除商品子表+装箱子表
        bizIPurchaseHeadMapper.deletePurchaseList(sids);
        // 表头重新统计装箱子表箱号
        for (String listHeadId : headIds) {
            bizIPurchaseListBoxMapper.updatePurchaseListBoxBySid(listHeadId);
        }
        return ResultObject.createInstance(true, "删除成功！");
    }

    public ResultObject getPurchaseListBySid(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功");
        BizIPurchaseList bizIPurchaseList = bizIPurchaseListMapper.selectByPrimaryKey(sid);
        if (bizIPurchaseList!= null) {
            BizIPurchaseListDto dto = bizIPurchaseListDtoMapper.toDto(bizIPurchaseList);
            resultObject.setData(dto);
        }
        return resultObject;
    }
}