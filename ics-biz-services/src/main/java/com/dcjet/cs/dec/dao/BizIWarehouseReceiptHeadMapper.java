package com.dcjet.cs.dec.dao;

import com.dcjet.cs.dec.model.BizIOrderHead;
import com.dcjet.cs.dec.model.BizIWarehouseReceiptHead;
import com.dcjet.cs.dec.model.BizIWarehouseReceiptHeadToFile;
import com.dcjet.cs.dto.dec.BizIOrderExtractDto;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.math.BigDecimal;
import java.util.List;

/**
 * 进口管理-订单信息表头Mapper
 */
public interface BizIWarehouseReceiptHeadMapper extends Mapper<BizIWarehouseReceiptHead>{

    /**
     * 查询获取数据
     * @param parentId
     * @return
     */
    BizIWarehouseReceiptHead getList(@Param("parentId") String parentId);
    /**
     * 查询获取数据
     * @param sid
     * @return
     */
    BizIWarehouseReceiptHead getListBySid(@Param("sid") String sid);


    /**
     * 查询废弃的数据
     * @param status
     * @param tradeCode
     * @return
     */

    List<String> selectWarehouseEntryNumber(@Param("status") String status, @Param("tradeCode") String tradeCode);

    /**
     * 查询废弃的数据
     * @param status
     * @param tradeCode
     * @return
     */
    List<String> selectladingNumber(@Param("status") String status, @Param("tradeCode") String tradeCode);

    void deleteHeadAndList(@Param("parentId")String parentId,@Param("listParentId")String listParentId);

    BigDecimal selectDiscountRate(@Param("productGrade") String productGrade,@Param("planNo")String planNo,@Param("tradeCode")String tradeCode);

    /**
     * 查询今年最大的进仓编号
     * @param warehouseEntryNumber
     * @return
     */
    String selectMaxWarehouseEntryNumber(@Param("warehouseEntryNumber")String warehouseEntryNumber);

    /**
     * 查询今年最大的提货单号
     * @param ladingNumber
     * @return
     */
    String selectMaxLadingNumber(@Param("ladingNumber")String ladingNumber);

    void updateByWarehouseEntryNumber(@Param("warehouseEntryNumber")String warehouseEntryNumber);

    void updateByLadingNumber(@Param("ladingNumber")String warehouseEntryNumber);

    BizIWarehouseReceiptHead checkExists(@Param("warehouseEntryNumber")String warehouseEntryNumber, @Param("sid") String sid);
    BizIWarehouseReceiptHeadToFile getHeadForFile(@Param("sid") String sid);

    String selectContract(@Param("sid")String sid);
}