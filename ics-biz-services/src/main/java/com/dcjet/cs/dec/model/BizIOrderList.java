package com.dcjet.cs.dec.model;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Id;

import com.dcjet.cs.dto.dec.BizIListTotal;
import com.xdo.validation.annotation.XdoSize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.io.Serializable;


/**
 * 进口管理-订单信息表头
 *
 * <AUTHOR>
 * @date 2025-03-07 15:37:37
 */
@Getter
@Setter
@Table(name = "t_biz_i_order_list")
public class BizIOrderList implements Serializable{
    private static final long serialVersionUID = 1L;
    /**
     * 主建SID
     * 字符类型(50)
     * 非必填
     */
    @Id
    @Column(name = "sid")
    private String sid;

    /**
     * 制单人
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "insert_user")
    private String insertUser;

    /**
     * 订单制单时间
     * timestamp
     * 非必填
     */
    @Column(name = "insert_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;

    /**
     * 订单制单时间-开始时间
     */
    @Transient
    private String insertTimeFrom;

    /**
     * 订单制单时间-结束时间
     */
    @Transient
    private String insertTimeTo;

    /**
     * 创建人姓名
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "insert_user_name")
    private String insertUserName;

    /**
     * 更新人
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 更新时间
     * timestamp
     * 非必填
     */
    @Column(name = "update_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 更新时间-开始时间
     */
    @Transient
    private String updateTimeFrom;

    /**
     * 更新时间-结束时间
     */
    @Transient
    private String updateTimeTo;

    /**
     * 更新人姓名
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "update_user_name")
    private String updateUserName;

    /**
     * 企业代码
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "trade_code")
    private String tradeCode;

    /**
     * 商品牌号
     * 字符类型(80)
     * 非必填
     */
    @Column(name = "product_grade")
    private String productGrade;

    /**
     * 单位
     * 字符类型(9)
     * 非必填
     */
    @Column(name = "unit")
    private String unit;

    /**
     * 数量
     * 数值类型(19,6)
     * 非必填
     */
    @Column(name = "qty")
    private BigDecimal qty;

    /**
     * 币种
     * 字符类型(10)
     * 非必填
     */
    @Column(name = "curr")
    private String curr;

    /**
     * 单价
     * 数值类型(19,5)
     * 非必填
     */
    @Column(name = "dec_price")
    private BigDecimal decPrice;

    /**
     * 总价
     * 数值类型(19,2)
     * 非必填
     */
    @Column(name = "dec_total")
    private BigDecimal decTotal;

    /**
     * 表头IO
     * 字符类型(50)
     * 非必填
     */
    @Column(name = "head_id")
    private String headId;

    /**
     * 商品类别
     * 字符类型(80)
     * 非必填
     */
    @Column(name = "product_type")
    private String productType;

    /**
     * 版本号

     * 字符类型(10)
     * 非必填
     */
    @Column(name = "version_no")
    private String versionNo;

    /**
     * 数据状态
     * 字符类型(10)
     * 非必填
     */
    @Column(name = "data_status")
    private String dataStatus;

    /**
     * 拓展字段1
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend1")
    private String extend1;

    /**
     * 拓展字段2
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend2")
    private String extend2;

    /**
     * 拓展字段3
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend3")
    private String extend3;

    /**
     * 拓展字段4
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend4")
    private String extend4;

    /**
     * 拓展字段5
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend5")
    private String extend5;

    /**
     * 拓展字段6
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend6")
    private String extend6;

    /**
     * 拓展字段7
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend7")
    private String extend7;

    /**
     * 拓展字段8
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend8")
    private String extend8;

    /**
     * 拓展字段9
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend9")
    private String extend9;

    /**
     * 拓展字段10
     * 字符类型(200)
     * 非必填
     */
    @Column(name = "extend10")
    private String extend10;

    /**
     * 合同编号
     * 数据库字段:contract_no
     * 字符类型(60)
     */
    @Column(name = "contract_no")
    private String contractNo;


    /**
     * 进口合同表头SID
     * 数据库字段:contract_head_id
     * 字符类型(60)
     */
    @Column(name = "contract_head_id")
    private String contractHeadId;


    /**
     * 进口合同表体的SID
     */
    @ApiModelProperty("进口合同表体的SID")
    @Column(name = "contract_list_id")
    private String contractListId;


    /**
     * 上一次复制的表体SID
     */
    @ApiModelProperty("上一次复制的表体SID")
    @Column(name = "last_copy_list_id")
    private String lastCopyListId;


}