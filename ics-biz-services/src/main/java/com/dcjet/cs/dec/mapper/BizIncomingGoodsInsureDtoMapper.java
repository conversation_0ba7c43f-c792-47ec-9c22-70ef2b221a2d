package com.dcjet.cs.dec.mapper;
import com.dcjet.cs.dec.model.BizIncomingGoodsInsure;
import com.dcjet.cs.dto.dec.BizIncomingGoodsInsureDto;
import com.dcjet.cs.dto.dec.BizIncomingGoodsInsureParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;


/**
 * BizIncomingGoodsInsureDto
 *
 * <AUTHOR>
 * @date 2025-05-24 13:25:43
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizIncomingGoodsInsureDtoMapper {

    /**
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizIncomingGoodsInsureDto toDto(BizIncomingGoodsInsure po);

    /**
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizIncomingGoodsInsure toPo(BizIncomingGoodsInsureParam param);

    /**
     * 数据库原始数据更新
     * @param bizIncomingGoodsInsureParam
     * @param BizIncomingGoodsInsure
     */
    void updatePo(BizIncomingGoodsInsureParam bizIncomingGoodsInsureParam, @MappingTarget BizIncomingGoodsInsure bizIncomingGoodsInsure);

    default void patchPo(BizIncomingGoodsInsureParam bizIncomingGoodsInsureParam , BizIncomingGoodsInsure bizIncomingGoodsInsure) {
        // TODO 自行实现局部更新
    }
}