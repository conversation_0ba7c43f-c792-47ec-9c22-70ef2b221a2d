<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.dec.dao.BizIReceiptHeadMapper">
    <sql id="Base_Column_List">
        t.sid,
        t.head_id,
        t.receipt_Number,
        t.contract_Number,
        t.receipt_Number,
        t.contract_Number,
        t.order_Number,
        t.delivery_Number,
        t.consignee,
        t.warehouse,
        t.delivery_Date,
        t.supplier,
        t.send_Ufida,
        t.inspection_Outstock,
        t.remark,
        t.create_By,
        t.create_Date,
        t.outstock_Document_Status,
        t.insert_user,
        t.insert_time,
        t.insert_user_name,
        t.update_user,
        t.update_time,
        t.update_user_name,
        t.trade_code
    </sql>

    <sql id="condition">

    </sql>
    <insert id="insertHeadList">
        insert into T_BIZ_I_RECEIPT_HEAD(
                                         SID,
                                         HEAD_ID,
                                         RECEIPT_NUMBER,
                                         CONTRACT_NUMBER,
                                         ORDER_NUMBER,
                                         CONSIGNEE,
                                         WAREHOUSE,
                                         SUPPLIER,
                                         SEND_UFIDA,
                                         INSPECTION_OUTSTOCK,
                                         CREATE_BY,
                                         CREATE_DATE,
                                         OUTSTOCK_DOCUMENT_STATUS,
                                         INSERT_USER,
                                         INSERT_USER_NAME,
                                         INSERT_TIME,
                                         TRADE_CODE)
        select
            #{receiptSid},
            #{sid},
            wh.lading_number,
            h.contract_no,
            h.order_no,
            '*********',
            '001',
            h.party_b,
            '0',
            '1',
            #{userInfo.userName},
            now(),
            '0',
            #{userInfo.userNo},
            #{userInfo.userName},
            now(),
            #{userInfo.company}
        from t_biz_i_order_head h
        left join T_BIZ_WAREHOUSE_RECEIPT_HEAD wh on wh.PARENT_ID = #{sid}
        left join T_BIZ_I_PURCHASE_HEAD ph on ph.HEAD_ID = #{sid}
        where h.sid = #{sid};
        insert into T_BIZ_I_RECEIPT_LIST(
                                         SID,
                                         HEAD_ID,
                                         TRADE_NAME,
                                         SHIPMENT_QUANTITY,
                                         ACTUAL_QUANTITY_ISSUED,
                                         UNIT,
                                         DEC_PRICE,
                                         CURR,
                                         AMOUNT,
                                         INSERT_USER,
                                         INSERT_USER_NAME,
                                         INSERT_TIME,
                                         TRADE_CODE)
        select
            pl.sid,
            #{receiptSid},
            pl.product_grade,
            pl.qty,
            pl.qty,
            pl.unit,
            ROUND(rl.total_amount/pl.qty,8),
            'CNY',
            rl.total_amount,
            #{userInfo.userNo},
            #{userInfo.userName},
            now(),
            #{userInfo.company}
        from T_BIZ_I_PURCHASE_HEAD ph
        left join T_BIZ_I_PURCHASE_LIST pl on pl.HEAD_ID = ph.sid
        left join T_BIZ_WAREHOUSE_RECEIPT_LIST rl on rl.SID = pl.SID
        where ph.HEAD_ID = #{sid};
    </insert>

    <select id="getList" resultType="com.dcjet.cs.dec.model.BizIReceiptHead" parameterType="com.dcjet.cs.dec.model.BizIReceiptHead">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
        T_BIZ_I_RECEIPT_HEAD t
        <where>
            t.trade_code = #{tradeCode}
        </where>
    </select>
    <select id="getEditDataByHeadId" resultType="com.dcjet.cs.dec.model.BizIReceiptHead">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        T_BIZ_I_RECEIPT_HEAD t
        <where>
            t.head_id = #{headId}
        </where>
    </select>
    <select id="selectUserMessage" resultType="java.lang.String">
        select
            name
            from T_IDC_USER
        where corp_code = #{tradeCode} and LOGIN_NAME = #{insertUser}

    </select>
</mapper>