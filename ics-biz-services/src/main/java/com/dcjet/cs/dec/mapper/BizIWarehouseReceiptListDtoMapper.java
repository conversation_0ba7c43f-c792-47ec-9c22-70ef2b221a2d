package com.dcjet.cs.dec.mapper;

import com.dcjet.cs.dec.model.BizIWarehouseReceiptHead;
import com.dcjet.cs.dec.model.BizIWarehouseReceiptList;
import com.dcjet.cs.dto.dec.BizIWarehouseReceiptHeadDto;
import com.dcjet.cs.dto.dec.BizIWarehouseReceiptHeadParam;
import com.dcjet.cs.dto.dec.BizIWarehouseReceiptListDto;
import com.dcjet.cs.dto.dec.BizIWarehouseReceiptListParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * BizIOrderHeadDto
 *
 * <AUTHOR>
 * @date 2025-03-07 15:37:18
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizIWarehouseReceiptListDtoMapper {

    /**
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizIWarehouseReceiptListDto toDto(BizIWarehouseReceiptList po);

    /**
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizIWarehouseReceiptList toPo(BizIWarehouseReceiptListParam param);

    /**
     * 数据库原始数据更新
     * @param param
     * @param list
     */
    void updatePo(BizIWarehouseReceiptListParam param, @MappingTarget BizIWarehouseReceiptList list);

    default void patchPo(BizIWarehouseReceiptListParam param , BizIWarehouseReceiptList list) {
        // TODO 自行实现局部更新
    }
}