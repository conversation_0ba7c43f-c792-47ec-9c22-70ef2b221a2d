package com.dcjet.cs.dec.dao;

import com.dcjet.cs.dec.model.BizIPurchaseListBox;
import com.dcjet.cs.dto.dec.BizIPurchaseListBoxSumData;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import tk.mybatis.mapper.common.Mapper;

/**
 * 进口管理-进货信息表体-装箱子表Mapper
 */
public interface BizIPurchaseListBoxMapper extends Mapper<BizIPurchaseListBox>{

    /**
     * 查询获取数据
     * @param bizIPurchaseListBox
     * @return
     */
    List<BizIPurchaseListBox> getList(BizIPurchaseListBox bizIPurchaseListBox);

    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(@Param("list")List<String> sids);


    /**
     * 根据headId更新 进货信息表体的 箱号 和 件数
     * @param headId  进货信息表头的主键
     * @return 返回执行成功行数，0为执行失败
     */
    int updatePurchaseListBoxAndQuantity(@Param("headId") String headId);


    /**
     * 统计装箱子表的汇总数据
     * @param headId 进货信息表头sid
     * @return ResultObject
     */
    BizIPurchaseListBoxSumData getSumData(@Param("headId") String headId);


    /**
     * 获取进货信息装箱子表数据
     * @param oldHeadSid 订单表头sid
     * @return 返回进货信息装箱子表数据
     */
    List<BizIPurchaseListBox> getPurchaseListBoxByHeadId(@Param("oldHeadSid") String oldHeadSid);

    /**
     * 根据headId更新 进货信息表体的 箱号
     * @param listHeadId  进货信息表头的主键
     * @return 返回执行成功行数，0为执行失败
     */
    int updatePurchaseListBoxBySid(@Param("listHeadId") String listHeadId);

}