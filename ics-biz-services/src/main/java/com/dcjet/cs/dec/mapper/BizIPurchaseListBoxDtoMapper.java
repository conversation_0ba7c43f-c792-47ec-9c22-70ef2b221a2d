package com.dcjet.cs.dec.mapper;

import com.dcjet.cs.dec.model.BizIPurchaseListBox;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

import com.dcjet.cs.dto.dec.BizIPurchaseListBoxParam;
import com.dcjet.cs.dto.dec.BizIPurchaseListBoxDto;

/**
 * BizIPurchaseListBoxDto
 *
 * <AUTHOR>
 * @date 2025-03-16 14:16:02
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizIPurchaseListBoxDtoMapper {

    /**
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizIPurchaseListBoxDto toDto(BizIPurchaseListBox po);

    /**
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizIPurchaseListBox toPo(BizIPurchaseListBoxParam param);

    /**
     * 数据库原始数据更新
     * @param bizIPurchaseListBoxParam
     * @param BizIPurchaseListBox
     */
    void updatePo(BizIPurchaseListBoxParam bizIPurchaseListBoxParam, @MappingTarget BizIPurchaseListBox bizIPurchaseListBox);

    default void patchPo(BizIPurchaseListBoxParam bizIPurchaseListBoxParam , BizIPurchaseListBox bizIPurchaseListBox) {
        // TODO 自行实现局部更新
    }
}