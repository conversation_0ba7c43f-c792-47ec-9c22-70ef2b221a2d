package com.dcjet.cs.dec.dao;


import com.dcjet.cs.dec.model.BizIncomingGoodsInsure;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import tk.mybatis.mapper.common.Mapper;

/**
 * 进货管理-投保信息Mapper
 */
public interface BizIncomingGoodsInsureMapper extends Mapper<BizIncomingGoodsInsure>{

    /**
     * 查询获取数据
     * @param bizIncomingGoodsInsure
     * @return
     */
    List<BizIncomingGoodsInsure> getList(BizIncomingGoodsInsure bizIncomingGoodsInsure);

    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(@Param("list")List<String> sids);


    /**
     * 根据主表id查询投保信息
     * @param headId
     * @return
     */
    BizIncomingGoodsInsure getDocumentByHeadId(@Param("headId") String headId);
}