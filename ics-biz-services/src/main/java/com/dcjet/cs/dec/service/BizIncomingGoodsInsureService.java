package com.dcjet.cs.dec.service;



import com.dcjet.cs.dec.dao.BizIncomingGoodsInsureMapper;
import com.dcjet.cs.dec.mapper.BizIncomingGoodsInsureDtoMapper;
import com.dcjet.cs.dec.model.BizIncomingGoodsDocument;
import com.dcjet.cs.dec.model.BizIncomingGoodsInsure;
import com.dcjet.cs.dto.dec.BizIncomingGoodsDocumentDto;
import com.dcjet.cs.dto.dec.BizIncomingGoodsDocumentParam;
import com.dcjet.cs.dto.dec.BizIncomingGoodsInsureDto;
import com.dcjet.cs.dto.dec.BizIncomingGoodsInsureParam;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;

import java.util.*;
import java.util.stream.Collectors;

import xdoi18n.XdoI18nUtil;

/**
 * BizIncomingGoodsInsure业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2025-05-24 13:25:42
 * 翻译使用：throw new ErrorException(400, XdoI18nUtil.t("xxxxxxxxxx"));
 */
@Service
public class BizIncomingGoodsInsureService extends BaseService<BizIncomingGoodsInsure> {

    private static final Logger log = LoggerFactory.getLogger(BizIncomingGoodsInsureService.class);

    @Resource
    private BizIncomingGoodsInsureMapper bizIncomingGoodsInsureMapper;

    @Resource
    private BizIncomingGoodsInsureDtoMapper bizIncomingGoodsInsureDtoMapper;

    @Override
    public Mapper<BizIncomingGoodsInsure> getMapper() {
        return bizIncomingGoodsInsureMapper;
    }



    /**
     * 获取分页信息
     *
     * @param bizIncomingGoodsInsureParam 查询参数
     * @param pageParam               分页参数
     * @return 分页结果
     */
    public ResultObject<List<BizIncomingGoodsInsureDto>> getListPaged(BizIncomingGoodsInsureParam bizIncomingGoodsInsureParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizIncomingGoodsInsure bizIncomingGoodsInsure = bizIncomingGoodsInsureDtoMapper.toPo(bizIncomingGoodsInsureParam);
        bizIncomingGoodsInsure.setTradeCode(userInfo.getCompany());
        Page<BizIncomingGoodsInsure> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
            .doSelectPage(() -> bizIncomingGoodsInsureMapper.getList( bizIncomingGoodsInsure));
        // 将PO转为DTO返回给前端
        List<BizIncomingGoodsInsureDto> bizIncomingGoodsInsureDtoList = page.getResult().stream()
            .map(bizIncomingGoodsInsureDtoMapper::toDto)
            .collect(Collectors.toList());

        return ResultObject.createInstance(bizIncomingGoodsInsureDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 新增记录
     *
     * @param bizIncomingGoodsInsureParam 插入参数
     * @param userInfo                用户信息
     * @return 新增的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIncomingGoodsInsureDto insert(BizIncomingGoodsInsureParam bizIncomingGoodsInsureParam, UserInfoToken userInfo) {
        BizIncomingGoodsInsure bizIncomingGoodsInsure = bizIncomingGoodsInsureDtoMapper.toPo(bizIncomingGoodsInsureParam);
        
        // 规范固定字段
        String sid = UUID.randomUUID().toString();
        bizIncomingGoodsInsure.setId(sid);
        bizIncomingGoodsInsure.setCreateBy(userInfo.getUserNo());
        bizIncomingGoodsInsure.setCreateTime(new Date());
        bizIncomingGoodsInsure.setTradeCode(userInfo.getCompany());

        // 新增数据
        int insertStatus = bizIncomingGoodsInsureMapper.insert(bizIncomingGoodsInsure);

        // 新增完成后 将数据转为DTO返回给前端
        return insertStatus > 0 ? bizIncomingGoodsInsureDtoMapper.toDto(bizIncomingGoodsInsure) : null;
    }

    /**
     * 修改记录
     *
     * @param bizIncomingGoodsInsureParam 更新参数
     * @param userInfo                用户信息
     * @return 更新后的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIncomingGoodsInsureDto update(BizIncomingGoodsInsureParam bizIncomingGoodsInsureParam, UserInfoToken userInfo) {
        BizIncomingGoodsInsure bizIncomingGoodsInsure = bizIncomingGoodsInsureMapper.selectByPrimaryKey(bizIncomingGoodsInsureParam.getId());
        bizIncomingGoodsInsureDtoMapper.updatePo(bizIncomingGoodsInsureParam, bizIncomingGoodsInsure);
        bizIncomingGoodsInsure.setUpdateBy(userInfo.getUserNo());
        bizIncomingGoodsInsure.setUpdateTime(new Date());

        // 更新数据
        int update = bizIncomingGoodsInsureMapper.updateByPrimaryKey(bizIncomingGoodsInsure);
        return update > 0 ? bizIncomingGoodsInsureDtoMapper.toDto(bizIncomingGoodsInsure) : null;
    }

    /**
     * 批量删除记录
     *
     * @param sids     要删除的SID列表
     * @param userInfo 用户信息
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
       bizIncomingGoodsInsureMapper.deleteBySids(sids);
    }



    /**
     * 功能描述:查询所有数据(导出查询)
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizIncomingGoodsInsureDto> selectAll(BizIncomingGoodsInsureParam exportParam, UserInfoToken userInfo) {
        BizIncomingGoodsInsure bizIncomingGoodsInsure = bizIncomingGoodsInsureDtoMapper.toPo(exportParam);
        bizIncomingGoodsInsure.setTradeCode(userInfo.getCompany());
        List<BizIncomingGoodsInsureDto> bizIncomingGoodsInsureDtos = new ArrayList<>();
        List<BizIncomingGoodsInsure> bizIncomingGoodsInsureLists = bizIncomingGoodsInsureMapper.getList(bizIncomingGoodsInsure);
        if (CollectionUtils.isNotEmpty(bizIncomingGoodsInsureLists)) {
           bizIncomingGoodsInsureDtos = bizIncomingGoodsInsureLists.stream().map(head -> {
                    BizIncomingGoodsInsureDto dto =  bizIncomingGoodsInsureDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizIncomingGoodsInsureDtos;
    }


    /**
     * 根据进货管理表头ID获取投保信息
     * @param headId 进货管理表头ID
     * @param userInfo 用户信息
     * @return 投保信息DTO对象
     */
    public ResultObject<BizIncomingGoodsInsureDto> getDocumentByHeadId(String headId, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功！");
        Assert.hasText(headId, "进货管理表头ID不能为空！");
        BizIncomingGoodsInsure bizIncomingGoodsInsure = bizIncomingGoodsInsureMapper.getDocumentByHeadId(headId);
        if (bizIncomingGoodsInsure != null) {
            BizIncomingGoodsInsureDto dto = bizIncomingGoodsInsureDtoMapper.toDto(bizIncomingGoodsInsure);
            resultObject.setData(dto);
        }
        return resultObject;
    }



    /**
     * 新增或编辑 投保信息
     * @param bizIncomingGoodsInsureParam 投保信息
     * @param userInfo 用户信息
     * @return 新增或编辑后的投保信息DTO对象
     */
    public BizIncomingGoodsInsureDto insertOrUpdate(BizIncomingGoodsInsureParam bizIncomingGoodsInsureParam, UserInfoToken userInfo) {

        BizIncomingGoodsInsure po = bizIncomingGoodsInsureDtoMapper.toPo(bizIncomingGoodsInsureParam);
        // 判断ID是否存在，如果存在则更新，不存在则新增
        if (StringUtils.isBlank(po.getId())){
            // 新增数据
            po.setCreateBy(userInfo.getUserNo());
            po.setCreateTime(new Date());
            po.setTradeCode(userInfo.getCompany());
            po.setId(UUID.randomUUID().toString());
            bizIncomingGoodsInsureMapper.insert(po);
        }else{
            // 更新数据
            po.setUpdateBy(userInfo.getUserNo());
            po.setUpdateTime(new Date());
            bizIncomingGoodsInsureMapper.updateByPrimaryKeySelective(po);
        }
        // 将PO转为DTO返回给前端
        return bizIncomingGoodsInsureDtoMapper.toDto(po);
    }
}