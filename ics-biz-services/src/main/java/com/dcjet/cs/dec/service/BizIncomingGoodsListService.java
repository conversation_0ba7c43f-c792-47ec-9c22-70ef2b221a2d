package com.dcjet.cs.dec.service;



import com.dcjet.cs.dec.dao.BizIncomingGoodsListMapper;
import com.dcjet.cs.dec.mapper.BizIncomingGoodsListDtoMapper;
import com.dcjet.cs.dec.model.BizIncomingGoodsList;
import com.dcjet.cs.dto.dec.BizIncomingGoodsListDto;
import com.dcjet.cs.dto.dec.BizIncomingGoodsListParam;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;

import java.util.*;
import java.util.stream.Collectors;

import xdoi18n.XdoI18nUtil;

/**
 * BizIncomingGoodsList业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2025-05-23 13:32:21
 * 翻译使用：throw new ErrorException(400, XdoI18nUtil.t("xxxxxxxxxx"));
 */
@Service
public class BizIncomingGoodsListService extends BaseService<BizIncomingGoodsList> {

    private static final Logger log = LoggerFactory.getLogger(BizIncomingGoodsListService.class);

    @Resource
    private BizIncomingGoodsListMapper bizIncomingGoodsListMapper;

    @Resource
    private BizIncomingGoodsListDtoMapper bizIncomingGoodsListDtoMapper;

    @Override
    public Mapper<BizIncomingGoodsList> getMapper() {
        return bizIncomingGoodsListMapper;
    }



    /**
     * 获取分页信息
     *
     * @param bizIncomingGoodsListParam 查询参数
     * @param pageParam               分页参数
     * @return 分页结果
     */
    public ResultObject<List<BizIncomingGoodsListDto>> getListPaged(BizIncomingGoodsListParam bizIncomingGoodsListParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizIncomingGoodsList bizIncomingGoodsList = bizIncomingGoodsListDtoMapper.toPo(bizIncomingGoodsListParam);
        bizIncomingGoodsList.setTradeCode(userInfo.getCompany());
        Page<BizIncomingGoodsList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
            .doSelectPage(() -> bizIncomingGoodsListMapper.getList( bizIncomingGoodsList));
        // 将PO转为DTO返回给前端
        List<BizIncomingGoodsListDto> bizIncomingGoodsListDtoList = page.getResult().stream()
            .map(bizIncomingGoodsListDtoMapper::toDto)
            .collect(Collectors.toList());

        return ResultObject.createInstance(bizIncomingGoodsListDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 新增记录
     *
     * @param bizIncomingGoodsListParam 插入参数
     * @param userInfo                用户信息
     * @return 新增的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIncomingGoodsListDto insert(BizIncomingGoodsListParam bizIncomingGoodsListParam, UserInfoToken userInfo) {
        BizIncomingGoodsList bizIncomingGoodsList = bizIncomingGoodsListDtoMapper.toPo(bizIncomingGoodsListParam);
        
        // 规范固定字段
        String sid = UUID.randomUUID().toString();
        bizIncomingGoodsList.setId(sid);
        bizIncomingGoodsList.setCreateBy(userInfo.getUserNo());
        bizIncomingGoodsList.setCreateTime(new Date());
        bizIncomingGoodsList.setTradeCode(userInfo.getCompany());

        // 新增数据
        int insertStatus = bizIncomingGoodsListMapper.insert(bizIncomingGoodsList);

        // 新增完成后 将数据转为DTO返回给前端
        return insertStatus > 0 ? bizIncomingGoodsListDtoMapper.toDto(bizIncomingGoodsList) : null;
    }

    /**
     * 修改记录
     *
     * @param bizIncomingGoodsListParam 更新参数
     * @param userInfo                用户信息
     * @return 更新后的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIncomingGoodsListDto update(BizIncomingGoodsListParam bizIncomingGoodsListParam, UserInfoToken userInfo) {
        BizIncomingGoodsList bizIncomingGoodsList = bizIncomingGoodsListMapper.selectByPrimaryKey(bizIncomingGoodsListParam.getId());
        bizIncomingGoodsListDtoMapper.updatePo(bizIncomingGoodsListParam, bizIncomingGoodsList);
        bizIncomingGoodsList.setUpdateBy(userInfo.getUserNo());
        bizIncomingGoodsList.setUpdateTime(new Date());

        // 更新数据
        int update = bizIncomingGoodsListMapper.updateByPrimaryKey(bizIncomingGoodsList);
        return update > 0 ? bizIncomingGoodsListDtoMapper.toDto(bizIncomingGoodsList) : null;
    }

    /**
     * 批量删除记录
     *
     * @param sids     要删除的SID列表
     * @param userInfo 用户信息
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
       bizIncomingGoodsListMapper.deleteBySids(sids);
    }



    /**
     * 功能描述:查询所有数据(导出查询)
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizIncomingGoodsListDto> selectAll(BizIncomingGoodsListParam exportParam, UserInfoToken userInfo) {
        BizIncomingGoodsList bizIncomingGoodsList = bizIncomingGoodsListDtoMapper.toPo(exportParam);
        bizIncomingGoodsList.setTradeCode(userInfo.getCompany());
        List<BizIncomingGoodsListDto> bizIncomingGoodsListDtos = new ArrayList<>();
        List<BizIncomingGoodsList> bizIncomingGoodsListLists = bizIncomingGoodsListMapper.getList(bizIncomingGoodsList);
        if (CollectionUtils.isNotEmpty(bizIncomingGoodsListLists)) {
           bizIncomingGoodsListDtos = bizIncomingGoodsListLists.stream().map(head -> {
                    BizIncomingGoodsListDto dto =  bizIncomingGoodsListDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizIncomingGoodsListDtos;
    }

}