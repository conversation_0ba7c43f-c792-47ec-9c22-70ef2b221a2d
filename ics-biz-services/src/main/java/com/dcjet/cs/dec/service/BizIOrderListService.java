package com.dcjet.cs.dec.service;

import com.dcjet.cs.Utils.NumberFormatterUtils;
import com.dcjet.cs.dec.dao.BizIOrderHeadMapper;
import com.dcjet.cs.dec.dao.BizIOrderListMapper;
import com.dcjet.cs.dec.mapper.BizIOrderListDtoMapper;
import com.dcjet.cs.dec.model.BizIOrderList;
import com.dcjet.cs.dto.dec.BizIListTotal;
import com.dcjet.cs.dto.dec.BizIOrderListDto;
import com.dcjet.cs.dto.dec.BizIOrderListParam;

import com.xdo.common.exception.ErrorException;
import com.xdo.domain.PageParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import xdoi18n.XdoI18nUtil;

/**
 * BizIOrderList业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2025-03-07 15:37:37
 * 翻译使用：throw new ErrorException(400, XdoI18nUtil.t("xxxxxxxxxx"));
 */
@Service
public class BizIOrderListService extends BaseService<BizIOrderList> {

    private static final Logger log = LoggerFactory.getLogger(BizIOrderListService.class);

    @Resource
    private BizIOrderListMapper bizIOrderListMapper;

    @Resource
    private BizIOrderListDtoMapper bizIOrderListDtoMapper;

    @Override
    public Mapper<BizIOrderList> getMapper() {
        return bizIOrderListMapper;
    }

    @Resource
    private BizIOrderHeadService bizIOrderHeadService;






    /**
     * 获取分页信息
     *
     * @param bizIOrderListParam 查询参数
     * @param pageParam               分页参数
     * @return 分页结果
     */
    public ResultObject<List<BizIOrderListDto>> getListPaged(BizIOrderListParam bizIOrderListParam, PageParam pageParam,UserInfoToken userInfo) {
        // 启用分页查询
        BizIOrderList bizIOrderList = bizIOrderListDtoMapper.toPo(bizIOrderListParam);
        bizIOrderList.setTradeCode(userInfo.getCompany());
        Page<BizIOrderList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
            .doSelectPage(() -> bizIOrderListMapper.getList( bizIOrderList));
        // 将PO转为DTO返回给前端
        List<BizIOrderListDto> bizIOrderListDtoList = page.getResult().stream()
            .map(bizIOrderListDtoMapper::toDto)
            .collect(Collectors.toList());

        return ResultObject.createInstance(bizIOrderListDtoList, (int) page.getTotal(), page.getPageNum());
    }





    /**
     * 新增记录
     *
     * @param bizIOrderListParam 插入参数
     * @param userInfo                用户信息
     * @return 新增的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIOrderListDto insert(BizIOrderListParam bizIOrderListParam, UserInfoToken userInfo) {
        BizIOrderList bizIOrderList = bizIOrderListDtoMapper.toPo(bizIOrderListParam);
        
        // 规范固定字段
        String sid = UUID.randomUUID().toString();
        bizIOrderList.setSid(sid);
        bizIOrderList.setInsertUser(userInfo.getUserNo());
        bizIOrderList.setInsertTime(new Date());
        bizIOrderList.setTradeCode(userInfo.getCompany());
        bizIOrderList.setInsertUserName(userInfo.getUserName());

        // 新增数据
        int insertStatus = bizIOrderListMapper.insert(bizIOrderList);

        // 新增完成后 将数据转为DTO返回给前端
        return insertStatus > 0 ? bizIOrderListDtoMapper.toDto(bizIOrderList) : null;
    }

    /**
     * 修改记录
     *
     * @param bizIOrderListParam 更新参数
     * @param userInfo                用户信息
     * @return 更新后的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIOrderListDto update(BizIOrderListParam bizIOrderListParam, UserInfoToken userInfo) {
        BizIOrderList bizIOrderList = bizIOrderListMapper.selectByPrimaryKey(bizIOrderListParam.getSid());
        bizIOrderHeadService.checkData(bizIOrderList.getHeadId(),"所选数据已产生后续单据，请将后续单据作废后再进行编辑!");
        // 判断数量是否为空，如果为空则不允许进行编辑
        if (bizIOrderListParam.getQty() == null) {
            throw new ErrorException(400, XdoI18nUtil.t("数量不能为空！"));
        }
        bizIOrderListDtoMapper.updatePo(bizIOrderListParam, bizIOrderList);

        // 商品牌号
        String productGrade = bizIOrderListParam.getProductGrade();
        // 合同号
        String contractNo = bizIOrderListParam.getContractNo();
        // 当前编辑的sid
        String sid = bizIOrderListParam.getSid();
        // 当前数量
        BigDecimal qty = bizIOrderListParam.getQty();

        checkExcess(sid,productGrade,contractNo,userInfo.getCompany(),qty);


        bizIOrderList.setUpdateUser(userInfo.getUserNo());
        bizIOrderList.setUpdateTime(new Date());
        bizIOrderList.setUpdateUserName(userInfo.getUserName());
        // 自动计算总价值
        if (bizIOrderList.getDecPrice() != null && bizIOrderList.getQty() != null){
            bizIOrderList.setDecTotal(bizIOrderList.getDecPrice().multiply(bizIOrderList.getQty()).setScale(5, BigDecimal.ROUND_HALF_UP));
        }

        // 更新数据
        int update = bizIOrderListMapper.updateByPrimaryKey(bizIOrderList);
        return update > 0 ? bizIOrderListDtoMapper.toDto(bizIOrderList) : null;
    }


    /**
     * 校验数量是否超出合同数量
     * @param sid 当前编辑的sid
     * @param productGrade 商品牌号
     * @param contractNo 合同号
     * @param tradeCode 企业代码
     * @param qty 当前编辑数量
     */
    public void checkExcess(String sid,String productGrade,String contractNo,String tradeCode,BigDecimal qty){
        // 获取合同原始数量
        BigDecimal originalQty = bizIOrderListMapper.getContractOriginalQty(productGrade,contractNo,tradeCode);
        if (originalQty == null){
            throw new ErrorException(400, XdoI18nUtil.t("未匹配到有效合同数据！"));
        }
        // 获取除了当前编辑的数据之外的数量
        BigDecimal totalQty = bizIOrderListMapper.getOrderOriginalQty(sid,productGrade,contractNo,tradeCode);
        BigDecimal total = BigDecimal.ZERO;
        if (totalQty != null){
            total = totalQty.add(qty);
        }else {
            total = qty;
        }

        // 判断数量是否超出合同数量
        if (total.compareTo(originalQty) > 0){
            throw new ErrorException(400, XdoI18nUtil.t("数量不允许超出该合同号下已形成订单的剩余数量！"));
        }
    }



    /**
     * 批量删除记录
     *
     * @param sids     要删除的SID列表
     * @param userInfo 用户信息
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
       // 判断删除数据的状态
       int count = bizIOrderListMapper.getDeleteStatus(sids);
       if (count > 0) {
           throw new ErrorException(400, XdoI18nUtil.t("仅编制状态数据允许删除！"));
       }
       // 获取表头数据ID
       if (CollectionUtils.isNotEmpty(sids)) {
           BizIOrderList bizIOrderList = bizIOrderListMapper.selectByPrimaryKey(sids.get(0));
           bizIOrderHeadService.checkData(bizIOrderList.getHeadId(),"所选数据已产生后续单据，请将后续单据作废后再进行删除！");
       }

       bizIOrderListMapper.deleteBySids(sids);
    }



    /**
     * 功能描述:查询所有数据(导出查询)
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizIOrderListDto> selectAll(BizIOrderListParam exportParam, UserInfoToken userInfo) {
        BizIOrderList bizIOrderList = bizIOrderListDtoMapper.toPo(exportParam);
        bizIOrderList.setTradeCode(userInfo.getCompany());
        List<BizIOrderListDto> bizIOrderListDtos = new ArrayList<>();
        List<BizIOrderList> bizIOrderListLists = bizIOrderListMapper.getList(bizIOrderList);
        if (CollectionUtils.isNotEmpty(bizIOrderListLists)) {
           bizIOrderListDtos = bizIOrderListLists.stream().map(head -> {
                BizIOrderListDto dto =  bizIOrderListDtoMapper.toDto(head);
                dto.setQtyStr(NumberFormatterUtils.formatNumber(head.getQty()));
                dto.setDecTotalStr(NumberFormatterUtils.formatNumber(head.getDecTotal()));
                dto.setDecPriceStr(NumberFormatterUtils.formatNumber(head.getDecPrice()));
//                dto.setQtyStr((head.getQty().stripTrailingZeros().scale() <= 0) ? head.getQty().stripTrailingZeros().toPlainString() +".00" : head.getQty().toPlainString());
//                dto.setDecTotalStr((head.getDecTotal().stripTrailingZeros().scale() <= 0) ? head.getDecTotal().stripTrailingZeros().toPlainString() +".00" : head.getDecTotal().toPlainString());
//                dto.setDecPriceStr((head.getDecPrice().stripTrailingZeros().scale() <= 0) ? head.getDecPrice().stripTrailingZeros().toPlainString() +".00" : head.getDecPrice().toPlainString());
                return dto;
            }).collect(Collectors.toList());
        }
        return bizIOrderListDtos;
    }


    /**
     * 获取进口订单表体-数量，金额汇总
     * @param bizIOrderListParam 查询参数
     * @param userInfo 用户信息
     * @return 分页结果
     */
    public ResultObject getITotal(BizIOrderListParam bizIOrderListParam,  UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true,"获取成功！");
        // 启用分页查询
        BizIOrderList bizIOrderList = bizIOrderListDtoMapper.toPo(bizIOrderListParam);
        bizIOrderList.setTradeCode(userInfo.getCompany());
        BizIListTotal bizIListTotal = bizIOrderListMapper.getITotal(bizIOrderList);
        resultObject.setData(bizIListTotal);
        return resultObject;
    }

    /**
     * 根据订单表头id查询订单表体数据
     * @param sid 订单表头id
     * @return 订单表体数据
     */
    public ResultObject getOrderListBySid(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true,"获取成功！");
        BizIOrderList bizIOrderList = bizIOrderListMapper.selectByPrimaryKey(sid);
        BizIOrderListDto dto = bizIOrderListDtoMapper.toDto(bizIOrderList);
        resultObject.setData(dto);
        return resultObject;
    }
}