package com.dcjet.cs.dec.dao;

import com.dcjet.cs.dec.model.BizIPurchaseHead;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import tk.mybatis.mapper.common.Mapper;

/**
 * 进口管理-进货信息表头Mapper
 */
public interface BizIPurchaseHeadMapper extends Mapper<BizIPurchaseHead>{

    /**
     * 查询获取数据
     * @param bizIPurchaseHead
     * @return
     */
    List<BizIPurchaseHead> getList(BizIPurchaseHead bizIPurchaseHead);

    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(@Param("list")List<String> sids);

    /**
     * 根据headId获取编辑数据
     * @param headId 订单表头sid
     * @return 进货信息表头sid
     */
    BizIPurchaseHead getEditDataByHeadId(@Param("headId") String headId);


    /**
     * 校验进货单号是否已经存在
     * @param purchaseOrderNo 进货单号
     * @param sid 进货信息表头sid
     * @param tradeCode 企业代码
     * @return 返回执行成功行数，0为执行失败
     */
    int getOrderNoIsExists(@Param("purchaseOrderNo") String purchaseOrderNo,
                           @Param("sid") String sid,
                           @Param("tradeCode")String tradeCode);


    /**
     * 更近进口订单表头的进货单号
     * @param purchaseOrderNo 进货单号
     * @param headId 订单表头sid
     * @return 返回执行成功行数，0为执行失败
     */
    int updateOderHeadPurchaseNo(@Param("purchaseOrderNo") String purchaseOrderNo, @Param("headId") String headId);


    /**
     * 删除进货信息表体数据
     * @param sids 进货信息表体sid
     * @return 返回执行成功行数，0为执行失败
     */
    int deletePurchaseList(@Param("sids") List<String> sids);


    List<String> getDeleteHeadId(@Param("sids") List<String> sids);
}