package com.dcjet.cs.dec.dao;

import com.dcjet.cs.dec.model.BizIPurchaseHead;
import com.dcjet.cs.dec.model.BizISellHead;
import com.xdo.common.token.UserInfoToken;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * 进口管理-进货信息表头Mapper
 */
public interface BizISellHeadMapper extends Mapper<BizISellHead>{

    /**
     * 查询获取数据
     * @return
     */
    List<BizISellHead> getList(BizISellHead bizISellHead);

    BizISellHead getEditDataByHeadId(@Param("headId") String headId);

    void insertHeadList(@Param("sellSid")String sellSid, @Param("sid")String sid, @Param("userInfo")UserInfoToken userInfo);
}