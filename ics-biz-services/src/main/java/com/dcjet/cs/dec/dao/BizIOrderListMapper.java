package com.dcjet.cs.dec.dao;

import com.dcjet.cs.dec.model.BizIOrderList;
import com.dcjet.cs.dto.dec.BizIListTotal;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import tk.mybatis.mapper.common.Mapper;

/**
 * 进口管理-订单信息表头Mapper
 */
public interface BizIOrderListMapper extends Mapper<BizIOrderList>{

    /**
     * 查询获取数据
     * @param bizIOrderList
     * @return
     */
    List<BizIOrderList> getList(BizIOrderList bizIOrderList);

    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(@Param("list")List<String> sids);


    /**
     * 获取进口订单表体-合计数据
     * @param bizIOrderList 查询参数
     * @return 合计数据
     */
    BizIListTotal getITotal(BizIOrderList bizIOrderList);


    /**
     * 根据订单表头id查询订单表体数据
     * @param oldHeadSid 订单表头id
     * @return 订单表体数据
     */
    List<BizIOrderList> getOrderListByHeadId(@Param("oldHeadSid") String oldHeadSid);


    /**
     * 表体删除时，判断是否存数据是否存在 非编制的（非 0） ，只有 编制状态下能删除
     * @param sids 订单表体的sid集合
     * @return 返回执行成功行数，0为执行失败
     */
    int getDeleteStatus(List<String> sids);

    void updateCorrelationID(@Param("newListId") String newListId, @Param("oldSid") String oldSid, @Param("headId") String headId, @Param("oldHeadId") String oldHeadId);

    /**
     * 获取合同原始数量（根据商品牌号+合同号+tradeCode）
     * @param productGrade 商品牌号
     * @param contractNo 合同号
     * @param tradeCode 企业代码
     * @return 合同原始数量
     */
    BigDecimal getContractOriginalQty(@Param("productGrade") String productGrade, @Param("contractNo") String contractNo, @Param("tradeCode") String tradeCode);


    /**
     * 获取除订单本身之外的数量（根据商品牌号+合同号+tradeCode）
     * @param sid 订单表体的sid
     * @param productGrade 商品牌号
     * @param contractNo 合同号
     * @param tradeCode 企业代码
     * @return 合同原始数量
     */
    BigDecimal getOrderOriginalQty(@Param("sid")String sid,@Param("productGrade") String productGrade, @Param("contractNo") String contractNo, @Param("tradeCode") String tradeCode);

}