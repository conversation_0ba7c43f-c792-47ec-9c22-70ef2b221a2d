package com.dcjet.cs.dec.mapper;
import com.dcjet.cs.dec.model.BizIncomingGoodsDocument;

import com.dcjet.cs.dto.dec.BizIncomingGoodsDocumentDto;

import com.dcjet.cs.dto.dec.BizIncomingGoodsDocumentParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;


/**
 * BizIncomingGoodsDocumentDto
 *
 * <AUTHOR>
 * @date 2025-05-23 15:06:34
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizIncomingGoodsDocumentDtoMapper {

    /**
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizIncomingGoodsDocumentDto toDto(BizIncomingGoodsDocument po);

    /**
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizIncomingGoodsDocument toPo(BizIncomingGoodsDocumentParam param);

    /**
     * 数据库原始数据更新
     * @param bizIncomingGoodsDocumentParam
     * @param BizIncomingGoodsDocument
     */
    void updatePo(BizIncomingGoodsDocumentParam bizIncomingGoodsDocumentParam, @MappingTarget BizIncomingGoodsDocument bizIncomingGoodsDocument);

    default void patchPo(BizIncomingGoodsDocumentParam bizIncomingGoodsDocumentParam , BizIncomingGoodsDocument bizIncomingGoodsDocument) {
        // TODO 自行实现局部更新
    }
}