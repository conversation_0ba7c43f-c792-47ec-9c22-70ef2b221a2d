package com.dcjet.cs.dec.service;

import com.dcjet.cs.dec.dao.BizIOrderHeadMapper;
import com.dcjet.cs.dec.dao.BizISellHeadMapper;
import com.dcjet.cs.dec.dao.BizISellListMapper;
import com.dcjet.cs.dec.mapper.BizISellHeadDtoMapper;
import com.dcjet.cs.dec.model.BizIOrderHead;
import com.dcjet.cs.dec.model.BizISellHead;
import com.dcjet.cs.dto.dec.BizIPurchaseListSumData;
import com.dcjet.cs.dto.dec.BizISellHeadDto;
import com.dcjet.cs.dto.dec.BizISellHeadParam;
import com.dcjet.cs.dto.dec.BizISellListSumData;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import xdoi18n.XdoI18nUtil;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * BizISellHead业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2025-03-07 15:37:58
 * 翻译使用：throw new ErrorException(400, XdoI18nUtil.t("xxxxxxxxxx"));
 */
@Service
public class BizISellHeadService extends BaseService<BizISellHead> {

    private static final Logger log = LoggerFactory.getLogger(BizISellHeadService.class);

    @Resource
    private BizISellHeadMapper BizISellHeadMapper;

    @Resource
    private BizISellListMapper bizISellListMapper;

    @Resource
    private BizIOrderHeadMapper bizIOrderHeadMapper;

    @Resource
    private BizISellHeadDtoMapper BizISellHeadDtoMapper;

    @Override
    public Mapper<BizISellHead> getMapper() {
        return BizISellHeadMapper;
    }



    /**
     * 获取分页信息
     *
     * @param BizISellHeadParam 查询参数
     * @param pageParam               分页参数
     * @return 分页结果
     */
    public ResultObject<List<BizISellHeadDto>> getListPaged(BizISellHeadParam BizISellHeadParam, PageParam pageParam,UserInfoToken userInfo) {
        // 启用分页查询
        BizISellHead BizISellHead = BizISellHeadDtoMapper.toPo(BizISellHeadParam);
        BizISellHead.setTradeCode(userInfo.getCompany());
        Page<BizISellHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
            .doSelectPage(() -> BizISellHeadMapper.getList( BizISellHead));
        // 将PO转为DTO返回给前端
        List<BizISellHeadDto> BizISellHeadDtoList = page.getResult().stream()
            .map(BizISellHeadDtoMapper::toDto)
            .collect(Collectors.toList());

        return ResultObject.createInstance(BizISellHeadDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 新增记录
     *
     * @param BizISellHeadParam 插入参数
     * @param userInfo                用户信息
     * @return 新增的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizISellHeadDto insert(BizISellHeadParam BizISellHeadParam, UserInfoToken userInfo) {
        BizISellHead BizISellHead = BizISellHeadDtoMapper.toPo(BizISellHeadParam);
        
        // 规范固定字段
        String sid = UUID.randomUUID().toString();
        BizISellHead.setSid(sid);
        BizISellHead.setInsertUser(userInfo.getUserNo());
        BizISellHead.setInsertTime(new Date());
        BizISellHead.setTradeCode(userInfo.getCompany());
        BizISellHead.setInsertUserName(userInfo.getUserName());

        // 新增数据
        int insertStatus = BizISellHeadMapper.insert(BizISellHead);

        // 新增完成后 将数据转为DTO返回给前端
        return insertStatus > 0 ? BizISellHeadDtoMapper.toDto(BizISellHead) : null;
    }

    /**
     * 修改记录
     *
     * @param BizISellHeadParam 更新参数
     * @param userInfo                用户信息
     * @return 更新后的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizISellHeadDto update(BizISellHeadParam BizISellHeadParam, UserInfoToken userInfo) {
        BizISellHead BizISellHead = BizISellHeadMapper.selectByPrimaryKey(BizISellHeadParam.getSid());
        BizISellHeadDtoMapper.updatePo(BizISellHeadParam, BizISellHead);
        BizISellHead.setDrawer(userInfo.getUserName());
//        BizISellHead.setBusinessDate(new Date());
        BizISellHead.setUpdateUser(userInfo.getUserNo());
        BizISellHead.setUpdateTime(new Date());
        BizISellHead.setUpdateUserName(userInfo.getUserName());

        // 更新数据
        int update = BizISellHeadMapper.updateByPrimaryKey(BizISellHead);
        return update > 0 ? BizISellHeadDtoMapper.toDto(BizISellHead) : null;
    }



    /**
     * 获取进货信息表头数据 根据订单表头sid
     * @param BizISellHeadParam 订单表头sid
     * @param userInfo 用户信息
     * @return 结果
     */
    public ResultObject<BizISellHeadDto> getPurchaseHeadByOrderSid(BizISellHeadParam BizISellHeadParam, UserInfoToken userInfo) {
        ResultObject<BizISellHeadDto> resultObject = ResultObject.createInstance(true,"获取成功！");
        BizISellHead BizISellHeadList = BizISellHeadMapper.getEditDataByHeadId(BizISellHeadParam.getHeadId());
        if (BizISellHeadList != null) {
            BizISellHeadDto BizISellHeadDto = BizISellHeadDtoMapper.toDto(BizISellHeadList);
            resultObject.setData(BizISellHeadDto);
        }
        return resultObject;
    }

    public void chargeback(String sid, UserInfoToken userInfo) {
        BizISellHead bizISellHead = BizISellHeadMapper.selectByPrimaryKey(sid);
        bizISellHead.setUpdateTime(new Date());
        bizISellHead.setUpdateUser(userInfo.getUserNo());
        bizISellHead.setUpdateUserName(userInfo.getUserName());
        bizISellHead.setSalesDocumentStatus("0");
        BizISellHeadMapper.updateByPrimaryKey(bizISellHead);
        //更改头状态
        BizIOrderHead bizIOrderHead = bizIOrderHeadMapper.selectByPrimaryKey(bizISellHead.getHeadId());
        bizIOrderHead.setSalesDataStatus("0");
        bizIOrderHeadMapper.updateByPrimaryKey(bizIOrderHead);
    }
}