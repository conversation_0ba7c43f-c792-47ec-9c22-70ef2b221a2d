package com.dcjet.cs.dec.mapper;

import com.dcjet.cs.dec.model.BizIOrderHead;
import com.dcjet.cs.dec.model.BizIWarehouseReceiptHead;
import com.dcjet.cs.dto.dec.BizIOrderHeadDto;
import com.dcjet.cs.dto.dec.BizIOrderHeadParam;
import com.dcjet.cs.dto.dec.BizIWarehouseReceiptHeadDto;
import com.dcjet.cs.dto.dec.BizIWarehouseReceiptHeadParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * BizIOrderHeadDto
 *
 * <AUTHOR>
 * @date 2025-03-07 15:37:18
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizIWarehouseReceiptHeadDtoMapper {

    /**
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizIWarehouseReceiptHeadDto toDto(BizIWarehouseReceiptHead po);

    /**
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizIWarehouseReceiptHead toPo(BizIWarehouseReceiptHeadParam param);

    /**
     * 数据库原始数据更新
     * @param param
     * @param head
     */
    void updatePo(BizIWarehouseReceiptHeadParam param, @MappingTarget BizIWarehouseReceiptHead head);

    default void patchPo(BizIWarehouseReceiptHeadParam param , BizIWarehouseReceiptHead head) {
        // TODO 自行实现局部更新
    }
}