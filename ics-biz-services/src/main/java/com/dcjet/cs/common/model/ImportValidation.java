package com.dcjet.cs.common.model;

import com.dcjet.cs.dto.base.BasicImportParam;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter @Setter
public class ImportValidation<T extends BasicImportParam> {

    /***
     *
     */
    private List<T> correctList;

    /***
     * 返回直接入库的数据
     */
    private int correctSize;

    /***
     *
     */
    private List<T> errorList;


    public ImportValidation(List<T> correct, List<T> error) {
        this.correctList = correct;
        this.errorList = error;
    }
}
