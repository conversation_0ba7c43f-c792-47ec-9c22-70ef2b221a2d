package com.dcjet.cs.common.excel;

import com.dcjet.cs.common.excel.converter.BigDecimalExcelConverter;
import com.dcjet.cs.common.excel.converter.DateExcelConverter;
import com.dcjet.cs.common.excel.converter.ExcelConverter;
import com.dcjet.cs.common.excel.converter.IntegerExcelConverter;

import java.math.BigDecimal;
import java.util.Date;

/***
 *
 */
public class ExcelConverterFactory {


    public static ExcelConverter createConvert(Class<?> type) {
        if (type == null) {
            return null;
        }
        if (BigDecimal.class.equals(type)) {
            return new BigDecimalExcelConverter();
        } else if (Date.class.equals(type)) {
            return new DateExcelConverter();
        } else if (Integer.class.equals(type)) {
            return new IntegerExcelConverter();
        }
        return null;
    }
}
