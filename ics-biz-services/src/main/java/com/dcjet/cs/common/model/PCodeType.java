package com.dcjet.cs.common.model;

public class PCodeType {
    /***
     * 商品编码
     */
    public static final String COMPLEX = "COMPLEX";
    /***
     * 检验检验
     */
    public static final String COMPLEX_CIQ = "COMPLEX_CIQ";
    /***
     * 币制
     */
    public static final String CURR = "CURR";
    /****
     * 币制旧
     */
    public static final String CURR_OUTDATED = "CURR_OUTDATED";
    /***
     * 关区
     */
    public static final String CUSTOMS_REL = "CUSTOMS_REL";
    /***
     * 出入境口岸
     */
    public static final String CIQ_ENTY_PORT = "CIQ_ENTY_PORT";

    /***
     * 国内地区
     */
    public static final String AREA = "AREA";

    /***
     * 国内地区
     */
    public static final String POST_AREA = "POST_AREA";
    /***
     * 征免方式
     */
    public static final String LEVYMODE = "LEVYMODE";
    /**
     * 征免性质
     */
    public static final String LEVYTYPE = "LEVYTYPE";
    /**
     * 监管证件
     */
    public static final String LICENSEDOCU = "LICENSEDOCU";
    /**
     * 港口
     */
    public static final String PORT_LIN = "PORT_LIN";
    /***
     * 加工种类
     */
    public static final String PRODUCT_TYPE_MNL = "PRODUCT_TYPE_MNL";
    /***
     * 国别(地区)
     */
    public static final String COUNTRY = "COUNTRY";
    /***
     * 国别(地区)旧
     */
    public static final String COUNTRY_OUTDATED = "COUNTRY_OUTDATED";
    /***
     *
     * 单位
     */
    public static final String UNIT = "UNIT";
    /***
     * 监管方式
     */
    public static final String TRADE = "TRADE";
    /***
     * 成交方式
     */
    public static final String TRANSAC = "TRANSAC";
    /***
     * 运输方式
     */
    public static final String TRANSF = "TRANSF";
    /***
     * 用途
     */
    public static final String USER_TO = "USER_TO";
    /***
     * 包装种类
     */
    public static final String WRAP = "WRAP";
    /***
     * 申报要素
     */
    public static final String ELEMENT = "ELEMENT";
    /***
     * 企业
     */
    public static final String COMPANY = "COMPANY";

    /***
     * 汇率
     */
    public static final String EXCHANGE_RATE = "EXCHANGE_RATE";

    /***
     * 中国海关汇率
     */
    public static final String EXCHANGE_CUSTOMS_CURRENT = "EXCHANGE_CUSTOMS_CURRENT";

    /***
     * 用途代码 用于报检  JJL - ADD 2020-03-31
     */
    public static final String USE_TO_CIQ = "USE_TO_CIQ";

    /***
     * 货物属性  用于报检  JJL - ADD 2020-04-13
     */
    public static final String GOODS_ATTR = "GOODS_ATTR";

    /***
     * 商品编码对应“特殊协定/协定税率”
     */
    public static final String COMPLEX_RATE_AGREEMENT = "COMPLEX_RATE_AGREEMENT";

    /***
     * 商品编码对应“增值税/普通税率/最惠国/暂定税率”
     */
    public static final String COMPLEX_RATE_INFO = "COMPLEX_RATE_INFO";

    public static final String CURR_ALL = "CURR_ALL";

    public static final String COUNTRY_ALL = "COUNTRY_ALL";
    public static final String KEY_PRODUCT_MARK = "KEY_PRODUCT_MARK";



    /***
     * 水路运输
     */
    public static final String TRAF_MODE_WATERWAY = "2";

    /***
     * 铁路运输
     */
    public static final String TRAF_MODE_RAILWAY = "3";
}
