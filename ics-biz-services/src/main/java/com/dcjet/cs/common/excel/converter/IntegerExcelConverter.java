package com.dcjet.cs.common.excel.converter;

import com.dcjet.cs.common.excel.ExcelColumnContext;
import com.dcjet.cs.common.model.ExcelToJava;

/**
 * <AUTHOR>
 */
public class IntegerExcelConverter implements ExcelConverter<Integer> {
    @Override
    public String convertToExcel(Integer value, ExcelColumnContext context) {
        return value != null ? value.toString() : "";
    }

    @Override
    public ExcelToJava<Integer> convertToJava(String value, ExcelColumnContext context) {
        ExcelToJava<Integer> toJava = new ExcelToJava<>();
        if (value == null) {
            return toJava;
        }

        try {
            toJava.setValue(Integer.parseInt(value));
        } catch (NumberFormatException ex) {
            toJava.setErrMsg(context.getExcelColumn().name()+ex.getMessage());
        }

        return toJava;
    }
}
