package com.dcjet.cs.common.excel.converter;

import com.dcjet.cs.common.excel.ExcelColumnContext;
import com.dcjet.cs.common.model.ExcelToJava;
import com.google.common.base.Strings;
import com.xdo.pcode.service.PCodeHolder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component("pcodeExcelConverter")
public class PCodeExcelConverter implements ExcelConverter<String> {

    @Resource
    private PCodeHolder pCodeHolder;

    @Override
    public String convertToExcel(String value, ExcelColumnContext context) {
        if (Strings.isNullOrEmpty(value)) {
            return value;
        }
        return value + " " + pCodeHolder.getName(context.getExcelColumn().tag(), value).orElse("");
    }

    @Override
    public ExcelToJava<String> convertToJava(String value, ExcelColumnContext context) {
        ExcelToJava<String> toJava = new ExcelToJava();
        toJava.setValue(value);

        if (Strings.isNullOrEmpty(value)) {
            return toJava;
        }
        String[] combinationVal = value.split("\\s");
        if (combinationVal.length == 0) {
            return toJava;
        }
        String val = combinationVal[0].trim();
        // 先从code中去拿，如果没有，就从name中去去拿
        String name = pCodeHolder.getName(context.getExcelColumn().tag(), val).orElse(null);
        if (!Strings.isNullOrEmpty(name)) {
            return toJava.setValue(val);

        }
        //获取栏位名称
        String fieldName = context.getExcelColumn().name();

        // name 转 code
        String code = pCodeHolder.getByName(context.getExcelColumn().tag(), val).orElse(null);
        if (Strings.isNullOrEmpty(code)) {
//            toJava.setErrMsg(value +" [" + fieldName + "]" + xdoi18n.XdoI18nUtil.t("是无效值"));
            toJava.setErrMsg(xdoi18n.XdoI18nUtil.t(value) +" [" + xdoi18n.XdoI18nUtil.t(fieldName) + "]" + xdoi18n.XdoI18nUtil.t("是无效值"));
        } else {
            toJava.setValue(code);
        }
        return toJava;
    }
}
