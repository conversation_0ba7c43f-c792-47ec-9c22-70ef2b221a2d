package com.dcjet.cs.common.component;

import com.dcjet.cs.common.dao.DistributedLockMapper;
import com.dcjet.cs.common.model.DistributedLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Component
public class DistributedLockComponent {

    @Resource
    private DistributedLockMapper distributedLockMapper;

    @Autowired
    private PlatformTransactionManager transactionManager;

    /***
     * 尝试获取锁
     *
     * @param sid
     * @param expire 单位分
     * @param tradeCode
     * @param userNo
     * @return
     */
    public boolean tryLock(String sid, int expire, String tradeCode, String userNo) {
        DistributedLock lock = distributedLockMapper.selectByPrimaryKey(sid);
        if (lock == null) {
            TransactionStatus status = transactionManager.getTransaction(new DefaultTransactionDefinition());
            try {
                lock(sid, expire, tradeCode, userNo);
                transactionManager.commit(status);
                return true;
            } catch (Exception ex) {
                transactionManager.rollback(status);
                return false;
            }
        } else {
            if (new Date().after(lock.getExpireDate())) {
                TransactionStatus status = transactionManager.getTransaction(new DefaultTransactionDefinition());
                try {
                    unlock(sid);
                    lock(sid, expire, tradeCode, userNo);
                    transactionManager.commit(status);
                    return true;
                }  catch (Exception ex) {
                    transactionManager.rollback(status);
                    return false;
                }
            }
        }
        return false;
    }

    public void unlock(String sid) {
        distributedLockMapper.deleteByPrimaryKey(sid);
    }

    /***
     * 加锁
     * @param sid
     * @param expire
     * @param tradeCode
     * @param userNo
     * @return
     */
    private DistributedLock lock(String sid, int expire, String tradeCode, String userNo) {
        DistributedLock lock = new DistributedLock();
        lock.setSid(sid);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.MINUTE, expire);
        lock.setTradeCode(tradeCode);
        lock.setExpireDate(calendar.getTime());
        lock.setInsertTime(new Date());
        lock.setInsertUser(userNo);
        distributedLockMapper.insert(lock);
        return lock;
    }
}
