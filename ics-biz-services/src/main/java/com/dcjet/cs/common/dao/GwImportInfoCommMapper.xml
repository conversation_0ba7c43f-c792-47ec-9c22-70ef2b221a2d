<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.common.dao.GwImportInfoCommMapper">
    <resultMap id="gwImportErrorResultMap" type="com.dcjet.cs.common.model.GwImportInfoComm">
		<id column="sid" property="sid" jdbcType="VARCHAR" />
		<result column="info_data" property="infoData" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
		<result column="insert_user" property="insertUser" jdbcType="VARCHAR" />
		<result column="insert_time" property="insertTime" jdbcType="TIMESTAMP" />
		<result column="update_user" property="updateUser" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR" />
		<result column="update_user_name" property="updateUserName" jdbcType="VARCHAR" />
		<result column="status" property="status" jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List" >
     sid
     ,info_data
     ,trade_code
     ,insert_user
     ,insert_time
     ,update_user
     ,update_time
     ,insert_user_name
     ,update_user_name
     ,status
    </sql>
    <sql id="condition">
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="gwImportErrorResultMap" parameterType="com.dcjet.cs.common.model.GwImportInfoComm">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        t_gw_import_info_comm t
        <where>
            <include refid="condition"></include>
        </where>
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from t_gw_import_info_comm t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
</mapper>
