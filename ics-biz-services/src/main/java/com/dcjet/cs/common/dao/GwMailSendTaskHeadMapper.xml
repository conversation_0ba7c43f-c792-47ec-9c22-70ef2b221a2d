<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.common.dao.GwMailSendTaskHeadMapper">
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultType="com.dcjet.cs.common.model.GwMailSendTaskHead" parameterType="com.dcjet.cs.common.model.GwMailSendTaskHead">
        SELECT
        sid
        ,sender
        ,recipient
        ,cc
        ,subject
        ,body
        ,bussiness_type
        ,daty_type
        ,state
        ,is_customized
        ,err_mess
        ,lock_mark
        ,trade_code
        ,insert_user
        ,insert_user_name
        ,insert_time
        ,update_user
        ,update_user_name
        ,update_time
        ,extend_field
        ,is_html
        FROM
        t_gw_mail_send_task_head t
        <where>
            <if test="tradeCode != null and tradeCode != ''">
                and t.TRADE_CODE = #{tradeCode}
            </if>
            and lock_mark = #{lockMark}
        </where>
    </select>
    <update id="updateLockMark">
        update t_gw_mail_send_task_head set lock_mark=#{lockMark},state='1' where state = #{state}
    </update>
    <select id="getAttachList" resultType="com.dcjet.cs.common.model.GwMailSendTaskAttach">
        SELECT
        sid
        ,head_id
        ,url
        ,file_name
        ,trade_code
        ,insert_user
        ,insert_time
        ,update_user
        ,update_time
        FROM
        t_gw_mail_send_task_attach t
         where head_id in (select sid from t_gw_mail_send_task_head where lock_mark=#{lockMark})
    </select>
</mapper>
