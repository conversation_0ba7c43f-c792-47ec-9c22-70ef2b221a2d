package com.dcjet.cs.common.service;

import com.dcjet.cs.common.dao.PrintCenterMapper;
import org.apache.ibatis.session.SqlSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * @Auther: Administrator
 * @Date: 2019/5/7 16:55
 * @Description:
 */
@Service
public class PrintCenterService {
    @Resource
    private PrintCenterMapper printCenterMapper;
    @Autowired
    private SqlSession sqlSession;
    public void getPrindData(Map<String,Object> mapConditions){
//   return sqlSession.selectList("com.dcjet.cs.common.dao.PrintCenterMapper.getPrindData",mapConditions);
        printCenterMapper.getPrindData(mapConditions);
    }
}
