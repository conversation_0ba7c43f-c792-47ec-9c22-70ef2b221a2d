<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.common.dao.GwstdHttpConfigMapper">
    <resultMap id="gwstdHttpConfigResultMap" type="com.dcjet.cs.common.model.GwstdHttpConfig">
        <id column="SID" property="sid" jdbcType="VARCHAR"/>
        <result column="BASE_URL" property="baseUrl" jdbcType="VARCHAR"/>
        <result column="SERVICE_URL" property="serviceUrl" jdbcType="VARCHAR"/>
        <result column="TOKEN" property="token" jdbcType="VARCHAR"/>
        <result column="TYPE" property="type" jdbcType="VARCHAR"/>
        <result column="EXTEND_FILED1" property="extendFiled1" jdbcType="VARCHAR"/>
        <result column="EXTEND_FILED2" property="extendFiled2" jdbcType="VARCHAR"/>
        <result column="EXTEND_FILED3" property="extendFiled3" jdbcType="VARCHAR"/>
        <result column="NOTE" property="note" jdbcType="VARCHAR"/>
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="INSERT_USER" property="insertUser" jdbcType="VARCHAR"/>
        <result column="INSERT_TIME" property="insertTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        SID,
        BASE_URL,
        SERVICE_URL,
        TOKEN,
        TYPE,
        EXTEND_FILED1,
        EXTEND_FILED2,
        EXTEND_FILED3,
        NOTE,
        TRADE_CODE,
        INSERT_USER,
        INSERT_TIME,
        UPDATE_USER,
        UPDATE_TIME
    </sql>
    <sql id="condition">
        TRADE_CODE = '9999999999'
        <if test="type != null and type != ''">
            and TYPE = #{type}
        </if>
    </sql>
    <select id="selectByType" resultType="com.dcjet.cs.common.model.GwstdHttpConfig">
        SELECT
            <include refid="Base_Column_List"></include>
        FROM T_GWSTD_HTTP_CONFIG
        WHERE TRADE_CODE = '9999999999' and TYPE = #{type}
    </select>
    <select id="findByType" resultType="com.dcjet.cs.common.model.GwstdHttpConfig">
        SELECT
        <include refid="Base_Column_List"></include>
        FROM T_GWSTD_HTTP_CONFIG
        WHERE TRADE_CODE = #{tradeCode} and TYPE = #{type}
    </select>

    <select id="selecttest" resultType="java.lang.String">
        select type from T_GWSTD_HTTP_CONFIG where SID='FC4E9EC0C4BE44559E868E964D0ADCA4'
    </select>
</mapper>
