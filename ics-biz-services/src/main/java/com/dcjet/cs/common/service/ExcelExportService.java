package com.dcjet.cs.common.service;

import com.dcjet.cs.common.excel.ExcelColumnContext;
import com.dcjet.cs.common.excel.ExcelConverterFactory;
import com.dcjet.cs.common.excel.converter.ExcelConverter;
import com.dcjet.cs.common.excel.converter.MapExcelConverter;
import com.dcjet.cs.dto.base.BasicDto;
import com.dcjet.cs.dto.base.annotation.ExcelColumn;
import com.dcjet.cs.util.ReflectUtil;
import com.dcjet.cs.util.variable.CommonVariable;
import com.google.common.base.Strings;
import com.xdo.common.util.DateUtils;
import com.xdo.common.util.Parameters;
import com.xdo.domain.KeyValuePair;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import lombok.SneakyThrows;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Stream;

/***
 * 通用的Excel导出服务
 *
 */
@Service
public class ExcelExportService {
    private static final int MAX_ROW = 1000;

    @Resource
    private Map<String, ExcelConverter> converterStrategyMap;


    public <T> ResponseEntity exportExcelByPage(String name, List<KeyValuePair<String, String>> header, PageParam page, Function<PageParam, ResultObject<List<T>>> function) throws IOException {
        PageParam pageParam = new PageParam();
        pageParam.setSort(page.getSort());
        pageParam.setPage(1);
        pageParam.setLimit(100);
        ResultObject<List<T>> resultObject = function.apply(pageParam);
        Stream<T> stream = Stream.generate(new Supplier<T>() {
            Iterator<T> iterator = resultObject.getData().iterator();
            boolean noMore = false;

            @Override
            public T get() {
                T val = null;
                if (noMore) {
                    return null;
                }

                if (iterator != null && iterator.hasNext()) {
                    val = iterator.next();
                } else {
                    pageParam.setPage(pageParam.getPage() + 1);
                    ResultObject<List<T>> pageObject = function.apply(pageParam);
                    List<T> list = pageObject.getData();
                    if (list != null && list.size() > 0) {
                        iterator = list.iterator();
                        if (iterator.hasNext()) {
                            val = iterator.next();
                        }
                    } else {
                        noMore = true;
                    }
                }
                return val;
            }
        }).limit(resultObject.getTotal());
        return exportExcel(URLEncoder.encode(name, CommonVariable.UTF8), header, stream);
    }

    /**
     * 获取带Excel流的HttpHeader
     *
     * @param name
     * @param header
     * @param dtos
     * @return
     * @throws Exception
     */
    public <T> ResponseEntity exportExcel(String name, List<KeyValuePair<String, String>> header, List<T> dtos) throws IOException {
        return exportExcel(name, header, dtos.stream());
    }

    /**
     * 获取带Excel流的HttpHeader
     *
     * @param name
     * @param header
     * @param stream
     * @return
     * @throws Exception
     */
    public <T> ResponseEntity exportExcel(String name, List<KeyValuePair<String, String>> header, Stream<T> stream) throws IOException {
        byte[] in = exportGeneric(URLDecoder.decode(name, CommonVariable.UTF8), header, stream);
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_TYPE, "application/vnd.ms-excel;charset=ISO8859-1");
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                + new String(name.replaceAll("\\+","%20").getBytes(CommonVariable.UTF8), "ISO8859-1") + ".xlsx");
        return ResponseEntity
                .ok()
                .headers(headers)
                .body(new InputStreamResource(new ByteArrayInputStream(in)));
    }


    /***
     *
     *
     * @param name
     * @param columnList
     * @return
     */
    public ResponseEntity exportImportTpl(String name, List<ExcelColumn> columnList) throws IOException {
        Parameters.requireNonEmpty(name);
        Parameters.requireNonEmpty(columnList);
        try (
                SXSSFWorkbook wb = new SXSSFWorkbook(MAX_ROW);
                ByteArrayOutputStream out = new ByteArrayOutputStream()
        ) {
            SXSSFSheet sheet = wb.createSheet(name);
            SXSSFRow headerRow  = sheet.createRow(0);
            SXSSFRow typeRow = sheet.createRow(1);
            SXSSFRow stateRow = sheet.createRow(2);
            SXSSFRow noteRow = sheet.createRow(3);

            columnList.sort(Comparator.comparing(ExcelColumn::order));

            DataFormat fmt = wb.createDataFormat();
            CellStyle textStyle = wb.createCellStyle();
            textStyle.setDataFormat(fmt.getFormat("@"));


            for (int index = 0; index < columnList.size(); index++) {
                ExcelColumn column = columnList.get(index);
                Font font = wb.createFont();
                if (column.required()) {
                   font.setColor(IndexedColors.RED.getIndex());
                } else {
                    font.setColor(IndexedColors.WHITE.getIndex());
                }
                // 设置所有的单元格为文本格式
                sheet.setDefaultColumnStyle(index, textStyle);

                // 表头
                CellStyle cellStyle = wb.createCellStyle();
                cellStyle.setAlignment(HorizontalAlignment.LEFT);
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                cellStyle.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
                cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                cellStyle.setFont(font);
                cellStyle.setBorderLeft(BorderStyle.THIN);
                cellStyle.setBorderTop(BorderStyle.THIN);
                cellStyle.setBorderBottom(BorderStyle.THIN);
                cellStyle.setBorderRight(BorderStyle.THIN);
                SXSSFCell headerCell = headerRow.createCell(index);
                headerCell.setCellStyle(cellStyle);
                headerCell.setCellType(CellType.STRING);
                headerCell.setCellValue(xdoi18n.XdoI18nUtil.t(column.name()));

                // 字符类型
                cellStyle = wb.createCellStyle();
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                cellStyle.setFillForegroundColor(IndexedColors.CORNFLOWER_BLUE.getIndex());
                cellStyle.setBorderLeft(BorderStyle.THIN);
                cellStyle.setBorderTop(BorderStyle.THIN);
                cellStyle.setBorderBottom(BorderStyle.THIN);
                cellStyle.setBorderRight(BorderStyle.THIN);

                font = wb.createFont();
                if (column.required()) {
                    font.setColor(IndexedColors.RED.getIndex());
                } else {
                    font.setColor(IndexedColors.BLACK.getIndex());
                }
                cellStyle.setFont(font);
                SXSSFCell typeCell = typeRow.createCell(index);
                typeCell.setCellStyle(cellStyle);
                typeCell.setCellType(CellType.STRING);
                typeCell.setCellValue(xdoi18n.XdoI18nUtil.t(column.type()));

                // 必填/可空
                cellStyle = wb.createCellStyle();
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                cellStyle.setFillForegroundColor(IndexedColors.PALE_BLUE.getIndex());
                cellStyle.setBorderLeft(BorderStyle.THIN);
                cellStyle.setBorderTop(BorderStyle.THIN);
                cellStyle.setBorderBottom(BorderStyle.THIN);
                cellStyle.setBorderRight(BorderStyle.THIN);
                cellStyle.setFont(font);
                SXSSFCell stateCell = stateRow.createCell(index);
                stateCell.setCellStyle(cellStyle);
                stateCell.setCellType(CellType.STRING);
                stateCell.setCellValue(column.required() ? xdoi18n.XdoI18nUtil.t("必填") : xdoi18n.XdoI18nUtil.t("可空"));


                // 备注
                cellStyle = wb.createCellStyle();
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                cellStyle.setFillForegroundColor(IndexedColors.CORNFLOWER_BLUE.getIndex());
                cellStyle.setBorderLeft(BorderStyle.THIN);
                cellStyle.setBorderTop(BorderStyle.THIN);
                cellStyle.setBorderBottom(BorderStyle.THIN);
                cellStyle.setBorderRight(BorderStyle.THIN);
                cellStyle.setFont(font);
                SXSSFCell noteCell = noteRow.createCell(index);
                noteCell.setCellStyle(cellStyle);
                noteCell.setCellType(CellType.STRING);
                noteCell.setCellValue(xdoi18n.XdoI18nUtil.t(column.note()));

                sheet.trackAllColumnsForAutoSizing();
                sheet.autoSizeColumn(index, true);
            }

            wb.write(out);
            HttpHeaders headers = new HttpHeaders();
            //解决空格变成+号问题
            name = URLEncoder.encode(name, CommonVariable.UTF8);
            name = name.replaceAll("\\+","%20");
            headers.add(HttpHeaders.CONTENT_TYPE, "application/vnd.ms-excel;charset=ISO8859-1");
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                    + new String(name.getBytes(CommonVariable.UTF8), "ISO8859-1") + ".xlsx");
            return ResponseEntity
                    .ok()
                    .headers(headers)
                    .body(new InputStreamResource(new ByteArrayInputStream(out.toByteArray())));
        }
    }

    /**
     * 获取带Excel流的HttpHeader
     *
     * @param name
     * @param header
     * @param data
     * @return
     * @throws Exception
     */
    public ResponseEntity exportExcelByMap(String name, List<KeyValuePair<String, String>> header, List<Map<String, Object>> data) throws Exception {
        byte[] bytes = exportByMap(URLDecoder.decode(name, CommonVariable.UTF8), header, data);
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_TYPE, "application/vnd.ms-excel;charset=ISO8859-1");
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                + new String(name.getBytes(CommonVariable.UTF8), "ISO8859-1") + ".xlsx");
        return ResponseEntity
                .ok()
                .headers(headers)
                .body(new InputStreamResource(new ByteArrayInputStream(bytes)));
    }

    /***
     * 导出数据，数据结构为Map
     *
     * @param sheetName
     * @param header
     * @param data
     */
    public byte[] exportByMap(String sheetName, List<KeyValuePair<String, String>> header, List<Map<String, Object>> data) throws IOException {
        Parameters.requireNonEmpty(sheetName);
        Parameters.requireNonEmpty(header);

        try (
                SXSSFWorkbook wb = new SXSSFWorkbook(MAX_ROW);
                ByteArrayOutputStream out = new ByteArrayOutputStream()
        ) {
            SXSSFSheet sheet = wb.createSheet(sheetName);
            int rowNum = 0;
            createHeader(wb, sheet, header, rowNum);
            rowNum++;

            int size = header.size();

            if (data != null) {
                for (Map<String, Object> map : data) {
                    Row row = sheet.createRow(rowNum);
                    for (int i = 0; i < size; i++) {
                        String key = header.get(i).getKey();
                        if (map.containsKey(key)) {
                            row.createCell(i).setCellValue(getCellValue(xdoi18n.XdoI18nUtil.t(map.get(key).toString())));

                        } else {
                            row.createCell(i);
                        }
                    }
                    rowNum++;
                }
            }

            wb.write(out);
            return out.toByteArray();
        }
    }

    /***
     * 导出Excel
     *
     * 支持组合对象导出，组合对象目前支持支持一层，如果是多层，则会失败！
     *
     * @param sheetName
     * @param header
     * @param stream
     * @param <T>
     * @return
     * @throws IOException
     */
    private <T> byte[] exportGeneric(String sheetName, List<KeyValuePair<String, String>> header, Stream<T> stream)
            throws IOException {

        Parameters.requireNonEmpty(sheetName);
        Parameters.requireNonEmpty(header);

        SXSSFWorkbook wb = new SXSSFWorkbook(MAX_ROW);
        SXSSFSheet sheet = wb.createSheet(sheetName);

        CellStyle defaultStyle = wb.createCellStyle();
        defaultStyle.setDataFormat(wb.getCreationHelper().createDataFormat().getFormat("@"));
        sheet.setForceFormulaRecalculation(true);
        sheet.setDefaultColumnStyle(5,defaultStyle);

        int rowNum = 0;
        createHeader(wb, sheet, header, rowNum);
        rowNum++;

        int size = header.size();

        int finalRowNum = rowNum;
        stream.forEachOrdered(new Consumer<T>() {
            int steamRowNum = finalRowNum;
            Map<String, ExcelColumnContext> columnContextMap;

            @SneakyThrows
            @Override
            public void accept(T t) {
                if (t == null) {
                    return;
                }
                if (columnContextMap == null) {
                    columnContextMap = getColumnContextMap(t.getClass());
                }
                Row row = sheet.createRow(steamRowNum);
                for (int i = 0; i < size; i++) {
                    String key = header.get(i).getKey();
                    ExcelColumnContext contentColumn = columnContextMap.get(key.toLowerCase());
                    if (contentColumn == null) {
                        continue;
                    }

                    String[] keys = key.split("\\.");
                    Object cellValue = null;
                    if (keys.length == 1) {
                        cellValue = contentColumn.getField().get(t);
                    } else {
                        Object subT = t;
                        for (int j = 0; j < keys.length; j++) {
                            String subKey = keys[j];
                            ExcelColumnContext subContentColumn = columnContextMap.get(subKey.toLowerCase());
                            if (subContentColumn == null) {
                                continue;
                            }
                            subT = subContentColumn.getField().get(subT);
                        }
                        if (subT != null) {
                            cellValue = contentColumn.getField().get(subT);
                        }
                    }

                    if (cellValue != null) {
                        if (contentColumn.getConverter() != null) {
                            cellValue = contentColumn.getConverter().convertToExcel(cellValue, contentColumn);
                        }
                        row.createCell(i).setCellValue(Optional.ofNullable(cellValue).orElse("").toString());
                    }
                }
                steamRowNum++;
            }
        });
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        wb.write(out);
        return out.toByteArray();
    }

    /***
     * 获取对象导出的上下文信息
     *
     * @param cla
     * @return
     */
    private Map<String, ExcelColumnContext> getColumnContextMap(Class cla) {
        Map<String, ExcelColumnContext> columnContextMap = new HashMap<>();
        List<Field> fieldList = ReflectUtil.getAllDeclaredFields(cla);
        for (Field field : fieldList) {
            field.setAccessible(true);

            ExcelColumn excelColumn = field.getAnnotation(ExcelColumn.class);
            ExcelColumnContext contentColumn = new ExcelColumnContext(excelColumn, field);
            if (excelColumn != null) {
                if (excelColumn.IOC()) {
                    contentColumn.setConverter(converterStrategyMap.get(excelColumn.converter()));
                } else {
                    // 手工初始化转换器 注意前后的优先级
                    if (!Strings.isNullOrEmpty(excelColumn.map())) {
                        contentColumn.setConverter(new MapExcelConverter());
                    }
                }
            }
            if (contentColumn.getConverter() == null) {
                // 设置默认的转换器
                contentColumn.setConverter(ExcelConverterFactory.createConvert(field.getType()));
            }
            if (!columnContextMap.containsKey(field.getName().toLowerCase())) {
                columnContextMap.put(field.getName().toLowerCase(), contentColumn);
            }

            if (ReflectUtil.isSubclassOf(field.getType(), BasicDto.class)) {
                Map<String, ExcelColumnContext> subColumnContextMap = getColumnContextMap(field.getType());
                subColumnContextMap.entrySet().stream().forEach(it -> {
                    columnContextMap.put(field.getName().concat(".").concat(it.getKey()).toLowerCase(), it.getValue());
                });
            }
        }
        return columnContextMap;
    }

    /***
     * 创建Excel头信息
     *
     * @param wb
     * @param sheet
     * @param header
     * @param rowNum
     * @return
     */
    private Row createHeader(Workbook wb, SXSSFSheet sheet, List<KeyValuePair<String, String>> header, int rowNum) {
        int size = header.size();
        Row row = sheet.createRow(rowNum);

        CellStyle cellStyle = createHeaderCellStyle(wb);
        sheet.trackAllColumnsForAutoSizing();

        for (int i = 0; i < size; i++) {
            Cell cell = row.createCell(i, CellType.STRING);
            cell.setCellValue(header.get(i).getValue());
            cell.setCellStyle(cellStyle);
            sheet.autoSizeColumn(i);
        }
        return row;
    }

    /**
     * 创建header cell style
     *
     * @param wb
     */
    private CellStyle createHeaderCellStyle(Workbook wb) {
        Font font = wb.createFont();
        font.setBold(true);
        font.setFontName("Microsoft YaHei");

        CellStyle cellStyle = wb.createCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setFont(font);
        return cellStyle;
    }

    /***
     *
     * @param obj
     * @return
     */
    private static String getCellValue(Object obj) {
        if (obj == null) {
            return "";
        }
        if (obj instanceof Date) {
            Date date = (Date) obj;
            return DateUtils.dateToString(date);
        } else if (obj instanceof LocalDate) {
            LocalDate date = (LocalDate) obj;
            return date.format(DateTimeFormatter.ISO_LOCAL_DATE);
        } else if (obj instanceof LocalDateTime) {
            LocalDateTime dateTime = (LocalDateTime) obj;
            return dateTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        } else if (obj instanceof BigDecimal) {
            return ((BigDecimal) obj).stripTrailingZeros().toPlainString();
        }

        return obj.toString();
    }
}
