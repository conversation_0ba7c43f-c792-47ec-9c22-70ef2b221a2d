package com.dcjet.cs.common.model;

import com.dcjet.cs.base.model.BasicModel;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * 分布式锁
 * 
 * generated by Generate 神码
 * <AUTHOR>
 * @date: 2019-12-23
 */
@Setter @Getter
@Table(name = "T_DISTRIBUTED_LOCK")
public class DistributedLock extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 补偿在锁失效未删除的情况下进行解锁
     */
    @Column(name = "EXPIRE_DATE")
    private Date expireDate;

    /**
     * 企业编码
     */
    @Column(name = "TRADE_CODE")
    private String tradeCode;
    
}