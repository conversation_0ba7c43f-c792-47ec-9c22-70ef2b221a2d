package com.dcjet.cs.common.event;

import com.dcjet.cs.common.dao.RequestLogMapper;
import com.dcjet.cs.common.model.RequestLog;
import com.dcjet.cs.common.model.RequestLogEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
public class RequestLogEventListener {

    private final Logger logger = LoggerFactory.getLogger(RequestLogEventListener.class);

    @Resource
    RequestLogMapper requestLogMapper;

    @Async
    @EventListener(value = RequestLogEvent.class)
    public void onRequestEvent(RequestLogEvent event) {
        try {
            RequestLog log = event.getRequestLog();
            log.preInsert(null);
            requestLogMapper.insert(log);
        } catch (Exception ex) {
            logger.error("保持请求日志记录出错!", ex);
        }

    }
}
