package com.dcjet.cs.common.model;


/**
 * 用于Excel转成Java过程中包装错误
 *
 * <AUTHOR>
 */
public class ExcelToJava<T> {
    private T value;

    private String errMsg;

    public ExcelToJava() {
    }

    public ExcelToJava(T t, String errMsg) {
        this.value = t;
        this.errMsg = errMsg;
    }

    public ExcelToJava<T> setValue(T t) {
        this.value = t;
        return this;
    }

    public T getValue() {
        return value;
    }

    public ExcelToJava<T> setErrMsg(String errMsg) {
        this.errMsg = errMsg;
        return this;
    }

    public String getErrMsg() {
        return this.errMsg;
    }
}
