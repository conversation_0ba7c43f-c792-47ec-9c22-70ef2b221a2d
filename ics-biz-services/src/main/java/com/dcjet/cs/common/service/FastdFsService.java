package com.dcjet.cs.common.service;

import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.file.XdoFileHandler;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.lang.invoke.MethodHandles;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

@Service
public class FastdFsService {
    private static final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Resource(name = "eternalXdoFileHandler")
    public XdoFileHandler fileHandler;

    @Resource(name = "otherEternalXdoFileHandler")
    public XdoFileHandler otherFileHandler;

    @Value("${fastdfs.http.url: }")
    private String fastDfsHttpUrl;

    public ResponseEntity getFileFromFastDFS(String fdfsId, String fileName) throws Exception {
        byte[] bytes = getByteFromFastDFS(fdfsId);
        HttpHeaders h = new HttpHeaders();
        fileName = URLEncoder.encode(fileName, CommonVariable.UTF8).replace("+", "%20");
        h.setContentDispositionFormData("attachment", fileName);
        h.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        return new ResponseEntity<>(bytes, h, HttpStatus.OK);
    }

    /**
     * 从华为云下载文件并写入指定的filePath
     *
     * @throws Exception
     */
    public void downloadFileToLocal(String fdfsId, String filePath) throws Exception {
        byte[] bytes = getByteFromFastDFS(fdfsId);
        //将字节流写入文件
        OutputStream stream = null;
        try {
            stream = new FileOutputStream(filePath);
            IOUtils.write(bytes, stream);
        } catch (Exception ex) {
            if (stream != null) {
                stream.close();
            }
            throw ex;
        } finally {
            if (stream != null) {
                stream.close();
            }
        }
    }

    public ResponseEntity getFileFromFastDFSByIOS(String fdfsId, String fileName) throws Exception {
        byte[] bytes = getByteFromFastDFS(fdfsId);
        Base64.Encoder encoder = Base64.getEncoder();
        byte[] byteEncoderFile = encoder.encode(bytes);
        InputStream inputStream = new ByteArrayInputStream(byteEncoderFile);
        if (inputStream != null) {
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_TYPE, "application/pdf;charset=ISO8859-1");
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                    + new String(URLEncoder.encode(fileName).getBytes(StandardCharsets.UTF_8), "ISO8859-1"));
            return ResponseEntity
                    .ok()
                    .headers(headers)
                    .body(new InputStreamResource(inputStream));
        }
        return null;
    }

//    public byte[] getByteFromFastDFS(String fdfsId) throws Exception {
//        byte[] bytes;
//        String url;
//        if (StringUtils.isNotBlank(fileHandler.getHandlerType()) && "HUAWEI".equals(fileHandler.getHandlerType()) && fdfsId.startsWith("group1")) {
//            url = fastDfsHttpUrl + fdfsId;
//            bytes = getFileByStream(fastDfsHttpUrl + fdfsId);
//        } else {
//            url = fdfsId;
//            bytes = fileHandler.downloadFile(fdfsId);
//        }
//
//        //添加补偿机制
//        if (bytes == null || bytes.length == 0) {
//            logger.info(String.format("未获取到指定的文件，正在补偿获取，文件全路径为：【%s】", url));
//            int tryCount = 2;
//            while (tryCount > 0) {
//                if (bytes == null || bytes.length == 0) {
//                    if (StringUtils.isNotBlank(fileHandler.getHandlerType()) && "HUAWEI".equals(fileHandler.getHandlerType()) && fdfsId.startsWith("group1")) {
//                        bytes = getFileByStream(fastDfsHttpUrl + fdfsId);
//                    } else {
//                        bytes = fileHandler.downloadFile(fdfsId);
//                    }
//                } else {
//                    break;
//                }
//                tryCount--;
//            }
//            logger.info(String.format("补偿获取文件结束，得到的字节大小为：【%s】", bytes == null ? 0 : bytes.length));
//        }
//        return bytes;
//    }
    public byte[] getByteFromFastDFS(String fdfsId) throws Exception {
        byte[] bytes;
        String url;

        if (fdfsId.startsWith("TIANYI")) {
            url = fdfsId;
            bytes = fileHandler.downloadFile(fdfsId);
        } else {
            url = fdfsId;
            bytes = otherFileHandler.downloadFile(fdfsId);
        }

        //添加补偿机制
        if (bytes == null || bytes.length == 0) {
            logger.info(String.format("未获取到指定的文件，正在补偿获取，文件全路径为：【%s】", url));
            int tryCount = 2;
            while (tryCount > 0) {
                if (bytes == null || bytes.length == 0) {
                    if (fdfsId.startsWith("TIANYI")) {
                        bytes = fileHandler.downloadFile(fdfsId);
                    } else {
                        bytes = otherFileHandler.downloadFile(fdfsId);
                    }
                } else {
                    break;
                }
                tryCount--;
            }
            logger.info(String.format("补偿获取文件结束，得到的字节大小为：【%s】", bytes == null ? 0 : bytes.length));
        }
        return bytes;
    }

    private byte[] getFileByStream(String fileUrl) {
        try {
            HttpURLConnection connection = (HttpURLConnection) new URL(fileUrl).openConnection();
            connection.setReadTimeout(5000);
            connection.setConnectTimeout(3000);
            connection.setRequestMethod("GET");
            connection.setRequestProperty("Accept-Encoding", "identity");
            connection.connect();
            int code = connection.getResponseCode();
            if (code == 200 || code == 206) {
                int contentLength = connection.getContentLength();
                byte[] btData = new byte[contentLength];
                InputStream is = connection.getInputStream();
                DataInputStream dataInputStream = new DataInputStream(is);
                dataInputStream.readFully(btData);
                dataInputStream.close();
                if (is != null) {
                    is.close();
                }
                return btData;
            }
        } catch (Exception ex) {
            logger.error("文件下载异常", ex);
        }
        return null;
    }

}
