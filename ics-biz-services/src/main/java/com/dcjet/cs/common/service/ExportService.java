package com.dcjet.cs.common.service;

import com.aspose.cells.*;
import com.dcjet.cs.dec.model.BizIReceiptHead;
import com.dcjet.cs.dec.model.BizIReceiptList;
import com.dcjet.cs.dto.bi.GwstdSealParam;
import com.dcjet.cs.util.Codes;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.customexport.CustomFunction;
import com.dcjet.cs.util.customexport.ExportDataSource;
import com.dcjet.cs.util.customexport.HashMapDataTable;
import com.google.common.base.Strings;
import com.google.zxing.BarcodeFormat;
import com.itextpdf.text.Document;
import com.itextpdf.text.pdf.PdfCopy;
import com.itextpdf.text.pdf.PdfReader;
import com.xdo.common.exception.ErrorException;
import com.xdo.file.XdoFileHandler;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDDocumentInformation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.lang.invoke.MethodHandles;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;


/**
 * <AUTHOR> 陈成
 * @version :   1.0
 * @date： 2018-12-03
 * 描述：
 */
@Service
public class ExportService {

    /***
     * 初始化日志
     */
    private static final Logger log = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /**
     * 文件服务器客户端
     */
    @Resource(name = "eternalXdoFileHandler")
    public XdoFileHandler fileHandler;
    @Autowired
    private FastdFsService fastdFsService;

    @Value("${dc.export.template:}")
    private String templatePath;

    @Value("${dc.export.temp:}")
    private String tempPath;

    /**
     * 获取license
     *
     * @return
     */
    public static boolean getLicense() {
        boolean result = false;
        try {
            InputStream is = ExportService.class.getClassLoader().getResourceAsStream("/files/license.xml");
            License aposeLic = new License();
            aposeLic.setLicense(is);
            result = true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 功能描述: 获取模板导出文件流
     *
     * @auther: zhuhui
     * @version :  1.0
     * @date: 2019/8/2
     * @param: data 填充数据源
     * @param: tempFileName 模板文件
     * @param: isExportPdf 是否是pdf导出
     * @return:
     */
    public static byte[] getTempExportFileStream(ExportDataSource data, String tempFileName, Boolean isExportPdf) throws Exception {
        // 验证License
        if (!getLicense()) {
            return null;
        }
        Workbook wb = null;
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            wb = new Workbook(tempFileName);
            WorkbookDesigner designer = new WorkbookDesigner();
            designer.setWorkbook(wb);
            designer.setCalculateFormula(true);
            if (data.getVriableMap() != null && !data.getVriableMap().isEmpty()) {
                for (Map.Entry<String, Object> entry : data.getVriableMap().entrySet()) {
                    designer.setDataSource(entry.getKey(), entry.getValue());
                }
            }
            if (data.getTableMap() != null && !data.getTableMap().isEmpty()) {
                for (Map.Entry<String, Object> entry : data.getTableMap().entrySet()) {
                    designer.setDataSource(entry.getKey(), entry.getValue());
                }
            }
            if (data.getBodyHashMap() != null && !data.getBodyHashMap().isEmpty()) {
                for (Map.Entry<String, Object> entry : data.getBodyHashMap().entrySet()) {
                    designer.setDataSource(entry.getKey(), new HashMapDataTable((List<Map<String, Object>>) entry.getValue()));
                }
            }
            designer.process();
            designer.getWorkbook().calculateFormula(true, new CustomFunction());
            int saveFormat = getSaveFormat(tempFileName, isExportPdf);
            wb.save(os, saveFormat);
            os.close();
        } catch (Exception ex) {
            throw ex;
        } finally {
            if (wb != null) {
                wb.dispose();
            }
        }
        return os.toByteArray();
    }

    /**
     * 功能描述: 获取模板导出文件流
     *
     * @auther: zhuhui
     * @version :  1.0
     * @date: 2019/8/2
     * @param: data 填充数据源
     * @param: tempFileName 模板文件
     * @param: isExportPdf 是否是pdf导出
     * @return:
     */
    public static byte[] getTempExportFileStreamPages(ExportDataSource data, String tempFileName, Boolean isExportPdf, Integer pCount, String billType) throws Exception {
        // 验证License
        if (!getLicense()) {
            return null;
        }

        Workbook wb = null;
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            wb = new Workbook(tempFileName);
            WorkbookDesigner designer = new WorkbookDesigner();
            designer.setWorkbook(wb);
            designer.setCalculateFormula(true);
            if (data.getVriableMap() != null && !data.getVriableMap().isEmpty()) {
                for (Map.Entry<String, Object> entry : data.getVriableMap().entrySet()) {
                    designer.setDataSource(entry.getKey(), entry.getValue());
                }
            }
            if (data.getTableMap() != null && !data.getTableMap().isEmpty()) {
                for (Map.Entry<String, Object> entry : data.getTableMap().entrySet()) {
                    designer.setDataSource(entry.getKey(), entry.getValue());
                }
            }
            if (data.getBodyHashMap() != null && !data.getBodyHashMap().isEmpty()) {
                for (Map.Entry<String, Object> entry : data.getBodyHashMap().entrySet()) {
                    if (pCount == 0) {
                        designer.setDataSource(entry.getKey(), new HashMapDataTable((List<Map<String, Object>>) entry.getValue()));
                    } else {

                        List<Map<String, Object>> list = (List<Map<String, Object>>) entry.getValue();
//                        designer.setDataSource(entry.getKey(), new HashMapDataTable(list));
                        if (list.size() > 0) {

                            Double pages = 1 + Math.ceil((list.size() - pCount) / pCount.doubleValue());

                            int ip = pages.intValue();

//                            String name = billType.equals("1") ? "发票" : (billType.equals("2") ? "箱单" : (billType.equals("3") ? "合同" : ""));
                            String name = CommonEnum.BILL_TYPE_STATUS.getValue(billType);
                            wb.getWorksheets().get(0).setName(name + "-" + 1);
                            int size = 2;
                            for (int i = 1; i < ip; i++) {
                                // 创建新的sheet
                                wb.getWorksheets().addCopy(0);
                                wb.getWorksheets().get(i).replace("&=body", "&=body" + i);
                                wb.getWorksheets().get(i).setName(name + "-" + size++);
                            }

                            designer.setCalculateFormula(true);
                            int j = 0;
                            String rno = "";
                            List<Map<String, Object>> lt = null;
                            for (int i = 0; i < list.size(); i++) {
                                if ((i - pCount) % pCount == 0) {
                                    lt = new ArrayList<>();
                                }
                                lt.add(list.get(i));

                                if (i % pCount == (pCount - 1) || i == (list.size() - 1)) {
                                    rno = j == 0 ? "" : String.valueOf(j);
                                    if (lt.size() == pCount && rno == "") {
                                        designer.setDataSource("body", new HashMapDataTable(lt));
                                    } else {
                                        designer.setDataSource("body" + rno, new HashMapDataTable(lt));
                                    }
                                    j++;
                                }
                            }
                        }
                    }
                }
            }
            designer.process();
            designer.getWorkbook().calculateFormula(true, new CustomFunction());

            int saveFormat = getSaveFormat(tempFileName, isExportPdf);
            wb.save(os, saveFormat);
            os.close();
        } catch (Exception ex) {
            throw ex;
        } finally {
            if (wb != null) {
                wb.dispose();
            }
        }
        return os.toByteArray();
    }

    /**
     * 根据文件名获取保存选项
     *
     * @param tempFileName
     * @return
     */
    private static int getSaveFormat(String tempFileName, Boolean isExportPdf) {
        if (isExportPdf) {
            return SaveFormat.PDF;
        }
        String extName = tempFileName.substring(tempFileName.lastIndexOf('.') + 1);
        switch (extName) {
            case "xls":
                return SaveFormat.EXCEL_97_TO_2003;
            case "xlsx":
                return SaveFormat.XLSX;
            case "xlsm":
                return SaveFormat.XLSM;
        }
        return SaveFormat.EXCEL_97_TO_2003;
    }

    /**
     * 功能描述: excel转pdf
     *
     * @auther: zhuhui
     * @version :  1.0
     * @date: 2019/7/8
     * @param: excelBytes excel二进制文件
     * @return:
     */
    public static byte[] excelToPdf(byte[] excelBytes) {
        // 验证License
        if (!getLicense()) {
            return null;
        }
        try {
            Workbook wb = new Workbook(new ByteArrayInputStream(excelBytes));//原始excel路径
            ByteArrayOutputStream fileOs = new ByteArrayOutputStream();
            wb.save(fileOs, new PdfSaveOptions());
            fileOs.close();
            return fileOs.toByteArray();
        } catch (Exception ex) {
            log.error("excel转pdf异常：", ex);
            ex.printStackTrace();
            return null;
        }
    }

    public static byte[] pdfSetTitle(byte[] fileBytes, String title) {
        // 验证License
        if (!getLicense()) {
            return null;
        }
        try {
            // 使用PDFBox设置标题
            PDDocument document = PDDocument.load(fileBytes);
            PDDocumentInformation info = document.getDocumentInformation();
            info.setTitle(title);  // 设置标题

            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            document.save(baos);
            document.close();
            return fileBytes = baos.toByteArray();
        } catch (Exception ex) {
            log.error("pdf设置标题异常：", ex);
            ex.printStackTrace();
            return null;
        }
    }



    /**
     * 通用-表体导出
     *
     * @param list     表体数据
     * @param fileName 导出名称
     * @param tname    模板名称
     */
    public <H, T> String export(List<T> list, String fileName, String tname) {
        // 验证License
        if (!getLicense()) {
            return "";
        }
        try {
            //临时文件路径
            String tempfile = tempPath + fileName;
            //模板路径
            String fp = templatePath + tname;

            if (list.size() > 0) {
                Workbook wb = new Workbook(fp);
                WorkbookDesigner designer = new WorkbookDesigner();
                designer.setWorkbook(wb);
                designer.setCalculateFormula(true);
                designer.setDataSource("List", list);
                designer.process();
                wb.save(tempfile);
                wb.dispose();
            }
            return tempfile;

        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }


    /**
     * 通用-表体导出
     *
     * @param fileName 导出名称
     * @param tname    模板名称
     */
    public String exportImport(String fileName, String tname) {
        // 验证License
        if (!getLicense()) {
            return "";
        }
        try {
            //临时文件路径
            String tempfile = tempPath + fileName;
            //模板路径
            String fp = templatePath + tname;

            Workbook wb = new Workbook(fp);
            WorkbookDesigner designer = new WorkbookDesigner();
            designer.setWorkbook(wb);
            designer.setCalculateFormula(true);
            designer.process();
            wb.save(tempfile);
            wb.dispose();

            return tempfile;

        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    /**
     * 通用-表体导出
     *
     * @param list     表体数据
     * @param fileName 导出名称
     * @param tname    模板名称
     */
    public <H, T> String exportPic(List<T> list, String fileName, String tname, int rows, int cols, int picWidth, int picHeight, GwstdSealParam gwstdSealParam) {
        // 验证License
        if (!getLicense()) {
            return "";
        }
        try {
            //临时文件路径
            String tempfile = tempPath + fileName;
            //模板路径
            String fp = templatePath + tname;

            if (list.size() > 0) {
                Workbook wb = new Workbook(fp);
                WorkbookDesigner designer = new WorkbookDesigner();

                designer.setWorkbook(wb);
                designer.setCalculateFormula(true);
                designer.setDataSource("Head", new ArrayList<GwstdSealParam>(){{add(gwstdSealParam);}});
                designer.setDataSource("List", list);
                designer.process();
                Workbook wk = designer.getWorkbook();
                Worksheet sheet = wk.getWorksheets().get(0);
                Cells cel = sheet.getCells();
                for (int i = 0; i < list.size(); i++) {
                    if (!Strings.isNullOrEmpty(cel.get(rows + i, cols).getStringValue())) {
                        InputStream is = new ByteArrayInputStream(fastdFsService.getByteFromFastDFS(cel.get(rows + i, cols).getStringValue()));
                        int pictureIndex = sheet.getPictures().add(rows + i, cols, is);
                        Picture picture = sheet.getPictures().get(pictureIndex);
                        picture.setWidth(picWidth);
                        picture.setHeight(picHeight);
                        cel.get(rows + i, cols).putValue("");
                    }
                }

                if (gwstdSealParam.getIsExistSeal() != null && gwstdSealParam.getIsExistSeal()) {
                    // 添加电子章
                    // 印章
                    InputStream is = new ByteArrayInputStream(fastdFsService.getByteFromFastDFS(gwstdSealParam.getFdfsId()));
                    for (int i = 0; i < wb.getWorksheets().getCount(); i++) {
                        Cells cells = wb.getWorksheets().get(0).getCells();
                        // 获取表页的最大行数
                        int maxRow = cells.getMaxRow();
                        // 获取表页的最大列数
                        int columnCount = cells.getMaxColumn();
                        switch (gwstdSealParam.getLocation()) {
                            // 左上
                            case "u0":
                                //Add Watermark
                                wb.getWorksheets().get(0).getShapes().addPicture(0, 0, is, 100, 100);
                                break;
                            // 右上
                            case "u2":
                                //Add Watermark
                                wb.getWorksheets().get(0).getShapes().addPicture(0, columnCount, is, 100, 100);
                                break;
                            // 左下
                            case "d0":
                                //Add Watermark
                                wb.getWorksheets().get(0).getShapes().addPicture(maxRow, 0, is, 100, 100);
                                break;
                            // 右下
                            case "d2":
                                //Add Watermark
                                wb.getWorksheets().get(0).getShapes().addPicture(maxRow, columnCount, is, 100, 100);
                                break;
                            default:
                                break;
                        }
                    }
                }

                wb.save(tempfile);
                wb.dispose();
            }
            return tempfile;


        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public <H, T> String exportForHuanHong(List<T> list, String fileName, String tname) {
        // 验证License
        if (!getLicense()) {
            return "";
        }
        try {
            //临时文件路径
            String tempfile = tempPath + fileName;
            //模板路径
            String fp = templatePath + tname;

            if (CollectionUtils.isNotEmpty(list)) {
                Workbook wb = new Workbook(fp);
                WorkbookDesigner designer = new WorkbookDesigner();
                designer.setWorkbook(wb);
                designer.setCalculateFormula(true);
                designer.setDataSource("List", list);
                designer.process();
                wb.save(tempfile);
                wb.dispose();
            } else {
                Workbook wb = new Workbook(templatePath + "huanhong" + File.separator + "empty_document.xlsx");
                wb.save(tempfile);
                wb.dispose();
            }
            return tempfile;

        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    /**
     * 通用-表头、表体导出
     *
     * @param head     表头数据
     * @param list     表体数据
     * @param fileName 导出名称
     * @param tname    模板名称
     */
    public <H, T> String export(List<H> head, List<T> list, String fileName, String tname) {
        // 验证License
        if (!getLicense()) {
            return "";
        }
        try {
            //临时文件路径
            String tempfile = tempPath + fileName;
            //模板路径
            String fp = templatePath + tname;

            if (head.size() > 0) {
                Workbook wb = new Workbook(fp);
                WorkbookDesigner designer = new WorkbookDesigner();
                designer.setWorkbook(wb);
                designer.setCalculateFormula(true);
                designer.setDataSource("Head", head);
                designer.setDataSource("List", list);
                designer.process();

                wb.save(tempfile);
                wb.dispose();
            }
            return tempfile;

        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }


    /**
     * 通用-表头、表体导出
     *
     * @param head     表头数据
     * @param list     表体数据
     * @param fileName 导出名称
     * @param tname    模板名称
     * @param headName 表头变量名
     * @param listName 表体变量名
     */
    public <H, T> String export(List<H> head, List<T> list, String fileName, String tname, String headName, String listName) {
        // 验证License
        if (!getLicense()) {
            return "";
        }
        try {
            //临时文件路径
            String tempfile = tempPath + fileName;
            //模板路径
            String fp = templatePath + tname;

            if (head.size() > 0) {
                Workbook wb = new Workbook(fp);
                WorkbookDesigner designer = new WorkbookDesigner();
                designer.setWorkbook(wb);
                designer.setCalculateFormula(true);
                designer.setDataSource(headName, head);
                designer.setDataSource(listName, list);
                designer.process();
                wb.save(tempfile);
                wb.dispose();
            }
            return tempfile;

        } catch (Exception ex) {
            ex.printStackTrace();
            //return null;
            throw new RuntimeException(ex);
        }
    }

    /**
     * 报关单最新单一窗口分页格式导出
     *
     * @param head
     * @param list
     * @param fileName
     * @param tname
     * @return
     * <AUTHOR>
     * @date 2019-03-13
     */
    public <H, T> String exportForBillPage(List<H> head, List<T> list, String fileName, String tname) {
        // 验证License
        if (!getLicense()) {
            return "";
        }
        try {
            //临时文件路径
            String tempfile = tempPath + fileName;
            //模板路径
            String fp = templatePath + tname;
            //报关单第二页开始表体每页显示条数
            Integer pCount = 12;

            if (list.size() > 0 && head.size() > 0) {
                Workbook wb = new Workbook(fp);

                if (list.size() < 3) {
                    //补行
                    int tempCount = 2 - list.size();
                    if (tempCount < 2) {
                        for (int i = 0; i < tempCount; i++) {
                            T bean = (T) new Object();
                            list.add(bean);
                        }
                    }
                } else {
                    //补行
                    int tempCount = pCount - (list.size() - 2) % pCount;
                    if (tempCount < pCount) {
                        for (int i = 0; i < tempCount; i++) {
                            T bean = (T) new Object();
                            list.add(bean);
                        }
                    }
                }

                Double pages = 1 + Math.ceil((list.size() - 2) / pCount.doubleValue());

                int ip = pages.intValue();

                for (int i = 2; i < ip; i++) {
                    wb.getWorksheets().addCopy(1);
                    wb.getWorksheets().get(i).replace("&=ListM", "&=ListM" + i);
                    wb.getWorksheets().get(i).replace("PAGE", (i + 1) + "/" + ip);
                }
                if (ip == 1) {
                    wb.getWorksheets().removeAt(1);
                } else {
                    wb.getWorksheets().get(1).replace("&=ListM", "&=ListM" + 1);
                    wb.getWorksheets().get(1).replace("PAGE", "2/" + ip);
                }
                wb.getWorksheets().get(0).replace("PAGE", "1/" + ip);
                WorkbookDesigner designer = new WorkbookDesigner();

                designer.setWorkbook(wb);
                designer.setDataSource("Head", head);
                designer.setCalculateFormula(true);

                int j = 0;
                String rno = "";
                List<T> lt = null;
                for (int i = 0; i < list.size(); i++) {
                    if (i < 2) {
                        pCount = 2;
                    } else {
                        pCount = 12;
                    }
                    if ((i - 2) % pCount == 0) {
                        lt = new ArrayList<>();
                    }
                    lt.add(list.get(i));

                    if (i % pCount == (pCount - 1) || i == (list.size() - 1)) {
                        rno = j == 0 ? "" : String.valueOf(j);
                        if (lt.size() == 2) {
                            designer.setDataSource("List", lt);
                        } else {
                            designer.setDataSource("ListM" + rno, lt);
                        }
                        j++;
                    }
                }
                designer.process();

                wb.save(tempfile);
                wb.dispose();

            }
            return tempfile;
        } catch (Exception ex) {
            log.error("打印出错", ex);
            throw new RuntimeException(ex);
        }

    }

    /**
     * @param list1
     * @param list2
     * @param list3
     * @param list4
     * @param fileName
     * @param tname
     * @param <H>
     * @param <T>
     * @return
     */
    public <H, T> String exportAeo(List<H> list1, List<T> list2, List<T> list3, List<T> list4, String fileName, String tname) {
        // 验证License
        if (!getLicense()) {
            return "";
        }
        try {
            //临时文件路径
            String tempfile = tempPath + fileName;
            //模板路径
            String fp = templatePath + tname;
            Workbook wb = new Workbook(fp);
            WorkbookDesigner designer = new WorkbookDesigner();
            designer.setWorkbook(wb);
            designer.setCalculateFormula(true);

            for (int i = 1; i <= 4; i++) {
                if (i == 1) {
                    designer.setDataSource("List1", list1);
                } else if (i == 2) {
                    designer.setDataSource("List2", list2);
                } else if (i == 3) {
                    designer.setDataSource("List3", list3);
                } else if (i == 4) {
                    designer.setDataSource("List4", list4);
                }
            }
            designer.process();
            wb.save(tempfile);
            wb.dispose();
            return tempfile;
        } catch (Exception ex) {
            log.error("打印出错", ex);
            throw new RuntimeException(ex);
        }
    }

    /**
     * 功能描述 清单批量打印
     *
     * @param head     1
     * @param list     2
     * @param tempPath 3
     * @return com.aspose.cells.Workbook
     * <AUTHOR>
     * @date 2021-06-04
     * @version 1.0
     */
    public <H, T> Workbook exportBillPage(H head, List<T> list, String tempPath) throws Exception {
        List<H> newHead = new ArrayList<>();
        newHead.add(head);
        Workbook wb = new Workbook(tempPath);
        //报关单第二页开始表体每页显示条数
        int pCount = 12;

        if (list.size() > 0 && newHead.size() > 0) {
            if (list.size() < 3) {
                //补行 第一页
                int tempCount = 2 - list.size();
                if (tempCount < 2) {
                    for (int i = 0; i < tempCount; i++) {
                        T bean = (T) new Object();
                        list.add(bean);
                    }
                }
            } else {
                //补行 多页
                int tempCount = pCount - (list.size() - 2) % pCount;
                if (tempCount < pCount) {
                    for (int i = 0; i < tempCount; i++) {
                        T bean = (T) new Object();
                        list.add(bean);
                    }
                }
            }

            Double pages = 1 + Math.ceil((list.size() - 2) / (double) pCount);

            int ip = pages.intValue();

            for (int i = 2; i < ip; i++) {
                wb.getWorksheets().addCopy(1);
                wb.getWorksheets().get(i).replace("&=ListM", "&=ListM" + i);
                wb.getWorksheets().get(i).replace("PAGE", (i + 1) + "/" + ip);
            }
            if (ip == 1) {
                wb.getWorksheets().removeAt(1);
            } else {
                wb.getWorksheets().get(1).replace("&=ListM", "&=ListM" + 1);
                wb.getWorksheets().get(1).replace("PAGE", "2/" + ip);
            }
            wb.getWorksheets().get(0).replace("PAGE", "1/" + ip);
            WorkbookDesigner designer = new WorkbookDesigner();

            designer.setWorkbook(wb);
            designer.setDataSource("Head", head);
            designer.setCalculateFormula(true);

            int j = 0;
            String rno = "";
            List<T> lt = null;
            for (int i = 0; i < list.size(); i++) {
                if (i < 2) {
                    pCount = 2;
                } else {
                    pCount = 12;
                }
                if ((i - 2) % pCount == 0) {
                    lt = new ArrayList<>();
                }
                lt.add(list.get(i));

                if (i % pCount == (pCount - 1) || i == (list.size() - 1)) {
                    rno = j == 0 ? "" : String.valueOf(j);
                    if (lt.size() == 2) {
                        designer.setDataSource("List", lt);
                    } else {
                        designer.setDataSource("ListM" + rno, lt);
                    }
                    j++;
                }
            }
            designer.process();

//            wb.save(tempfile);
//            wb.dispose();
            return wb;
        }
        return null;
    }

    /**
     * 报关单最新单一窗口分页格式导出
     *
     * @param head
     * @param list
     * @param fileName
     * @param tname
     * @return
     * <AUTHOR>
     * @date 2019-03-13
     */
    public <H, T> String exportForEntryPage(List<H> head, List<T> list, String fileName, String tname, String entryTypeString) {
        // 验证License
        if (!getLicense()) {
            return "";
        }
        try {
            //临时文件路径
            String tempfile = tempPath + fileName;
            //模板路径
            String fp = templatePath + tname;
            //报关单第二页开始表体每页显示条数
            Integer pCount = 14;

            if (list.size() > 0 && head.size() > 0) {
                Workbook wb = new Workbook(fp);

                if (list.size() < 7) {
                    //补行
                    int tempCount = 6 - list.size();
                    if (tempCount < 6) {
                        for (int i = 0; i < tempCount; i++) {
                            T bean = (T) new Object();
                            list.add(bean);
                        }
                    }
                } else {
                    //补行
                    int tempCount = pCount - (list.size() - 6) % pCount;
                    if (tempCount < pCount) {
                        for (int i = 0; i < tempCount; i++) {
                            T bean = (T) new Object();
                            list.add(bean);
                        }
                    }
                }

                Double pages = 1 + Math.ceil((list.size() - 6) / pCount.doubleValue());

                int ip = pages.intValue();

                for (int i = 2; i < ip; i++) {
                    wb.getWorksheets().addCopy(1);
                    wb.getWorksheets().get(i).replace("&=ListM", "&=ListM" + i);
                    wb.getWorksheets().get(i).replace("PAGE", (i + 1) + "/" + ip);

                    if (i < ip - 1) {
                        wb.getWorksheets().get(i).replace("&=Head.totalAll", "");
                    }
                    wb.getWorksheets().get(i).replace("ENTRYTYPE", entryTypeString);
                }
                if (ip == 1) {
                    wb.getWorksheets().removeAt(1);
                } else {
                    wb.getWorksheets().get(1).replace("&=ListM", "&=ListM" + 1);
                    wb.getWorksheets().get(1).replace("PAGE", "2/" + ip);
                    wb.getWorksheets().get(0).replace("&=Head.totalAll", "");
                    if (ip > 2) {
                        wb.getWorksheets().get(1).replace("&=Head.totalAll", "");
                    }
                    wb.getWorksheets().get(1).replace("ENTRYTYPE", entryTypeString);
                }
                wb.getWorksheets().get(0).replace("PAGE", "1/" + ip);
                wb.getWorksheets().get(0).replace("ENTRYTYPE", entryTypeString);
                WorkbookDesigner designer = new WorkbookDesigner();
                designer.setWorkbook(wb);
                designer.setDataSource("Head", head);
                designer.setCalculateFormula(true);

                int j = 0;
                String rno = "";
                List<T> lt = null;
                for (int i = 0; i < list.size(); i++) {
                    if (i < 6) {
                        pCount = 6;
                    } else {
                        pCount = 14;
                    }
                    if ((i - 6) % pCount == 0) {
                        lt = new ArrayList<>();
                    }
                    lt.add(list.get(i));

                    if (i % pCount == (pCount - 1) || i == (list.size() - 1)) {
                        rno = j == 0 ? "" : String.valueOf(j);
                        if (lt.size() == 6) {
                            designer.setDataSource("List", lt);
                        } else {
                            designer.setDataSource("ListM" + rno, lt);
                        }
                        j++;
                    }
                }
                designer.process();

                wb.save(tempfile);
                wb.dispose();

            }
            return tempfile;
        } catch (Exception ex) {
            log.error("打印出错", ex);
            throw new RuntimeException(ex);
        }

    }

    public <H, T> String exportForFree(List<H> head, List<T> list, String fileName, String tname) {
        // 验证License
        if (!getLicense()) {
            return "";
        }
        try {
            //临时文件路径
            String tempfile = tempPath + fileName;
            //模板路径
            String fp = templatePath + tname;
            //报关单第二页开始表体每页显示条数
            Integer pCount = 10;

            if (list.size() > 0 && head.size() > 0) {
                Workbook wb = new Workbook(fp);

                if (list.size() < 10) {
                    //补行
                    int tempCount = 10 - list.size();
                    if (tempCount < 10) {
                        for (int i = 0; i < tempCount; i++) {
                            T bean = (T) new Object();
                            list.add(bean);
                        }
                    }
                } else {
                    //补行
                    int tempCount = pCount - (list.size() - 10) % pCount;
                    if (tempCount < pCount) {
                        for (int i = 0; i < tempCount; i++) {
                            T bean = (T) new Object();
                            list.add(bean);
                        }
                    }
                }

                Double pages = 1 + Math.ceil((list.size() - 10) / pCount.doubleValue());

                int ip = pages.intValue();

                for (int i = 2; i < ip; i++) {
                    wb.getWorksheets().addCopy(1);
                    wb.getWorksheets().get(i).replace("&=ListM", "&=ListM" + i);
                    wb.getWorksheets().get(i).replace("PAGE", (i + 1) + "/" + ip);
                }
                if (ip == 1) {
                    wb.getWorksheets().removeAt(1);
                } else {
                    wb.getWorksheets().get(1).replace("&=ListM", "&=ListM" + 1);
                    wb.getWorksheets().get(1).replace("PAGE", "2/" + ip);
                }
                wb.getWorksheets().get(0).replace("PAGE", "1/" + ip);
                WorkbookDesigner designer = new WorkbookDesigner();

                designer.setWorkbook(wb);
                designer.setDataSource("Head", head);
                designer.setCalculateFormula(true);

                int j = 0;
                String rno = "";
                List<T> lt = null;
                for (int i = 0; i < list.size(); i++) {
                    if ((i - 10) % pCount == 0) {
                        lt = new ArrayList<>();
                    }
                    lt.add(list.get(i));

                    if (i % pCount == (pCount - 1) || i == (list.size() - 1)) {
                        rno = j == 0 ? "" : String.valueOf(j);
                        if (lt.size() == 10 && rno == "") {
                            designer.setDataSource("List", lt);
                        } else {
                            designer.setDataSource("ListM" + rno, lt);
                        }
                        j++;
                    }
                }
                designer.process();

                wb.save(tempfile);
                wb.dispose();

            }
            return tempfile;
        } catch (Exception ex) {
            log.error("打印出错", ex);
            throw new RuntimeException(ex);
        }

    }

    /**
     * @param list
     * @param fileName
     * @param tname
     * @return java.lang.String
     * @description 减免税-导出用途说明书
     * <AUTHOR>
     * @date 2020/5/23 15:14
     */
    public <H, T> String exportForInstructions(List<T> list, String fileName, String tname) {
        // 验证License
        if (!getLicense()) {
            return "";
        }
        try {
            // 临时文件路径
            String tempfile = tempPath + fileName;
            //模板路径
            String fp = templatePath + tname;

            if (list.size() > 0) {
                Workbook wb = new Workbook(fp);
                int ip = list.size();
                for (int i = 1; i < ip; i++) {
                    // 创建新的sheet
                    wb.getWorksheets().addCopy(0);
                    wb.getWorksheets().get(i).replace("&=List", "&=List" + i);
                }
                WorkbookDesigner designer = new WorkbookDesigner();
                designer.setWorkbook(wb);
                designer.setCalculateFormula(true);

                List<T> b = null;
                for (int i = 0; i < ip; i++) {
                    b = new ArrayList<>();
                    b.add(list.get(i));
                    if (i == 0) {
                        designer.setDataSource("List", b);
                    } else {
                        designer.setDataSource("List" + i, b);
                    }
                }
                designer.process();
                wb.save(tempfile);
                wb.dispose();
            }
            return tempfile;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public <H, T> String exportForBox(List<T> list, String fileName, String tname) {
        // 验证License
        if (!getLicense()) {
            return "";
        }
        try {
            // 临时文件路径
            String tempfile = tempPath + fileName;
            tempfile = xdoi18n.XdoI18nUtil.t(tempfile).replaceAll("\\+","");
            //模板路径
            String fp = templatePath + tname;
            //报关单第二页开始表体每页显示条数
            Integer pCount = 10;

            if (list.size() > 0) {
                Workbook wb = new Workbook(fp);
                /*替换模板中Head文字*/
                int count = wb.getWorksheets().getCount();
                for (int i = 0; i < count; i++) {
                    wb.getWorksheets().get(i).replace("唛头", xdoi18n.XdoI18nUtil.t("唛头"));
                    wb.getWorksheets().get(i).replace("订单号", xdoi18n.XdoI18nUtil.t("订单号"));
                    wb.getWorksheets().get(i).replace("客户订单号", xdoi18n.XdoI18nUtil.t("客户订单号"));
                    wb.getWorksheets().get(i).replace("企业料号", xdoi18n.XdoI18nUtil.t("企业料号"));
                    wb.getWorksheets().get(i).replace("客户料号", xdoi18n.XdoI18nUtil.t("客户料号"));
                    wb.getWorksheets().get(i).replace("货物名称", xdoi18n.XdoI18nUtil.t("货物名称"));
                    wb.getWorksheets().get(i).replace("数量", xdoi18n.XdoI18nUtil.t("数量"));
                    wb.getWorksheets().get(i).replace("单位", xdoi18n.XdoI18nUtil.t("单位"));
                    wb.getWorksheets().get(i).replace("箱数", xdoi18n.XdoI18nUtil.t("箱数"));
                    wb.getWorksheets().get(i).replace("箱号", xdoi18n.XdoI18nUtil.t("箱号"));
                    wb.getWorksheets().get(i).replace("托盘数", xdoi18n.XdoI18nUtil.t("托盘数"));
                    wb.getWorksheets().get(i).replace("托盘号", xdoi18n.XdoI18nUtil.t("托盘号"));
                    wb.getWorksheets().get(i).replace("净重", xdoi18n.XdoI18nUtil.t("净重"));
                    wb.getWorksheets().get(i).replace("毛重", xdoi18n.XdoI18nUtil.t("毛重"));
                    wb.getWorksheets().get(i).replace("体积（M3)", xdoi18n.XdoI18nUtil.t("体积（M3)"));
                    wb.getWorksheets().get(i).replace("长宽高", xdoi18n.XdoI18nUtil.t("长宽高"));
                    wb.getWorksheets().get(i).replace("备注", xdoi18n.XdoI18nUtil.t("备注"));
                }
                T t = list.remove(list.size() - 1);
                if (list.size() < 10) {
                    //补行
                    int tempCount = 10 - list.size();
                    if (tempCount < 10) {
                        for (int i = 0; i < tempCount; i++) {
                            T bean = (T) new Object();
                            list.add(bean);
                        }
                    }
                } else {
                    //补行
                    int tempCount = pCount - (list.size() - 10) % pCount;
                    if (tempCount < pCount) {
                        for (int i = 0; i < tempCount; i++) {
                            T bean = (T) new Object();
                            list.add(bean);
                        }
                    }
                }

                Double pages = 1 + Math.ceil((list.size() - 10) / pCount.doubleValue());

                int ip = pages.intValue();

                for (int i = 2; i < ip; i++) {
                    wb.getWorksheets().addCopy(1);
                    wb.getWorksheets().get(i).replace("&=ListM", "&=ListM" + i);
                }
                if (ip == 1) {
                    wb.getWorksheets().removeAt(1);
                } else {
                    wb.getWorksheets().get(1).replace("&=ListM", "&=ListM" + 1);
                }
                WorkbookDesigner designer = new WorkbookDesigner();

                designer.setWorkbook(wb);
                designer.setCalculateFormula(true);

                int j = 0;
                String rno = "";
                List<T> lt = null;
                for (int i = 0; i < list.size(); i++) {
                    if ((i - 10) % pCount == 0) {
                        lt = new ArrayList<>();
                    }
                    lt.add(list.get(i));

                    if (i % pCount == (pCount - 1) || i == (list.size() - 1)) {
                        rno = j == 0 ? "" : String.valueOf(j);
                        if (lt.size() == 10 && rno == "") {
                            if (list.size() == pCount) {
                                lt.add(t);
                            }
                            designer.setDataSource("List", lt);
                        } else {
                            if (i == list.size() - 1) {
                                lt.add(t);
                            }
                            designer.setDataSource("ListM" + rno, lt);
                        }
                        j++;
                    }
                }
                designer.process();

                wb.save(tempfile);
                wb.dispose();

            }
            return tempfile;
        } catch (Exception ex) {
            log.error("打印出错", ex);
            throw new RuntimeException(ex);
        }
    }

    public boolean removeFile(String tempFileName) {
        File file = new File(templatePath + tempFileName);
        if (file.exists()) {
            file.delete();
            return true;
        } else {
            return false;
        }
    }

    /**
     * 将BufferedImage转换为InputStream
     *
     * @param image
     * @return
     */
    public InputStream bufferedImageToInputStream(BufferedImage image, String imgType) {
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            if (StringUtils.isEmpty(imgType)) {
                imgType = "jpg";
            }
            ImageIO.write(image, imgType, os);
            InputStream input = new ByteArrayInputStream(os.toByteArray());
            return input;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public <H, T, M, N> String exportForShipmentList(List<H> head1, List<T> list1, List<M> head2, List<N> list2,
                                                     String fileName, String tname) {
        // 验证License
        if (!getLicense()) {
            return "";
        }
        try {
            //临时文件路径
            String tempfile = tempPath + fileName;
            //模板路径
            String fp = templatePath + tname;

            if (head1.size() > 0 && head2.size() > 0) {
                Workbook wb = new Workbook(fp);
                WorkbookDesigner designer = new WorkbookDesigner();
                designer.setWorkbook(wb);
                designer.setCalculateFormula(true);
                designer.setDataSource("MHead", head1);
                designer.setDataSource("MList", list1);
                designer.setDataSource("KHead", head2);
                designer.setDataSource("KList", list2);
                designer.process();
                wb.save(tempfile);
                wb.dispose();
            }
            return tempfile;

        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    /***
     * 合并pdf文件
     * @param pdfFilenames
     * @param filename
     * @return
     * @throws Exception
     */
    public String combinPdf(List<String> pdfFilenames, String filename) throws Exception {
        String targetFilename = tempPath + filename;
        PdfReader reader = null;
        Document doc = new Document();
        PdfCopy pdfCopy = new PdfCopy(doc, new FileOutputStream(targetFilename));
        int pageCount = 0;
        doc.open();
        if (CollectionUtils.isNotEmpty(pdfFilenames)) {
            for (int i = 0; i < pdfFilenames.size(); ++i) {
                reader = new PdfReader(pdfFilenames.get(i));
                pageCount = reader.getNumberOfPages();
                for (int j = 1; j <= pageCount; ++j) {
                    pdfCopy.addPage(pdfCopy.getImportedPage(reader, j));
                }
            }
        }
        doc.close();
        return targetFilename;
    }

    public String combinExcel(String file1, String file2, String sheetName) throws Exception {
        try {
            String exportFileName = UUID.randomUUID().toString() + ".xlsx";
            Workbook wb1 = new Workbook(tempPath + file1);
            Workbook wb2 = new Workbook(tempPath + file2);
            wb2.getWorksheets().get(0).setName(sheetName);
            wb1.combine(wb2);
            wb1.save(tempPath + exportFileName);
            return exportFileName;
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return file1;
        }
    }

    public String toZip(List<String> srcFiles, Map<String, String> fileNameMap) throws RuntimeException {
        long start = System.currentTimeMillis();
        String fileName = tempPath + UUID.randomUUID().toString() + ".zip";
        ZipOutputStream zos = null;
        try {
            FileOutputStream out = new FileOutputStream(fileName);
            zos = new ZipOutputStream(out);
            for (String srcFile : srcFiles) {
                byte[] buf = new byte[4096];
//                if("物资清单".equals(fileNameMap.get(srcFile))) {
//                    zos.putNextEntry(new ZipEntry(fileNameMap.get(srcFile)+".xlsx"));
//                } else {
                zos.putNextEntry(new ZipEntry(fileNameMap.get(srcFile) + ".pdf"));
//                }
                int len;
                FileInputStream in = new FileInputStream(srcFile);
                while ((len = in.read(buf)) != -1) {
                    zos.write(buf, 0, len);
                }
                in.close();
            }
//            zos.setComment("导出文件");
            zos.flush();
            zos.closeEntry();
            long end = System.currentTimeMillis();
            log.info("导出文件打包完成，耗时：" + (end - start) + " ms");
        } catch (Exception e) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("打包文件时出错:") + e.toString());
        } finally {
            if (zos != null) {
                try {
                    zos.close();
                } catch (IOException e) {
                    log.error("打包文件时出错:", e);
                }
            }
        }
        return fileName;
    }

    public String exportBillMaterials(String exportFileName, GwstdSealParam gwstdSealParam) {
        // 验证License
        if (!getLicense()) {
            return "";
        }
        try {
            //临时文件路径
            String tempfile = tempPath + File.separator + UUID.randomUUID().toString() + ".pdf";
            //模板路径
            String fp = exportFileName;

            Workbook wb = new Workbook(fp);
//                WorkbookDesigner designer = new WorkbookDesigner();
//                designer.setWorkbook(wb);
//                designer.setCalculateFormula(true);
//                designer.process();

            if (gwstdSealParam.getIsExistSeal() != null && gwstdSealParam.getIsExistSeal()) {
                // 添加电子章
                // 印章
                byte[] bytes = fastdFsService.getByteFromFastDFS(gwstdSealParam.getFdfsId());
                for (int i = 0; i < wb.getWorksheets().getCount(); i++) {
                    InputStream is = new ByteArrayInputStream(bytes);
                    Worksheet sheet = wb.getWorksheets().get(i);
                    Cells cells = sheet.getCells();
                    // 获取表页的最大行数
                    int maxRow = cells.getMaxRow();
                    // 获取表页的最大列数
                    int columnCount = cells.getMaxColumn();
                    switch (gwstdSealParam.getLocation()) {
                        // 左上
                        case "u0":
                            //Add Watermark
                            sheet.getShapes().addPicture(0, 0, is, 100, 100);
                            break;
                        // 右上
                        case "u2":
                            //Add Watermark
                            sheet.getShapes().addPicture(0, columnCount - 4, is, 100, 100);
                            break;
                        // 左下
                        case "d0":
                            //Add Watermark
                            sheet.getShapes().addPicture(maxRow - 5, 0, is, 100, 100);
                            break;
                        // 右下
                        case "d2":
                            //Add Watermark
                            sheet.getShapes().addPicture(maxRow - 5, columnCount - 4, is, 100, 100);
                            break;
                        default:
                            break;
                    }
                    is.close();
                }
            }

            wb.save(tempfile);
            wb.dispose();
            return tempfile;

        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }


    public <H, T> String exportNotify(List<H> head, List<T> list, String fileName, String tname) {
        if (!getLicense()) return "";
        try {
            String tempfile = tempPath + fileName;
            String fp = templatePath + tname;

            if (head.size() > 0) {
                Workbook wb = new Workbook(fp);
                WorkbookDesigner designer = new WorkbookDesigner(wb);
                designer.setCalculateFormula(true);
                designer.setDataSource("Head", head);
                designer.setDataSource("List", list);
                designer.process();

                // 合并单元格处理
                Worksheet sheet = wb.getWorksheets().get(0);
                int dataStartRow = 3; // 根据模板调整起始行
                int lastRow = sheet.getCells().getMaxDataRow()-1;

                // 处理第一列（A列）合并及竖排文本
                mergeAndStyleFirstColumn(sheet, dataStartRow, lastRow);

                // 处理第二列（B列）合并相同客户
                mergeClientColumn(sheet, dataStartRow, lastRow);

                wb.save(tempfile);
                wb.dispose();
            }
            return tempfile;
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    // 合并第一列并设置竖排文本
    private void mergeAndStyleFirstColumn(Worksheet sheet, int startRow, int endRow) {
        int colIndex = 0; // A列
        if (endRow < startRow) return;

        Range range = sheet.getCells().createRange(startRow, colIndex, endRow - startRow + 1, 1);
        range.merge();

        Style style = sheet.getCells().get(startRow, colIndex).getStyle();

        style.setVerticalAlignment(TextAlignmentType.CENTER);
        sheet.getCells().get(startRow, colIndex).setStyle(style);
    }

    // 合并客户列（B列）连续相同值
    private void mergeClientColumn(Worksheet sheet, int startRow, int endRow) {
        int colIndex = 1; // B列
        String currentValue = null;
        int mergeStart = startRow;

        for (int r = startRow; r <= endRow; r++) {
            Cell cell = sheet.getCells().get(r, colIndex);
            String value = cell.getStringValue().replace("_x000d_", "").trim(); // 清理数据

            if (r == startRow) {
                currentValue = value;
                continue;
            }

            if (!value.equals(currentValue)) {
                if (r - mergeStart > 1) {
                    Range range = sheet.getCells().createRange(mergeStart, colIndex, r - mergeStart, 1);
                    range.merge();
                }
                mergeStart = r;
                currentValue = value;
            }
        }

        // 处理最后一段
        if (endRow - mergeStart >= 1) {
            Range range = sheet.getCells().createRange(mergeStart, colIndex, endRow - mergeStart + 1, 1);
            range.merge();
        }
    }

}
