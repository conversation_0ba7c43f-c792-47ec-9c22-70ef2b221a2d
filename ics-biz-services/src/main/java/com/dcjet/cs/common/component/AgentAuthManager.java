package com.dcjet.cs.common.component;

import com.dcjet.cs.common.dao.AgentAuthMapper;
import com.dcjet.cs.common.model.AgentAuth;
import com.google.common.base.Strings;
import com.xdo.common.exception.ArgumentException;
import com.xdo.common.exception.ErrorException;
import org.mybatis.spring.MyBatisSystemException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

@Component
public class AgentAuthManager {
    private final Logger logger = LoggerFactory.getLogger(AgentAuthManager.class);

    /***
     *
     */
    private final String AUTH_PREFIX = "Bearer ";

    /***
     *
     */
    private final String X_GW_ID = "x-gw-id";

    /***
     *
     */
    private final String X_GW_TIME = "x-gw-time";

    @Resource
    private AgentAuthMapper agentAuthMapper;


    public Map<String, String> getAuthInfo(HttpHeaders headers) {
        String appId = headers.getFirst(X_GW_ID);
        String time = headers.getFirst(X_GW_TIME);
        String token = headers.getFirst(HttpHeaders.AUTHORIZATION);
        return new HashMap<String, String>() {{
           put(X_GW_ID, appId);
           put(X_GW_TIME, time);
           put(HttpHeaders.AUTHORIZATION, token);
        }};
    }

    public AgentAuth getAndValidation(HttpHeaders headers, String tradeCode) {
        String appId = headers.getFirst(X_GW_ID);
        String time = headers.getFirst(X_GW_TIME);
        String token = headers.getFirst(HttpHeaders.AUTHORIZATION);

        if (Strings.isNullOrEmpty(appId)) {
            throw new ArgumentException(X_GW_ID + xdoi18n.XdoI18nUtil.t("不能为空"));
        }

        if (Strings.isNullOrEmpty(time)) {
            throw new ArgumentException(X_GW_TIME + xdoi18n.XdoI18nUtil.t("不能为空"));
        }

        if (Strings.isNullOrEmpty(token) || token.length() < 7) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("token不能为空"));
        }

        AgentAuth agentAuth;
        try {
            agentAuth = agentAuthMapper.getByAppId(appId);
        } catch (MyBatisSystemException ex) {
            logger.warn("配置参数异常", ex);
            throw new ErrorException(500, xdoi18n.XdoI18nUtil.t("当前企业的配置信息异常，请联系对接人员"));
        }
        if (agentAuth == null) {
            throw new ArgumentException(X_GW_ID + xdoi18n.XdoI18nUtil.t("无效，请联系您的客服"));
        }

        String plaintext = agentAuth.getSecretKey() + time;
        String cipher = AUTH_PREFIX + DigestUtils.md5DigestAsHex(plaintext.getBytes(StandardCharsets.UTF_8));
        if (!token.equalsIgnoreCase(cipher)) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("token无法匹配！"));
        }

        if (Strings.isNullOrEmpty(agentAuth.getAuthTradeCode())) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("没有货主企业授权数据提交"));
        }

        if (Strings.isNullOrEmpty(tradeCode)) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("境内收发货人编号不能为空"));
        }
        if (!agentAuth.getAuthTradeCode().contains(tradeCode)) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("无权操作当前货主企业的数据"));
        }

        return agentAuth;
    }

    public Map<String, String> getSendAuthHeaders(String agentCode, String plaintext) {
        AgentAuth agentAuth;
        try {
            agentAuth = agentAuthMapper.getByAgent(agentCode);
        } catch (MyBatisSystemException ex) {
            logger.warn("未找到当前企业的授权配置信息" + agentCode, ex);
            throw new ErrorException(500, xdoi18n.XdoI18nUtil.t("当前报关公司的授权配置信息异常，请联系对接人员"));
        }

        if (agentAuth == null) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("没有当前报关公司的授权配置信息") );
        }

        plaintext = plaintext + agentAuth.getSecretKey();
        String token = DigestUtils.md5DigestAsHex(plaintext.getBytes(StandardCharsets.UTF_8));

        Map<String, String> headers = new HashMap(1);
        headers.put("Authorization", "Bearer " + token);
        return headers;
    }
}
