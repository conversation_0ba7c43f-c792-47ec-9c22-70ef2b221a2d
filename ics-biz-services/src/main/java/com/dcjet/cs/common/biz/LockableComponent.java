package com.dcjet.cs.common.biz;

import com.dcjet.cs.base.model.BasicModel;
import com.dcjet.cs.base.repository.BasicMapper;
import com.xdo.common.exception.ArgumentException;
import com.xdo.common.token.UserInfoToken;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class LockableComponent<E extends BasicModel & Lockable> {

    /***
     * 业务锁定
     *
     * @param idList
     * @param mapper
     * @param token
     */
    public void lock(List<String> idList, BasicMapper<E> mapper, UserInfoToken token) {
        if (idList == null || idList.size() == 0) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("请选择要锁定的数据"));
        }
        List<E> list = mapper.selectByIdList(idList);
        if (list.size() == 0) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("未找到可以锁定的数据"));
        }

        list.forEach(it -> {
            it.locked();
            it.preUpdate(token);
        });
        mapper.updateListByPrimaryKey(list);
    }

    /***
     *
     * @param idList
     * @param mapper
     * @param token
     */
    public void unlock(List<String> idList, BasicMapper<E> mapper, UserInfoToken token) {
        if (idList == null || idList.size() == 0) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("请选择要解锁的数据"));
        }
        List<E> list = mapper.selectByIdList(idList);
        if (list.size() == 0) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("未找到可以解锁的数据"));
        }

        list.forEach(it -> {
            it.unlocked();
            it.preUpdate(token);
        });
        mapper.updateListByPrimaryKey(list);
    }
}
