package com.dcjet.cs.common.dao;

import com.dcjet.cs.common.model.AgentAuth;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/***
 * 申报单位接口鉴权
 * 
 * generated by Generate 神码
 * <AUTHOR>
 * @date: 2021-03-23
 */
public interface AgentAuthMapper extends Mapper<AgentAuth> {

    /**
     * 查询获取数据
     * @param param
     * @return
     */
    List<AgentAuth> getList(AgentAuth param);


    /***
     * Get record by app_id
     *
     * @param appId
     * @return
     */
    AgentAuth getByAppId(@Param("appId") String appId);


    /***
     *
     * @param agentCode
     * @return
     */
    AgentAuth getByAgent(String agentCode);



} 