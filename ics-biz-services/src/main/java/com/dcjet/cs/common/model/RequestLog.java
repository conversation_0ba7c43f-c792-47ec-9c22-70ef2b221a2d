package com.dcjet.cs.common.model;

import com.dcjet.cs.base.model.BasicModel;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 网络请求日志
 * 
 * generated by Generate 神码
 * <AUTHOR>
 * @date: 2021-03-23
 */
@Setter @Getter
@Table(name = "T_GWSTD_REQUEST_LOG")
public class RequestLog extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;
    /***
     * 报关行对接接口
     */
    public static final String BIZ_AGENT = "agent";

    /***
     * 报关行回执消息
     */
    public static final String BIZ_AGENT_MSG = "agent_msg";


    /***
     * 发送报关行
     */
    public static final String BIZ_AGENT_DEC = "agent_dec";

    /***
     * 发送报关行
     */
    public static final String BIZ_AGENT_APPROVAL = "agent_approval";

    /**
     * 内销
     */
    public static final String BIZ_MRP = "mrp";
    
    /**
     * 协议
     */
    @Column(name = "PROTOCOL")
    private String protocol;
    
    /**
     * 方法
     */
    @Column(name = "METHOD")
    private String method;
    
    /**
     * 地址
     */
    @Column(name = "URL")
    private String url;
    
    /**
     * 耗时
     */
    @Column(name = "TIME")
    private Integer time;
    
    /**
     * 状态
     */
    @Column(name = "STATUS")
    private Integer status;
    
    /**
     * 业务类型
     */
    @Column(name = "BIZ_TYPE")
    private String bizType;
    
    /**
     * 请求体
     */
    @Column(name = "REQUEST_BODY")
    private String requestBody;
    
    /**
     * 响应体
     */
    @Column(name = "RESPONSE_BODY")
    private String responseBody;

    /***
     * 业务ID
     */
    @Column(name = "BIZ_ID")
    private String bizId;
    
    /**
     * 备注
     */
    @Column(name = "NOTE")
    private String note;
    
}