package com.dcjet.cs.common.model;

import lombok.*;

import java.util.List;
import java.util.Map;

@Setter
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ImportData<T> {

    /***
     * 业务参数
     */
    private Map<String, Object> bizParam;

    /***
     *
     */
    private List<T> correctData;

    /***
     *
     */
    private List<Map<String, Object>> errorData;

    /**
     * 头字段
     */
    private Map<Integer, String> headMap;

    /***
     * 导入的字段
     */
    private Map<String, String> headFieldMap;
}
