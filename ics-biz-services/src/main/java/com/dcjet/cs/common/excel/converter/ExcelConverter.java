package com.dcjet.cs.common.excel.converter;

import com.dcjet.cs.common.excel.ExcelColumnContext;
import com.dcjet.cs.common.model.ExcelToJava;

import javax.annotation.Nonnull;

public interface ExcelConverter<T> {

    /***
     *
     *
     * @param value
     * @param context
     * @return
     */
    String convertToExcel(T value, ExcelColumnContext context);

    /***
     *
     *
     * @param value
     * @param context
     * @return
     */
    @Nonnull
    ExcelToJava<T> convertToJava(String value, ExcelColumnContext context);
}
