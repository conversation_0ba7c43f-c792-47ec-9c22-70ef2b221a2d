package com.dcjet.cs.common.dao;

import com.dcjet.cs.common.model.RequestLog;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/***
 * 网络请求日志
 * 
 * generated by Generate 神码
 * <AUTHOR>
 * @date: 2021-03-23
 */
public interface RequestLogMapper extends Mapper<RequestLog> {

    /**
     * 查询获取数据
     * @param param
     * @return
     */
    List<RequestLog> getList(RequestLog param);


    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(@Param("sids") List<String> sids, @Param("tradeCode") String tradeCode);

} 