package com.dcjet.cs.common.dao;
import com.dcjet.cs.common.model.GwMailSendTaskAttach;
import com.dcjet.cs.common.model.GwMailSendTaskHead;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
* generated by Generate 神码
* GwMailSendTaskHead
* <AUTHOR>
* @date: 2021-11-2
*/
public interface GwMailSendTaskHeadMapper extends Mapper<GwMailSendTaskHead> {
    /**
     * 查询获取数据
     * @param gwMailSendTaskHead
     * @return
     */
    List<GwMailSendTaskHead> getList(GwMailSendTaskHead gwMailSendTaskHead);
    /**
     * 查询获取数据
     * @param lockMark
     * @return
     */
    List<GwMailSendTaskAttach> getAttachList( @Param("lockMark") String lockMark);

    int updateLockMark(@Param("lockMark") String lockMark, @Param("state") String state);
}
