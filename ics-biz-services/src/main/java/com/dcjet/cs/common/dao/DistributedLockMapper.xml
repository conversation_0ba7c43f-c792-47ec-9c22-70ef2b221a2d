<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.common.dao.DistributedLockMapper">
    <resultMap id="resultMap" type="com.dcjet.cs.common.model.DistributedLock">
        <id column="SID" property="sid" jdbcType="VARCHAR"/>
        <result column="EXPIRE_DATE" property="expireDate" jdbcType="DATE"/>
        <result column="INSERT_TIME" property="insertTime" jdbcType="DATE"/>
        <result column="INSERT_USER" property="insertUser" jdbcType="VARCHAR"/>
        <result column="INSERT_USER_NAME" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="DATE"/>
        <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="columns">
        t.SID,
		t.EXPIRE_DATE,
		t.INSERT_TIME,
		t.INSERT_USER,
		t.INSERT_USER_NAME,
		t.TRADE_CODE,
		t.UPDATE_TIME,
		t.UPDATE_USER,
		t.UPDATE_USER_NAME 
    </sql>
    <sql id="condition">
        <if test="sid != null and sid != ''">
            and t.SID = #{sid,jdbcType=VARCHAR}
        </if>
            and t.TRADE_CODE = #{tradeCode,jdbcType=VARCHAR}
    </sql>
    <select id="getList" parameterType="map" resultMap="resultMap">
        SELECT 
        <include refid="columns"/>
        FROM T_DISTRIBUTED_LOCK t
        <where>
            <include refid="condition"></include>
        </where>
    </select>
    <delete id="deleteBySids" parameterType="java.lang.Integer">
        delete from T_DISTRIBUTED_LOCK
        <where>
            <foreach collection="list" item="item" open="(" separator="or" close=")">
                sid=#{item}
            </foreach>
        </where>
    </delete>
</mapper>