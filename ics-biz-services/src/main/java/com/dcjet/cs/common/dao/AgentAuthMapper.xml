<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.common.dao.AgentAuthMapper">
    <sql id="columns">
        t.SID,
		t.TRADE_CODE,
		t.AGENT_CODE,
		t.APP_ID,
		t.SECRET_KEY,
		t.AUTH_TRADE_CODE,
		t.INSERT_USER,
		t.INSERT_USER_NAME,
		t.INSERT_TIME,
		t.UPDATE_USER,
		t.UPDATE_USER_NAME,
		t.UPDATE_TIME 
    </sql>
    <sql id="condition">
		<if test="tradeCode != null and tradeCode != ''">
            and t.TRADE_CODE = #{tradeCode,jdbcType=VARCHAR}
        </if>
		<if test="agentCode != null and agentCode != ''">
            and t.AGENT_CODE = #{agentCode,jdbcType=VARCHAR}
        </if>
		<if test="appId != null and appId != ''">
            and t.APP_ID = #{appId,jdbcType=VARCHAR}
        </if>
		<if test="secretKey != null and secretKey != ''">
            and t.SECRET_KEY = #{secretKey,jdbcType=VARCHAR}
        </if>
		<if test="authTradeCode != null and authTradeCode != ''">
            and t.AUTH_TRADE_CODE = #{authTradeCode,jdbcType=VARCHAR}
        </if>
    </sql>
    <select id="getList" parameterType="com.dcjet.cs.common.model.AgentAuth" resultType="com.dcjet.cs.common.model.AgentAuth">
        SELECT 
        <include refid="columns"/>
        FROM T_GWSTD_AGENT_AUTH t
        <where>
            <include refid="condition"></include>
        </where>
    </select>
    <select id="getByAppId" resultType="com.dcjet.cs.common.model.AgentAuth">
        SELECT
        <include refid="columns"/>
        FROM T_GWSTD_AGENT_AUTH t WHERE t.APP_ID = #{appId,jdbcType=VARCHAR}
    </select>
    <select id="getByAgent" resultType="com.dcjet.cs.common.model.AgentAuth">
        SELECT
        <include refid="columns"/>
        FROM T_GWSTD_AGENT_AUTH t WHERE t.AGENT_CODE = #{agentCode,jdbcType=VARCHAR}
    </select>
</mapper>