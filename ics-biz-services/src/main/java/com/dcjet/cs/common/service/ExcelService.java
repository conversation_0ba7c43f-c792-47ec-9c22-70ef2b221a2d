package com.dcjet.cs.common.service;


import com.dcjet.cs.util.ExcelExportUtilSelf;
import com.dcjet.cs.util.variable.CommonVariable;
import com.google.common.base.Strings;
import com.xdo.common.excel.ExcelExportUtil;
import com.xdo.common.util.DateUtils;
import com.xdo.domain.KeyValuePair;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.net.URLDecoder;
import java.util.List;
/**
 * <AUTHOR>
 * @date: 2019-4-17
 */
@Service
public class ExcelService {
    /***
     * 数据类型date
     */
    private static final String DATA_TYPE_DATE = "DATE";
    /***
     * 数据类型 Number
     */
    private static final String DATA_TYPE_NUMBER = "NUMBER";
    /***
     *
     * @param columnType
     * @param value
     * @return
     */
    private Object getColumnType(String columnType, String value) {
        if (Strings.isNullOrEmpty(value)) {
            return value;
        }
        switch (columnType) {
            case DATA_TYPE_DATE:
                return DateUtils.stringToSqlDate(value, "yyyy-MM-dd");
            case DATA_TYPE_NUMBER:
                try {
                    Number number = NumberUtils.createNumber(value);
                    return number;
                } catch (NumberFormatException e) {
                    return null;
                }
            default:
                return value;
        }
    }
    /**
     * 获取带Excel流的HttpHeader
     *
     * @param name
     * @param header
     * @param dtos
     * @return
     * @throws Exception
     */
    public <T> ResponseEntity getExcelHeaders(String name, List<KeyValuePair<String, String>> header, List<T> dtos) throws Exception {
        ByteArrayInputStream in = null;
        name = name.replaceAll("\\+","%20");
        in = new ExcelExportUtil<T>().exportGeneric(URLDecoder.decode(name, CommonVariable.UTF8), header, dtos);
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_TYPE, "application/vnd.ms-excel;charset=ISO8859-1");
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                + new String(name.getBytes(CommonVariable.UTF8), "ISO8859-1") + ".xlsx");
        return ResponseEntity
                .ok()
                .headers(headers)
                .body(new InputStreamResource(in));
    }

    /**
     * 获取带Excel流的HttpHeader
     *
     * @param name
     * @param header
     * @param dtos
     * @return
     * @throws Exception
     */
    public <T> ResponseEntity getExcelHeaders(String sheetName, String name, List<KeyValuePair<String, String>> header, List<T> dtos) throws Exception {
        ByteArrayInputStream in = null;
        name = name.replaceAll("\\+","%20");
        in = new ExcelExportUtil<T>().exportGeneric(URLDecoder.decode(sheetName, CommonVariable.UTF8), header, dtos);
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_TYPE, "application/vnd.ms-excel;charset=ISO8859-1");
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                + new String(name.getBytes(CommonVariable.UTF8), "ISO8859-1") + ".xlsx");
        return ResponseEntity
                .ok()
                .headers(headers)
                .body(new InputStreamResource(in));
    }

    /**
     * 获取带Excel流的HttpHeader
     *
     * @param name
     * @param header
     * @param dtos
     * @return
     * @throws Exception
     */
    public <T> ResponseEntity getExcelHeadersMatOrg(String sheetName, String name, List<KeyValuePair<String, String>> header, List<T> dtos) throws Exception {
        ByteArrayInputStream in = null;
        name = name.replaceAll("\\+","%20");
        in = new ExcelExportUtilSelf<T>().exportGenericMarOrg(URLDecoder.decode(sheetName, CommonVariable.UTF8), header, dtos);
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_TYPE, "application/vnd.ms-excel;charset=ISO8859-1");
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                + new String(name.getBytes(CommonVariable.UTF8), "ISO8859-1") + ".xlsx");
        return ResponseEntity
                .ok()
                .headers(headers)
                .body(new InputStreamResource(in));
    }

    public <T> ResponseEntity getExcelWithDecTaxHeader(String sheetName, String name, List<KeyValuePair<String, String>> header, List<T> dtos) throws Exception {
        ByteArrayInputStream in = null;
        name = name.replaceAll("\\+","%20");
        in = new ExcelExportUtilSelf<T>().exportDecTaxHeader(URLDecoder.decode(sheetName, CommonVariable.UTF8), header, dtos);
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_TYPE, "application/vnd.ms-excel;charset=ISO8859-1");
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                + new String(name.getBytes(CommonVariable.UTF8), "ISO8859-1") + ".xlsx");
        return ResponseEntity
                .ok()
                .headers(headers)
                .body(new InputStreamResource(in));
    }

    /**
     * 获取带Excel流的HttpHeader
     *
     * @param name
     * @param header
     * @param dtos
     * @return
     * @throws Exception
     */
    public <T> ResponseEntity getExcelHeadersMat(String sheetName, String name, List<KeyValuePair<String, String>> header, List<T> dtos) throws Exception {
        ByteArrayInputStream in = null;
        name = name.replaceAll("\\+","%20");
        in = new ExcelExportUtilSelf<T>().exportGenericMar(URLDecoder.decode(sheetName, CommonVariable.UTF8), header, dtos);
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_TYPE, "application/vnd.ms-excel;charset=ISO8859-1");
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                + new String(name.getBytes(CommonVariable.UTF8), "ISO8859-1") + ".xlsx");
        return ResponseEntity
                .ok()
                .headers(headers)
                .body(new InputStreamResource(in));
    }

    /**
     * 获取带Excel流的HttpHeader
     *
     * @param name
     * @param header
     * @param dtos
     * @return
     * @throws Exception
     */
    public <T> ResponseEntity getExcelHeadersNoBond(String sheetName, String name, List<KeyValuePair<String, String>> header, List<T> dtos) throws Exception {
        ByteArrayInputStream in = null;
        name = name.replaceAll("\\+","%20");
        in = new ExcelExportUtilSelf<T>().exportGenericNoBond(URLDecoder.decode(sheetName, CommonVariable.UTF8), header, dtos);
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_TYPE, "application/vnd.ms-excel;charset=ISO8859-1");
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                + new String(name.getBytes(CommonVariable.UTF8), "ISO8859-1") + ".xlsx");
        return ResponseEntity
                .ok()
                .headers(headers)
                .body(new InputStreamResource(in));
    }

    /**
     * 获取带Excel流的HttpHeader
     * 进出口大提单、非保税小提单
     *
     * @param name
     * @param header
     * @param dtos
     * @return
     * @throws Exception
     */
    public <T> ResponseEntity getExcelHeadersHeadList(String sheetName, String name, List<KeyValuePair<String, String>> header, List<T> dtos) throws Exception {
        ByteArrayInputStream in = null;
        name = name.replaceAll("\\+","%20");
        in = new ExcelExportUtilSelf<T>().exportGenericHeadList(URLDecoder.decode(sheetName, CommonVariable.UTF8), header, dtos);
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_TYPE, "application/vnd.ms-excel;charset=ISO8859-1");
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                + new String(name.getBytes(CommonVariable.UTF8), "ISO8859-1") + ".xlsx");
        return ResponseEntity
                .ok()
                .headers(headers)
                .body(new InputStreamResource(in));
    }

    /**
     * 获取带Excel流的HttpHeader
     * 进出口保税
     *
     * @param name
     * @param header
     * @param dtos
     * @return
     * @throws Exception
     */
    public <T> ResponseEntity getExcelHeadersBondList(String sheetName, String name, List<KeyValuePair<String, String>> header, List<T> dtos) throws Exception {
        ByteArrayInputStream in = null;
        name = name.replaceAll("\\+","%20");
        in = new ExcelExportUtilSelf<T>().exportGenericBondList(URLDecoder.decode(sheetName, CommonVariable.UTF8), header, dtos);
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_TYPE, "application/vnd.ms-excel;charset=ISO8859-1");
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                + new String(name.getBytes(CommonVariable.UTF8), "ISO8859-1") + ".xlsx");
        return ResponseEntity
                .ok()
                .headers(headers)
                .body(new InputStreamResource(in));
    }

    public <T> ResponseEntity getExcelDecTrack(String sheetName, String name, List<KeyValuePair<String, String>> header, List<T> dtos) throws Exception{
        ByteArrayInputStream in = null;
        name = name.replaceAll("\\+","%20");
        in = new ExcelExportUtilSelf<T>().exportGenericDecTrack(URLDecoder.decode(sheetName, CommonVariable.UTF8), header, dtos);
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_TYPE, "application/vnd.ms-excel;charset=ISO8859-1");
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                + new String(name.getBytes(CommonVariable.UTF8), "ISO8859-1") + ".xlsx");
        return ResponseEntity
                .ok()
                .headers(headers)
                .body(new InputStreamResource(in));
    }

    public <T>ResponseEntity getExcelHeadersUpdate(String sheetName, String name, List<KeyValuePair<String, String>> header, List<T> dtos)throws Exception {
        ByteArrayInputStream in = null;
        name = name.replaceAll("\\+","%20");
        in = new ExcelExportUtilSelf<T>().exportGenericPallet(URLDecoder.decode(sheetName, CommonVariable.UTF8), header, dtos);
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_TYPE, "application/vnd.ms-excel;charset=ISO8859-1");
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                + new String(name.getBytes(CommonVariable.UTF8), "ISO8859-1") + ".xlsx");
        return ResponseEntity
                .ok()
                .headers(headers)
                .body(new InputStreamResource(in));
    }

    public <T>ResponseEntity getExcelBatchEdit(String sheetName,String gMark, String name, List<KeyValuePair<String, String>> header, List<T> dtos) throws Exception {
        ByteArrayInputStream in = null;
        name = name.replaceAll("\\+","%20");
        in = new ExcelExportUtilSelf<T>().exportGenericBatchEdit(URLDecoder.decode(sheetName, CommonVariable.UTF8),gMark, header, dtos);
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_TYPE, "application/vnd.ms-excel;charset=ISO8859-1");
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                + new String(name.getBytes(CommonVariable.UTF8), "ISO8859-1") + ".xlsx");
        return ResponseEntity
                .ok()
                .headers(headers)
                .body(new InputStreamResource(in));
    }

}
