package com.dcjet.cs.common.excel.converter;

import com.dcjet.cs.common.excel.ExcelColumnContext;
import com.dcjet.cs.common.model.ExcelToJava;
import com.google.common.base.Strings;

import java.util.Objects;
import java.util.Optional;

public class MapExcelConverter implements ExcelConverter<Object> {


    @Override
    public String convertToExcel(Object value, ExcelColumnContext contentColumn) {
        String key = Optional.ofNullable(value).orElse("").toString();
        if (Strings.isNullOrEmpty(key)) {
            return key;
        }

        if (contentColumn.getMap() == null) {
            return key;
        }

        String val = contentColumn.getMap().get(key);
        if (Strings.isNullOrEmpty(val)) {
            return key;
        } else {
            return key + " " + val;
        }
    }

    @Override
    public ExcelToJava<Object> convertToJava(String value, ExcelColumnContext context) {
        ExcelToJava<Object> toJava = new ExcelToJava();

        if (Strings.isNullOrEmpty(value)) {
            return toJava;
        }
        String[] combinationVal = value.split("\\s");
        if (combinationVal.length == 0) {
            return toJava;
        }
        String key = combinationVal[0].trim();
        String name =  context.getMap().get(key);
        if (Strings.isNullOrEmpty(name)) {
            return toJava;
        }

        if (Objects.equals(Integer.class, context.getField().getType())) {
            return toJava.setValue(Integer.parseInt(key));
        }

        toJava.setValue(key);
        return toJava;
    }
}
