package com.dcjet.cs.common.excel.converter;

import com.dcjet.cs.common.excel.ExcelColumnContext;
import com.dcjet.cs.common.model.ExcelToJava;

public class DoubleExcelConverter implements ExcelConverter<Double> {

    @Override
    public String convertToExcel(Double value, ExcelColumnContext context) {
        return value != null ? value.toString() : "";
    }

    @Override
    public ExcelToJava<Double> convertToJava(String value, ExcelColumnContext context) {
        ExcelToJava<Double> toJava = new ExcelToJava<>();
        if (value == null) {
            return toJava;
        }

        try {
            toJava.setValue(Double.parseDouble(value));
        } catch (NumberFormatException ex) {
            toJava.setErrMsg(xdoi18n.XdoI18nUtil.t("数据格式错误") +context.getExcelColumn().name()+" "+ value);
        }

        return toJava;
    }
}
