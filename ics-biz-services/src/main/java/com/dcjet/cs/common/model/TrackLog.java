package com.dcjet.cs.common.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;
/**
 * 跟踪数据对象
 * <AUTHOR>
 * @date: 2019-4-17
 */
@Getter
@Setter
@Table(name = "T_TRACK_LOG")
public class TrackLog implements Serializable {
    private static final long serialVersionUID = 1L;
    /***
     * 主键 UUID
     */
    private String id;
    /***
     * 执行的controller name
     */
    private String controller;
    /***
     * 执行的方法
     */
    private String handle;
    /***
     * 标记的模块名称
     */
    @Column(name = "MODULE_NAME")
    private String moduleName;
    /***
     * 标记的功能点
     */
    @Column(name = "HANDLE_NAME")
    private String handleName;
    /***
     * 调用的用户
     */
    @Column(name = "INSERT_USER")
    private String insertUser;
    /***
     * 调用时间
     */
    @Column(name = "INSERT_TIME")
    private Date insertTime;
}