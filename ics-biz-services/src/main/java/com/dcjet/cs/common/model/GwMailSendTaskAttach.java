package com.dcjet.cs.common.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xdo.common.token.UserInfoToken;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;
import java.util.UUID;

/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2021-11-2
 */
@Setter
@Getter
@Table(name = "t_gw_mail_send_task_attach")
public class GwMailSendTaskAttach implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 唯一键
     */
	@Id
	@Column(name = "sid")
	private  String sid;
	/**
     * 邮件发送任务表头的sid
     */
	@Column(name = "head_id")
	private  String headId;
	/**
     * 附件所在路径（文件服务器地址或者本地目录）
     */
	@Column(name = "url")
	private  String url;
	/**
     * 原始文件名称
     */
	@Column(name = "file_name")
	private  String fileName;
	/**
     * 企业代码
     */
	@Column(name = "trade_code")
	private  String tradeCode;
	/**
     * 创建人
     */
	@Column(name = "insert_user")
	private  String insertUser;
	/**
     * 创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "insert_time")
	private  Date insertTime;
	/**
     * 更新人
     */
	@Column(name = "update_user")
	private  String updateUser;
	/**
     * 更新时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "update_time")
	private  Date updateTime;

	public GwMailSendTaskAttach initData(String headId, UserInfoToken token) {
		this.sid = UUID.randomUUID().toString();
		this.headId = headId;
		this.tradeCode = token.getCompany();
		this.insertTime = new Date();
		this.insertUser = token.getUserNo();
		return this;
	}
}
