<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.common.dao.PrintCenterMapper">
    <!-- 列表查询 and 条件 begin-->
    <select id="getPrindData" statementType="CALLABLE"  parameterType="java.util.Map">
       { CALL P_GET_PRINT_DATA_I (
         #{dataType,mode=IN,jdbcType=VARCHAR},
         #{sid,mode=IN,jdbcType=VARCHAR},
         #{head,mode=OUT,jdbcType=CURSOR,javaType=ResultSet,resultMap=java.lang.Object},
         #{invoice,mode=OUT,jdbcType=CURSOR,javaType=ResultSet,resultMap=java.lang.Object}
        )
        }
    </select>
</mapper>
