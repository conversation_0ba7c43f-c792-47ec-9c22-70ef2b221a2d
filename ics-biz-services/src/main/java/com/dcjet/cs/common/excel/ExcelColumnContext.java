package com.dcjet.cs.common.excel;

import com.dcjet.cs.common.excel.converter.ExcelConverter;
import com.dcjet.cs.dto.base.annotation.ExcelColumn;
import com.dcjet.cs.util.Constants;
import com.google.common.base.Strings;
import com.xdo.common.exception.ArgumentException;
import lombok.Getter;
import lombok.Setter;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.Format;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Getter
public class ExcelColumnContext {

    private ExcelColumn excelColumn;

    /***
     *
     */
    private Format format;


    /***
     *
     */
    private Map<String, String> map;

    /***
     *
     */
    private Field field;

    /***
     *
     */
    @Setter
    private ExcelConverter converter;



    public ExcelColumnContext() {

    }

    public ExcelColumnContext(ExcelColumn column, Field field) {
        this.excelColumn = column;
        this.field = field;
        if (column != null) {
            setFormat(column.format(), column.roundingMode());
            parseMap(column.map());
        }
    }

    /***
     *
     * @param format
     * @param roundingMode
     */
    private void setFormat(String format, RoundingMode roundingMode) {
        if (Strings.isNullOrEmpty(format)) {
            return;
        }

        if (field == null) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("设置转换的数据字段为空"));
        }

        if (BigDecimal.class.equals(field.getType())) {
            DecimalFormat df = new DecimalFormat(format);
            if (roundingMode != null) {
                df.setRoundingMode(roundingMode);
            }
            this.format = df;
        } else if (Date.class.equals(field.getType())) {
            this.format = new SimpleDateFormat(format);
        }
    }

    public Format getFormat() {
        if (Date.class.equals(field.getType())) {
            if (this.format == null) {
                this.format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            }
        }
        return this.format;
    }

    /***
     *
     * @param content
     */
    private Map<String, String> parseMap(String content) {
        if (Strings.isNullOrEmpty(content)) {
            return null;
        }

        String[] items = content.split(",");
        if (map == null) {
            map = new HashMap<>(Constants.LIST_INITIAL_CAPACITY);
        } else {
            map.clear();
        }

        for (String item : items) {
           String[] val = item.split(":");
           if (val.length == 2) {
              map.put(val[0], val[1]);
           }
        }
        return map;
    }

    public Object getFormats() {
        if (Date.class.equals(field.getType())) {
            if (this.format == null) {
                this.format = new SimpleDateFormat("yyyy-MM-dd");
            }
        }
        return this.format;
    }

    public Object getFormats1() {
        if (Date.class.equals(field.getType())) {
            if (this.format == null) {
                this.format = new SimpleDateFormat("yyyy-MM-dd HH");
            }
        }
        return this.format;
    }
}
