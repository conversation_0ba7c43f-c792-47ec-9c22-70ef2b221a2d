package com.dcjet.cs.common.model;

import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 */
@Setter
@Getter
public class RequestLogEvent extends ApplicationEvent {


    private final RequestLog requestLog;

    /**
     * Create a new {@code ApplicationEvent}.
     *
     * @param source the object on which the event initially occurred or with
     *               which the event is associated (never {@code null})
     * @param requestLogDTO
     */
    public RequestLogEvent(Object source, RequestLog requestLogDTO) {
        super(source);
        this.requestLog = requestLogDTO;
    }
}
