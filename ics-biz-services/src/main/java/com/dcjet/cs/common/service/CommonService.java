package com.dcjet.cs.common.service;

import com.dcjet.cs.common.dao.GwstdHttpConfigMapper;
import com.dcjet.cs.common.dao.RequestLogMapper;
import com.dcjet.cs.common.model.GwstdHttpConfig;
import com.dcjet.cs.common.model.RequestLog;
import com.dcjet.cs.util.ConstantsStatus;
import com.dcjet.cs.util.variable.CommonVariable;
import com.github.benmanes.caffeine.cache.Cache;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.findsword.PmsSession;
import com.xdo.findsword.PmsSessionI;
import com.xdo.pcode.service.PCodeHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.invoke.MethodHandles;
import java.util.*;

/**
 *
 * @author: 朱正东
 * @date: 2020-01-16
 */
@Service
public class CommonService {
    private final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** 如果是单个企业则配置成true;如果是集团企业则配置成false */
    @Value("${pms.single:true}")
    private Boolean pmsSingle;

    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private GwstdHttpConfigMapper gwstdHttpConfigMapper;
    @Resource
    private PmsSession pmsSession;
    @Resource
    PmsSessionI multiUserPmsSession;
    @Resource
    private RequestLogMapper requestLogMapper;


    /**
     * pcode打印转换简单封装
     * @param inCode
     * @param pCodeType
     * @return
     */
    public String convertPCode(String inCode,String pCodeType)
    {
        return convertPCode(inCode,pCodeType,ConstantsStatus.STATUS_1);
    }

    /**
     * @description pcode打印转换简单封装
     * @param inCode
     * @param pCodeType
     * @param convertType "1":"|","2":" ","3":"()"
     * <AUTHOR>
     * @date  2020/10/23 9:17
     * @return
     */
    public String convertPCode(String inCode, String pCodeType, String convertType) {
        if (StringUtils.isNotBlank(inCode)) {
            String value = pCodeHolder.getValue(pCodeType, inCode);
            if (StringUtils.isNotBlank(value)) {
                if (ConstantsStatus.STATUS_1.equals(convertType)) {
                    return inCode + " " + value;
                }
                if (ConstantsStatus.STATUS_2.equals(convertType)) {
                    return inCode + "|" + value;
                }
                if (ConstantsStatus.STATUS_3.equals(convertType)) {
                    return inCode + "(" + value + ")";
                }
                if (ConstantsStatus.STATUS_4.equals(convertType)) {
                    return "(" + inCode + ")" + value;
                }
            }
        }

        return inCode;
    }

    /**
     * 拼接code/name
     * @param first
     * @param second
     * @return
     */
    public String concatStr(String first,String second) {
        return first + "(" + second + ")";
    }

    /**
     * 提单表体根据数据库返回错误进行格式化
     * @param strErrMsg
     * @return
     */
    public String sqlErrorMsg(String strErrMsg)
    {
        if(strErrMsg.toUpperCase().contains("CODE_T_S"))
        {
            return xdoi18n.XdoI18nUtil.t("表体商品编码不能为空！");
        }
        if(strErrMsg.toUpperCase().contains("TRADE_MODE"))
        {
            return xdoi18n.XdoI18nUtil.t("表体监管方式不能为空！");
        }
        if(strErrMsg.toUpperCase().contains("COP_G_NO"))
        {
            return xdoi18n.XdoI18nUtil.t("表体备案料号不能为空！");
        }
        else if(strErrMsg.toUpperCase().contains("G_NAME"))
        {
            return xdoi18n.XdoI18nUtil.t("表体申商品名称不能为空！") ;
        }
        else if(strErrMsg.toUpperCase().contains("G_MODEL"))
        {
            return xdoi18n.XdoI18nUtil.t("表体申报规格型号不能为空！") ;
        }
        if(strErrMsg.toUpperCase().contains("UNIT"))
        {
            return xdoi18n.XdoI18nUtil.t("表体申报计量单位不能为空！");
        }
        if(strErrMsg.toUpperCase().contains("QTY"))
        {
            return xdoi18n.XdoI18nUtil.t("表体申报数量不能为空！");
        }
        if(strErrMsg.toUpperCase().contains("DEC_PRICE"))
        {
            return xdoi18n.XdoI18nUtil.t("表体申报单价不能为空！");
        }
        if(strErrMsg.toUpperCase().contains("DEC_TOTAL"))
        {
            return xdoi18n.XdoI18nUtil.t("表体申报总价不能为空！");
        }
        else if(strErrMsg.toUpperCase().contains("ORIGIN_COUNTRY"))
        {
            return xdoi18n.XdoI18nUtil.t("表体原产国不能为空！") ;
        }
        else if(strErrMsg.toUpperCase().contains("DESTINATION_COUNTRY"))
        {
            return xdoi18n.XdoI18nUtil.t("表体最终目的国不能为空！");
        }
        else if(strErrMsg.toUpperCase().contains("CURR"))
        {
            return xdoi18n.XdoI18nUtil.t("表体币制不能为空！");
        }
        else {
            return strErrMsg;
        }
    }

    /**
     * 功能描述 list去重
     * <AUTHOR>
     * @date 2020/2/21
     * @version 1.0
     * @param arrStr 1
     * @return java.lang.String[]
    */
    public static String[] distinctData(String[] arrStr) {
        List<String> list = new ArrayList<String>();
        for (int i = 0; i < arrStr.length; i++) {
            if (!list.contains(arrStr[i])) {
                list.add(arrStr[i]);
            }
        }
        return list.toArray(new String[1]);
    }

    /**
     * 获取企业配置的http接口信息(新)
     * @return
     */
    public GwstdHttpConfig getHttpConfigInfo(String type) {
        return getHttpConfigInfo(CommonVariable.TRADE_CODE_9999999999, type);
    }

    /**
     * 获取企业配置的http接口信息(新)
     * @return
     */
    public GwstdHttpConfig getHttpConfigInfo(String tradeCode, String type) {
        GwstdHttpConfig configParam = new GwstdHttpConfig();
        configParam.setTradeCode(tradeCode);
        configParam.setType(type);
        List<GwstdHttpConfig> gwstdHttpConfig = gwstdHttpConfigMapper.select(configParam);

        if (CollectionUtils.isEmpty(gwstdHttpConfig)) {
            throw new ErrorException(400, String.format("%s%s",xdoi18n.XdoI18nUtil.t("未配置http接口,type:"), type));
        }

        GwstdHttpConfig s = gwstdHttpConfig.get(0);
        if (StringUtils.isBlank(s.getBaseUrl()) || StringUtils.isBlank(s.getServiceUrl())) {
            logger.info("未配置http接口,type:{}", s.getType());
            throw new ErrorException(400, String.format("%s%s",xdoi18n.XdoI18nUtil.t("未配置http接口,type:"), type));
        } else {
            //处理url
            boolean base = !s.getBaseUrl().endsWith("\\") && !s.getBaseUrl().endsWith("/");
            boolean service = !s.getServiceUrl().startsWith("\\") && !s.getServiceUrl().startsWith("/");
            if (base && service) {
                s.setBaseUrl(s.getBaseUrl() + "/");
            }
            if (!base && !service) {
                s.setBaseUrl(s.getBaseUrl().substring(0, s.getBaseUrl().length() - 1));
            }
        }
        return s;

//        String key = tradeCode + type;
//
//        httpConfigCache = gwstdHttpMapperConfig.getHttpConfigCache();
//        if (httpConfigCache == null) {
//            // 缓存
//            httpConfigCache = Caffeine.newBuilder()
//                    .expireAfterWrite(10, TimeUnit.DAYS)
//                    .refreshAfterWrite(30, TimeUnit.MINUTES)
//                    .build(k -> refreshHttpConfig(k));
//
//            cacheConfig(httpConfigCache);
//        }
//
//        GwstdHttpConfig httpConfig = httpConfigCache.getIfPresent(key);
//        if (null != httpConfig) {
//            return httpConfig;
//        }
//
//        // 全量更新缓存
//        cacheConfig(httpConfigCache);
//
//        return httpConfigCache.getIfPresent(key);
    }

//    @Scheduled(cron = "0 0/15 * * * ?")
//    public void cacheHttpConfig() {
//        cacheConfig(gwstdHttpMapperConfig.getHttpConfigCache());
//    }

//    public void cacheHttpConfig(Cache<String, GwstdHttpConfig> httpConfigCache) {
//        cacheConfig(httpConfigCache);
//    }

    private void cacheConfig(Cache<String, GwstdHttpConfig> httpConfigCache) {
        GwstdHttpConfig configParam = new GwstdHttpConfig();
        List<GwstdHttpConfig> gwstdHttpConfig = gwstdHttpConfigMapper.select(configParam);
        if (CollectionUtils.isNotEmpty(gwstdHttpConfig)) {
            gwstdHttpConfig.stream().forEach(s -> {
                if (StringUtils.isBlank(s.getBaseUrl()) || StringUtils.isBlank(s.getServiceUrl())) {
                    logger.info("未配置http接口,type:{}", s.getType());
                    return;
                } else {
                    //处理url
                    boolean base = !s.getBaseUrl().endsWith("\\") && !s.getBaseUrl().endsWith("/");
                    boolean service = !s.getServiceUrl().startsWith("\\") && !s.getServiceUrl().startsWith("/");
                    if (base && service) {
                        s.setBaseUrl(s.getBaseUrl() + "/");
                    }
                    if (!base && !service) {
                        s.setBaseUrl(s.getBaseUrl().substring(0, s.getBaseUrl().length() - 1));
                    }
                }
                httpConfigCache.put(s.getTradeCode() + s.getType(), s);

            });
        }
    }

    private GwstdHttpConfig refreshHttpConfig(String key) {
        GwstdHttpConfig configParam = new GwstdHttpConfig();
        configParam.setType(key.substring(10));
        configParam.setTradeCode(key.substring(0, 10));
        List<GwstdHttpConfig> gwstdHttpConfig = gwstdHttpConfigMapper.select(configParam);
        if(CollectionUtils.isNotEmpty(gwstdHttpConfig)) {
            GwstdHttpConfig config = gwstdHttpConfig.get(0);
            if (StringUtils.isBlank(config.getBaseUrl()) || StringUtils.isBlank(config.getServiceUrl())) {
                return null;
            } else {
                //处理url
                boolean base = !config.getBaseUrl().endsWith("\\") && !config.getBaseUrl().endsWith("/");
                boolean service = !config.getServiceUrl().startsWith("\\") && !config.getServiceUrl().startsWith("/");
                if (base && service) {
                    config.setBaseUrl(config.getBaseUrl() + "/");
                }
//                httpConfigCache.put(key, config);
                return config;
            }
        }
        return null;
    }

    /**
     * 当前时间为  2022-01-02 00：01：02
     * @param map 数据库表配置的定时配置       例：{ "timing_type": "1", "type": "a", "time": ["00"], "interval": "" }  每天0点执行一次
     * @param current 当前时间                00
     * @param jobType 任务类型                AUTO_DUTYFORM
     * @param dateNowStr 当前时间执行节点      2022-01-02 00
     * @param lastStr 上次执行时间节点         2022-01-01 00
     * @return
     */
    private String check(HashMap<String, Object> map, String current, String jobType, String dateNowStr, String lastStr) {
        if (!((ArrayList) map.get("time")).contains(current)) {
            return String.format("[%s]%s", jobType,xdoi18n.XdoI18nUtil.t("任务未到运行时间"));
        }
        if (StringUtils.isNotBlank(lastStr) && lastStr.equals(dateNowStr)) {
            return String.format("[%s]%s", jobType,xdoi18n.XdoI18nUtil.t("任务当前时间段已执行"));
        }
        return "";
    }

    /**
     * 根据企业类型获取获取token
     */
    public String getToken(String tradeCode) {
        return getToken(tradeCode, "**********获取的token为：");
    }

    /**
     * 根据企业类型获取获取token
     */
    public String getToken(String tradeCode, String msg) {
        // 获取token
        String token = "";
        if (pmsSingle) {
            token = pmsSession.getAccessToken();
        } else {
            token = multiUserPmsSession.getAccessToken(tradeCode);
        }
        logger.info(String.format(msg + "%s, 单个企业:%s **********", token, pmsSingle));
        return token;
    }


    /**
     * 记录日志
     * @param userInfo
     * @param sendLjqEntry
     * @param ljqStatus
     * @param bizType
     * @param errMessage
     */
    public void recodeRequestLog(UserInfoToken userInfo, String sendLjqEntry, Integer ljqStatus, String bizType, String errMessage) {
        RequestLog requestLog = new RequestLog(){{
            setSid(UUID.randomUUID().toString());
            setTradeCode(userInfo.getCompany());
            setMethod(sendLjqEntry);
            setStatus(ljqStatus);
            setBizType(bizType);
            setNote(errMessage.length() < 512 ? errMessage : errMessage.substring(0, 512));
            setInsertTime(new Date());
            setInsertUser(userInfo.getUserNo());
            setInsertUserName(userInfo.getUserName());
        }};
        requestLogMapper.insert(requestLog);
    }
    /**
     * 去除特殊字符 例：\t：制表符 \n：换行符 \r：回车符
     * @param character 传入字符串
     * @return
     */
    public String parseCharacter(String character) {
        if (StringUtils.isNotBlank(character)) {
            character.trim();
            character = character.replace("\\n", "");
            character = character.replace("\\t", "");
            character = character.replace("\\r", "");
        }
        return character;
    }

}
