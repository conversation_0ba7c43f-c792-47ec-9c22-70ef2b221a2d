package com.dcjet.cs.common.dao;

import com.dcjet.cs.common.model.GwstdHttpConfig;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

/**
* generated by Generate 神码
* ErpHttpInterface
* <AUTHOR>
* @date: 2020-9-8
*/
public interface GwstdHttpConfigMapper extends Mapper<GwstdHttpConfig> {


    /***
     * 根据 type 获取配置连接信息
     *
     * @param type
     * @return
     */
    GwstdHttpConfig selectByType(String type);


    /***
     * 根据类型和企业编码查询配置
     *
     * @param tradeCode
     * @param type
     * @return
     */
    GwstdHttpConfig findByType(@Param("tradeCode") String tradeCode, @Param("type") String type);

    String selecttest();
}
