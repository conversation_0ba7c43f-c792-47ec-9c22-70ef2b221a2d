package com.dcjet.cs.common.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Transient;

@Getter
@Setter
public abstract class AbstractBasicKafka {

    /**
     * 企业代码
     */
    @Transient
    private String tradeCode;
    /**
     * 消息类型
     */
    @Transient
    private String msgType;
    /**
     * 处理的service(spring 上下文注册的bean,首字母小写,如:xxxService)
     */
    @Transient
    private String serviceName;

    /**
     * 业务主键
     */
    @Transient
    private String businessId;

    /**
     * 生成业务主键,由各业务消息自定义
     *
     * @return 业务主键
     */
    abstract public String generateBizId();
}
