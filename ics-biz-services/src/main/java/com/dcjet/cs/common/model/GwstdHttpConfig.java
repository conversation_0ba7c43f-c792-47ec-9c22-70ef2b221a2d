package com.dcjet.cs.common.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * generated by Generate 神码
 * <AUTHOR>
 * @date: 2019-9-9
 */
@Setter
@Getter
@Table(name = "T_GWSTD_HTTP_CONFIG")
public class GwstdHttpConfig implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
	 * 唯一键
	 */
	@Id
	@Column(name = "SID")
	private String sid;
	/**
	 * Http接口基地址(如:http://ip:port/)
	 */
	@Column(name = "BASE_URL")
	private String baseUrl;
	/**
	 * Http接口服务地址
	 */
	@Column(name = "SERVICE_URL")
	private String serviceUrl;
	/**
	 * TOKEN
	 */
	@Column(name = "TOKEN")
	private String token;
	/**
	 * 接口类型
	 */
	@Column(name = "TYPE")
	private String type;
	/**
	 * 备用字段1(预留)
	 */
	@Column(name = "EXTEND_FILED1")
	private String extendFiled1;
	/**
	 * 备用字段2(预留)
	 */
	@Column(name = "EXTEND_FILED2")
	private String extendFiled2;
	/**
	 * 备用字段3(预留)
	 */
	@Column(name = "EXTEND_FILED3")
	private String extendFiled3;
	/**
	 * 备注
	 */
	@Column(name = "NOTE")
	private String note;
	/**
	 * 企业海关编码
	 */
	@Column(name = "TRADE_CODE")
	private String tradeCode;
	/**
	 * 创建人
	 */
	@Column(name = "INSERT_USER")
	private String insertUser;
	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "INSERT_TIME")
	private Date insertTime;
	/**
	 * 更新人
	 */
	@Column(name = "UPDATE_USER")
	private String updateUser;
	/**
	 * 更新时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "UPDATE_TIME")
	private Date updateTime;
}
