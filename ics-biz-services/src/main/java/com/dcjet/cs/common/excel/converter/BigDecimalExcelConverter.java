package com.dcjet.cs.common.excel.converter;

import com.dcjet.cs.common.excel.ExcelColumnContext;
import com.dcjet.cs.common.model.ExcelToJava;

import java.math.BigDecimal;

public class BigDecimalExcelConverter implements ExcelConverter<BigDecimal> {

    @Override
    public String convertToExcel(BigDecimal value, ExcelColumnContext context) {
        if (value == null) {
            return "";
        }
        if (context.getFormat() == null) {
            return value.stripTrailingZeros().toPlainString();
        }

        return context.getFormat().format(value);
    }

    @Override
    public ExcelToJava<BigDecimal> convertToJava(String value, ExcelColumnContext context) {
        ExcelToJava<BigDecimal> toJava = new ExcelToJava<>();
        if (value == null){
            return toJava;
        }
        try {
            toJava.setValue(new BigDecimal(value));
        }catch (NumberFormatException e){
            toJava.setErrMsg(xdoi18n.XdoI18nUtil.t("数据格式错误") +context.getExcelColumn().name()+" "+ value);
        }
        return toJava;
    }
}
