<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.common.dao.RequestLogMapper">
    <sql id="columns">
        t.SID,
		t.TRADE_CODE,
		t.PROTOCOL,
		t.METHOD,
		t.URL,
		t.TIME,
		t.STATUS,
		t.BIZ_TYPE,
		t.REQUEST_BODY,
		t.RESPONSE_BODY,
        t.BIZ_ID,
		t.NOTE,
		t.INSERT_USER,
		t.INSERT_USER_NAME,
		t.INSERT_TIME,
		t.UPDATE_USER,
		t.UPDATE_USER_NAME,
		t.UPDATE_TIME 
    </sql>
    <sql id="condition">
		<if test="tradeCode != null and tradeCode != ''">
            and t.TRADE_CODE = #{tradeCode,jdbcType=VARCHAR}
        </if>
		<if test="protocol != null and protocol != ''">
            and t.PROTOCOL = #{protocol,jdbcType=VARCHAR}
        </if>
		<if test="method != null and method != ''">
            and t.METHOD = #{method,jdbcType=VARCHAR}
        </if>
		<if test="url != null and url != ''">
            and t.URL = #{url,jdbcType=VARCHAR}
        </if>
		<if test="status != null and status != ''">
            and t.STATUS = #{status,jdbcType=NUMERIC}
        </if>
        <if test="bizId != null and bizId != ''">
            and t.BIZ_ID = #{bizId,jdbcType=VARCHAR}
        </if>
		<if test="bizType != null and bizType != ''">
            and t.BIZ_TYPE = #{bizType,jdbcType=VARCHAR}
        </if>
    </sql>
    <select id="getList" parameterType="com.dcjet.cs.common.model.RequestLog" resultType="com.dcjet.cs.common.model.RequestLog">
        SELECT
        <include refid="columns"/>
        FROM T_GWSTD_REQUEST_LOG t
        <where>
            <include refid="condition"></include>
        </where>
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_GWSTD_REQUEST_LOG t where t.SID in
        <foreach collection="ids"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
        and t.trade_code = #{tradeCode}
    </delete>
</mapper>