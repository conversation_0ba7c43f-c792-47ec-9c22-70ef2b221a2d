package com.dcjet.cs.common.service;

import com.alibaba.excel.EasyExcel;
import com.dcjet.cs.base.service.ImportService;
import com.dcjet.cs.common.dao.GwImportInfoCommMapper;
import com.dcjet.cs.common.excel.ExcelColumnContext;
import com.dcjet.cs.common.excel.ExcelConverterFactory;
import com.dcjet.cs.common.excel.converter.ExcelConverter;
import com.dcjet.cs.common.model.ExcelToJava;
import com.dcjet.cs.common.model.GwImportInfoComm;
import com.dcjet.cs.common.model.ImportData;
import com.dcjet.cs.common.model.ImportValidation;
import com.dcjet.cs.dto.base.BasicImportParam;
import com.dcjet.cs.dto.base.ImportResult;
import com.dcjet.cs.dto.base.annotation.ExcelColumn;
import com.dcjet.cs.dto.common.ExcelImportParam;
import com.dcjet.cs.util.Constants;
import com.dcjet.cs.util.ReflectUtil;
import com.dcjet.cs.util.variable.CommonVariable;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JavaType;
import com.google.common.base.Strings;
import com.xdo.common.exception.ArgumentException;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.json.JsonObjectMapper;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.KeyValuePair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.io.IOException;
import java.io.InputStream;
import java.lang.invoke.MethodHandles;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class ExcelImportService<T extends BasicImportParam> {
    private static final Logger LOGGER = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    private final int EXPIRATION_TIME = 60;
    private final String ERR_MSG = "errMsg";
    @Resource
    private Map<String, ExcelConverter> converterStrategyMap;

    @Resource
    private ExcelExportService excelExportService;
    @Resource
    private GwImportInfoCommMapper gwImportInfoCommMapper;
    @Resource
    private Validator validator;
    /***
     * @param importableService
     * @param key
     * @param token
     * @return
     */
    public int importCorrectData(ImportService<T> importableService, String key, UserInfoToken token, Class<T> clazz) {
//        String correctData = redisTemplate.opsForValue().get(key);
        GwImportInfoComm gwImportInfoComm=gwImportInfoCommMapper.selectByPrimaryKey(key);
        String correctData=gwImportInfoComm.getInfoData();
        if (Strings.isNullOrEmpty(correctData)) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("暂存的正确数据已经失效，无法导入，请重新上传文件"));
        }
        JavaType javaType = JsonObjectMapper.getInstance().getMapper().getTypeFactory().constructParametricType(ImportData.class, clazz);
        ImportData<T> importData = null;
        try {
            importData = JsonObjectMapper.getInstance().getMapper().readValue(correctData, javaType);
        } catch (IOException e) {
            LOGGER.error(e.getMessage(), e);
            throw new ErrorException(500, xdoi18n.XdoI18nUtil.t("导入正确数据的时候，数据实体转换失败"), e.getMessage());
        }
        if (importData == null || importData.getCorrectData() == null || importData.getCorrectData().size() == 0) {
            return 0;
        }
        int insertCount = importableService.importSave(importData, token);
//        redisTemplate.delete(key);
        gwImportInfoCommMapper.deleteByPrimaryKey(key);
        return insertCount;
    }

    /**
     *
     * @param key
     * @return
     * @throws Exception
     */
    public ResponseEntity exportErrorData(String key) throws Exception {
//        String correctData = redisTemplate.opsForValue().get(key);
        GwImportInfoComm gwImportInfoComm=gwImportInfoCommMapper.selectByPrimaryKey(key);
        String correctData=gwImportInfoComm.getInfoData();
        TypeReference<ImportData<T>> typeReference = new TypeReference<ImportData<T>>() {};
        ImportData<T> importData = JsonObjectMapper.getInstance().fromJson(correctData, typeReference);
        if (importData == null || importData.getHeadFieldMap() == null) {
           throw new ArgumentException(xdoi18n.XdoI18nUtil.t("找不到需要导出的错误数据"));
        }
        List<KeyValuePair<String, String>> header = new ArrayList<>(Constants.LIST_INITIAL_CAPACITY);
        header.add(new KeyValuePair<>(ERR_MSG, xdoi18n.XdoI18nUtil.t("错误原因")));

        for (Map.Entry<Integer, String> excelKey:importData.getHeadMap().entrySet())
        {
            if(importData.getHeadFieldMap().get(excelKey.getValue())!=null)
            {
                header.add(new KeyValuePair<String,String>(xdoi18n.XdoI18nUtil.t(importData.getHeadFieldMap().get(excelKey.getValue()).toString()), xdoi18n.XdoI18nUtil.t(excelKey.getValue())));
            }
        }
        return excelExportService.exportExcelByMap(URLEncoder.encode(xdoi18n.XdoI18nUtil.t("错误数据"), CommonVariable.UTF8), header, importData.getErrorData());
    }

    public ResponseEntity exportTpl(String tplName, Class<T> head) throws Exception {
        List<Field> fieldList = ReflectUtil.getAllDeclaredFields(head);
        List<ExcelColumn> columnList = new ArrayList<>(fieldList.size());

        for (Field field : fieldList) {
            field.setAccessible(true);
            ExcelColumn column = field.getDeclaredAnnotation(ExcelColumn.class);
            if (column != null) {
                columnList.add(column);
            }
        }
        return excelExportService.exportImportTpl(tplName, columnList);
    }

    /***
     *
     * @param inputStream
     * @param importableService
     * @param param
     * @param head
     * @param token
     * @return
     * @throws InstantiationException
     * @throws IllegalAccessException
     */
    public ImportResult importExcel(InputStream inputStream, ImportService<T> importableService,
                                    ExcelImportParam param, Class<T> head, UserInfoToken token) throws InstantiationException, IllegalAccessException {
        return importExcel(inputStream, importableService, param, head, token, true);
    }

    /***
     *
     * @param inputStream
     * @param importableService
     * @param param
     * @param head
     * @param token
     * @param basicValidate
     * @return
     * @throws IllegalAccessException
     * @throws InstantiationException
     */
    public ImportResult importExcel(InputStream inputStream, ImportService<T> importableService, ExcelImportParam param, Class<T> head, UserInfoToken token, boolean basicValidate) throws IllegalAccessException, InstantiationException {
        if (inputStream == null) {
            throw new ArgumentException(xdoi18n.XdoI18nUtil.t("上传数据为空"));
        }
        if (param == null) {
            param = new ExcelImportParam();
        }

        List<Map<Integer, String>> list = EasyExcel.read(inputStream).headRowNumber(param.getHeadRow()).sheet().doReadSync();
        if (list == null || list.size() == 0) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("第一行表头无数据，请下载正确的模版。"));
        }
        if (list.size() <param.getStartRow()) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("导入模板无数据"));
        }
        Map<Integer, String> headMap = list.get(param.getHeadRow());
        Map<String, String> headFieldMap = new LinkedHashMap<>(headMap.size());
        Map<String, ExcelColumnContext> columnContextMap = getExcelColumnContextMap(headMap, headFieldMap, head);

        ImportData<T> resultData = new ImportData<>();
        resultData.setHeadFieldMap(headFieldMap);
        resultData.setHeadMap(headMap);

        List<T> correctList = new ArrayList<>(headMap.entrySet().size());
        List<Map<String, Object>> errorList = new ArrayList<>();

        for (int i = param.getStartRow() - 1; i < list.size(); i++) {
            T t = head.newInstance();
            Map<String, Object> errorMap = new HashMap<>(headMap.entrySet().size());
            Map<Integer, String> row = list.get(i);
            boolean errorRow = false;
            for (Map.Entry<Integer, String> cell : headMap.entrySet()) {
                if (Strings.isNullOrEmpty(cell.getValue())) {
                    continue;
                }
                ExcelColumnContext columnContext = columnContextMap.get(cell.getValue());
                if (columnContext != null) {
                    Field field = columnContext.getField();
                    String value = row.get(cell.getKey());
                    errorMap.put(field.getName(), value);
                    if (errorRow) {
                        continue;
                    }
                    try {
                        if (columnContext.getConverter() != null) {
                            if (!Strings.isNullOrEmpty(value)) {
                                ExcelToJava<Object> val = columnContext.getConverter().convertToJava(value, columnContext);
                                if (Strings.isNullOrEmpty(val.getErrMsg())) {
                                    if (val.getValue() != null) {
                                        field.set(t, val.getValue());
                                    }
                                } else {
                                    errorRow = true;
                                    errorMap.put(ERR_MSG, val.getErrMsg());
                                    errorMap.put(field.getName(), value);
                                }
                            }

                        } else {
                            field.set(t, value);
                        }
                    } catch (IllegalArgumentException | IllegalAccessException e) {
                        errorRow = true;
                        errorMap.put(ERR_MSG, e.getMessage());
                        errorMap.put(field.getName(), value);
                    }
                }
            }
            if (errorRow) {
                errorList.add(errorMap);
            } else {
                if (basicValidate) {
                    Set<ConstraintViolation<T>> set = validator.validate(t);
                    if (set.size() > 0) {
                        errorMap.put(ERR_MSG, set.stream().map(it-> it.getMessage()).collect(Collectors.joining("|")));
                        errorList.add(errorMap);
                        continue;
                    }
                }
                correctList.add(t);
            }
        }
        resultData.setCorrectData(correctList);
        resultData.setErrorData(errorList);


        Map<String, Object> bizParam = null;
        if (!Strings.isNullOrEmpty(param.getBizParam())) {
            bizParam = JsonObjectMapper.getInstance().fromJson(param.getBizParam(), Map.class);
        }
        resultData.setBizParam(bizParam);
        ImportResult result = new ImportResult();

        if (resultData.getCorrectData().size() > 0) {
            // 业务验证数据
            ImportValidation<T> importValidation = importableService.importValidation(resultData, param, token);
            resultData.setCorrectData(importValidation.getCorrectList());
            result.setCorrectSize(importValidation.getCorrectSize());

            if (importValidation.getErrorList() != null && importValidation.getErrorList().size() > 0) {
                if (resultData.getErrorData() == null) {
                    resultData.setErrorData(new ArrayList<>());
                }
                importValidation.getErrorList().forEach(it -> {
                    Map<String, Object> errorMap = new HashMap<>(headMap.entrySet().size());
                    for (Map.Entry<Integer, String> cell : headMap.entrySet()) {
                        if (Strings.isNullOrEmpty(cell.getValue())) {
                            continue;
                        }
                        ExcelColumnContext columnContext = columnContextMap.get(cell.getValue());

                        if (columnContext != null) {
                            Field field = columnContext.getField();

                            try {
                                Object value = field.get(it);
                                errorMap.put(field.getName(), value);
                            } catch (IllegalAccessException e) {
                                LOGGER.error(xdoi18n.XdoI18nUtil.t("获取错误数据错误"), e);
                            }
                        }
                    }
                    errorMap.put(ERR_MSG, it.getErrMsg());
                    resultData.getErrorData().add(errorMap);
                });
            }
        }

        if (param.isDirectImport() && resultData.getCorrectData() != null && resultData.getCorrectData().size() > 0) {
            importableService.importSave(resultData, token);
            result.setCorrectSize(resultData.getCorrectData().size());
            resultData.setCorrectData(null);
        }

        cacheImportData(resultData, result,token);
        return result;
    }

    /***
     * 缓存数据到redis
     *
     * @param resultData
     * @return
     */
    private ImportResult cacheImportData(ImportData<T> resultData, ImportResult result,UserInfoToken userInfo) {
        if (resultData.getCorrectData() != null && resultData.getCorrectData().size() > 0) {
            String key=Constants.REDIS_KEY_PREFIX + UUID.randomUUID().toString();
            result.setCorrectKey(key);
            result.setCorrectSize(resultData.getCorrectData().size());
            // 暂存
            List<Map<String, Object>> errorList = resultData.getErrorData();
            resultData.setErrorData(null);

            GwImportInfoComm gwImportInfoComm=new GwImportInfoComm();
            gwImportInfoComm.setSid(key);
            gwImportInfoComm.setInfoData(JsonObjectMapper.getInstance().toJson(resultData));
            gwImportInfoComm.setInsertUser(userInfo.getUserNo());
            gwImportInfoComm.setInsertUserName(userInfo.getUserName());
            gwImportInfoComm.setTradeCode(userInfo.getCompany());
            gwImportInfoComm.setInsertTime(new Date());
            gwImportInfoCommMapper.insert(gwImportInfoComm);

//            redisTemplate.opsForValue().set(result.getCorrectKey(), JsonObjectMapper.getInstance().toJson(resultData), EXPIRATION_TIME, TimeUnit.MINUTES);

            resultData.setErrorData(errorList);
        }
        if (resultData.getErrorData() != null && resultData.getErrorData().size() > 0) {
            String key=Constants.REDIS_KEY_PREFIX + UUID.randomUUID().toString();
            result.setErrorKey(key);
            result.setErrorSize(resultData.getErrorData().size());
            // redis缓存前清理掉正确的数据
            resultData.setCorrectData(null);
            GwImportInfoComm gwImportInfoComm=new GwImportInfoComm();
            gwImportInfoComm.setSid(key);
            gwImportInfoComm.setInfoData(JsonObjectMapper.getInstance().toJson(resultData));
            gwImportInfoComm.setInsertUser(userInfo.getUserNo());
            gwImportInfoComm.setInsertUserName(userInfo.getUserName());
            gwImportInfoComm.setTradeCode(userInfo.getCompany());
            gwImportInfoComm.setInsertTime(new Date());
            gwImportInfoCommMapper.insert(gwImportInfoComm);
//            redisTemplate.opsForValue().set(result.getErrorKey(), JsonObjectMapper.getInstance().toJson(resultData), EXPIRATION_TIME, TimeUnit.MINUTES);
        }
        return result;
    }


    /***
     * 转换导入配置的上下文信息
     *
     * @param headMap
     * @param headFieldMap
     * @param head
     * @return
     */
    private Map<String, ExcelColumnContext> getExcelColumnContextMap(Map<Integer, String> headMap, Map<String, String> headFieldMap, Class<T> head) {
        Map<String, ExcelColumnContext> columnContextMap = new HashMap<>(8);
        List<Field> fieldList = ReflectUtil.getAllDeclaredFields(head);
        for (Field field : fieldList) {
            field.setAccessible(true);
            ExcelColumn column = field.getDeclaredAnnotation(ExcelColumn.class);
            if (column != null) {
                if (!headMap.containsValue(column.name())) {
                    continue;
                }
                ExcelColumnContext columnContext = new ExcelColumnContext(column, field);
                columnContextMap.put(column.name(), columnContext);
                if (column.IOC()) {
                    columnContext.setConverter(converterStrategyMap.get(column.converter()));
                } else {
                    columnContext.setConverter(ExcelConverterFactory.createConvert(field.getType()));
                }
            }
        }

        // 保证 headfieldMap 的顺序
        ArrayList<ExcelColumnContext> columnContexts = new ArrayList<>(columnContextMap.values());
        columnContexts.sort(Comparator.comparing((it) -> it.getExcelColumn().order()));
        for(ExcelColumnContext columnContext : columnContexts) {
            headFieldMap.put(columnContext.getExcelColumn().name(), columnContext.getField().getName());
        }


        return columnContextMap;
    }

}
