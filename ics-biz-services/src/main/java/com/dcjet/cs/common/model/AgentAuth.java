package com.dcjet.cs.common.model;

import com.dcjet.cs.base.model.BasicModel;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 申报单位接口鉴权
 * 
 * generated by Generate 神码
 * <AUTHOR>
 * @date: 2021-03-23
 */
@Setter @Getter
@Table(name = "T_GWSTD_AGENT_AUTH")
public class AgentAuth extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;

    
    /**
     * 申报单位编码
     */
    @Column(name = "AGENT_CODE")
    private String agentCode;
    
    /**
     * 应用标识
     */
    @Column(name = "APP_ID")
    private String appId;
    
    /**
     * 应用KEY
     */
    @Column(name = "SECRET_KEY")
    private String secretKey;
    
    /**
     * 授权货主企业
     */
    @Column(name = "AUTH_TRADE_CODE")
    private String authTradeCode;
    
}