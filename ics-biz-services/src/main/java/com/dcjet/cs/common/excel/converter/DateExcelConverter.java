package com.dcjet.cs.common.excel.converter;

import com.dcjet.cs.common.excel.ExcelColumnContext;
import com.dcjet.cs.common.model.ExcelToJava;
import com.google.common.base.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class DateExcelConverter implements ExcelConverter<Date> {
    private static final Logger LOGGER = LoggerFactory.getLogger(DateExcelConverter.class);

    @Override
    public String convertToExcel(Date value, ExcelColumnContext context) {
        if (value == null) {
            return "";
        }
        return context.getFormat().format(value);
    }

    @Override
    public ExcelToJava<Date> convertToJava(String value, ExcelColumnContext context) {
        ExcelToJava toJava = new ExcelToJava();

        if (Strings.isNullOrEmpty(value)) {
            return toJava;
        }

        int str = value.length();
        int len = 10;
        try {
            if (str==len) {
                toJava.setValue(((SimpleDateFormat) context.getFormats()).parse(value));
            }else if (str==13) {
                toJava.setValue(((SimpleDateFormat) context.getFormats1()).parse(value));
            }else {
                toJava.setValue(((SimpleDateFormat) context.getFormat()).parse(value));
            }
        } catch (ParseException e) {
            toJava.setErrMsg(xdoi18n.XdoI18nUtil.t("转换日期错误") +context.getExcelColumn().name()+" "+ e.getMessage());
        }

        return toJava;
    }
}
