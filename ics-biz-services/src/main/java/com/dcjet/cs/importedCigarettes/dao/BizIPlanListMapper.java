package com.dcjet.cs.importedCigarettes.dao;
import com.dcjet.cs.importedCigarettes.model.BizIContractList;
import com.dcjet.cs.importedCigarettes.model.BizIPlanList;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
/**
* generated by Generate 神码
* BizIPlanList
* <AUTHOR>
* @date: 2025-3-13
*/
public interface BizIPlanListMapper extends Mapper<BizIPlanList> {
    /**
     * 查询获取数据
     * @param bizIPlanList
     * @return
     */
    List<BizIPlanList> getList(BizIPlanList bizIPlanList);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    int deleteByHeadIds(List<String> sids);

    List<BizIPlanList> getContractListByHeadId(@Param("sid") String sid);

    List<BizIPlanList> checkProductName(BizIPlanList bizIPlanList);
    List<BizIPlanList> checkProductNameWithOutSelf(BizIPlanList bizIPlanList);
}
