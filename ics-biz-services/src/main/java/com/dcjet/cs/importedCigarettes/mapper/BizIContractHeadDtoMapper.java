package com.dcjet.cs.importedCigarettes.mapper;
import com.dcjet.cs.dto.importedCigarettes.*;
import com.dcjet.cs.importedCigarettes.model.BizIContractHead;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-3-7
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizIContractHeadDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizIContractHeadDto toDto(BizIContractHead po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizIContractHead toPo(BizIContractHeadParam param);
    /**
     * 数据库原始数据更新
     * @param bizIContractHeadParam
     * @param bizIContractHead
     */
    void updatePo(BizIContractHeadParam bizIContractHeadParam, @MappingTarget BizIContractHead bizIContractHead);
    default void patchPo(BizIContractHeadParam bizIContractHeadParam, BizIContractHead bizIContractHead) {
        // TODO 自行实现局部更新
    }
}
