package com.dcjet.cs.importedCigarettes.model;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2025-3-7
 */
@Setter
@Getter
@Table(name = "t_biz_i_contract_head")
public class BizIContractHead implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 主键
     */
	 @Id
	@Column(name = "sid")
	private  String sid;
	/**
     * 业务类型
     */
	@Column(name = "business_type")
	private  String businessType;
	/**
     * 计划编号
     */
	@Column(name = "plan_no")
	private  String planNo;
	/**
     * 计划年度（格式: YYYY）
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "plan_year")
	private  Date planYear;
	/**
     * 上下半年（如：上半年/H1）
     */
	@Column(name = "half_year")
	private  String halfYear;
	/**
     * 买方名称
     */
	@Column(name = "buyer")
	private  String buyer;
	/**
     * 卖方名称
     */
	@Column(name = "seller")
	private  String seller;
	/**
     * 合同编号
     */
	@Column(name = "contract_no")
	private  String contractNo;
	/**
     * 合同生效日期
     */
	@Column(name = "contract_effective_date")
	private  String contractEffectiveDate;
	/**
     * 合同有效期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "contract_expiry_date")
	private  Date contractExpiryDate;
	/**
     * 签约日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "sign_date")
	private  Date signDate;
	/**
     * 装货港
     */
	@Column(name = "loading_port")
	private  String loadingPort;
	/**
     * 到货港
     */
	@Column(name = "arrival_port")
	private  String arrivalPort;
	/**
     * 贸易条款
     */
	@Column(name = "trade_terms")
	private  String tradeTerms;
	/**
     * 价格条款对应的港口
     */
	@Column(name = "price_term_port")
	private  String priceTermPort;
	/**
     * 出口国家或地区
     */
	@Column(name = "export_country")
	private  String exportCountry;
	/**
     * 合同总金额
     */
	@Column(name = "total_amount")
	private  BigDecimal totalAmount;
	/**
     * 合同总数量
     */
	@Column(name = "total_quantity")
	private  Integer totalQuantity;
	/**
     * 短溢数%
     */
	@Column(name = "short_over_percent")
	private  BigDecimal shortOverPercent;
	/**
	 * 备注
	 */
	@Column(name = "note")
	private  String note;
	/**
     * 制单人
     */
	@Column(name = "prepared_by")
	private  String preparedBy;
	/**
     * 制单时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "prepare_time")
	private  Date prepareTime;
	/**
     * 制单时间-开始
     */
	@Transient
	private String prepareTimeFrom;
	/**
     * 制单时间-结束
     */
	@Transient
    private String prepareTimeTo;
	/**
     * 数据状态
     */
	@Column(name = "data_status")
	private  String dataStatus;
	/**
     * 确认时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "confirm_time")
	private  Date confirmTime;
	/**
     * 审批状态
     */
	@Column(name = "approval_status")
	private  String approvalStatus;
	/**
     * 版本号
     */
	@Column(name = "version_no")
	private  String versionNo;
	/**
     * 
     */
	@Column(name = "trade_code")
	private  String tradeCode;
	/**
     * 
     */
	@Column(name = "parent_id")
	private  String parentId;
	/**
     * 
     */
	@Column(name = "insert_user")
	private  String insertUser;
	/**
     * 
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "insert_time")
	private  Date insertTime;
	/**
     * 
     */
	@Column(name = "update_user")
	private  String updateUser;
	/**
     * 
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "update_time")
	private  Date updateTime;
	/**
     * 
     */
	@Column(name = "insert_user_name")
	private  String insertUserName;
	/**
     * 
     */
	@Column(name = "update_user_name")
	private  String updateUserName;
	/**
     * 贸易条款文本框
     */
	@Column(name = "extend1")
	private  String extend1;
	/**
     * 
     */
	@Column(name = "extend2")
	private  String extend2;
	/**
     * 
     */
	@Column(name = "extend3")
	private  String extend3;
	/**
     * 
     */
	@Column(name = "extend4")
	private  String extend4;
	/**
     * 
     */
	@Column(name = "extend5")
	private  String extend5;
	/**
     * 
     */
	@Column(name = "extend6")
	private  String extend6;
	/**
     * 
     */
	@Column(name = "extend7")
	private  String extend7;
	/**
     * 
     */
	@Column(name = "extend8")
	private  String extend8;
	/**
     * 
     */
	@Column(name = "extend9")
	private  String extend9;
	/**
     * 
     */
	@Column(name = "extend10")
	private  String extend10;

	@Transient
	private BigDecimal planQuantity;
}
