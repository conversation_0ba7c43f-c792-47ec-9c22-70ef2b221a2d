package com.dcjet.cs.importedCigarettes.mapper;
import com.dcjet.cs.dto.importedCigarettes.*;
import com.dcjet.cs.importedCigarettes.model.BizIContractList;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizIContractListDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizIContractListDto toDto(BizIContractList po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizIContractList toPo(BizIContractListParam param);
    /**
     * 数据库原始数据更新
     * @param bizIContractListParam
     * @param bizIContractList
     */
    void updatePo(BizIContractListParam bizIContractListParam, @MappingTarget BizIContractList bizIContractList);
    default void patchPo(BizIContractListParam bizIContractListParam, BizIContractList bizIContractList) {
        // TODO 自行实现局部更新
    }
}
