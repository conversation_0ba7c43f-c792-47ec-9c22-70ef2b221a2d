package com.dcjet.cs.importedCigarettes.mapper;
import com.dcjet.cs.dto.importedCigarettes.*;
import com.dcjet.cs.importedCigarettes.model.BizIPlanList;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-3-13
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizIPlanListDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizIPlanListDto toDto(BizIPlanList po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizIPlanList toPo(BizIPlanListParam param);
    /**
     * 数据库原始数据更新
     * @param bizIPlanListParam
     * @param bizIPlanList
     */
    void updatePo(BizIPlanListParam bizIPlanListParam, @MappingTarget BizIPlanList bizIPlanList);
    default void patchPo(BizIPlanListParam bizIPlanListParam, BizIPlanList bizIPlanList) {
        // TODO 自行实现局部更新
    }
}
