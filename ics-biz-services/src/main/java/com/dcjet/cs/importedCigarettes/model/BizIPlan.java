package com.dcjet.cs.importedCigarettes.model;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2025-3-13
 */
@Setter
@Getter
@Table(name = "t_biz_i_plan")
public class BizIPlan implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 
     */
	 @Id
	@Column(name = "sid")
	private  String sid;
	/**
     * 父sid
     */
	@Column(name = "parent_id")
	private  String parentId;
	/**
     * 企业编码
     */
	@Column(name = "trade_code")
	private  String tradeCode;
	/**
     * 业务类型，默认国营贸易进口卷烟，置灰不可修改
     */
	@Column(name = "business_type")
	private  String businessType;
	/**
     * 计划编号，唯一性校验
     */
	@Column(name = "plan_id")
	private  String planId;
	/**
     * 计划年度
     */
	@Column(name = "plan_year")
	private  Date planYear;
	/**
     * 上下半年，0表示上半年，1表示下半年
     */
	@Column(name = "half_year")
	private  String halfYear;
	/**
     * 备注
     */
	@Column(name = "remark")
	private  String remark;
	/**
     * 单据状态，0编制，1确认，2作废
     */
	@Column(name = "status")
	private  String status;
	/**
     * 确认时间，点击确认按钮成功提交的时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "confirm_time")
	private  Date confirmTime;
	/**
     * 审批状态，0不涉及审批，1未审批，2审批中，3审批通过，4审批退回
     */
	@Column(name = "appr_status")
	private  String apprStatus;
	/**
     * 版本号，初始为1，版本复制递增
     */
	@Column(name = "version_no")
	private  String versionNo;
	/**
     * 制单人，自动识别最后操作人
     */
	@Column(name = "insert_user")
	private  String insertUser;
	/**
     * 
     */
	@Column(name = "insert_user_name")
	private  String insertUserName;
	/**
     * 制单时间，系统自动生成
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "insert_time")
	private  Date insertTime;
	/**
     * 制单时间，系统自动生成-开始
     */
	@Transient
	private String insertTimeFrom;
	/**
     * 制单时间，系统自动生成-结束
     */
	@Transient
    private String insertTimeTo;
	/**
     * 修改人，最后操作人
     */
	@Column(name = "update_user")
	private  String updateUser;
	/**
     * 修改时间，最后操作时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "update_time")
	private  Date updateTime;
	/**
     * 
     */
	@Column(name = "update_user_name")
	private  String updateUserName;
	/**
     * 
     */
	@Column(name = "extend1")
	private  String extend1;
	/**
     * 
     */
	@Column(name = "extend2")
	private  String extend2;
	/**
     * 
     */
	@Column(name = "extend3")
	private  String extend3;
	/**
     * 
     */
	@Column(name = "extend4")
	private  String extend4;
	/**
     * 
     */
	@Column(name = "extend5")
	private  String extend5;
	/**
     * 
     */
	@Column(name = "extend6")
	private  String extend6;
	/**
     * 
     */
	@Column(name = "extend7")
	private  String extend7;
	/**
     * 
     */
	@Column(name = "extend8")
	private  String extend8;
	/**
     * 
     */
	@Column(name = "extend9")
	private  String extend9;
	/**
     * 
     */
	@Column(name = "extend10")
	private  String extend10;

	@Transient
	private String createrUser;

	@Transient
	private Date createrTime;

	@Transient
	private String hasHeadCtr;
	@Transient
	private String isCopy;
}
