<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.importedCigarettes.dao.BizIPlanMapper">
    <resultMap id="bizIplanResultMap" type="com.dcjet.cs.importedCigarettes.model.BizIPlan">
        <id column="sid" property="sid" jdbcType="VARCHAR" />
        <result column="parent_id" property="parentId" jdbcType="VARCHAR" />
        <result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
        <result column="business_type" property="businessType" jdbcType="VARCHAR" />
        <result column="plan_id" property="planId" jdbcType="VARCHAR" />
        <result column="plan_year" property="planYear" jdbcType="INTEGER" />
        <result column="half_year" property="halfYear" jdbcType="VARCHAR" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="confirm_time" property="confirmTime" jdbcType="TIMESTAMP" />
        <result column="appr_status" property="apprStatus" jdbcType="VARCHAR" />
        <result column="version_no" property="versionNo" jdbcType="VARCHAR" />
        <result column="insert_user" property="insertUser" jdbcType="VARCHAR" />
        <result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR" />
        <result column="insert_time" property="insertTime" jdbcType="TIMESTAMP" />
        <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="update_user_name" property="updateUserName" jdbcType="VARCHAR" />
        <result column="extend1" property="extend1" jdbcType="VARCHAR" />
        <result column="extend2" property="extend2" jdbcType="VARCHAR" />
        <result column="extend3" property="extend3" jdbcType="VARCHAR" />
        <result column="extend4" property="extend4" jdbcType="VARCHAR" />
        <result column="extend5" property="extend5" jdbcType="VARCHAR" />
        <result column="extend6" property="extend6" jdbcType="VARCHAR" />
        <result column="extend7" property="extend7" jdbcType="VARCHAR" />
        <result column="extend8" property="extend8" jdbcType="VARCHAR" />
        <result column="extend9" property="extend9" jdbcType="VARCHAR" />
        <result column="extend10" property="extend10" jdbcType="VARCHAR" />
        <result column="createrUser" property="createrUser" jdbcType="VARCHAR" />
        <result column="createrTime" property="createrTime" jdbcType="VARCHAR" />
    </resultMap>
    <sql id="Base_Column_List" >
      t.SID
     ,t.PARENT_ID
     ,t.TRADE_CODE
     ,t.BUSINESS_TYPE
     ,t.PLAN_ID
     ,t.PLAN_YEAR
     ,t.HALF_YEAR
     ,t.REMARK
     ,t.STATUS
     ,t.CONFIRM_TIME
     ,t.APPR_STATUS
     ,t.VERSION_NO
     ,t.INSERT_USER
     ,t.INSERT_USER_NAME
     ,t.INSERT_TIME
     ,t.UPDATE_USER
     ,t.UPDATE_TIME
     ,t.UPDATE_USER_NAME
     ,t.EXTEND1
     ,t.EXTEND2
     ,t.EXTEND3
     ,t.EXTEND4
     ,t.EXTEND5
     ,t.EXTEND6
     ,t.EXTEND7
     ,t.EXTEND8
     ,t.EXTEND9
     ,t.EXTEND10
     ,COALESCE(t.UPDATE_USER_NAME,t.INSERT_USER_NAME) as createrUser
     ,COALESCE(t.UPDATE_TIME,t.INSERT_TIME) as createrTime
    </sql>
    <sql id="condition">
        <if test="tradeCode != null and tradeCode != ''">
            and t.trade_code =#{tradeCode}
        </if>
        <if test="planId != null and planId != ''">
            and t.PLAN_ID like '%'|| #{planId} || '%'
        </if>
        <if test="status != null and status != ''">
            and t.STATUS = #{status}
        </if>
        <if test="status == null or status == ''">
            and t.STATUS !='2'
        </if>
        <if test="insertTimeFrom != null and insertTimeFrom != ''">
            <![CDATA[ and coalesce(t.update_time,t.insert_time) >= to_date(#{insertTimeFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="insertTimeTo != null and insertTimeTo != ''">
            <![CDATA[ and coalesce(t.update_time,t.insert_time) <= DATEADD(DAY, 1, to_date(#{insertTimeTo}, 'yyyy-mm-dd hh24:mi:ss')) ]]>
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="bizIplanResultMap" parameterType="com.dcjet.cs.importedCigarettes.model.BizIPlan">
        SELECT
        <include refid="Base_Column_List" />
        ,case  when (select count(*) from T_BIZ_I_PLAN head
        left join T_BIZ_I_CONTRACT_HEAD ctr on ctr.PLAN_NO=head.PLAN_ID
        where head.sid=t.sid and ctr.DATA_STATUS!='2') > 0 Then '1' ELSE '0' END as hasHeadCtr,
        (SELECT CASE WHEN head.version_no != 1 THEN 1 ELSE 0 END
        FROM T_BIZ_I_PLAN head
        WHERE head.sid = t.sid ) as isCopy
        FROM
        T_BIZ_I_PLAN t
        <where>
            <include refid="condition"></include>
        </where>
        order by COALESCE(t.UPDATE_TIME,t.INSERT_TIME) desc
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_BIZ_I_PLAN t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>

    <select id="checkPlanYear" resultType="int">
        SELECT COUNT(*) AS COUNT
        FROM T_BIZ_I_PLAN
        WHERE
        EXTRACT(YEAR FROM PLAN_YEAR) = EXTRACT(YEAR FROM TO_DATE(#{planYear}, 'YYYY-MM-DD HH24:MI:SS'))
        AND HALF_YEAR = #{halfYear}
        AND TRADE_CODE = #{tradeCode}
        AND STATUS != '2'
        <if test="sid != null and sid != ''">
            AND SID != #{sid}
        </if>
    </select>

    <select id="checkPlanId" resultType="int">
        SELECT COUNT(*) AS COUNT
        FROM T_BIZ_I_PLAN
        WHERE PLAN_ID = #{planId}
        AND TRADE_CODE = #{tradeCode}
        <if test="sid != null and sid != ''">
            AND SID != #{sid}
        </if>
    </select>

    <select id="checkPlanStatus" resultType="int">
        select count(*)
        from T_BIZ_I_PLAN
        where status != #{status}
        and sid in
        <foreach collection="sids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="checkContractUsed" resultType="java.lang.Integer">
        select count(*)
        from t_biz_i_contract_head
        where plan_no in (
        select distinct plan_id
        from t_biz_i_plan
        where sid in
        <foreach collection="sids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        )
    </select>

    <update id="updateStatus">
        UPDATE T_BIZ_I_PLAN
        SET status = #{status},
        update_user = #{updateUser},
        update_time = now(),
        update_user_name = #{updateUserName}
        <if test="confirmTime !=null">
           ,confirm_time =#{confirmTime}
        </if>
        WHERE sid = #{sid}
    </update>
    <update id="updateApprovalStatus">
        UPDATE T_BIZ_I_PLAN
        SET appr_status = #{apprStatus}
        WHERE sid = #{sid}
    </update>
    <select id="checkPlanIdNotCancel" resultType="java.lang.String">
        select
        sid
        from T_BIZ_I_PLAN t
        where t.plan_id in  (select  distinct plan_id
        from T_BIZ_I_PLAN t
        where SID = #{planSid}
        ) and  t.STATUS !=2;
    </select>

    <select id="getValidOrdersSids" resultType="java.lang.String">
        select sid from T_BIZ_I_PLAN
        where plan_id  = #{orderNo} and trade_code = #{tradeCode} and STATUS != '2'
    </select>

    <select id="getMaxVersionNoByContract" resultType="com.dcjet.cs.importedCigarettes.model.BizIPlan">
        select sid,VERSION_NO  from T_BIZ_I_PLAN
        where plan_id = #{planId}
        and trade_code = #{tradeCode}
        order by CAST(VERSION_NO AS INTEGER) desc limit 1
    </select>

    <insert id="copyVersion">
        with temp as (select TO_NUMBER(COALESCE(TO_NUMBER(MAX(VERSION_NO)), 0) + 1) AS serial_no
        from T_BIZ_I_ORDER_HEAD t
        where t.plan_no in (select distinct plan_no
        from T_BIZ_I_ORDER_HEAD t
        where SID = #{oldOrderHeadSid})
        and t.trade_code = #{tradeCode})
        INSERT INTO "T_BIZ_I_PLAN"("SID", "PARENT_ID", "TRADE_CODE", "BUSINESS_TYPE", "PLAN_ID", "PLAN_YEAR",
        "HALF_YEAR", "REMARK", "STATUS", "CONFIRM_TIME", "APPR_STATUS", "VERSION_NO",
        "INSERT_USER", "INSERT_USER_NAME", "INSERT_TIME", "UPDATE_USER", "UPDATE_TIME",
        "UPDATE_USER_NAME", "EXTEND1", "EXTEND2", "EXTEND3", "EXTEND4", "EXTEND5", "EXTEND6",
        "EXTEND7", "EXTEND8", "EXTEND9", "EXTEND10")
        select #{newOrderHeadSid}, PARENT_ID, TRADE_CODE , BUSINESS_TYPE  , PLAN_ID , PLAN_YEAR , HALF_YEAR , REMARK , '0'
        , null , '0', (select serial_no from temp), #{insertUserNo} , #{insertUserName} , now(), null , null , null ,
        EXTEND1 , EXTEND2  , EXTEND3  , EXTEND4 , EXTEND5 , EXTEND6 , EXTEND7 , EXTEND8, EXTEND9 , EXTEND10
        from "T_BIZ_I_PLAN"
        where sid = #{oldOrderHeadSid};


    </insert>

    <update id="updateCancelByContract">
        UPDATE T_BIZ_I_PLAN h
        SET status = '2'
        where plan_id = #{planId}
        and trade_code = #{tradeCode}
    </update>
</mapper>
