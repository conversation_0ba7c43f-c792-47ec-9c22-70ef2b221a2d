<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.importedCigarettes.dao.BizIPlanListMapper">
    <resultMap id="bizIPlanListResultMap" type="com.dcjet.cs.importedCigarettes.model.BizIPlanList">
		<id column="sid" property="sid" jdbcType="VARCHAR" />
		<result column="head_id" property="headId" jdbcType="VARCHAR" />
		<result column="product_name" property="productName" jdbcType="VARCHAR" />
		<result column="supplier" property="supplier" jdbcType="VARCHAR" />
		<result column="english_brand" property="englishBrand" jdbcType="VARCHAR" />
		<result column="origin" property="origin" jdbcType="VARCHAR" />
		<result column="plan_quantity" property="planQuantity" jdbcType="NUMERIC" />
		<result column="unit" property="unit" jdbcType="VARCHAR" />
		<result column="curr" property="curr" jdbcType="VARCHAR" />
		<result column="unit_price" property="unitPrice" jdbcType="NUMERIC" />
		<result column="total_amount" property="totalAmount" jdbcType="NUMERIC" />
		<result column="discount_rate" property="discountRate" jdbcType="NUMERIC" />
		<result column="discount_amount" property="discountAmount" jdbcType="NUMERIC" />
		<result column="insert_user" property="insertUser" jdbcType="VARCHAR" />
		<result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR" />
		<result column="insert_time" property="insertTime" jdbcType="TIMESTAMP" />
		<result column="update_user" property="updateUser" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="update_user_name" property="updateUserName" jdbcType="VARCHAR" />
		<result column="extend1" property="extend1" jdbcType="VARCHAR" />
		<result column="extend2" property="extend2" jdbcType="VARCHAR" />
		<result column="extend3" property="extend3" jdbcType="VARCHAR" />
		<result column="extend4" property="extend4" jdbcType="VARCHAR" />
		<result column="extend5" property="extend5" jdbcType="VARCHAR" />
		<result column="extend6" property="extend6" jdbcType="VARCHAR" />
		<result column="extend7" property="extend7" jdbcType="VARCHAR" />
		<result column="extend8" property="extend8" jdbcType="VARCHAR" />
		<result column="extend9" property="extend9" jdbcType="VARCHAR" />
		<result column="extend10" property="extend10" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List" >
      t.id as sid
     ,t.head_id
     ,t.product_name
     ,t.supplier
     ,t.english_brand
     ,t.origin
     ,t.plan_quantity
     ,t.unit
     ,t.curr
     ,t.unit_price
     ,t.total_amount
     ,t.discount_rate
     ,t.discount_amount
     ,t.create_by as insert_user
     ,t.insert_user_name
     ,t.create_time as insert_time
     ,t.update_by as update_user
     ,t.update_time
     ,t.update_user_name
     ,t.extend1
     ,t.extend2
     ,t.extend3
     ,t.extend4
     ,t.extend5
     ,t.extend6
     ,t.extend7
     ,t.extend8
     ,t.extend9
     ,t.extend10
     ,t.trade_code
    </sql>
    <sql id="condition">
        <if test="tradeCode != null and tradeCode != ''">
            and t.trade_code =#{tradeCode}
        </if>
        <if test="headId != null and headId != ''">
            and t.head_id = #{headId}
        </if>
        <if test="productName != null and productName != ''">
            and t.product_name like '%'|| #{productName} || '%'
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="bizIPlanListResultMap" parameterType="com.dcjet.cs.importedCigarettes.model.BizIPlanList">
        SELECT
        <include refid="Base_Column_List" />,
        CASE
        WHEN EXISTS (
            SELECT 1
            FROM T_BIZ_I_CONTRACT_LIST ctr
            JOIN T_BIZ_I_CONTRACT_HEAD ctrh ON ctrh.SID = ctr.HEAD_ID
            WHERE ctr.PARENT_ID = t.id
            AND ctrh.DATA_STATUS != '2'
        ) THEN true ELSE false END AS hasCtr
        FROM
        t_biz_i_plan_list t
        <where>
            <include refid="condition"></include>
        </where>
        ORDER BY t.supplier, t.unit_price ASC
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from t_biz_i_plan_list t where t.id in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
    <delete id="deleteByHeadIds" parameterType="java.util.List">
        delete from t_biz_i_plan_list t where t.head_id in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>

    <select id="getContractListByHeadId" resultType="com.dcjet.cs.importedCigarettes.model.BizIPlanList">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        t_biz_i_plan_list t
        <where>
            head_id = #{sid}
        </where>
        ORDER BY t.supplier, t.unit_price ASC
    </select>
    <select id="checkProductName" resultType="com.dcjet.cs.importedCigarettes.model.BizIPlanList">
        SELECT <include refid="Base_Column_List" /> FROM t_biz_i_plan_list t WHERE t.product_name = #{productName} and t.head_id = #{headId}
    </select>
    <select id="checkProductNameWithOutSelf" resultType="com.dcjet.cs.importedCigarettes.model.BizIPlanList">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_biz_i_plan_list t
        WHERE t.product_name = #{productName}
              and t.head_id = #{headId}
              and t.id  != #{sid}
    </select>
</mapper>
