package com.dcjet.cs.importedCigarettes.dao;
import com.dcjet.cs.dto.dec.BizIListTotal;
import com.dcjet.cs.importedCigarettes.model.BizIContractList;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
/**
* generated by Generate 神码
* BizIContractList
* <AUTHOR>
* @date: 2025-3-11
*/
public interface BizIContractListMapper extends Mapper<BizIContractList> {
    /**
     * 查询获取数据
     * @param bizIContractList
     * @return
     */
    List<BizIContractList> getList(BizIContractList bizIContractList);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    void insertByPlanList(@Param("planNo") String planNo,@Param("seller") String seller,@Param("tradeCode") String tradeCode, @Param("headId") String headId, @Param("userNo") String userNo);

    void updateListContractQty(@Param("sid") String sid, @Param("planNo") String planNo);

    void updateListFormedQuantity(@Param("planNo") String planNo);

    int checkContractInOrderList(@Param("sid") String sid);

    void copyListByHeadId(@Param("oldHeadId") String oldHeadId, @Param("headId") String headId, @Param("userNo") String userNo );

    List<BizIContractList> getContractListByHeadId(@Param("sid") String sid);

    void updateCorrelationID(@Param("newListId") String newListId, @Param("oldSid") String oldSid);

    BizIListTotal getContractTotal(BizIContractList bizIContractList);

    void deleteContractQtyIsZero(@Param("headId") String sid);
}
