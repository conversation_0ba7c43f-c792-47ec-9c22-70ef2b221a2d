<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.importedCigarettes.dao.BizIContractListMapper">
    <resultMap id="bizIContractListResultMap" type="com.dcjet.cs.importedCigarettes.model.BizIContractList">
		<id column="sid" property="sid" jdbcType="VARCHAR" />
		<result column="business_type" property="businessType" jdbcType="VARCHAR" />
		<result column="data_status" property="dataStatus" jdbcType="VARCHAR" />
		<result column="version_no" property="versionNo" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
		<result column="parent_id" property="parentId" jdbcType="VARCHAR" />
		<result column="head_id" property="headId" jdbcType="VARCHAR" />
		<result column="goods_brand" property="goodsBrand" jdbcType="VARCHAR" />
		<result column="unit" property="unit" jdbcType="VARCHAR" />
		<result column="plan_quantity" property="planQuantity" jdbcType="NUMERIC" />
		<result column="formed_quantity" property="formedQuantity" jdbcType="NUMERIC" />
		<result column="executable_quantity" property="executableQuantity" jdbcType="NUMERIC" />
		<result column="contract_quantity" property="contractQuantity" jdbcType="NUMERIC" />
		<result column="curr" property="curr" jdbcType="VARCHAR" />
		<result column="unit_price" property="unitPrice" jdbcType="NUMERIC" />
		<result column="total_value" property="totalValue" jdbcType="NUMERIC" />
		<result column="goods_category" property="goodsCategory" jdbcType="VARCHAR" />
		<result column="insert_user" property="insertUser" jdbcType="VARCHAR" />
		<result column="insert_time" property="insertTime" jdbcType="TIMESTAMP" />
		<result column="update_user" property="updateUser" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR" />
		<result column="update_user_name" property="updateUserName" jdbcType="VARCHAR" />
		<result column="extend1" property="extend1" jdbcType="VARCHAR" />
		<result column="extend2" property="extend2" jdbcType="VARCHAR" />
		<result column="extend3" property="extend3" jdbcType="VARCHAR" />
		<result column="extend4" property="extend4" jdbcType="VARCHAR" />
		<result column="extend5" property="extend5" jdbcType="VARCHAR" />
		<result column="extend6" property="extend6" jdbcType="VARCHAR" />
		<result column="extend7" property="extend7" jdbcType="VARCHAR" />
		<result column="extend8" property="extend8" jdbcType="VARCHAR" />
		<result column="extend9" property="extend9" jdbcType="VARCHAR" />
		<result column="extend10" property="extend10" jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List" >
     sid
     ,business_type
     ,data_status
     ,version_no
     ,trade_code
     ,parent_id
     ,head_id
     ,goods_brand
     ,unit
     ,plan_quantity
     ,formed_quantity
     ,executable_quantity
     ,contract_quantity
     ,curr
     ,unit_price
     ,total_value
     ,goods_category
     ,insert_user
     ,insert_time
     ,update_user
     ,update_time
     ,insert_user_name
     ,update_user_name
     ,extend1
     ,extend2
     ,extend3
     ,extend4
     ,extend5
     ,extend6
     ,extend7
     ,extend8
     ,extend9
     ,extend10
    </sql>
    <sql id="condition">
    <if test="dataStatus != null and dataStatus != ''">
		and data_status = #{dataStatus}
	</if>
    <if test="versionNo != null and versionNo != ''">
		and version_no = #{versionNo}
	</if>
    <if test="tradeCode != null and tradeCode != ''">
		and trade_code = #{tradeCode}
	</if>
    <if test="headId != null and headId != ''">
		and head_id = #{headId}
	</if>
    <if test="goodsBrand != null and goodsBrand != ''">
	  and goods_brand like '%'|| #{goodsBrand} || '%'
	</if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="bizIContractListResultMap" parameterType="com.dcjet.cs.importedCigarettes.model.BizIContractList">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        t_biz_i_contract_list t
        <where>
            <include refid="condition"></include>
        </where>
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from t_biz_i_contract_list t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
    <delete id="deleteContractQtyIsZero">
        delete from t_biz_i_contract_list t where t.head_id = #{headId}
        and t.contract_quantity = 0
    </delete>
    <insert id="insertByPlanList">
        insert into t_biz_i_contract_list(
                                           sid
                                         ,business_type
                                         ,data_status
                                         ,version_no
                                         ,trade_code
                                         ,parent_id
                                         ,head_id
                                         ,goods_brand
                                         ,unit
                                         ,plan_quantity
                                         ,formed_quantity
                                         ,executable_quantity
                                         ,contract_quantity
                                         ,curr
                                         ,unit_price
                                         ,total_value
                                         ,goods_category
                                         ,insert_user
                                         ,insert_time
        )
        select
            sys_guid(),'1', '0', p.version_no, p.TRADE_CODE, pl.id, #{headId}
             ,pl.PRODUCT_NAME, pl.UNIT, pl.PLAN_QUANTITY, 0 as formed_quantity, 0 as executable_quantity, 0 as contract_quantity
             , pl.CURR, pl.UNIT_PRICE, 0 as total_value, mat.MERCHANDISE_CATEGORIES as goods_category, #{userNo},now()
        from T_BIZ_I_PLAN_LIST pl
                 left join T_BIZ_I_PLAN p on p.SID = pl.HEAD_ID
                 left join T_BIZ_MATERIAL_INFORMATION mat on mat.G_NAME = pl.PRODUCT_NAME and POSITION('1' in mat.COMMON_MARK) > 0
        where pl.SUPPLIER = #{seller} and p.PLAN_ID = #{planNo} and p.TRADE_CODE = #{tradeCode} and p.STATUS = '1'
        ;
    </insert>
    <update id="updateListContractQty">
        <![CDATA[
        UPDATE T_BIZ_I_CONTRACT_LIST cl
        SET
            formed_quantity = tmp.total_formed,
            executable_quantity = cl.PLAN_QUANTITY - tmp.total_formed,
            contract_quantity = cl.PLAN_QUANTITY - tmp.total_formed,
            total_value =
                CASE
                -- 检查计算结果是否在 DECIMAL(19,2) 的范围内
                WHEN ROUND((cl.PLAN_QUANTITY - tmp.total_formed) * cl.UNIT_PRICE, 2) <= 99999999999999999.99
                THEN ROUND((cl.PLAN_QUANTITY - tmp.total_formed) * cl.UNIT_PRICE, 2)
                ELSE NULL  -- 超出范围则置为 NULL
                END
            FROM (
                SELECT
                    h2.PLAN_NO,
                    l2.GOODS_BRAND,
                    SUM(l2.contract_quantity) AS total_formed
                FROM T_BIZ_I_CONTRACT_LIST l2
                JOIN T_BIZ_I_CONTRACT_HEAD h2
                    ON h2.SID = l2.HEAD_ID
                WHERE h2.DATA_STATUS != '2' and h2.PLAN_NO = #{planNo}
                GROUP BY h2.PLAN_NO, l2.GOODS_BRAND
            ) tmp
        JOIN T_BIZ_I_CONTRACT_HEAD h
            ON h.SID = cl.HEAD_ID
            WHERE tmp.PLAN_NO = h.PLAN_NO
              AND tmp.GOODS_BRAND = cl.GOODS_BRAND
              AND h.PLAN_NO = #{planNo} and h.sid = #{sid};
        ]]>
    </update>
    <update id="updateListFormedQuantity">
        UPDATE T_BIZ_I_CONTRACT_LIST cl
        SET
            formed_quantity = tmp.total_formed,
            executable_quantity = cl.PLAN_QUANTITY - tmp.total_formed
            FROM (
                SELECT
                    h2.PLAN_NO,
                    l2.GOODS_BRAND,
                    SUM(l2.contract_quantity) AS total_formed,
                    SUM(l2.PLAN_QUANTITY) AS total_plan
                FROM T_BIZ_I_CONTRACT_LIST l2
                JOIN T_BIZ_I_CONTRACT_HEAD h2
                    ON h2.SID = l2.HEAD_ID
                WHERE h2.DATA_STATUS != '2'
                GROUP BY h2.PLAN_NO, l2.GOODS_BRAND
            ) tmp
        JOIN T_BIZ_I_CONTRACT_HEAD h
        ON h.SID = cl.HEAD_ID
        WHERE tmp.PLAN_NO = h.PLAN_NO
          AND tmp.GOODS_BRAND = cl.GOODS_BRAND
          AND h.PLAN_NO = #{planNo};
    </update>
    <select id="checkContractInOrderList" resultType="java.lang.Integer">
        select count(1) from T_BIZ_I_ORDER_LIST l
            left join T_BIZ_I_ORDER_HEAD h on h.sid = l.HEAD_ID
        where
            CONTRACT_LIST_ID = #{sid}
          and h.DATA_STATUS != '2';
    </select>
    <select id="copyListByHeadId">
        insert into t_biz_i_contract_list(
                                           sid
                                         ,business_type
                                         ,data_status
                                         ,version_no
                                         ,trade_code
                                         ,parent_id
                                         ,head_id
                                         ,goods_brand
                                         ,unit
                                         ,plan_quantity
                                         ,formed_quantity
                                         ,executable_quantity
                                         ,contract_quantity
                                         ,curr
                                         ,unit_price
                                         ,total_value
                                         ,goods_category
                                         ,insert_user
                                         ,insert_time
        )
        select
             sys_guid()
             ,business_type
             ,data_status
             ,version_no
             ,trade_code
             ,parent_id
             ,#{headId}
             ,goods_brand
             ,unit
             ,plan_quantity
             ,formed_quantity
             ,executable_quantity
             ,contract_quantity
             ,curr
             ,unit_price
             ,total_value
             ,goods_category
             ,#{userNo}
             ,now()
        from t_biz_i_contract_list where head_id = #{oldHeadId}
    </select>
    <select id="getContractListByHeadId" resultType="com.dcjet.cs.importedCigarettes.model.BizIContractList">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        t_biz_i_contract_list t
        <where>
            head_id = #{sid}
        </where>
    </select>
    <select id="getContractTotal" resultType="com.dcjet.cs.dto.dec.BizIListTotal">
        SELECT
            sum(t.contract_quantity) as qtyTotal,
            sum(t.total_value) as decTotal
        FROM
        t_biz_i_contract_list t
        <where>
            <include refid="condition"></include>
        </where>
    </select>

    <update id="updateCorrelationID">
        update t_biz_i_contract_list set parent_id = #{newListId} where parent_id = #{oldSid};
    </update>

</mapper>
