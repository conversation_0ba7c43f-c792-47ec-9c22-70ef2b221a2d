package com.dcjet.cs.importedCigarettes.service;
import com.dcjet.cs.dto.dec.BizIListTotal;
import com.dcjet.cs.importedCigarettes.dao.BizIContractHeadMapper;
import com.dcjet.cs.importedCigarettes.model.BizIContractHead;
import com.xdo.common.exception.ErrorException;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.importedCigarettes.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.importedCigarettes.dao.BizIContractListMapper;
import com.dcjet.cs.importedCigarettes.mapper.BizIContractListDtoMapper;
import com.dcjet.cs.importedCigarettes.model.BizIContractList;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import jdk.nashorn.internal.runtime.options.Option;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Slf4j
@Service
public class BizIContractListService extends BaseService<BizIContractList> {
    @Resource
    private BizIContractListMapper bizIContractListMapper;
    @Resource
    private BizIContractHeadMapper bizIContractHeadMapper;
    @Resource
    private BizIContractListDtoMapper bizIContractListDtoMapper;
    public static final BigDecimal DECIMAL19_2_MAX = new BigDecimal("99999999999999999.99");

    @Override
    public Mapper<BizIContractList> getMapper() {
        return bizIContractListMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizIContractListParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizIContractListDto>> getListPaged(BizIContractListParam bizIContractListParam, PageParam pageParam) {
        // 启用分页查询
        BizIContractList bizIContractList = bizIContractListDtoMapper.toPo(bizIContractListParam);
        Page<BizIContractList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizIContractListMapper.getList(bizIContractList));
        List<BizIContractListDto> bizIContractListDtos = page.getResult().stream().map(head -> {
            BizIContractListDto dto = bizIContractListDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizIContractListDto>> paged = ResultObject.createInstance(bizIContractListDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizIContractListParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIContractListDto insert(BizIContractListParam bizIContractListParam, UserInfoToken userInfo) {
        BizIContractList bizIContractList = bizIContractListDtoMapper.toPo(bizIContractListParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizIContractList.setSid(sid);
        bizIContractList.setInsertUser(userInfo.getUserNo());
        bizIContractList.setInsertTime(new Date());
        // 新增数据
        int insertStatus = bizIContractListMapper.insert(bizIContractList);
        return  insertStatus > 0 ? bizIContractListDtoMapper.toDto(bizIContractList) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizIContractListParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIContractListDto update(BizIContractListParam bizIContractListParam, UserInfoToken userInfo) {
        BizIContractList bizIContractList = bizIContractListMapper.selectByPrimaryKey(bizIContractListParam.getSid());
        bizIContractListDtoMapper.updatePo(bizIContractListParam, bizIContractList);
        //校验对应进口计划 版本号是否一致
        if (bizIContractListMapper.checkContractInOrderList(bizIContractList.getSid()) > 0){
            throw new ErrorException(400, "所选数据已产生后续单据，请将后续单据作废后再进行编辑修改");
        }
        bizIContractList.setUpdateUser(userInfo.getUserNo());
        bizIContractList.setUpdateTime(new Date());
        BigDecimal unitPrice = bizIContractList.getUnitPrice();
        BigDecimal contractQuantity = bizIContractList.getContractQuantity();

        if (unitPrice != null && contractQuantity != null) {
            BigDecimal totalValue = unitPrice
                    .multiply(contractQuantity)
                    .setScale(2, RoundingMode.HALF_UP); // 四舍五入保留两位
            bizIContractList.setTotalValue(totalValue);
        }
        // 校验 totalValue 是否超出范围
        if (bizIContractList.getTotalValue().compareTo(DECIMAL19_2_MAX) > 0) {
            // 抛出明确的业务异常，提示错误信息
            throw new IllegalArgumentException(
                    "总值 超出数值型(19,2)范围，当前计算值: " + bizIContractList.getTotalValue());
        }
        // 更新数据
        int update = bizIContractListMapper.updateByPrimaryKey(bizIContractList);
        BizIContractHead bizIContractHead = bizIContractHeadMapper.selectByPrimaryKey(bizIContractList.getHeadId());
//        // 更新已形成合同数量
//        bizIContractListMapper.updateListFormedQuantity(bizIContractHead.getPlanNo());
        // 更新已形成合同数量，添加死锁异常捕获
        updateListFormedQuantity(bizIContractHead.getPlanNo());
        return update > 0 ? bizIContractListDtoMapper.toDto(bizIContractList) : null;
    }

    public void updateListFormedQuantity(String planNo){
        try {
            bizIContractListMapper.updateListFormedQuantity(planNo);
        } catch (Exception e) {
            // 捕获死锁异常
            if (e.toString().contains("dm.jdbc.driver.DMException: 死锁") ||
                    (e.getCause() != null && e.getCause().toString().contains("dm.jdbc.driver.DMException: 死锁"))) {
                // 记录日志
                log.error("更新已形成合同数量时发生死锁。合同计划号: " + planNo, e);
            } else {
                // 其他异常继续抛出
                throw e;
            }
        }
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
        String planNo = "";
        for (String sid : sids) {
            if (bizIContractListMapper.checkContractInOrderList(sid) > 0){
                throw new ErrorException(400, "所选数据已产生后续单据，请将后续单据作废后再进行删除");
            }
            BizIContractList bizIContractList = bizIContractListMapper.selectByPrimaryKey(sid);
            BizIContractHead bizIContractHead = bizIContractHeadMapper.selectByPrimaryKey(bizIContractList.getHeadId());
            planNo = bizIContractHead.getPlanNo();
        }
		bizIContractListMapper.deleteBySids(sids);
        //更新已执行合同数量
        if (StringUtils.isNotBlank(planNo)) {
            bizIContractListMapper.updateListFormedQuantity(planNo);
        }
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizIContractListDto> selectAll(BizIContractListParam exportParam, UserInfoToken userInfo) {
        BizIContractList bizIContractList = bizIContractListDtoMapper.toPo(exportParam);
        // bizIContractList.setTradeCode(userInfo.getCompany());
        List<BizIContractListDto> bizIContractListDtos = new ArrayList<>();
        List<BizIContractList> bizIContractLists = bizIContractListMapper.getList(bizIContractList);
        if (CollectionUtils.isNotEmpty(bizIContractLists)) {
            bizIContractListDtos = bizIContractLists.stream().map(head -> {
                BizIContractListDto dto = bizIContractListDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizIContractListDtos;
    }

    public ResultObject<BizIContractListDto> getContractTotal(BizIContractListParam bizIContractListParam, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true,"获取成功！");

        BizIContractList bizIContractList = bizIContractListDtoMapper.toPo(bizIContractListParam);
        bizIContractList.setTradeCode(userInfo.getCompany());
        BizIListTotal bizIListTotal = bizIContractListMapper.getContractTotal(bizIContractList);
        resultObject.setData(bizIListTotal);
        return resultObject;
    }
}
