package com.dcjet.cs.importedCigarettes.dao;
import com.dcjet.cs.dto.dec.CopyVersionData;
import com.dcjet.cs.importedCigarettes.model.BizIContractHead;
import com.dcjet.cs.importedCigarettes.model.BizIPlan;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;

/**
 * generated by Generate 神码
 * BizIPlan
 *
 * <AUTHOR>
 * @date: 2025-3-13
 */
public interface BizIPlanMapper extends Mapper<BizIPlan> {
    /**
     * 查询获取数据
     *
     * @param bizIPlan
     * @return
     */
    List<BizIPlan> getList(BizIPlan bizIPlan);

    /**
     * 批量删除
     *
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    int checkPlanYear(BizIPlan bizIPlan);

    int checkPlanId(BizIPlan bizIPlan);

    int checkPlanStatus(@Param("sids") List<String> sids, @Param("status") String status);

    int checkContractUsed(@Param("sids") List<String> sids);

    int updateStatus(BizIPlan bizIPlan);

    int updateApprovalStatus(BizIPlan bizIPlan);

    /**
     * 校验是否存在 同一个订单号是否存在未作废的数据
     * @param sid 订单表头sid
     * @return 返回订单号集合
     */
    List<String> checkPlanIdNotCancel(@Param("planSid") String sid);

    /**
     * 获取有效订单的sid集合
     * @param orderNo 订单号
     * @param tradeCode 企业编码
     * @return 返回有效订单sid集合
     */
    List<String> getValidOrdersSids(@Param("orderNo") String orderNo, @Param("tradeCode") String tradeCode);

    BizIPlan getMaxVersionNoByContract(BizIPlan bizIContractHead);

    /**
     * 复制进口订单表头数据
     * @param copyVersionData 进口数据
     * @return 返回执行成功行数，0为执行失败  > 1 为执行成功
     */
    int copyVersion(CopyVersionData copyVersionData);

    void updateCancelByContract(@Param("planId") String planId, @Param("tradeCode") String tradeCode);


}
