package com.dcjet.cs.importedCigarettes.service;
import com.xdo.common.exception.ErrorException;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.importedCigarettes.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.importedCigarettes.dao.BizIPlanListMapper;
import com.dcjet.cs.importedCigarettes.mapper.BizIPlanListDtoMapper;
import com.dcjet.cs.importedCigarettes.model.BizIPlanList;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-3-13
 */
@Service
public class BizIPlanListService extends BaseService<BizIPlanList> {
    @Resource
    private BizIPlanListMapper bizIPlanListMapper;
    @Resource
    private BizIPlanListDtoMapper bizIPlanListDtoMapper;
    @Override
    public Mapper<BizIPlanList> getMapper() {
        return bizIPlanListMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizIPlanListParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizIPlanListDto>> getListPaged(BizIPlanListParam bizIPlanListParam, PageParam pageParam) {
        // 启用分页查询
        BizIPlanList bizIPlanList = bizIPlanListDtoMapper.toPo(bizIPlanListParam);
        Page<BizIPlanList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizIPlanListMapper.getList(bizIPlanList));
        List<BizIPlanListDto> bizIPlanListDtos = page.getResult().stream().map(head -> {
            BizIPlanListDto dto = bizIPlanListDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizIPlanListDto>> paged = ResultObject.createInstance(bizIPlanListDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizIPlanListParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIPlanListDto insert(BizIPlanListParam bizIPlanListParam, UserInfoToken userInfo) {
        BizIPlanList bizIPlanList = bizIPlanListDtoMapper.toPo(bizIPlanListParam);
        if(StringUtils.isNotBlank(bizIPlanListParam.getProductName())){
            List<BizIPlanList> check = bizIPlanListMapper.checkProductName(bizIPlanList);
            if(check.size() > 0){
                throw new ErrorException(400,"商品名称已存在！");
            }
        }
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizIPlanList.setSid(sid);
        bizIPlanList.setInsertUser(userInfo.getUserNo());
        bizIPlanList.setInsertTime(new Date());
        bizIPlanList.setTradeCode(userInfo.getCompany());
        // 新增数据
        int insertStatus = bizIPlanListMapper.insert(bizIPlanList);
        return  insertStatus > 0 ? bizIPlanListDtoMapper.toDto(bizIPlanList) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizIPlanListParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIPlanListDto update(BizIPlanListParam bizIPlanListParam, UserInfoToken userInfo) {
        BizIPlanList bizIPlanList = bizIPlanListMapper.selectByPrimaryKey(bizIPlanListParam.getSid());
        bizIPlanListDtoMapper.updatePo(bizIPlanListParam, bizIPlanList);
        if(StringUtils.isNotBlank(bizIPlanListParam.getProductName())){
            List<BizIPlanList> check = bizIPlanListMapper.checkProductName(bizIPlanList);
            if(check.stream().anyMatch(x -> !x.getSid().equals(bizIPlanListParam.getSid()))){
                throw new ErrorException(400,"商品名称已存在！");
            }
        }
        bizIPlanList.setUpdateUser(userInfo.getUserNo());
        bizIPlanList.setUpdateTime(new Date());
        bizIPlanList.setTradeCode(userInfo.getCompany());
        // 更新数据
        int update = bizIPlanListMapper.updateByPrimaryKey(bizIPlanList);
        return update > 0 ? bizIPlanListDtoMapper.toDto(bizIPlanList) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		bizIPlanListMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizIPlanListDto> selectAll(BizIPlanListParam exportParam, UserInfoToken userInfo) {
        BizIPlanList bizIPlanList = bizIPlanListDtoMapper.toPo(exportParam);
        // bizIPlanList.setTradeCode(userInfo.getCompany());
        List<BizIPlanListDto> bizIPlanListDtos = new ArrayList<>();
        List<BizIPlanList> bizIPlanLists = bizIPlanListMapper.getList(bizIPlanList);
        if (CollectionUtils.isNotEmpty(bizIPlanLists)) {
            bizIPlanListDtos = bizIPlanLists.stream().map(head -> {
                BizIPlanListDto dto = bizIPlanListDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizIPlanListDtos;
    }
}
