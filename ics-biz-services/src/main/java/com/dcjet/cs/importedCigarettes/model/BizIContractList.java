package com.dcjet.cs.importedCigarettes.model;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Setter
@Getter
@Table(name = "t_biz_i_contract_list")
public class BizIContractList implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 
     */
	 @Id
	@Column(name = "sid")
	private  String sid;
	/**
     * 
     */
	@Column(name = "business_type")
	private  String businessType;
	/**
     * 
     */
	@Column(name = "data_status")
	private  String dataStatus;
	/**
     * 
     */
	@Column(name = "version_no")
	private  String versionNo;
	/**
     * 
     */
	@Column(name = "trade_code")
	private  String tradeCode;
	/**
     * 
     */
	@Column(name = "parent_id")
	private  String parentId;
	/**
     * 关联主合同表ID
     */
	@Column(name = "head_id")
	private  String headId;
	/**
     * 商品品牌
     */
	@Column(name = "goods_brand")
	private  String goodsBrand;
	/**
     * 单位
     */
	@Column(name = "unit")
	private  String unit;
	/**
     * 计划数量
     */
	@Column(name = "plan_quantity")
	private  BigDecimal planQuantity;
	/**
     * 已形成合同数量
     */
	@Column(name = "formed_quantity")
	private  BigDecimal formedQuantity;
	/**
     * 可执行合同量
     */
	@Column(name = "executable_quantity")
	private  BigDecimal executableQuantity;
	/**
     * 合同数量
     */
	@Column(name = "contract_quantity")
	private  BigDecimal contractQuantity;
	/**
     * 货币代码
     */
	@Column(name = "curr")
	private  String curr;
	/**
     * 单价
     */
	@Column(name = "unit_price")
	private  BigDecimal unitPrice;
	/**
     * 总值
     */
	@Column(name = "total_value")
	private  BigDecimal totalValue;
	/**
     * 商品类别
     */
	@Column(name = "goods_category")
	private  String goodsCategory;
	/**
     * 
     */
	@Column(name = "insert_user")
	private  String insertUser;
	/**
     * 
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "insert_time")
	private  Date insertTime;
	/**
     * 
     */
	@Column(name = "update_user")
	private  String updateUser;
	/**
     * 
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "update_time")
	private  Date updateTime;
	/**
     * 
     */
	@Column(name = "insert_user_name")
	private  String insertUserName;
	/**
     * 
     */
	@Column(name = "update_user_name")
	private  String updateUserName;
	/**
     * 
     */
	@Column(name = "extend1")
	private  String extend1;
	/**
     * 
     */
	@Column(name = "extend2")
	private  String extend2;
	/**
     * 
     */
	@Column(name = "extend3")
	private  String extend3;
	/**
     * 
     */
	@Column(name = "extend4")
	private  String extend4;
	/**
     * 
     */
	@Column(name = "extend5")
	private  String extend5;
	/**
     * 
     */
	@Column(name = "extend6")
	private  String extend6;
	/**
     * 
     */
	@Column(name = "extend7")
	private  String extend7;
	/**
     * 
     */
	@Column(name = "extend8")
	private  String extend8;
	/**
     * 
     */
	@Column(name = "extend9")
	private  String extend9;
	/**
     * 
     */
	@Column(name = "extend10")
	private  String extend10;
}
