package com.dcjet.cs.importedCigarettes.mapper;
import com.dcjet.cs.dto.importedCigarettes.*;
import com.dcjet.cs.importedCigarettes.model.BizIPlan;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-3-13
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizIPlanDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizIPlanDto toDto(BizIPlan po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizIPlan toPo(BizIPlanParam param);
    BizIPlan toHeadWithDetailPo(BizIPlanWithDetailParam param);
    /**
     * 数据库原始数据更新
     * @param bizIplanParam
     * @param bizIplan
     */
    void updatePo(BizIPlanParam bizIplanParam, @MappingTarget BizIPlan bizIplan);
    default void patchPo(BizIPlanParam bizIplanParam, BizIPlan bizIplan) {
        // TODO 自行实现局部更新
    }
}
