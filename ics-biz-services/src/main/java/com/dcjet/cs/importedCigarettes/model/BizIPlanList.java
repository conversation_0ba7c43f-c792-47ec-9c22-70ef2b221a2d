package com.dcjet.cs.importedCigarettes.model;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2025-3-13
 */
@Setter
@Getter
@Table(name = "t_biz_i_plan_list")
public class BizIPlanList implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 
     */
	 @Id
	 @Column(name = "id")
	private  String sid;
	/**
     * 主表ID
     */
	@Column(name = "head_id")
	private  String headId;
	/**
     * 商品名称（关联物料信息）
     */
	@Column(name = "product_name")
	private  String productName;
	/**
     * 供应商（关联物料信息，不可修改）
     */
	@Column(name = "supplier")
	private  String supplier;
	/**
     * 英文品牌（关联物料信息，可修改）
     */
	@Column(name = "english_brand")
	private  String englishBrand;
	/**
     * 原产地
     */
	@Column(name = "origin")
	private  String origin;
	/**
     * 计划数量（数值）
     */
	@Column(name = "plan_quantity")
	private  BigDecimal planQuantity;
	/**
     * 计划数量单位（默认万支）
     */
	@Column(name = "unit")
	private  String unit;
	/**
     * 币种（三位字母代码）
     */
	@Column(name = "curr")
	private  String curr;
	/**
     * 计划单价（关联物料信息，可修改）
     */
	@Column(name = "unit_price")
	private  BigDecimal unitPrice;
	/**
     * 计划总金额
     */
	@Column(name = "total_amount")
	private  BigDecimal totalAmount;
	/**
     * 折扣率（单位：%）
     */
	@Column(name = "discount_rate")
	private  BigDecimal discountRate;
	/**
     * 折扣金额
     */
	@Column(name = "discount_amount")
	private  BigDecimal discountAmount;
	/**
     * 制单人，自动识别最后操作人
     */
	@Column(name = "create_by")
	private  String insertUser;
	/**
     * 
     */
	@Column(name = "insert_user_name")
	private  String insertUserName;
	/**
     * 制单时间，系统自动生成
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "create_time")
	private  Date insertTime;
	/**
     * 修改人，最后操作人
     */
	@Column(name = "update_by")
	private  String updateUser;
	/**
     * 修改时间，最后操作时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "update_time")
	private  Date updateTime;
	/**
     * 
     */
	@Column(name = "update_user_name")
	private  String updateUserName;
	/**
     * 
     */
	@Column(name = "extend1")
	private  String extend1;
	/**
     * 
     */
	@Column(name = "extend2")
	private  String extend2;
	/**
     * 
     */
	@Column(name = "extend3")
	private  String extend3;
	/**
     * 
     */
	@Column(name = "extend4")
	private  String extend4;
	/**
     * 
     */
	@Column(name = "extend5")
	private  String extend5;
	/**
     * 
     */
	@Column(name = "extend6")
	private  String extend6;
	/**
     * 
     */
	@Column(name = "extend7")
	private  String extend7;
	/**
     * 
     */
	@Column(name = "extend8")
	private  String extend8;
	/**
     * 
     */
	@Column(name = "extend9")
	private  String extend9;
	/**
     * 
     */
	@Column(name = "extend10")
	private  String extend10;
	/**
     * 企业编码
     */
	@Column(name = "trade_code")
	private  String tradeCode;
	/**
     * 是否存在下游数据
     */
	@Transient
	private  String hasCtr;



	/**
	 * 创建人部门编码
	 */
	@Column(name = "sys_org_code")
	private  String sysOrgCode;
}
