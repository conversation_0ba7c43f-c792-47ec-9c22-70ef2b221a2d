package com.dcjet.cs.auxiliaryMaterials.mapper;
import com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatForContractListDto;
import com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatForContractListParam;
import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatForContractList;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate dcits
 *
 * <AUTHOR>
 * @date: 2025-5-22
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizIAuxmatForContractListDtoMapper {
    /***
     * 转换DTO到数据库对象
     * @param po
     * @return
     */
    BizIAuxmatForContractListDto toDto(BizIAuxmatForContractList po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizIAuxmatForContractList toPo(BizIAuxmatForContractListParam param);
    void updatePo(BizIAuxmatForContractListParam bizIAuxmatForContractListParam, @MappingTarget BizIAuxmatForContractList bizIAuxmatForContractList);
}
