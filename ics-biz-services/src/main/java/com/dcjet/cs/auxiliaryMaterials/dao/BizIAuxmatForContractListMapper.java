package com.dcjet.cs.auxiliaryMaterials.dao;
import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatForContractList;
import com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatForContractListDto;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
import java.util.Map;
/**
* generated by Generate dcits
* BizIAuxmatForContractList
* <AUTHOR>
* @date: 2025-5-22
*/
public interface BizIAuxmatForContractListMapper extends Mapper<BizIAuxmatForContractList> {
    /**
     * 根据参数查询
     *
     * @param bizIAuxmatForContractList
     * @return
     */
    List<BizIAuxmatForContractList> getList(BizIAuxmatForContractList bizIAuxmatForContractList);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
    /**
     * 根据表头headId批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    void deleteByHeadIds(List<String> sids);
    /**
     * 根据表头headId查询是否存在表体数据
     * @param sids
     * @return
     */
    int getListNumByHeadIds(List<String> sids);

    BizIAuxmatForContractListDto getContractTotal(BizIAuxmatForContractList bizIAuxmatForContractList);

    List<BizIAuxmatForContractList> getPlanListPaged(BizIAuxmatForContractList bizIAuxmatForContractList);

    void insertByOrderNoList(@Param("orderNoList") List<String> orderNoList, @Param("sid") String sid, @Param("userNo") String userNo, @Param("userName") String userName);
}
