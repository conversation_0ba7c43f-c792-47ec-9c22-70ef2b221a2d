<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.auxiliaryMaterials.dao.BizIAuxmatForContractHeadMapper">
    <resultMap id="bizIAuxmatForContractHeadResultMap" type="com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatForContractHead">
		<id column="id" property="id" jdbcType="VARCHAR" />
		<result column="business_type" property="businessType" jdbcType="VARCHAR" />
		<result column="contract_no" property="contractNo" jdbcType="VARCHAR" />
		<result column="customer_name" property="customerName" jdbcType="VARCHAR" />
		<result column="supplier_name" property="supplierName" jdbcType="VARCHAR" />
		<result column="sign_date" property="signDate" jdbcType="TIMESTAMP" />
		<result column="contract_start_date" property="contractStartDate" jdbcType="TIMESTAMP" />
		<result column="contract_end_date" property="contractEndDate" jdbcType="TIMESTAMP" />
		<result column="sign_place" property="signPlace" jdbcType="VARCHAR" />
		<result column="sign_place_en" property="signPlaceEn" jdbcType="VARCHAR" />
		<result column="port_of_shipment" property="portOfShipment" jdbcType="VARCHAR" />
		<result column="port_of_destination" property="portOfDestination" jdbcType="VARCHAR" />
		<result column="customs_port" property="customsPort" jdbcType="VARCHAR" />
		<result column="payment_method" property="paymentMethod" jdbcType="VARCHAR" />
		<result column="currency" property="currency" jdbcType="VARCHAR" />
		<result column="remark" property="remark" jdbcType="VARCHAR" />
		<result column="doc_status" property="docStatus" jdbcType="VARCHAR" />
		<result column="confirm_date" property="confirmDate" jdbcType="TIMESTAMP" />
		<result column="audit_status" property="auditStatus" jdbcType="VARCHAR" />
		<result column="data_state" property="dataState" jdbcType="VARCHAR" />
		<result column="version_no" property="versionNo" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
		<result column="sys_org_code" property="sysOrgCode" jdbcType="VARCHAR" />
		<result column="parent_id" property="parentId" jdbcType="VARCHAR" />
		<result column="create_by" property="createBy" jdbcType="VARCHAR" />
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
		<result column="update_by" property="updateBy" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="insert_user_name" property="insertUserName" jdbcType="VARCHAR" />
		<result column="update_user_name" property="updateUserName" jdbcType="VARCHAR" />
		<result column="extend1" property="extend1" jdbcType="VARCHAR" />
		<result column="extend2" property="extend2" jdbcType="VARCHAR" />
		<result column="extend3" property="extend3" jdbcType="VARCHAR" />
		<result column="extend4" property="extend4" jdbcType="VARCHAR" />
		<result column="extend5" property="extend5" jdbcType="VARCHAR" />
		<result column="extend6" property="extend6" jdbcType="VARCHAR" />
		<result column="extend7" property="extend7" jdbcType="VARCHAR" />
		<result column="extend8" property="extend8" jdbcType="VARCHAR" />
		<result column="extend9" property="extend9" jdbcType="VARCHAR" />
		<result column="extend10" property="extend10" jdbcType="VARCHAR" />
		<result column="total_amount" property="totalAmount" jdbcType="NUMERIC" />
	</resultMap>
	<sql id="Base_Column_List" >
     id
     ,business_type
     ,contract_no
     ,customer_name
     ,supplier_name
     ,sign_date
     ,contract_start_date
     ,contract_end_date
     ,sign_place
     ,sign_place_en
     ,port_of_shipment
     ,port_of_destination
     ,customs_port
     ,payment_method
     ,currency
     ,remark
     ,doc_status
     ,confirm_date
     ,audit_status
     ,data_state
     ,version_no
     ,trade_code
     ,sys_org_code
     ,parent_id
     ,create_by
     ,create_time
     ,update_by
     ,update_time
     ,insert_user_name
     ,update_user_name
     ,extend1
     ,extend2
     ,extend3
     ,extend4
     ,extend5
     ,extend6
     ,extend7
     ,extend8
     ,extend9
     ,extend10
     ,(select sum(AMOUNT) from t_biz_i_auxmat_for_contract_list cl where cl.head_id = t.id ) as total_amount
    </sql>
    <sql id="condition">
    <if test="tradeCode != null and tradeCode != ''">
        and trade_code = #{tradeCode}
    </if>
    <if test="businessType != null and businessType != ''">
	  and business_type like '%'|| #{businessType} || '%'
	</if>
    <if test="contractNo != null and contractNo != ''">
	  and contract_no like '%'|| #{contractNo} || '%'
	</if>
    <if test="customerName != null and customerName != ''">
	  and customer_name like '%'|| #{customerName} || '%'
	</if>
    <if test="supplierName != null and supplierName != ''">
	  and supplier_name like '%'|| #{supplierName} || '%'
	</if>
        <if test="_databaseId == 'oracle' and signDateFrom != null and signDateFrom != ''">
            <![CDATA[ and sign_date >= to_date(#{signDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId == 'oracle' and signDateTo != null and signDateTo != ''">
            <![CDATA[ and sign_date <= to_date(#{signDateTo}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId == 'postgresql' and signDateFrom != null and signDateFrom != ''">
            <![CDATA[ and sign_date >= to_date(#{signDateFrom}, 'yyyy-MM-dd hh24:mi:ss')::date]]>
        </if>
        <if test="_databaseId == 'postgresql' and signDateTo != null and signDateTo != ''">
            <![CDATA[ and sign_date <= to_date(#{signDateTo}, 'yyyy-MM-dd hh24:mi:ss')::date ]]>
        </if>
        <if test="_databaseId == 'oracle' and contractStartDateFrom != null and contractStartDateFrom != ''">
            <![CDATA[ and contract_start_date >= to_date(#{contractStartDateFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId == 'oracle' and contractStartDateTo != null and contractStartDateTo != ''">
            <![CDATA[ and contract_start_date <= to_date(#{contractStartDateTo}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId == 'postgresql' and contractStartDateFrom != null and contractStartDateFrom != ''">
            <![CDATA[ and contract_start_date >= to_date(#{contractStartDateFrom}, 'yyyy-MM-dd hh24:mi:ss')::date]]>
        </if>
        <if test="_databaseId == 'postgresql' and contractStartDateTo != null and contractStartDateTo != ''">
            <![CDATA[ and contract_start_date <= to_date(#{contractStartDateTo}, 'yyyy-MM-dd hh24:mi:ss')::date ]]>
        </if>
    <if test="docStatus != null and docStatus != ''">
		and doc_status = #{docStatus}
	</if>
    <if test="createBy != null and createBy != ''">
	  and create_by like '%'|| #{createBy} || '%'
	</if>
        <if test="_databaseId != 'oracle' and createTimeFrom != null and createTimeFrom != ''">
            <![CDATA[ and create_time >= to_date(#{createTimeFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId != 'oracle' and createTimeTo != null and createTimeTo != ''">
            <![CDATA[ and create_time <= to_date(#{createTimeTo}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="_databaseId == 'postgresql' and createTimeFrom != null and createTimeFrom != ''">
            <![CDATA[ and create_time >= to_timestamp(#{createTimeFrom}, 'yyyy-MM-dd hh24:mi:ss')::timestamp]]>
        </if>
        <if test="_databaseId == 'postgresql' and createTimeTo != null and createTimeTo != ''">
            <![CDATA[ and create_time <= to_timestamp(#{createTimeTo}, 'yyyy-MM-dd hh24:mi:ss')::timestamp ]]>
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="bizIAuxmatForContractHeadResultMap" parameterType="com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatForContractHead">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        t_biz_i_auxmat_for_contract_head t
        <where>
            <include refid="condition"></include>
        </where>
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from t_biz_i_auxmat_for_contract_head t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
</mapper>
