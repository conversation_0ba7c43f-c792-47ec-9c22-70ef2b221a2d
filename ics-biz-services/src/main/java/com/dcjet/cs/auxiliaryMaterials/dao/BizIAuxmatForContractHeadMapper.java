package com.dcjet.cs.auxiliaryMaterials.dao;
import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatForContractHead;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import javax.validation.constraints.NotEmpty;
import java.util.List;
/**
* generated by Generate 神码
* BizIAuxmatForContractHead
* <AUTHOR>
* @date: 2025-5-22
*/
public interface BizIAuxmatForContractHeadMapper extends Mapper<BizIAuxmatForContractHead> {
    /**
     * 查询获取数据
     * @param bizIAuxmatForContractHead
     * @return
     */
    List<BizIAuxmatForContractHead> getList(BizIAuxmatForContractHead bizIAuxmatForContractHead);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    int checkContractNo(@Param("contractNo") String contractNo, @Param("id") String id);

    int checkCanDelBySids(List<String> sids);
}
