package com.dcjet.cs.auxiliaryMaterials.model;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2025-5-22
 */
@Setter
@Getter
@Table(name = "t_biz_i_auxmat_for_contract_head")
public class BizIAuxmatForContractHead implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 主键id
     */
	 @Id
	@Column(name = "id")
	private  String id;
	/**
     * 业务类型，下拉框
     */
	@Column(name = "business_type")
	private  String businessType;
	/**
     * 合同号，文本
     */
	@Column(name = "contract_no")
	private  String contractNo;
	/**
     * 买方
     */
	@Column(name = "customer_name")
	private  String customerName;
	/**
     * 卖方
     */
	@Column(name = "supplier_name")
	private  String supplierName;
	/**
     * 签约日期，日期控件
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "sign_date")
	private  Date signDate;
	/**
     * 签约日期，日期控件-开始
     */
	@Transient
	private String signDateFrom;
	/**
     * 签约日期，日期控件-结束
     */
	@Transient
    private String signDateTo;
	/**
     * 合同生效期，日期控件
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "contract_start_date")
	private  Date contractStartDate;
	/**
     * 合同生效期，日期控件-开始
     */
	@Transient
	private String contractStartDateFrom;
	/**
     * 合同生效期，日期控件-结束
     */
	@Transient
    private String contractStartDateTo;
	/**
     * 合同有效期，日期控件
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "contract_end_date")
	private  Date contractEndDate;
	/**
     * 签约地点，下拉框
     */
	@Column(name = "sign_place")
	private  String signPlace;
	/**
     * 签约地点(英文)，文本
     */
	@Column(name = "sign_place_en")
	private  String signPlaceEn;
	/**
     * 装运港，下拉框
     */
	@Column(name = "port_of_shipment")
	private  String portOfShipment;
	/**
     * 目的港，下拉框
     */
	@Column(name = "port_of_destination")
	private  String portOfDestination;
	/**
     * 报关口岸，下拉框
     */
	@Column(name = "customs_port")
	private  String customsPort;
	/**
     * 付款方式，下拉框
     */
	@Column(name = "payment_method")
	private  String paymentMethod;
	/**
     * 币种，下拉框
     */
	@Column(name = "currency")
	private  String currency;
	/**
     * 备注，文本
     */
	@Column(name = "remark")
	private  String remark;
	/**
     * 单据状态，文本
     */
	@Column(name = "doc_status")
	private  String docStatus;
	/**
     * 确认时间，日期控件
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "confirm_date")
	private  Date confirmDate;
	/**
     * 审核状态，文本
     */
	@Column(name = "audit_status")
	private  String auditStatus;
	/**
     * 数据状态
     */
	@Column(name = "data_state")
	private  String dataState;
	/**
     * 版本号
     */
	@Column(name = "version_no")
	private  String versionNo;
	/**
     * 业务编码
     */
	@Column(name = "trade_code")
	private  String tradeCode;
	/**
     * 所属机构编码
     */
	@Column(name = "sys_org_code")
	private  String sysOrgCode;
	/**
     * 父级id
     */
	@Column(name = "parent_id")
	private  String parentId;
	/**
     * 创建人
     */
	@Column(name = "create_by")
	private  String createBy;
	/**
     * 创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "create_time")
	private  Date createTime;
	/**
     * 创建时间-开始
     */
	@Transient
	private String createTimeFrom;
	/**
     * 创建时间-结束
     */
	@Transient
    private String createTimeTo;
	/**
     * 修改人
     */
	@Column(name = "update_by")
	private  String updateBy;
	/**
     * 修改时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "update_time")
	private  Date updateTime;
	/**
     * 创建人姓名
     */
	@Column(name = "insert_user_name")
	private  String insertUserName;
	/**
     * 修改人姓名
     */
	@Column(name = "update_user_name")
	private  String updateUserName;
	/**
     * 扩展字段1
     */
	@Column(name = "extend1")
	private  String extend1;
	/**
     * 扩展字段2
     */
	@Column(name = "extend2")
	private  String extend2;
	/**
     * 扩展字段3
     */
	@Column(name = "extend3")
	private  String extend3;
	/**
     * 扩展字段4
     */
	@Column(name = "extend4")
	private  String extend4;
	/**
     * 扩展字段5
     */
	@Column(name = "extend5")
	private  String extend5;
	/**
     * 扩展字段6
     */
	@Column(name = "extend6")
	private  String extend6;
	/**
     * 扩展字段7
     */
	@Column(name = "extend7")
	private  String extend7;
	/**
     * 扩展字段8
     */
	@Column(name = "extend8")
	private  String extend8;
	/**
     * 扩展字段9
     */
	@Column(name = "extend9")
	private  String extend9;
	/**
     * 扩展字段10
     */
	@Column(name = "extend10")
	private  String extend10;

	@Transient
	private BigDecimal totalAmount;
}
