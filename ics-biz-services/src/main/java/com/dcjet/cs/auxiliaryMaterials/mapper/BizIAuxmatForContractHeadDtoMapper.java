package com.dcjet.cs.auxiliaryMaterials.mapper;
import com.dcjet.cs.dto.auxiliaryMaterials.*;
import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatForContractHead;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2025-5-22
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BizIAuxmatForContractHeadDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BizIAuxmatForContractHeadDto toDto(BizIAuxmatForContractHead po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BizIAuxmatForContractHead toPo(BizIAuxmatForContractHeadParam param);
    /**
     * 数据库原始数据更新
     * @param bizIAuxmatForContractHeadParam
     * @param bizIAuxmatForContractHead
     */
    void updatePo(BizIAuxmatForContractHeadParam bizIAuxmatForContractHeadParam, @MappingTarget BizIAuxmatForContractHead bizIAuxmatForContractHead);
    default void patchPo(BizIAuxmatForContractHeadParam bizIAuxmatForContractHeadParam, BizIAuxmatForContractHead bizIAuxmatForContractHead) {
        // TODO 自行实现局部更新
    }
}
