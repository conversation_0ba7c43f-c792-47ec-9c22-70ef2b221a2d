package com.dcjet.cs.auxiliaryMaterials.service;
import com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatForContractListDto;
import com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatForContractListParam;
import com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatForContractListExportParam;
import com.dcjet.cs.auxiliaryMaterials.dao.BizIAuxmatForContractListMapper;
import com.dcjet.cs.auxiliaryMaterials.mapper.BizIAuxmatForContractListDtoMapper;
import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatForContractList;
import org.apache.commons.collections4.CollectionUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate dcits
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-5-22
 */
@Service
public class BizIAuxmatForContractListService extends BaseService<BizIAuxmatForContractList> {
    @Resource
    private BizIAuxmatForContractListMapper bizIAuxmatForContractListMapper;
    @Resource
    private BizIAuxmatForContractListDtoMapper bizIAuxmatForContractListDtoMapper;
    @Override
    public Mapper<BizIAuxmatForContractList> getMapper() {
        return bizIAuxmatForContractListMapper;
    }
    /**
     * 功能描述: grid分页查询
     *
     * @param bizIAuxmatForContractListParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizIAuxmatForContractListDto>> selectAllPaged(BizIAuxmatForContractListParam bizIAuxmatForContractListParam, PageParam pageParam) {
        // 启用分页查询
        BizIAuxmatForContractList bizIAuxmatForContractList = bizIAuxmatForContractListDtoMapper.toPo(bizIAuxmatForContractListParam);
        Page<BizIAuxmatForContractList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizIAuxmatForContractListMapper.getList(bizIAuxmatForContractList));
        List<BizIAuxmatForContractListDto> bizIAuxmatForContractListDtos = page.getResult().stream().map(head -> {
            BizIAuxmatForContractListDto dto = bizIAuxmatForContractListDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		 ResultObject<List<BizIAuxmatForContractListDto>> paged = ResultObject.createInstance(bizIAuxmatForContractListDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @return
     */
    public List<BizIAuxmatForContractListDto> selectAll(BizIAuxmatForContractListParam exportParam) {
        BizIAuxmatForContractList bizIAuxmatForContractList = bizIAuxmatForContractListDtoMapper.toPo(exportParam);
        List<BizIAuxmatForContractListDto> bizIAuxmatForContractListDtos = new ArrayList<>();
        List<BizIAuxmatForContractList> bizIAuxmatForContractLists = bizIAuxmatForContractListMapper.getList(bizIAuxmatForContractList);
        if (CollectionUtils.isNotEmpty(bizIAuxmatForContractLists)) {
            bizIAuxmatForContractListDtos = bizIAuxmatForContractLists.stream().map(item -> {
                BizIAuxmatForContractListDto dto = bizIAuxmatForContractListDtoMapper.toDto(item);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizIAuxmatForContractListDtos;
    }
    /**
     * 功能描述:新增
     *
     * @param model
     * @param userInfo
     * @return
     */
    public BizIAuxmatForContractListDto insert(BizIAuxmatForContractListParam model, UserInfoToken userInfo) {
        String sid = UUID.randomUUID().toString();
        BizIAuxmatForContractList bizIAuxmatForContractList = bizIAuxmatForContractListDtoMapper.toPo(model);
        bizIAuxmatForContractList.setId(sid);
        bizIAuxmatForContractList.setCreateBy(userInfo.getUserNo());
        bizIAuxmatForContractList.setCreateTime(new Date());
        int insertStatus = bizIAuxmatForContractListMapper.insert(bizIAuxmatForContractList);
       return insertStatus > 0 ? bizIAuxmatForContractListDtoMapper.toDto(bizIAuxmatForContractList) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizIAuxmatForContractListParam
     * @param userInfo
     * @return
     */
    public BizIAuxmatForContractListDto update(BizIAuxmatForContractListParam bizIAuxmatForContractListParam, UserInfoToken userInfo) {
        BizIAuxmatForContractList bizIAuxmatForContractList = bizIAuxmatForContractListMapper.selectByPrimaryKey(bizIAuxmatForContractListParam.getSid());
        bizIAuxmatForContractListDtoMapper.updatePo(bizIAuxmatForContractListParam, bizIAuxmatForContractList);
        bizIAuxmatForContractList.setUpdateBy(userInfo.getUserNo());
        bizIAuxmatForContractList.setUpdateTime(new Date());
        // 更新数据
        int update = bizIAuxmatForContractListMapper.updateByPrimaryKey(bizIAuxmatForContractList);
         return update > 0 ? bizIAuxmatForContractListDtoMapper.toDto(bizIAuxmatForContractList) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    public void delete(List<String> sids, UserInfoToken userInfo) {
        bizIAuxmatForContractListMapper.deleteBySids(sids);
    }
	public int getListNumByHeadIds(List<String> sids) {
        return bizIAuxmatForContractListMapper.getListNumByHeadIds(sids);
    }
}
