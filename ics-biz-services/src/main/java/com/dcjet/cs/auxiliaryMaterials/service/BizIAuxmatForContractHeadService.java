package com.dcjet.cs.auxiliaryMaterials.service;
import com.dcjet.cs.auxiliaryMaterials.dao.BizIAuxmatForContractListMapper;
import com.dcjet.cs.auxiliaryMaterials.mapper.BizIAuxmatForContractListDtoMapper;
import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatForContractList;
import com.dcjet.cs.importedCigarettes.dao.BizIContractHeadMapper;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.auxiliaryMaterials.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.auxiliaryMaterials.dao.BizIAuxmatForContractHeadMapper;
import com.dcjet.cs.auxiliaryMaterials.mapper.BizIAuxmatForContractHeadDtoMapper;
import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatForContractHead;
import com.dcjet.cs.util.CommonEnum;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.exception.ErrorException;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import xdoi18n.XdoI18nUtil;
import javax.annotation.Resource;
import java.beans.Transient;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-5-22
 */
@Service
public class BizIAuxmatForContractHeadService extends BaseService<BizIAuxmatForContractHead> {
    @Resource
    private BizIContractHeadMapper bizIContractHeadMapper;
    @Resource
    private BizIAuxmatForContractListMapper bizIAuxmatForContractListMapper;
    @Resource
    private BizIAuxmatForContractListDtoMapper bizIAuxmatForContractListDtoMapper;
    @Resource
    private BizIAuxmatForContractHeadMapper bizIAuxmatForContractHeadMapper;
    @Resource
    private BizIAuxmatForContractHeadDtoMapper bizIAuxmatForContractHeadDtoMapper;
    @Override
    public Mapper<BizIAuxmatForContractHead> getMapper() {
        return bizIAuxmatForContractHeadMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizIAuxmatForContractHeadParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizIAuxmatForContractHeadDto>> getListPaged(BizIAuxmatForContractHeadParam bizIAuxmatForContractHeadParam, PageParam pageParam) {
        // 启用分页查询
        BizIAuxmatForContractHead bizIAuxmatForContractHead = bizIAuxmatForContractHeadDtoMapper.toPo(bizIAuxmatForContractHeadParam);
        Page<BizIAuxmatForContractHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizIAuxmatForContractHeadMapper.getList(bizIAuxmatForContractHead));
        List<BizIAuxmatForContractHeadDto> bizIAuxmatForContractHeadDtos = page.getResult().stream().map(head -> {
            BizIAuxmatForContractHeadDto dto = bizIAuxmatForContractHeadDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizIAuxmatForContractHeadDto>> paged = ResultObject.createInstance(bizIAuxmatForContractHeadDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizIAuxmatForContractHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIAuxmatForContractHeadDto insert(BizIAuxmatForContractHeadParam bizIAuxmatForContractHeadParam, UserInfoToken userInfo) {
        BizIAuxmatForContractHead bizIAuxmatForContractHead = bizIAuxmatForContractHeadDtoMapper.toPo(bizIAuxmatForContractHeadParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizIAuxmatForContractHead.setId(sid);
        bizIAuxmatForContractHead.setCreateBy(userInfo.getUserNo());
        bizIAuxmatForContractHead.setCreateTime(new Date());
        bizIAuxmatForContractHead.setTradeCode(userInfo.getCompany());
        bizIAuxmatForContractHead.setInsertUserName(userInfo.getUserName());
        //生成对应表体数据
        List<String> orderNoList = bizIAuxmatForContractHeadParam.getOrderNo();
        if (CollectionUtils.isEmpty(orderNoList)) {
            throw new ErrorException(400, "请选择需要新增数据！");
        }
        bizIAuxmatForContractListMapper.insertByOrderNoList(orderNoList, sid, userInfo.getUserNo(), userInfo.getUserName());
        //设置固定字段
        bizIAuxmatForContractHead.setSupplierName(orderNoList.get(0).split("_")[1]);
        bizIAuxmatForContractHead.setBusinessType(CommonEnum.businessTypeEnum.type_2.getCode());
        bizIAuxmatForContractHead.setDocStatus(CommonEnum.OrderStatusEnum.DRAFT.getValue());
        bizIAuxmatForContractHead.setAuditStatus(CommonEnum.APPR_STAUTS_ENUM.APPR_STAUTS_0.getCode());
        bizIAuxmatForContractHead.setVersionNo("1");
        bizIAuxmatForContractHead.setCurrency("USD");
        bizIAuxmatForContractHead.setCustomsPort("CHN331");
        //获取中国烟草国际有限公司 code
        String buyerCode = bizIContractHeadMapper.getBuyerCodeByName("中国烟草国际有限公司", userInfo.getCompany());
        if (StringUtils.isNotBlank(buyerCode)) {
            bizIAuxmatForContractHead.setCustomerName(buyerCode);
        }else {
            bizIAuxmatForContractHead.setCustomerName("中国烟草国际有限公司");
        }
        // 新增数据
        int insertStatus = bizIAuxmatForContractHeadMapper.insert(bizIAuxmatForContractHead);
        return  insertStatus > 0 ? bizIAuxmatForContractHeadDtoMapper.toDto(bizIAuxmatForContractHead) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizIAuxmatForContractHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIAuxmatForContractHeadDto update(BizIAuxmatForContractHeadParam bizIAuxmatForContractHeadParam, UserInfoToken userInfo) {
        BizIAuxmatForContractHead bizIAuxmatForContractHead = bizIAuxmatForContractHeadMapper.selectByPrimaryKey(bizIAuxmatForContractHeadParam.getSid());
        //校验合同号唯一
        if (bizIAuxmatForContractHeadMapper.checkContractNo(bizIAuxmatForContractHeadParam.getContractNo(),bizIAuxmatForContractHeadParam.getId()) > 0){
            throw new ErrorException(400, "合同号已存在，请重新输入！");
        }
        bizIAuxmatForContractHeadDtoMapper.updatePo(bizIAuxmatForContractHeadParam, bizIAuxmatForContractHead);
        bizIAuxmatForContractHead.setUpdateBy(userInfo.getUserNo());
        bizIAuxmatForContractHead.setUpdateTime(new Date());
        // 更新数据
        int update = bizIAuxmatForContractHeadMapper.updateByPrimaryKey(bizIAuxmatForContractHead);
        return update > 0 ? bizIAuxmatForContractHeadDtoMapper.toDto(bizIAuxmatForContractHead) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
        //校验数据状态
        if (bizIAuxmatForContractHeadMapper.checkCanDelBySids(sids) > 0){
            throw new ErrorException(400, "仅编制状态数据允许删除");
        }
		bizIAuxmatForContractHeadMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizIAuxmatForContractHeadDto> selectAll(BizIAuxmatForContractHeadParam exportParam, UserInfoToken userInfo) {
        BizIAuxmatForContractHead bizIAuxmatForContractHead = bizIAuxmatForContractHeadDtoMapper.toPo(exportParam);
        // bizIAuxmatForContractHead.setTradeCode(userInfo.getCompany());
        List<BizIAuxmatForContractHeadDto> bizIAuxmatForContractHeadDtos = new ArrayList<>();
        List<BizIAuxmatForContractHead> bizIAuxmatForContractHeads = bizIAuxmatForContractHeadMapper.getList(bizIAuxmatForContractHead);
        if (CollectionUtils.isNotEmpty(bizIAuxmatForContractHeads)) {
            bizIAuxmatForContractHeadDtos = bizIAuxmatForContractHeads.stream().map(head -> {
                BizIAuxmatForContractHeadDto dto = bizIAuxmatForContractHeadDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizIAuxmatForContractHeadDtos;
    }

    /**
     * 确认数据状态
     * @param bizIAuxmatForContractHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject confirmDataStatus(BizIAuxmatForContractHeadParam bizIAuxmatForContractHeadParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, XdoI18nUtil.t("确认成功"));
        BizIAuxmatForContractHead bizIAuxmatForContractHead = bizIAuxmatForContractHeadMapper.selectByPrimaryKey(bizIAuxmatForContractHeadParam.getId());
        if (bizIAuxmatForContractHead == null) {
            throw new ErrorException(400, "辅料合同数据不存在，请刷新");
        }
        if (CommonEnum.OrderStatusEnum.CONFIRMED.getValue().equals(bizIAuxmatForContractHead.getDocStatus())){
            throw new ErrorException(400, "该数据已经确认，无需重复操作");
        }
        bizIAuxmatForContractHead.setDocStatus(CommonEnum.OrderStatusEnum.CONFIRMED.getValue());
        bizIAuxmatForContractHead.setUpdateBy(userInfo.getUserNo());
        bizIAuxmatForContractHead.setUpdateTime(new Date());
        bizIAuxmatForContractHead.setConfirmDate(new Date());
        bizIAuxmatForContractHeadMapper.updateByPrimaryKey(bizIAuxmatForContractHead);
        return result;
    }

    /**
     * 发送审核
     * @param bizIAuxmatForContractHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject sendAudit(BizIAuxmatForContractHeadParam bizIAuxmatForContractHeadParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, XdoI18nUtil.t("发送审核成功"));
        BizIAuxmatForContractHead bizIAuxmatForContractHead = bizIAuxmatForContractHeadMapper.selectByPrimaryKey(bizIAuxmatForContractHeadParam.getId());
        if (bizIAuxmatForContractHead == null) {
            throw new ErrorException(400, "辅料合同数据不存在，请刷新");
        }
        if (!CommonEnum.OrderStatusEnum.CONFIRMED.getValue().equals(bizIAuxmatForContractHead.getDocStatus())){
            throw new ErrorException(400, "只有数据状态为1确认的数据允许操作发送审批");
        }
        if (!CommonEnum.OrderApprStatusEnum.NOT_INVOLVED.getValue().equals(bizIAuxmatForContractHead.getAuditStatus())){
            throw new ErrorException(400, "只有未审核数据允许操作发送审批");
        }
        bizIAuxmatForContractHead.setAuditStatus(CommonEnum.OrderApprStatusEnum.NOT_APPROVED.getValue());
        bizIAuxmatForContractHead.setUpdateBy(userInfo.getUserNo());
        bizIAuxmatForContractHead.setUpdateTime(new Date());
        bizIAuxmatForContractHeadMapper.updateByPrimaryKey(bizIAuxmatForContractHead);
        return result;
    }

    /**
     * 作废数据状态
     * @param bizIAuxmatForContractHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject cancelDataStatus(BizIAuxmatForContractHeadParam bizIAuxmatForContractHeadParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, XdoI18nUtil.t("作废成功"));
        BizIAuxmatForContractHead bizIAuxmatForContractHead = bizIAuxmatForContractHeadMapper.selectByPrimaryKey(bizIAuxmatForContractHeadParam.getId());
        if (bizIAuxmatForContractHead == null) {
            throw new ErrorException(400, "辅料合同数据不存在，请刷新");
        }
        if (CommonEnum.OrderApprStatusEnum.NOT_APPROVED.getValue().equals(bizIAuxmatForContractHead.getAuditStatus())){
            throw new ErrorException(400, "审批中的数据不允许作废");
        }
        bizIAuxmatForContractHead.setDocStatus(CommonEnum.OrderStatusEnum.CANCELLED.getValue());
        bizIAuxmatForContractHead.setUpdateBy(userInfo.getUserNo());
        bizIAuxmatForContractHead.setUpdateTime(new Date());
        bizIAuxmatForContractHeadMapper.updateByPrimaryKey(bizIAuxmatForContractHead);
        return result;
    }

    public ResultObject<List<BizIAuxmatForContractListDto>> getPlanListPaged(BizIAuxmatForContractListParam bizIAuxmatForContractListParam, PageParam pageParam) {
        // 启用分页查询
        BizIAuxmatForContractList bizIAuxmatForContractList = bizIAuxmatForContractListDtoMapper.toPo(bizIAuxmatForContractListParam);
        Page<BizIAuxmatForContractList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizIAuxmatForContractListMapper.getPlanListPaged(bizIAuxmatForContractList));
        List<BizIAuxmatForContractListDto> bizIAuxmatForContractListDtos = page.getResult().stream().map(head -> {
            BizIAuxmatForContractListDto dto = bizIAuxmatForContractListDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<BizIAuxmatForContractListDto>> paged = ResultObject.createInstance(bizIAuxmatForContractListDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
}
