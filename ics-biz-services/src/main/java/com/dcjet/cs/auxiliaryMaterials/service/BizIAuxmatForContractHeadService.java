package com.dcjet.cs.auxiliaryMaterials.service;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.auxiliaryMaterials.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.auxiliaryMaterials.dao.BizIAuxmatForContractHeadMapper;
import com.dcjet.cs.auxiliaryMaterials.mapper.BizIAuxmatForContractHeadDtoMapper;
import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatForContractHead;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-5-22
 */
@Service
public class BizIAuxmatForContractHeadService extends BaseService<BizIAuxmatForContractHead> {
    @Resource
    private BizIAuxmatForContractHeadMapper bizIAuxmatForContractHeadMapper;
    @Resource
    private BizIAuxmatForContractHeadDtoMapper bizIAuxmatForContractHeadDtoMapper;
    @Override
    public Mapper<BizIAuxmatForContractHead> getMapper() {
        return bizIAuxmatForContractHeadMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizIAuxmatForContractHeadParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizIAuxmatForContractHeadDto>> getListPaged(BizIAuxmatForContractHeadParam bizIAuxmatForContractHeadParam, PageParam pageParam) {
        // 启用分页查询
        BizIAuxmatForContractHead bizIAuxmatForContractHead = bizIAuxmatForContractHeadDtoMapper.toPo(bizIAuxmatForContractHeadParam);
        Page<BizIAuxmatForContractHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizIAuxmatForContractHeadMapper.getList(bizIAuxmatForContractHead));
        List<BizIAuxmatForContractHeadDto> bizIAuxmatForContractHeadDtos = page.getResult().stream().map(head -> {
            BizIAuxmatForContractHeadDto dto = bizIAuxmatForContractHeadDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizIAuxmatForContractHeadDto>> paged = ResultObject.createInstance(bizIAuxmatForContractHeadDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizIAuxmatForContractHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIAuxmatForContractHeadDto insert(BizIAuxmatForContractHeadParam bizIAuxmatForContractHeadParam, UserInfoToken userInfo) {
        BizIAuxmatForContractHead bizIAuxmatForContractHead = bizIAuxmatForContractHeadDtoMapper.toPo(bizIAuxmatForContractHeadParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizIAuxmatForContractHead.setId(sid);
        bizIAuxmatForContractHead.setCreateBy(userInfo.getUserNo());
        bizIAuxmatForContractHead.setCreateTime(new Date());
        // 新增数据
        int insertStatus = bizIAuxmatForContractHeadMapper.insert(bizIAuxmatForContractHead);
        return  insertStatus > 0 ? bizIAuxmatForContractHeadDtoMapper.toDto(bizIAuxmatForContractHead) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizIAuxmatForContractHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIAuxmatForContractHeadDto update(BizIAuxmatForContractHeadParam bizIAuxmatForContractHeadParam, UserInfoToken userInfo) {
        BizIAuxmatForContractHead bizIAuxmatForContractHead = bizIAuxmatForContractHeadMapper.selectByPrimaryKey(bizIAuxmatForContractHeadParam.getSid());
        bizIAuxmatForContractHeadDtoMapper.updatePo(bizIAuxmatForContractHeadParam, bizIAuxmatForContractHead);
        bizIAuxmatForContractHead.setUpdateBy(userInfo.getUserNo());
        bizIAuxmatForContractHead.setUpdateTime(new Date());
        // 更新数据
        int update = bizIAuxmatForContractHeadMapper.updateByPrimaryKey(bizIAuxmatForContractHead);
        return update > 0 ? bizIAuxmatForContractHeadDtoMapper.toDto(bizIAuxmatForContractHead) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		bizIAuxmatForContractHeadMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizIAuxmatForContractHeadDto> selectAll(BizIAuxmatForContractHeadParam exportParam, UserInfoToken userInfo) {
        BizIAuxmatForContractHead bizIAuxmatForContractHead = bizIAuxmatForContractHeadDtoMapper.toPo(exportParam);
        // bizIAuxmatForContractHead.setTradeCode(userInfo.getCompany());
        List<BizIAuxmatForContractHeadDto> bizIAuxmatForContractHeadDtos = new ArrayList<>();
        List<BizIAuxmatForContractHead> bizIAuxmatForContractHeads = bizIAuxmatForContractHeadMapper.getList(bizIAuxmatForContractHead);
        if (CollectionUtils.isNotEmpty(bizIAuxmatForContractHeads)) {
            bizIAuxmatForContractHeadDtos = bizIAuxmatForContractHeads.stream().map(head -> {
                BizIAuxmatForContractHeadDto dto = bizIAuxmatForContractHeadDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizIAuxmatForContractHeadDtos;
    }
}
