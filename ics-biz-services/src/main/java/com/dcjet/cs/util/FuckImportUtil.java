package com.dcjet.cs.util;

import com.dcjet.cs.base.model.BasicImportModel;
import com.dcjet.cs.dto.base.BasicImportParam;
import com.xdo.dataimport.model.EnumCollection;
import com.xdo.dataimport.model.ExcelImportDto;
import com.xdo.dataimport.utils.XdoImportLogger;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class FuckImportUtil {

    public static  <E extends BasicImportModel> List<ExcelImportDto> fuckCheckDataByModel (
            Map<String, List<Object>> mapObjectList,
            Function<List<Object>, List<E>> function) {
        if (mapObjectList.size() <= 0) {
            XdoImportLogger.log("导入数据序列化的实体列表集合中不存在数据");
            return null;
        }

        List<Object> objectList = mapObjectList.entrySet().iterator().next().getValue();
        int intObjectListSize = objectList.size();
        XdoImportLogger.log("共获取实体：" + intObjectListSize);

        // 0：正确数据 1：错误数据 2：警告数据 对应于EnumCollection.EntityListType的枚举的ordinal
        final int CORRECT_FLAG = EnumCollection.EntityListType.CORRECT_LIST.ordinal();
        final int WRONG_FLAG = EnumCollection.EntityListType.WRONG_LIST.ordinal();

        List<E> list = function.apply(objectList);
        XdoImportLogger.log(String.format("共获取插入实体：[%d]执行快速入库操作", list.size()));

        ExcelImportDto<E> excelImportDto = new ExcelImportDto<>();
        List<E> correctList = list.stream().filter(it -> it.getTempFlag() == CORRECT_FLAG).collect(Collectors.toList());
        excelImportDto.setCorrectNumber(correctList.size());
        excelImportDto.setCorrectList(correctList);

        List<E> wrongList = list.stream().filter(it -> it.getTempFlag() == WRONG_FLAG).collect(Collectors.toList());
        excelImportDto.setWrongNumber(wrongList.size());
        if (wrongList.size() > 0 && correctList.size() > 0) {
            wrongList.addAll(correctList);
        }
        excelImportDto.setWrongList(wrongList);
        XdoImportLogger.log(String.format("correct：[%d];wrong：[%d]", excelImportDto.getCorrectList().size(), excelImportDto.getWrongList().size()));

        List<ExcelImportDto> excelImportDtoList = new ArrayList<>();
        excelImportDtoList.add(excelImportDto);

        return excelImportDtoList;
    }

    public static  <E extends BasicImportParam> List<ExcelImportDto> fuckCheckDataByParam (
            Map<String, List<Object>> mapObjectList,
            Function<List<Object>, List<E>> function) {
        if (mapObjectList.size() <= 0) {
            XdoImportLogger.log("导入数据序列化的实体列表集合中不存在数据");
            return null;
        }

        List<Object> objectList = mapObjectList.entrySet().iterator().next().getValue();
        int intObjectListSize = objectList.size();
        XdoImportLogger.log("共获取实体：" + intObjectListSize);

        // 0：正确数据 1：错误数据 2：警告数据 对应于EnumCollection.EntityListType的枚举的ordinal
        final int CORRECT_FLAG = EnumCollection.EntityListType.CORRECT_LIST.ordinal();
        final int WRONG_FLAG = EnumCollection.EntityListType.WRONG_LIST.ordinal();

        List<E> list = function.apply(objectList);
        XdoImportLogger.log(String.format("共获取插入实体：[%d]执行快速入库操作", list.size()));

        ExcelImportDto<E> excelImportDto = new ExcelImportDto<>();
        List<E> correctList = list.stream().filter(it -> it.getTempFlag() == CORRECT_FLAG).collect(Collectors.toList());
        excelImportDto.setCorrectNumber(correctList.size());
        excelImportDto.setCorrectList(correctList);

        List<E> wrongList = list.stream().filter(it -> it.getTempFlag() == WRONG_FLAG).collect(Collectors.toList());
        excelImportDto.setWrongNumber(wrongList.size());
        if (wrongList.size() > 0 && correctList.size() > 0) {
            wrongList.addAll(correctList);
        }
        excelImportDto.setWrongList(wrongList);
        XdoImportLogger.log(String.format("correct：[%d];wrong：[%d]", excelImportDto.getCorrectList().size(), excelImportDto.getWrongList().size()));

        List<ExcelImportDto> excelImportDtoList = new ArrayList<>();
        excelImportDtoList.add(excelImportDto);

        return excelImportDtoList;
    }
}


