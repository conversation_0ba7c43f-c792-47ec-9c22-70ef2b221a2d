package com.dcjet.cs.util;

import com.dcjet.cs.util.variable.CommonVariable;
import org.apache.commons.httpclient.DefaultHttpMethodRetryHandler;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.commons.httpclient.methods.GetMethod;
import org.apache.commons.httpclient.params.HttpMethodParams;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;
import java.io.*;
import java.lang.invoke.MethodHandles;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.util.Map;
import java.util.zip.GZIPInputStream;

/**
 * @Auther: zhuhui
 * @Date: 2019/4/26 15:41
 * @Description:
 */
//TODO:新版需要
public class RequestUtil {
    private static final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    public static String sendPost (String url,String token,String param) throws Exception {
       return sendPost(url,token,param,"application/json", CommonVariable.UTF8);
    }

    public static String sendGet (String url,String token) throws Exception {
        return sendGet(url,token,null,CommonVariable.UTF8);
    }

    public static String sendGet (String url,String token,String param) throws Exception {
        return sendGet(url,token,param,CommonVariable.UTF8);
    }

    public static String sendPost(String url, String token, String param, String contentType, String encode) throws Exception {
        PrintWriter out = null;
        BufferedReader in = null;
        String result = "";

        try {
            URL realUrl = new URL(url);
            URLConnection connection = realUrl.openConnection();
            connection.setRequestProperty("accept", "*/*");
            connection.setRequestProperty("connection", "Keep-Alive");
            connection.setRequestProperty("Content-Type", contentType + ";" + encode);
            if(token != null){
                connection.setRequestProperty("Authorization","Bearer "+token);
            }

            connection.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            connection.setDoOutput(true);
            connection.setDoInput(true);

//            // 通过远程url连接对象打开连接
//            HttpURLConnection connection = (HttpURLConnection) realUrl.openConnection();
//            // 设置连接请求方式
//            connection.setRequestMethod("POST");
//            // 设置连接主机服务器超时时间：15000毫秒
//            connection.setConnectTimeout(15000);
//            // 设置读取主机服务器返回数据超时时间：60000毫秒
//            connection.setReadTimeout(60000);
//
//            // 默认值为：false，当向远程服务器传送数据/写数据时，需要设置为true
//            connection.setDoOutput(true);
//            // 默认值为：true，当前向远程服务读取数据时，设置为true，该参数可有可无
//            connection.setDoInput(true);
//            // 设置传入参数的格式:请求参数应该是 name1=value1&name2=value2 的形式。
//            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
//            // 设置鉴权信息：Authorization: Bearer da3efcbf-0845-4fe3-8aba-ee040be542c0
//            connection.setRequestProperty("Authorization", "Bearer " + token);

            out = new PrintWriter(new OutputStreamWriter(connection.getOutputStream(), encode));
            out.print(param);
            out.flush();
            String contentEncoding = connection.getHeaderField("Content-Encoding");
            if (StringUtils.isNotBlank(contentEncoding) && "gzip".equals(contentEncoding.toLowerCase())) {
                GZIPInputStream gzipInputStream = new GZIPInputStream(connection.getInputStream());
                in = new BufferedReader(new InputStreamReader(gzipInputStream, encode));
            } else {
                in = new BufferedReader(new InputStreamReader(connection.getInputStream(), encode));
            }

            String line;
            while ((line = in.readLine()) != null) {
                result = result + line;
            }
        } catch (Exception var18) {
            var18.printStackTrace();
            logger.error("POST 请求异常：",var18);
            throw var18;
        } finally {
            try {
                if (out != null) {
                    out.close();
                }

                if (in != null) {
                    in.close();
                }
            } catch (Exception var17) {
                var17.printStackTrace();
                logger.error("流关闭异常：",var17);
                throw var17;
            }

        }

        return result;
    }

    public static String sendPost(String url, String token, String param, String contentType, String encode,Map<String,String> otherHeader) throws Exception {
        PrintWriter out = null;
        BufferedReader in = null;
        String result = "";

        try {
            URL realUrl = new URL(url);
            URLConnection connection = realUrl.openConnection();
            connection.setRequestProperty("accept", "*/*");
            connection.setRequestProperty("connection", "Keep-Alive");
            connection.setRequestProperty("Content-Type", contentType + ";" + encode);
            if(token != null){
                connection.setRequestProperty("Authorization","Bearer "+token);
            }
            if(otherHeader!=null)
            {
                for(Map.Entry<String, String> entry : otherHeader.entrySet()){
                    if(entry.getValue()!="" || entry.getValue()!=null)
                    {
                        connection.setRequestProperty(entry.getKey(), entry.getValue());
                    }
                }
            }
            connection.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            connection.setDoOutput(true);
            connection.setDoInput(true);

//            // 通过远程url连接对象打开连接
//            HttpURLConnection connection = (HttpURLConnection) realUrl.openConnection();
//            // 设置连接请求方式
//            connection.setRequestMethod("POST");
//            // 设置连接主机服务器超时时间：15000毫秒
//            connection.setConnectTimeout(15000);
//            // 设置读取主机服务器返回数据超时时间：60000毫秒
//            connection.setReadTimeout(60000);
//
//            // 默认值为：false，当向远程服务器传送数据/写数据时，需要设置为true
//            connection.setDoOutput(true);
//            // 默认值为：true，当前向远程服务读取数据时，设置为true，该参数可有可无
//            connection.setDoInput(true);
//            // 设置传入参数的格式:请求参数应该是 name1=value1&name2=value2 的形式。
//            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
//            // 设置鉴权信息：Authorization: Bearer da3efcbf-0845-4fe3-8aba-ee040be542c0
//            connection.setRequestProperty("Authorization", "Bearer " + token);

            out = new PrintWriter(new OutputStreamWriter(connection.getOutputStream(), encode));
            out.print(param);
            out.flush();
            String contentEncoding = connection.getHeaderField("Content-Encoding");
            if (StringUtils.isNotBlank(contentEncoding) && "gzip".equals(contentEncoding.toLowerCase())) {
                GZIPInputStream gzipInputStream = new GZIPInputStream(connection.getInputStream());
                in = new BufferedReader(new InputStreamReader(gzipInputStream, encode));
            } else {
                in = new BufferedReader(new InputStreamReader(connection.getInputStream(), encode));
            }

            String line;
            while ((line = in.readLine()) != null) {
                result = result + line;
            }
        } catch (Exception var18) {
            var18.printStackTrace();
            logger.error("POST 请求异常：",var18);
            throw var18;
        } finally {
            try {
                if (out != null) {
                    out.close();
                }

                if (in != null) {
                    in.close();
                }
            } catch (Exception var17) {
                var17.printStackTrace();
                logger.error("流关闭异常：",var17);
                throw var17;
            }

        }

        return result;
    }

    public static String sendGet(String url,String token, String param, String encode) throws Exception {

        return sendGet(url,token,param,encode,null);
    }


    public static String sendGet(String url, String token, String param, String encode,Map<String,String> otherHeader) throws Exception {
        // 输入流
        InputStream is = null;
        BufferedReader br = null;
        String result = null;
        // 创建httpClient实例
        HttpClient httpClient = new HttpClient();
        // 设置http连接主机服务超时时间：15000毫秒
        // 先获取连接管理器对象，再获取参数对象,再进行参数的赋值
        httpClient.getHttpConnectionManager().getParams().setConnectionTimeout(15000);
        // 创建一个Get方法实例对象
        GetMethod getMethod;
        if(StringUtils.isNotBlank(param)) {
            getMethod = new GetMethod(url + "?" + param);
        }else {
            getMethod = new GetMethod(url);
        }
        getMethod.setRequestHeader("accept", "*/*");
        getMethod.setRequestHeader("Authorization","Bearer "+token);
        getMethod.setRequestHeader("connection", "Keep-Alive");
        getMethod.setRequestHeader("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
        getMethod.setRequestHeader("Content-Type", "application/json;" + encode);
        if(otherHeader!=null)
        {
            for(Map.Entry<String, String> entry : otherHeader.entrySet()){
                if(entry.getValue()!="" || entry.getValue()!=null)
                {
                    getMethod.setRequestHeader(entry.getKey(), entry.getValue());
                }
            }
        }

        // 设置get请求超时为60000毫秒
        getMethod.getParams().setParameter(HttpMethodParams.SO_TIMEOUT, 60000);
        // 设置请求重试机制，默认重试次数：3次，参数设置为true，重试机制可用，false相反
        getMethod.getParams().setParameter(HttpMethodParams.RETRY_HANDLER, new DefaultHttpMethodRetryHandler(3, true));
        try {
            // 执行Get方法
            int statusCode = httpClient.executeMethod(getMethod);
            // 判断返回码
            if (statusCode != HttpStatus.SC_OK) {
                // 如果状态码返回的不是ok,说明失败了,打印错误信息
                System.err.println("Method faild: " + getMethod.getStatusLine());
            } else {
                // 通过getMethod实例，获取远程的一个输入流
                is = getMethod.getResponseBodyAsStream();
                // 包装输入流
                br = new BufferedReader(new InputStreamReader(is, CommonVariable.UTF8));

                StringBuffer sbf = new StringBuffer();
                // 读取封装的输入流
                String temp = null;
                while ((temp = br.readLine()) != null) {
                    sbf.append(temp).append("\r\n");
                }

                result = sbf.toString();
            }

        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            // 关闭资源
            if (null != br) {
                try {
                    br.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (null != is) {
                try {
                    is.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            // 释放连接
            getMethod.releaseConnection();
        }
        return result;
    }

    public static String sendPostByXML (String url,String token,String param) throws Exception {
        return sendPost(url,token,param,"application/xml",CommonVariable.UTF8);
    }

    /**
     * 将对象直接转换成String类型的 XML输出
     *
     * @param obj
     * @return
     */
    public static String convertToXml(Object obj) {
        // 创建输出流
        StringWriter sw = new StringWriter();
        try {
            // 利用jdk中自带的转换类实现
            JAXBContext context = JAXBContext.newInstance(obj.getClass());
            Marshaller marshaller = context.createMarshaller();
            // 格式化xml输出的格式
            marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT,
                    Boolean.TRUE);
            // 将对象转换成输出流形式的xml
            marshaller.marshal(obj, sw);
        } catch (JAXBException e) {
            e.printStackTrace();
        }
        return sw.toString();
    }

    @SuppressWarnings("unchecked")
    /**
     * 将String类型的xml转换成对象
     */
    public static Object convertXmlStrToObject(Class clazz, String xmlStr) {
        Object xmlObject = null;
        try {
            JAXBContext context = JAXBContext.newInstance(clazz);
            // 进行将Xml转成对象的核心接口
            Unmarshaller unmarshaller = context.createUnmarshaller();
            StringReader sr = new StringReader(xmlStr);
            xmlObject = unmarshaller.unmarshal(sr);
        } catch (JAXBException e) {
            e.printStackTrace();
        }
        return xmlObject;
    }

    /**
     * 下载文件并返回绝对路径
     * @param downloadUrl
     * @param savePath
     * @param fileName
     * @throws Exception
     */
    public static String downloadFile(String downloadUrl,String savePath,String fileName) throws Exception {
        URL url = new URL(downloadUrl);
        HttpURLConnection conn = (HttpURLConnection)url.openConnection();
        //设置超时间为3秒
        conn.setConnectTimeout(3*1000);
        //防止屏蔽程序抓取而返回403错误
        conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
        //得到输入流
        InputStream inputStream = conn.getInputStream();
        //获取自己数组
        byte[] getData = readInputStream(inputStream);
        //文件保存位置
        File saveDir = new File(savePath);
        if(!saveDir.exists()) {
            saveDir.mkdir();
        }
        String path = saveDir + File.separator + fileName;
        File file = new File(path);
        FileOutputStream fos = new FileOutputStream(file);
        fos.write(getData);
        if(fos != null) {
            fos.close();
        }
        if(inputStream!=null){
            inputStream.close();
        }
        System.out.println("info:"+url+" download success");
        return path;
    }

    /**
     * 从输入流中获取字节数组
     * @param inputStream
     * @return
     * @throws IOException
     */
    public static  byte[] readInputStream(InputStream inputStream) throws IOException {
        byte[] buffer = new byte[1024];
        int len;
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        while((len = inputStream.read(buffer)) != -1) {
            bos.write(buffer, 0, len);
        }
        bos.close();
        return bos.toByteArray();
    }

}
