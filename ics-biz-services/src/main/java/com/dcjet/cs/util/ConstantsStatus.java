package com.dcjet.cs.util;

/**
 * <AUTHOR>
 * @date: 2019-4-17
 */
public class ConstantsStatus {


    public final static String STATUS_0 = "0";
    public final static String STATUS_1 = "1";
    public final static String STATUS_2 = "2";
    public final static String STATUS_3 = "3";
    public final static String STATUS_4 = "4";
    public final static String STATUS_5 = "5";
    public final static String STATUS_6 = "6";
    public final static String STATUS_7 = "7";
    public final static String STATUS_8 = "8";
    public final static String STATUS_9 = "9";
    public final static String STATUS_B = "-1";

    //进口发送
    public final static String TYPE_I = "I";
    //出口发送
    public final static String TYPE_E = "E";
    //减免税
    public final static String TYPE_T = "T";
    //内审可发送状态
    public  final static  String[] SEND_STATUS_IE=new String[]{"-1","0","1","JC","9"};
    //内审可撤回状态
    public  final static  String[] RETURN_STATUS=new String[]{"2","8"};

    public final static String AUTH_TYPE_Y = "Y";
    public final static String AUTH_TYPE_N = "N";

    //调用接口返回状态
    public final static String RETURN_STATUS_3 = "3";
    public final static String RETURN_STATUS_4 = "4";

    //税单下载申请失败，且接口返回状态是3003（报文中心在下载中，但关务重复发起申请）
    public final static String STATUS_3003 = "3003";

    //代理相关
    //报关行权限,包含 预录入单制单,报关追踪维护。格式：0,1(未授权/授权)
    public final static String DECLARE_AUTH_1 = "1";
    public final static String DECLARE_AUTH_2 = "2";
    public final static String DECLARE_AUTH_3 = "3";
    public final static String DECLARE_AUTH_4 = "4";
    public final static String DECLARE_AUTH_5 = "5";
    public final static String DECLARE_AUTH_6 = "6";
    public final static String DECLARE_AUTH_7 = "7";
    public final static String DECLARE_AUTH_8 = "8";

    //货代权限,包含 预录入单制单,物流追踪维护。格式：0,1...(未授权/授权)
    public final static String FORWARD_AUTH_1 = "1";
    public final static String FORWARD_AUTH_2 = "2";
    public final static String FORWARD_AUTH_3 = "3";
    public final static String FORWARD_AUTH_4 = "4";
    public final static String FORWARD_AUTH_5 = "5";
    public final static String FORWARD_AUTH_6 = "6";

    //费用相关
    // 计算类型（1 一般报价单票数 2 一般报价单计费重量 3 一般报价单车柜型 4 天/重量计 5 天/体积计 6 体积 7 快件报价单）
    public final static String CAL_TYPE_1 = "1";
    public final static String CAL_TYPE_3 = "3";
    public final static String CAL_TYPE_7 = "7";

    public  final static  String[] TRANS_MODE_COST=new String[]{"1", "3","7"};
    public  final static  String[] TRANS_MODE_COST2=new String[]{"3","7"};

    //UPS国别对应区
    public final static String REGION_AU_5 = "AU-5区";
    public final static String REGION_CA_6 = "CA-6区";
    public final static String REGION_EU1_7 = "EU1-7区";
    public final static String REGION_EU2_7 = "EU2-7区";
    public final static String REGION_HK_1 = "HK-1区";
    public final static String REGION_JP_3 = "JP-3区";
    public final static String REGION_KR_2 = "KR-2区";
    public final static String REGION_MX_6 = "MX-6区";
    public final static String REGION_MY_4 = "MY-4区";
    public final static String REGION_PH_4 = "PH-4区";
    public final static String REGION_SG_4 = "SG-4区";
    public final static String REGION_TH_4 = "TH-4区";
    public final static String REGION_TW_2 = "TW-2区";
    public final static String REGION_US_6 = "US-6区";
    public final static String REGION_VN_4 = "VN-4区";

    /** 出口计划可修改删除状态 */
    public final static String[] EXPORT_PLAN_STATUS = new String[]{"0", "5"};

    //免表 概要申报状态
    public final static String STATUS_01 ="01"; // 未发送
    public final static String STATUS_02 ="02"; // 发送成功
    public final static String STATUS_03 ="03"; // 发送失败
    //进口清关表可修改状态
    public  final static  String[] DEC_I_PLAN_STATUS = new String[]{"0","1"};
}
