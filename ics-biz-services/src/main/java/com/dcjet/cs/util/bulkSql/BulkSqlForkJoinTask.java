package com.dcjet.cs.util.bulkSql;

import java.lang.reflect.Method;
import java.util.List;
import java.util.concurrent.RecursiveTask;
import java.util.function.Function;

public class BulkSqlForkJoinTask<M> extends RecursiveTask<Integer> {
    private BulkSqlOpt bulkSqlOpt;

    private List<M> ls;

    private Function function;

    private Method method;

    public BulkSqlForkJoinTask(BulkSqlOpt bulkSqlOpt, Method method, List<M> ls, Function function) {
        this.bulkSqlOpt = bulkSqlOpt;
        this.method = method;
        this.ls = ls;
        this.function = function;
    }

    @Override
    protected Integer compute() {
        if(ls.size() <= 10) {
            Integer result;
            try {
                result = (Integer)method.invoke(bulkSqlOpt, ls, function);
            } catch (Exception e) {
                return -2;
            }
            return result;
        } else {
            int mid = ls.size()/2;
            BulkSqlForkJoinTask leftTask = new BulkSqlForkJoinTask(bulkSqlOpt, method, ls.subList(0, mid), function);
            BulkSqlForkJoinTask rightTask = new BulkSqlForkJoinTask(bulkSqlOpt, method, ls.subList(mid, ls.size()), function);
            invokeAll(leftTask,rightTask);
            return (Integer) leftTask.join() + (Integer) rightTask.join();
        }
    }
}
