package com.dcjet.cs.util.pojo;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SftpDirModel {

    private String tempFilePath;//本地临时文件目录
    private String rootPath;//SFTP根目录
    private String receiveDir;//包含根目录的RECEIVE目录
    private String allDir;//不包含根目录的ALL目录
    private String fullAllDir;//包含根目录的ALL目录
    private String successDir;//不包含根目录的SUCCESS目录
    private String fullSuccessDir;//包含根目录的SUCCESS目录
    private String errorDir;//不包含根目录的ERROR目录
    private String fullErrorDir;//包含根目录的ERROR目录
}
