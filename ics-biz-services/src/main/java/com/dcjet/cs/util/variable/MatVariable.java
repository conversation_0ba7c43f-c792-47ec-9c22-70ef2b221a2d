package com.dcjet.cs.util.variable;

/**
 * 功能描述 物料中心静态变量
 * <AUTHOR>
 * @date 2021-05-06
 * @version 1.0
 * @return
*/
public class MatVariable {
    public final static String STATUS_0 = "0";
    public final static String STATUS_1 = "1";
    public final static String STATUS_2 = "2";
    public final static String STATUS_3 = "3";
    public final static String STATUS_5 = "5";
    //物料备案-料件
    public final static String APPR_TYPE_OI = "OI";
    //物料备案-成品
    public final static String APPR_TYPE_OE = "OE";
    //商品归类-料件
    public final static String APPR_TYPE_YI = "YI";
    //商品归类-成品
    public final static String APPR_TYPE_YE = "YE";
    //商品归类-非保税
    public final static String APPR_TYPE_YM = "YM";

    //数据来源 0 新增
    public final static String DATS_SOURCE_0 = "0";
    //数据来源 1 导入
    public final static String DATS_SOURCE_1 = "1";
    //数据来源 2 导入(待归类)
    public final static String DATS_SOURCE_2 = "2";

    //单耗申报状态 1 已申报 2 未申报
    public final static String UCNS_DCL_1 = "1";
    public final static String UCNS_DCL_2 = "2";

    //是否生效标志 1：生效  0：不生效
    public final static String VALID_MARK_0 = "0";
    public final static String VALID_MARK_1 = "1";
    //归类标记 1-主料件 2-辅料件；
    public final static String CLASS_MARK_1 = "1";

    //备案删除状态
    public  final static  String[] DELETE_STATUS=new String[]{"0", "8","-1"};
    public  final static  String[] DELETE_STATUS_EMSHEAD=new String[]{"0", "8","-1","JD"};
    //备案恢复状态
    public  final static  String[] ROBACK_STATUS=new String[]{"0", "-1", "JD"};
    public  final static  String[] ROBACK_STATUS_ORG=new String[]{"2", "8", "JA", "JC"};
    //备案内审可发送状态
    public  final static  String[] SEND_STATUS=new String[]{"-1","0","1","JC","JD"};
    public  final static  String[] SEND_STATUS_EMSHEAD=new String[]{"8","J1"};
    public  final static  String[] FILTER_STATUS_CLASSIFY=new String[]{"1", "2"};
    //可修改状态
    public  final static  String[] UPDATE_STATUS=new String[]{"0", "8","-1","JD","JC","J1"};
    //单损耗内审可发送状态
    public  final static  String[] SEND_STATUS_APPR=new String[]{"0", "-1", "JD"};
    //企业内审可发送状态
    public  final static  String[] SEND_STATUS_Y=new String[]{"-1","0"};
    //企业数据可删除状态
    public  final static  String[] DELETE_STATUS_Y=new String[]{"0","-1"};
    /**
     * 通用小数位
     */
    public final static Integer COMM_SCALE = 5;
    //净重小数位
    public final static Integer NET_SCALE = 8;
    //净重小数位
    public final static Integer STR_LENG_255 = 255;
    public final static Integer STR_LENG_50 = 50;

}
