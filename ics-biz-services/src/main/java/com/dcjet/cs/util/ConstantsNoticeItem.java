package com.dcjet.cs.util;

/**
 * 预警通知邮件类型
 */
public class ConstantsNoticeItem {

    /**
     * 进口预报单已分单
     */
    public final static String DEC_I_SENDED = "DEC_I_SENDED";
    /**
     * 进口非保税预录入单已接受委托
     */
    public final static String DEC_I_RECEIVED = "DEC_I_RECEIVED";
    /**
     * 进口报关单草单已发送
     */
    public final static String ENTRY_I_SENDED = "ENTRY_I_SENDED";
    /**
     * 接收到进口申报前草单
     */
    public final static String ENTRY_I_RECEIVED = "ENTRY_I_RECEIVED";
    /**
     * 出口报关单草单已发送
     */
    public final static String ENTRY_E_SENDED = "ENTRY_E_SENDED";
    /**
     * 接收到出口申报前草单
     */
    public final static String ENTRY_E_RECEIVED = "ENTRY_E_RECEIVED";
    /**
     * 进口报关单复核退回
     */
    public final static String ENTRY_I_NO_PASS = "ENTRY_I_NO_PASS";
    /**
     * 进口报关单复核通过
     */
    public final static String ENTRY_I_PASS = "ENTRY_I_PASS";
    /**
     * 出口报关单复核退回
     */
    public final static String ENTRY_E_NO_PASS = "ENTRY_E_NO_PASS";
    /**
     * 出口报关单复核通过
     */
    public final static String ENTRY_E_PASS = "ENTRY_E_PASS";

}
