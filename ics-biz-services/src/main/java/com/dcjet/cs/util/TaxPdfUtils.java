package com.dcjet.cs.util;


import org.apache.pdfbox.pdmodel.PDDocument;
import org.springframework.web.multipart.MultipartFile;
import technology.tabula.*;
import technology.tabula.extractors.BasicExtractionAlgorithm;
import technology.tabula.extractors.SpreadsheetExtractionAlgorithm;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.File;
import java.io.InputStream;
import java.util.*;

import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

public class TaxPdfUtils {

    // 预定义所有需要提取的字段名（左侧列内容）
    private static final List<String> DETAIL_FIELDS_LEFT = new ArrayList<>(Arrays.asList(
            "年份","税费单详细信息","报关单号", "税种", "申报口岸", "收发货单位", "申报单位", "运输工具号",
            "监管方式", "进/出口日期", "退补税标志", "税款金额", "征税操作员", "缴款期限",
            "收入系统", "预算科目名称"
    ));

    private static final List<String> DETAIL_FIELDS_RIGHT = new ArrayList<>(Arrays.asList(
            "x","y","税单序号", "现场税单序", "进出口岸", "消费使用单位",
            "提单号", "合同号", "征免性质", "进出口标志", "滞报滞纳标志", "税款金额大写",
            "税单开征日期", "收入机关", "预算级次", "收款国库"
    ));

    private static final List<String> GOODS_DETAIL_FIELDS = new ArrayList<>(Arrays.asList(
            "codeTs","gName","quantity", "unit", "curr","exchangeRate", "dutyValue", "valoremRate", "qtyRate",
            "taxAmount","X"
    ));


    public static void main(String[] args) {
        try {
            ObjectExtractor oe = new ObjectExtractor(PDDocument.load(new File("/Users/<USER>/Downloads/MTCSHHM650F023税单.pdf")));
            PageIterator pages = oe.extract();
            SpreadsheetExtractionAlgorithm sea = new SpreadsheetExtractionAlgorithm();

            BasicExtractionAlgorithm bea = new BasicExtractionAlgorithm();


            List<Map<String, Object>> result = new ArrayList<>();

            while (pages.hasNext()) {
                Page page = pages.next();
                List<Table> tables = sea.extract(page);

                List<Table> tables1 = bea.extract(page);

                if (tables.isEmpty()) continue;

                // 解析详细信息（第一张表）
                Map<String, String> details = parseDetails(tables.get(0));
                String taxType = details.getOrDefault("税种", "未知税种");

                // 解析货物信息（后续表格）
                // 在main方法中替换原有表格处理
                List<RectangularTextContainer> allCells = new ArrayList<>();
                tables1.forEach(t -> t.getRows().forEach(r -> allCells.addAll(r)));

                List<Map<String, Object>> goodsList = parseGoodsNew(allCells);

                // 处理未知税种合并逻辑
                if ("未知税种".equals(taxType) && !result.isEmpty()) {
                    // 合并到上一个税种的list
                    Map<String, Object> lastTaxData = result.get(result.size() - 1);
                    List<Map<String, Object>> lastList = (List<Map<String, Object>>) lastTaxData.get("list");
                    lastList.addAll(goodsList);
                } else {
                    // 创建新的税种条目（确保list可修改）
                    Map<String, Object> taxData = new LinkedHashMap<>();
                    taxData.put("taxType", taxType);
                    taxData.put("head", details);
                    // 使用可变的ArrayList替代可能不可变的Collections.emptyList()
                    taxData.put("list", new ArrayList<>(goodsList));
                    result.add(taxData);
                }
            }

            ObjectMapper mapper = new ObjectMapper();
            String json = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(result);
            System.out.println(json);

            oe.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static String parseTaxInvoice(MultipartFile file) throws Exception {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("上传文件不能为空");
        }
        ObjectExtractor oe = null;
        PDDocument doc = null;

        try (InputStream is = file.getInputStream()) {
            // 读取PDF文档
            doc = PDDocument.load(is);
            oe = new ObjectExtractor(doc);

            PageIterator pages = oe.extract();
            List<Map<String, Object>> result = new ArrayList<>();
            SpreadsheetExtractionAlgorithm sea = new SpreadsheetExtractionAlgorithm();
            BasicExtractionAlgorithm bea = new BasicExtractionAlgorithm();

            while (pages.hasNext()) {
                Page page = pages.next();
                List<Table> tables = sea.extract(page);
                if (tables.isEmpty()) continue;

                // 解析基础信息
                Map<String, String> details = parseDetails(tables.get(0));
                String taxType = details.getOrDefault("税种", "未知税种");
                String taxAmount = details.getOrDefault("税款金额", "未知金额");


                // 解析货物表格
                List<Table> dataTables = bea.extract(page);
                List<RectangularTextContainer> allCells = new ArrayList<>();
                dataTables.forEach(t -> t.getRows().forEach(r -> allCells.addAll(r)));
                List<Map<String, Object>> goodsList = parseGoodsNew(allCells);

                // 处理未知税种合并逻辑
                if ("未知税种".equals(taxType) && !result.isEmpty()) {
                    // 合并到上一个税种的list
                    Map<String, Object> lastTaxData = result.get(result.size() - 1);
                    List<Map<String, Object>> lastList = (List<Map<String, Object>>) lastTaxData.get("list");
                    lastList.addAll(goodsList);
                } else {
                    // 创建新的税种条目（确保list可修改）
                    Map<String, Object> taxData = new LinkedHashMap<>();
                    taxData.put("taxType", taxType);
                    taxData.put("taxAmount", taxAmount);
                    taxData.put("head", details);
                    // 使用可变的ArrayList替代可能不可变的Collections.emptyList()
                    taxData.put("list", new ArrayList<>(goodsList));
                    result.add(taxData);
                }
            }

            // 转换为JSON字符串
            return new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(result);
        } finally {
            // 确保资源关闭
            if (oe != null) oe.close();
            if (doc != null) doc.close();
        }
    }


    // 解析两列式详细信息表格
    private static Map<String, String> parseDetails(Table table) {
        Map<String, String> details = new LinkedHashMap<>();
        List<List<RectangularTextContainer>> rows = table.getRows();

        for (int i = 0; i < rows.size(); i++) {
            if (rows.get(i).size() < 2) continue; // 忽略不完整行

            // 左侧单元格：字段名 | 右侧单元格：字段值
            String fieldName = rows.get(i).get(0).getText().replaceAll("\\s+", " ").trim();
            String fieldValue = rows.get(i).get(1).getText().replaceAll("\\s+", " ").trim();

            details.put(DETAIL_FIELDS_LEFT.get(i), fieldName);
            details.put(DETAIL_FIELDS_RIGHT.get(i), fieldValue);
        }

        return details;
    }

    //解析货物信息
    private static List<Map<String, Object>> parseGoodsNew(List<RectangularTextContainer> cells) {
        List<Map<String, Object>> goodsList = new ArrayList<>();
        final double ERROR_RANGE = 5.0; // 允许的误差范围
        AtomicReference<Double> yFlag = new AtomicReference<>(Double.MAX_VALUE);

        // 按Y坐标升序排序
        List<RectangularTextContainer> sortedCells = cells.stream()
                .sorted(Comparator.comparingDouble(RectangularTextContainer::getY))
                .collect(Collectors.toList());

        // 动态分组逻辑
        List<List<RectangularTextContainer>> rows = new ArrayList<>();
        if (!sortedCells.isEmpty()) {
            List<RectangularTextContainer> currentRow = new ArrayList<>();
            double baseY = sortedCells.get(0).getY();

            for (RectangularTextContainer cell : sortedCells) {
                if (Math.abs(cell.getY() - baseY) <= ERROR_RANGE) {
                    currentRow.add(cell);
                } else {
                    rows.add(currentRow);
                    currentRow = new ArrayList<>();
                    currentRow.add(cell);
                    baseY = cell.getY();
                }
            }
            rows.add(currentRow); // 添加最后一行
        }

        // 处理每一行数据
        for (List<RectangularTextContainer> row : rows) {
            Map<String, Object> goods = new LinkedHashMap<>();
            StringBuilder sb = new StringBuilder();
            boolean isTaxRateRow = false;

            // 按X坐标排序（左到右）
            row.sort(Comparator.comparingDouble(RectangularTextContainer::getX));

            for (RectangularTextContainer cell : row) {
                String text = cell.getText().trim();
                sb.append(text).append(" ");

                // 标记税率行
                if (text.matches("税率 ?税率|折算率|税率")) {
                    yFlag.set(cell.getY());
                    isTaxRateRow = true;
                    break;
                }
            }

            // 若当前行在税率行下方，则解析为货物信息
            if (!isTaxRateRow && row.get(0).getY() > yFlag.get()) {
                String[] parts = sb.toString().split("\\s+");
                for (int i = 0; i < parts.length && i < GOODS_DETAIL_FIELDS.size(); i++) {
                    goods.put(GOODS_DETAIL_FIELDS.get(i), parts[i]);
                }
                goodsList.add(goods);
            }
        }

        return goodsList;
    }

}
