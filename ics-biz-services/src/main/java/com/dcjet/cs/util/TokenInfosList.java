package com.dcjet.cs.util;


import com.xdo.findsword.domain.TokenInfo;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

@Data
@Component
@ConfigurationProperties(prefix = "pms.token")
public class TokenInfosList {
    private List<TokenInfo> infos;
    public List<TokenInfo> getInfos() { return infos; }
    public void setInfos(List<TokenInfo> infos) { this.infos = infos;}
}
