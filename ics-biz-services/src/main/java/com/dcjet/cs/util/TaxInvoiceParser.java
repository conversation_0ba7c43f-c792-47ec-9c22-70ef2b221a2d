package com.dcjet.cs.util;

import org.apache.pdfbox.pdmodel.PDDocument;
import technology.tabula.*;
import technology.tabula.extractors.BasicExtractionAlgorithm;
import technology.tabula.extractors.SpreadsheetExtractionAlgorithm;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.File;
import java.util.*;

import java.util.stream.Collectors;

public class TaxInvoiceParser {

    // 预定义所有需要提取的字段名（左侧列内容）
    private static final List<String> DETAIL_FIELDS_LEFT = new ArrayList<>(Arrays.asList(
            "年份","税费单详细信息","报关单号", "税种", "申报口岸", "收发货单位", "申报单位", "运输工具号",
            "监管方式", "进/出口日期", "退补税标志", "税款金额", "征税操作员", "缴款期限",
            "收入系统", "预算科目名称"
    ));

    private static final List<String> DETAIL_FIELDS_RIGHT = new ArrayList<>(Arrays.asList(
            "x","y","税单序号", "现场税单序", "进出口岸", "消费使用单位",
            "提单号", "合同号", "征免性质", "进出口标志", "滞报滞纳标志", "税款金额大写",
            "税单开征日期", "收入机关", "预算级次", "收款国库"
    ));


    public static void main(String[] args) {
        try {
            ObjectExtractor oe = new ObjectExtractor(PDDocument.load(new File("/Users/<USER>/Downloads/MTCSHHM650F023税单.pdf")));
            PageIterator pages = oe.extract();
            SpreadsheetExtractionAlgorithm sea = new SpreadsheetExtractionAlgorithm();

            BasicExtractionAlgorithm bea = new BasicExtractionAlgorithm();


            List<Map<String, Object>> result = new ArrayList<>();

            while (pages.hasNext()) {
                Page page = pages.next();
                List<Table> tables = sea.extract(page);

                List<Table> tables1 = bea.extract(page);

                if (tables.isEmpty()) continue;

                // 解析详细信息（第一张表）
                Map<String, String> details = parseDetails(tables.get(0));
                String taxType = details.getOrDefault("税种", "未知税种");

                // 解析货物信息（后续表格）
                // 在main方法中替换原有表格处理
                List<RectangularTextContainer> allCells = new ArrayList<>();
                tables1.forEach(t -> t.getRows().forEach(r -> allCells.addAll(r)));

                List<Map<String, Object>> goodsList = parseGoods(allCells);

                // 构建数据结构
                Map<String, Object> taxData = new LinkedHashMap<>();
                taxData.put("taxType", taxType);
                taxData.put("head", details);
                taxData.put("list", goodsList != null ? goodsList : Collections.emptyList());
                result.add(taxData);
            }

            ObjectMapper mapper = new ObjectMapper();
            String json = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(result);
            System.out.println(json);

            oe.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // 解析两列式详细信息表格
    private static Map<String, String> parseDetails(Table table) {
        Map<String, String> details = new LinkedHashMap<>();
        List<List<RectangularTextContainer>> rows = table.getRows();

        for (int i = 0; i < rows.size(); i++) {
            if (rows.get(i).size() < 2) continue; // 忽略不完整行

            // 左侧单元格：字段名 | 右侧单元格：字段值
            String fieldName = rows.get(i).get(0).getText().replaceAll("\\s+", " ").trim();
            String fieldValue = rows.get(i).get(1).getText().replaceAll("\\s+", " ").trim();


            details.put(DETAIL_FIELDS_LEFT.get(i), fieldName);
            details.put(DETAIL_FIELDS_RIGHT.get(i), fieldValue);

        }

        return details;
    }

    // 修改后的货物解析方法
    private static List<Map<String, Object>> parseGoods(List<RectangularTextContainer> cells) {
        List<Map<String, Object>> goodsList = new ArrayList<>();

        // 可配置的误差范围（单位：点）
        final int ERROR_RANGE = 2; // ±2误差
        final int GROUP_INTERVAL = ERROR_RANGE * 2;

        Map<Integer, List<RectangularTextContainer>> rows = cells.stream()
                .collect(Collectors.groupingBy(
                        cell -> (int) (Math.round(cell.getY() / GROUP_INTERVAL) * GROUP_INTERVAL),
                        TreeMap::new,
                        Collectors.toList()
                ));

        rows.forEach((y, rowCells) -> {
            // 跳过表头行（y坐标过滤）
            if (y <= 360) return;

            Map<String, Object> goods = new LinkedHashMap<>();
            System.out.println("rowCells大小：" + rowCells.size());
            rowCells.sort(Comparator.comparingDouble(RectangularTextContainer::getX));

            goodsList.add(parseRowData(rowCells));

        });
        return goodsList;
    }


    private static Map<String, Object> parseRowData(List<RectangularTextContainer> cells) {
        Map<String, Object> goods = new LinkedHashMap<>();

        // 根据图片坐标定义列边界（单位：点）
        final double COL1_MAX = 250; // 税号+货名 (x=34.12~238.47)
        final double COL2_MAX = 385; // 数量信息 (x=276.85~385.10)
        final double COL3_MAX = 435; // 完税价格 (x=401.74~435.79)
        final double COL4_START = 440; // 税率税额 (x=449.81~540.58)

        for (RectangularTextContainer cell : cells) {
            double x = cell.getX();
            String text = cell.getText().trim();
            System.out.printf("x=%.2f → %s\n", cell.getX(), text);

            // 列分配逻辑
            if (x <= COL1_MAX) {
                parseHsAndName(text, goods);
            } else if (x <= COL2_MAX) {
                parseQuantityInfo(text, goods);
            } else if (x <= COL3_MAX) {
                parseDutiableValue(text, goods);
            } else if (x >= COL4_START) {
                parseTaxDetails(text, goods);
            }
        }

        return goods;
    }

    // 解析方法实现
    private static void parseHsAndName(String text, Map<String, Object> goods) {
        if (text.matches("^\\d{10}.*")) {
            goods.put("codeTs", text.substring(0, 10));
            goods.put("gName", text.substring(10)
                    .replaceAll("\\s+", " ") // 合并空格
                    .replaceAll("[（）]", "")); // 去除中文括号
        }
    }

    private static void parseQuantityInfo(String text, Map<String, Object> goods) {
        // 匹配格式：4.2 千支 USD 7.1191
        System.out.println("数量单位币制汇率：" + text);
        String[] s = text.split(" ");
        if (s.length == 1){
            goods.put("exchangeRate", Double.parseDouble(s[0]));
        } else if  (s.length == 2){
            goods.put("exchangeRate", Double.parseDouble(s[0]));
            goods.put("dutyValue", Double.parseDouble(s[1]));
        } else if (s.length == 3) {
            goods.put("quantity", Double.parseDouble(s[0]));
            goods.put("unit", s[1].trim());
            goods.put("currency", s[2]);
        } else if (s.length > 3) {
            goods.put("quantity", Double.parseDouble(s[0]));
            goods.put("unit", s[1].trim());
            goods.put("currency", s[2]);
            goods.put("exchangeRate", Double.parseDouble(s[3]));
        }
        //Matcher matcher = Pattern.compile("(\\d+\\.?\\d*)([^\\d]+)([A-Z]{3})(\\d+\\.\\d+)").matcher(text);
    }

    private static void parseDutiableValue(String text, Map<String, Object> goods) {
        if (text.matches("\\d+\\.?\\d*")) {
            goods.put("dutyValue", Double.parseDouble(text));
        }
        String[] s = text.split(" ");
        if (s.length == 1){
            goods.put("dutyValue", Double.parseDouble(text));
        }else if (s.length == 2){
            goods.put("exchangeRate", Double.parseDouble(s[0]));
            goods.put("dutyValue", Double.parseDouble(s[1]));
        }
    }

    private static void parseTaxDetails(String text, Map<String, Object> goods) {
        // 处理两种格式：
        // 1. "0.25 0 38466.63" → 取第1和第3值
        // 2. "税率税率" → 跳过
        if (text.matches(".*[\\u4e00-\\u9fa5]+.*")) return;

        String[] parts = text.split("\\s+");
        if (parts.length >= 3) {
            try {
                goods.put("valoremRate", Double.parseDouble(parts[0]));
                goods.put("qtyRate", Double.parseDouble(parts[1]));
                goods.put("taxAmount", Double.parseDouble(parts[2]));
            } catch (NumberFormatException e) {
                System.err.println("税率解析异常: " + text);
            }
        }
    }
}