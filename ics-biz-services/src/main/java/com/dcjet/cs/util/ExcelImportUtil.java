package com.dcjet.cs.util;

import com.fasterxml.jackson.databind.MapperFeature;
import com.google.common.base.Strings;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.json.JsonObjectMapper;
import com.xdo.common.util.DateUtils;
import com.xdo.domain.KeyValuePair;
import org.apache.poi.ss.usermodel.*;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Excel 读取数据
 *
 * @author: 高士勇
 * @date: 2019-01-14
 */
public class ExcelImportUtil  {

    /***
     * 字段属性的名称所在的行
     */
    private static final short FIELD_NAME_ROW_NUM = 0;

    /***
     *
     * @param stream
     * @param sheetName
     * @param startRow
     * @return
     */
    public static List<List<String>> read(InputStream stream, String sheetName, int startRow) throws IOException {
        try (Workbook workbook = WorkbookFactory.create(stream)) {
            Sheet sheet = getSheet(workbook, sheetName);
            DataFormatter objDefaultFormat = new DataFormatter();
            FormulaEvaluator objFormulaEvaluator = workbook.getCreationHelper().createFormulaEvaluator();
            List<List<String>> result = new ArrayList<>();
            for (Row row : sheet) {
                if (row.getRowNum() < startRow) {
                    continue;
                }

                List<String> rowData = new ArrayList<>(row.getLastCellNum());
                result.add(rowData);
                for (Cell cell : row) {
                    objFormulaEvaluator.evaluate(cell);
                    String cellValue = objDefaultFormat.formatCellValue(cell, objFormulaEvaluator);
                    rowData.add(cellValue);
                }
            }
            return result;
        }
    }

    /***
     *
     * @param stream
     * @param cls
     * @param sheetName
     * @param startRow
     * @param <E>
     * @return
     * @throws IOException
     */
    public static <E extends Object> List<E> read(InputStream stream, Class<E> cls, String sheetName, int startRow,List<KeyValuePair<String,String>> headlist) throws IOException, NoSuchFieldException {
        try (Workbook workbook = WorkbookFactory.create(stream)) {
            Sheet sheet = getSheet(workbook, sheetName);

            if(sheet==null)
            {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("选择的导入模板下载错误，导入sheet名称为：")+sheetName);
            }

            List<E> list = new ArrayList<>();
            List<String> fieldList = new ArrayList<>();
            DataFormatter objDefaultFormat = new DataFormatter();
            FormulaEvaluator objFormulaEvaluator = workbook.getCreationHelper().createFormulaEvaluator();
            for (Row row : sheet) {
                if (row.getRowNum() == FIELD_NAME_ROW_NUM) {
                    for (Cell cell : row) {
                        objFormulaEvaluator.evaluate(cell);
                        String cellValue = objDefaultFormat.formatCellValue(cell, objFormulaEvaluator);
                        if (!Strings.isNullOrEmpty(cellValue)) {
                            fieldList.add(cellValue.trim());
                        }
                    }
                }

                if (row.getRowNum() < startRow -1) {
                    continue;
                }

                if (fieldList.size()!=headlist.size()) {
                    throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("请下载最新的导入模板"));
                }

                Map<String, Object> map = new HashMap<>(headlist.size());
                boolean emptyRow = true;
                for (Cell cell : row) {
                    if (headlist.size() > cell.getColumnIndex()) {
                        objFormulaEvaluator.evaluate(cell);
                        String cellValue = objDefaultFormat.formatCellValue(cell, objFormulaEvaluator);

                        if (emptyRow && !Strings.isNullOrEmpty(cellValue)) {
                            emptyRow = false;
                        }

                        String key = headlist.get(cell.getColumnIndex()).getKey();
                        if(!Strings.isNullOrEmpty(key)) {
                            Field field = cls.getDeclaredField(key);

                            Object value = converToObject(cellValue.trim(), field.getType().getSimpleName());
                            map.put(key, value);
                        }
                    }
                }
                if (emptyRow) {
                    return list;
                }
                // 解决fastJson 多 gMark gModel 不敏感的问题

                JsonObjectMapper.getInstance().getMapper().configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES, true);

                try {
                    list.add(JsonObjectMapper.getInstance().convert(map, cls));
                } catch (Exception e) {
                    throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("导入模板中导入行数或者导入数据类型错误！"));
                }

            }
            if (list.size()==0) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("模板中没有有效数据可以导入"));
            }
            return list;
        }
    }


    /***
     *
     * @param stream
     * @param cls
     * @param sheetName
     * @param startRow
     * @param <E>
     * @return
     * @throws IOException
     */
    public static <E extends Object> List<E> read(InputStream stream, Class<E> cls, String sheetName, int startRow) throws IOException, NoSuchFieldException {
        try (Workbook workbook = WorkbookFactory.create(stream)) {
            Sheet sheet = getSheet(workbook, sheetName);
            List<E> list = new ArrayList<>();
            List<String> fieldList = new ArrayList<>();
            DataFormatter objDefaultFormat = new DataFormatter();
            FormulaEvaluator objFormulaEvaluator = workbook.getCreationHelper().createFormulaEvaluator();
            for (Row row : sheet) {
                if (row.getRowNum() == FIELD_NAME_ROW_NUM) {
                    for (Cell cell : row) {
                        objFormulaEvaluator.evaluate(cell);
                        String cellValue = objDefaultFormat.formatCellValue(cell, objFormulaEvaluator);
                        if (!Strings.isNullOrEmpty(cellValue)) {
                            fieldList.add(cellValue.trim());
                        }
                    }
                }

                if (row.getRowNum() < startRow -1) {
                    continue;
                }

                if (fieldList.size() == 0) {
                    throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("选择的模板下载错误，导入sheet名称为：")+sheetName);
                }

                Map<String, Object> map = new HashMap<>(fieldList.size());
                boolean emptyRow = true;
                for (Cell cell : row) {
                    if (fieldList.size() > cell.getColumnIndex()) {
                        objFormulaEvaluator.evaluate(cell);
                        String cellValue = objDefaultFormat.formatCellValue(cell, objFormulaEvaluator);

                        if (emptyRow && !Strings.isNullOrEmpty(cellValue)) {
                            emptyRow = false;
                        }
                        String key = fieldList.get(cell.getColumnIndex());
                        Field field = cls.getDeclaredField(key);

                        Object value = converToObject(cellValue, field.getType().getSimpleName());
                        map.put(key, value);
                    }
                }
                if (emptyRow) {
                    return list;
                }
                // 解决fastJson 多 gMark gModel 不敏感的问题

                JsonObjectMapper.getInstance().getMapper().configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES, true);

                try {
                    list.add(JsonObjectMapper.getInstance().convert(map, cls));
                } catch (Exception e) {
                    throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("导入模板中导入行数或者导入数据类型错误！"));
                }

            }
            if (list.size()==0) {
                throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("模板中没有有效数据可以导入"));
            }
            return list;
        }
    }

    /***
     *
     * @param cellValue
     * @param typeName
     * @return
     */
    private static Object converToObject(String cellValue, String typeName) {
        switch (typeName) {
            case "Date":
                return DateUtils.stringToDate(cellValue, "yyyy-MM-dd");
            default:
                return cellValue;
        }
    }

    /**
     * @param workbook
     * @param sheetName
     * @return
     */
    private static Sheet getSheet(Workbook workbook, String sheetName) {
        Sheet sheet;
        if (Strings.isNullOrEmpty(sheetName)) {
            sheet = workbook.getSheetAt(0);
        } else {
            sheet = workbook.getSheet(sheetName);
        }

        if (sheet == null) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("选择的模板下载错误，导入sheet名称为：")+sheetName);
        }
        return sheet;
    }
}
