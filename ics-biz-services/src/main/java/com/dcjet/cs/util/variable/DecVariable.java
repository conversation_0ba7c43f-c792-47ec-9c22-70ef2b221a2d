package com.dcjet.cs.util.variable;

import org.apache.commons.lang3.StringUtils;

/**
 * 功能描述 预录入单进出口静态变量
 * <AUTHOR>
 * @date 2021-05-06
 * @version 1.0
 * @return
*/
public class DecVariable {
    /** 单据内部编号长度 */
    public final static int EMS_LIST_NO_LENGTH = 32;
    //进口生成预录入单内部编号默认值
    public static final String MODULE_NAME_I = "LI";
    //出口生成预录入单内部编号默认值
    public static final String MODULE_NAME_E = "LE";
    //类型0
    public final static String STATUS_0 = "0";
    //类型1
    public final static String STATUS_1 = "1";
    //类型2
    public final static String STATUS_2 = "2";
    public final static String STATUS_3 = "3";
    public final static String STATUS_4 = "4";

    /** 进口报关单 */
    public final static String I_ENTRY = "I_ENTRY";
    /** 出口报关单 */
    public final static String E_ENTRY = "E_ENTRY";

    /** 大提单 */
    public final static String DEC_TYPE_0 = "0";
    //小提单
    public final static String DEC_TYPE_1 = "1";
    //报关单类型 1-进口报关单
    public final static String ENTREY_TYPE_1 = "1";
    //报关单类型 1-出口报关单
    public final static String ENTREY_TYPE_2 = "2";
    //报关/转关关系标志 0
    public final static String REL_TYPE_0 = "0";
    //征免方式代码 1 照章征税
    public final static String DUTY_MODE_1 = "1";
    //征免方式代码 3 全免
    public final static String DUTY_MODE_3 = "3";

    public final static String TRAF_MODE_1 = "1";
    public final static String TRAF_MODE_2 = "2";
    //运输方式：固定值，9-其他方式运输
    public final static String TRAF_MODE_9 = "9";

    public final static String TRANS_MODE_1 = "1";
    public final static String TRANS_MODE_2 = "2";
    public final static String TRANS_MODE_3 = "3";
    public final static String TRANS_MODE_4 = "4";
    public final static String TRANS_MODE_7 = "7";

    public final static String PROMISE_ITEM_1 = "1";
    public final static String PROMISE_ITEM_2 = "2";
    public final static String PROMISE_ITEM_3 = "3";
    public final static String PROMISE_ITEM_4 = "4";
    public final static String PROMISE_ITEM_6 = "6";
    public final static String PROMISE_ITEM_7 = "7";
    public final static String PROMISE_ITEM_8 = "8";
    public final static String PROMISE_ITEM_9 = "9";
    public final static String PROMISE_ITEM_A = "A";
    public final static String PROMISE_ITEM_B = "B";
    public final static String PROMISE_ITEM_Z = "Z";
    public final static String PROMISE_ITEM_Y = "Y";
    public final static String PROMISE_ITEM_C = "C";
    public final static String PROMISE_ITEM_S = "S";

    public final static String GOOD_STATUS_6 = "6";
    public final static String GOOD_STATUS_7 = "7";
    public final static String GOOD_STATUS_8 = "8";

    //进口发送
    public final static String APPR_TYPE_I = "I";
    //出口发送
    public final static String APPR_TYPE_E = "E";

    //采购确认 0 未确认 1 已确认
    public final static String PURCHASE_CONFIRM_0 = "0";
    //财务确认 0 未确认 1 已确认
    public final static String FINANCE_CONFIRM_1 = "1";

    //进出口内审可发送状态
    public  final static  String[] SEND_STATUS_IE=new String[]{"-1","0","1","JC","9"};
    //进出口可删除状态
    public  final static  String[] DEL_STATUS_IE=new String[]{"0","9","-1","C","R"};
    //内审可撤回状态
    public  final static  String[] RETURN_STATUS=new String[]{"2","8"};
    /**一般贸易报关单可发送状态未发送 发送失败*/
    public final static String[] SEND_STATUS_VALID = new String[]{"-1", "0"};

    /**
     * 通用小数位(进出口)
     */
    public final static Integer COMM_SCALE = 5;
    //总价小数位(进出口)
    public final static Integer TOTAL_SCALE = 4;

    //进口随附单据类型
    public static final String ATTACHED_IM = "IM";
    //出口随附单据类型
    public static final String ATTACHED_EM = "EM";
    //免表随附单据
    public static final String ATTACHED_DEV = "DEV";

    public static final String fixPromiseItems(String promiseItems) {
        //价格说明 根据数据库中1,2,3,4 选择前三位是否选择
        // 默认空状态
        String strPromiseItems = "99999";
        StringBuilder strBuilder = new StringBuilder(strPromiseItems);
        if (StringUtils.isNotBlank(promiseItems)) {
            // 选中， 是状态
            if (promiseItems.contains("1")) {
                strBuilder.setCharAt(0, '1');
            }
            if (promiseItems.contains("2")) {
                strBuilder.setCharAt(1, '1');
            }
            if (promiseItems.contains("3")) {
                strBuilder.setCharAt(2, '1');
            }
            if (promiseItems.contains("A")) {
                strBuilder.setCharAt(3, '1');
            }
            if (promiseItems.contains("B")) {
                strBuilder.setCharAt(4, '1');
            }
            // 选中，否状态
            if (promiseItems.contains("9")) {
                strBuilder.setCharAt(0, '0');
            }
            if (promiseItems.contains("8")) {
                strBuilder.setCharAt(1, '0');
            }
            if (promiseItems.contains("7")) {
                strBuilder.setCharAt(2, '0');
            }
            if (promiseItems.contains("Z")) {
                strBuilder.setCharAt(3, '0');
            }
            if (promiseItems.contains("Y")) {
                strBuilder.setCharAt(4, '0');
            }
        }
        return strBuilder.toString();
    }
}
