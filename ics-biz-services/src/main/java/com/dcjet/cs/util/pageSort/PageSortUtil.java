package com.dcjet.cs.util.pageSort;

import com.dcjet.cs.util.GenericsUtils;
import com.xdo.domain.SortParam;
import org.apache.commons.collections4.CollectionUtils;

import javax.persistence.Column;
import java.lang.reflect.Field;
import java.util.List;

public class PageSortUtil {

    public static void setSortParam(List<SortParam> sortParams, Class clazz) throws NoSuchFieldException {
        if (CollectionUtils.isEmpty(sortParams)) {
            return;
        }
        for (SortParam sp : sortParams) {
            Field[] fields = clazz.getDeclaredFields();
            for(Field f:fields){
                if(f.getName().equalsIgnoreCase(sp.getName())){
                    Column col = clazz.getDeclaredField(f.getName()).getAnnotation(Column.class);
                    sp.setName(col.name());
                }
            }

        }
    }

    public static void setSortParam(List<SortParam> sortParams, Object o) throws NoSuchFieldException {
        setSortParam(sortParams, o, 0);
    }

    public static void setSortParam(List<SortParam> sortParams, Object o, int index) throws NoSuchFieldException {
        Class clazz = GenericsUtils.getClassT(o, index);
        setSortParam(sortParams, clazz);
    }
}
