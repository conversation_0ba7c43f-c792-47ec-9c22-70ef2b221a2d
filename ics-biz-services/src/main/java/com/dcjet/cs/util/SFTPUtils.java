package com.dcjet.cs.util;

import com.jcraft.jsch.*;
import com.xdo.common.exception.ErrorException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.lang.invoke.MethodHandles;
import java.util.*;

/**
 * @description:
 * @author: WJ
 * @createDate: 2022/8/4 10:56
 */
public class SFTPUtils {

    private static final Logger log = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    /** 主机名称IP */
    private String host;
    /** 端口 */
    private int port = 22;
    /** 用户名 */
    private String username;
    /** 密码 */
    private String password;

    private ChannelSftp sftp = null;
    private Channel channel = null;
    private Session session = null;

    public SFTPUtils(String host, String userName, String password) {
        this.host = host;
        this.username = userName;
        this.password = password;
    }

    public SFTPUtils(String host, int port, String userName, String password) {
        this.host = host;
        this.port = port;
        this.username = userName;
        this.password = password;
    }

    /**
     * 连接SFTP服务器
     *
     * @throws JSchException
     */
    public void connect() throws JSchException {
        JSch jSch = new JSch();
        session = jSch.getSession(username, host, port);
        session.setPassword(password);
        session.setConfig(this.buildConfig());
        session.connect();
        channel = session.openChannel("sftp");
        channel.connect();
        sftp = (ChannelSftp) channel;
        log.info("连接主机：{} 登录成功", host);
    }

    /**
     * 构建连接配置参数
     *
     * @return Properties
     */
    private Properties buildConfig() {
        Properties properties = new Properties();
        properties.put("StrictHostKeyChecking", "no");
        return properties;
    }

    /**
     * 关闭连接
     */
    public void disconnect() {
        try {
            if (null != sftp) {
                if (sftp.isConnected()) {
                    sftp.disconnect();
                }
                if (channel.isConnected()) {
                    channel.disconnect();
                }
                if (session.isConnected()) {
                    session.disconnect();
                }
            }
        } catch (Throwable e) {
            log.error("关闭SFTP连接异常:{}", e);
        }
    }

    /**
     * 切换目录,目录需要存在
     * @param sftpPath
     */
    public void cd(String sftpPath) throws Exception {
        sftp.cd(sftpPath);
    }

    /**
     * 初始化目录,不存在自动创建
     * @param sftpPath
     */
    public void cdDir(String sftpPath) throws Exception {
        String[] directories = sftpPath.split("/");
        for (String directory : directories) {
            if (directory.length() > 0) {
                try {
                    sftp.cd(directory);
                } catch (Exception e) {
                    sftp.mkdir(directory);
                    sftp.cd(directory);
                }
            }
        }
    }

    /**
     * 下载文件
     *
     * @param sftpPath   服务器路径，不指定路径默认是FTP的根路径，指定路径是指的SFTP的根路径下开始。
     *                   例如：SFTP根路径为：/sftp/file，那么默认下载文件会去根路径下载，而指定 sftpPath 也是从根路径下开始；
     *                   指定 sftpPath 为 word，那么是从 /sftp/file/word 路径中查找文件下载。为空表示忽略该参数。
     * @param fileName   文件名
     * @param toFilePath 下载保存文件路径，路径+文件名，例如：d:/test.txt
     * @return
     */
    public boolean downloadFile(String sftpPath, String fileName, String toFilePath) {
        FileOutputStream fileOutputStream = null;
        try {
            if (StringUtils.isNotBlank(sftpPath)) {
                sftp.cd(sftpPath);
            }
            fileOutputStream = new FileOutputStream(new File(toFilePath));
            sftp.get(fileName, fileOutputStream);
            return true;
        } catch (Exception e) {
            log.error("下载文件错误", e);
        } finally {
            if (fileOutputStream != null) {
                try {
                    fileOutputStream.close();
                } catch (IOException e) {
                    //ignore
                }
            }
        }
        return false;
    }

    /**
     * 下载文件 -- 获取当前时间2分钟前的文件,避免文件正在被写入的时候被读取
     *
     * @param sftpPath   服务器路径，不指定路径默认是FTP的根路径，指定路径是指的SFTP的根路径下开始。
     *                   例如：SFTP根路径为：/sftp/file，那么默认下载文件会去根路径下载，而指定 sftpPath 也是从根路径下开始；
     *                   指定 sftpPath 为 word，那么是从 /sftp/file/word 路径中查找文件下载。为空表示忽略该参数。
     * @param fileName   文件名
     * @param toFilePath 下载保存文件路径，路径+文件名，例如：d:/test.txt
     * @return
     */
    public boolean downloadFilePlus(String sftpPath, String fileName, String toFilePath) {
        FileOutputStream fileOutputStream = null;
        try {
            if (StringUtils.isNotBlank(sftpPath)) {
                sftp.cd(sftpPath);
            }
            SftpATTRS attrs = sftp.stat(sftpPath + "/" + fileName);
            long lastModifiedTime = attrs.getMTime() * 1000L; //转换成毫秒单位
            if (lastModifiedTime + 2*60000 > System.currentTimeMillis()) {
                log.info("当前文件：{}最后更新时间距当前日期小于2分钟,不处理", sftpPath);
                return false;
            }
            fileOutputStream = new FileOutputStream(new File(toFilePath));
            sftp.get(fileName, fileOutputStream);
            return true;
        } catch (Exception e) {
            log.error("下载文件错误", e);
        } finally {
            if (fileOutputStream != null) {
                try {
                    fileOutputStream.close();
                } catch (IOException e) {
                    //ignore
                }
            }
        }
        return false;
    }

    /**
     * 上传文件
     *
     * @param sftpPath      服务器路径，不指定路径默认是FTP的根路径，指定路径是指的SFTP的根路径下开始。
     *                      例如：SFTP根路径为：/sftp/file，那么默认下载文件会去根路径下载，而指定 sftpPath 也是从根路径下开始；
     *                      指定 sftpPath 为 word，那么是从 /sftp/file/word 路径中查找文件下载。为空表示忽略该参数。
     * @param fileName      上传后文件名
     * @param localFilePath 文件路径，路径+文件名，例如：d:/test.txt
     * @return
     */
    public boolean uploadFile(String sftpPath, String fileName, String localFilePath) throws FileNotFoundException {
        FileInputStream inputStream = new FileInputStream(new File(localFilePath));
        return uploadFile(sftpPath, fileName, inputStream);
    }

    /**
     * 上传文件
     *
     * @param sftpPath    服务器路径，不指定路径默认是FTP的根路径，指定路径是指的SFTP的根路径下开始。
     *                    例如：SFTP根路径为：/sftp/file，那么默认下载文件会去根路径下载，而指定 sftpPath 也是从根路径下开始；
     *                    指定 sftpPath 为 word，那么是从 /sftp/file/word 路径中查找文件下载。为空表示忽略该参数。
     * @param fileName    上传后文件名
     * @param inputStream 文件输入流
     * @return
     */
    public boolean uploadFile(String sftpPath, String fileName, InputStream inputStream) {
        try {
            if (StringUtils.isNotBlank(sftpPath)) {
                sftp.cd(sftpPath);
            }
            sftp.put(inputStream, fileName);
            return true;
        } catch (Exception e) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("上传文件错误"));
        } finally {
            if (null != inputStream) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("上传文件错误", e);
                }
            }
        }
    }

    /**
     * 删除文件
     *
     * @param sftpPath 服务器路径，不指定路径默认是FTP的根路径，指定路径是指的SFTP的根路径下开始。
     *                 例如：SFTP根路径为：/sftp/file，那么默认下载文件会去根路径下载，而指定 sftpPath 也是从根路径下开始；
     *                 指定 sftpPath 为 word，那么是从 /sftp/file/word 路径中查找文件下载。为空表示忽略该参数。
     * @param fileName 文件名
     * @return
     */
    public boolean deleteFile(String sftpPath, String fileName) {
        try {
            if (StringUtils.isNotBlank(sftpPath)) {
                sftp.cd(sftpPath);
            }
            sftp.rm(fileName);
            return true;
        } catch (Exception e) {
            log.error("删除文件失败", e);
        }
        return false;
    }

    /**
     * 慎用 -- 因为没有操作权限
     * 将SFTP目录文件移动到其他目录
     * @param oldPath
     * @param newPath
     * @throws SftpException
     */
    public void moveFile(String oldPath,String newPath) throws SftpException {
        sftp.rename(oldPath,newPath);
    }

    /**
     * 查询指定目录下信息
     *
     * @param sftpPath 服务器路径，不指定路径默认是FTP的根路径，指定路径是指的SFTP的根路径下开始。
     *                 例如：SFTP根路径为：/sftp/file，那么默认下载文件会去根路径下载，而指定 sftpPath 也是从根路径下开始；
     *                 指定 sftpPath 为 word，那么是从 /sftp/file/word 路径中查找文件下载。为空表示忽略该参数。
     * @return
     */
    public List<String> listFiles(String sftpPath) throws SftpException {
        Vector files = sftp.ls(sftpPath);
        List<String> result = new ArrayList<String>();
        Iterator iterator = files.iterator();
        while (iterator.hasNext()) {
            ChannelSftp.LsEntry isEntity = (ChannelSftp.LsEntry) iterator.next();
            if (isEntity.getFilename().equals(".") || isEntity.getFilename().equals("..")) {
                continue;
            }
            result.add(isEntity.getFilename());
        }
        return result;
    }
}
