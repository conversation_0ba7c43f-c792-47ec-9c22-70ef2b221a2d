package com.dcjet.cs.util.emptyCheck;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

/**
 * 空值处理切面
 * <AUTHOR>
 * @date 2021-11-29
 */
@Aspect
@Component
@Slf4j
public class EmptyCheckAspect {

    @Pointcut("@annotation(com.dcjet.cs.util.emptyCheck.ElegantEmptyCheck)")
    public void elegantEmptyCheckPointcut() {}


    @Around("elegantEmptyCheckPointcut()")
    public Object emptyCheck(ProceedingJoinPoint point) throws Throwable {
        // 获取注解
        MethodSignature methodSignature = (MethodSignature)point.getSignature();
        ElegantEmptyCheck annotation = methodSignature.getMethod().getAnnotation(ElegantEmptyCheck.class);

        // 获取 method参数信息
        Object[] args = point.getArgs();
        int[] anno_paramIndexs = annotation.paramIndexs();
        String[] anno_paramNames = annotation.paramNames();

        // 空值处理
        if (anno_paramIndexs != null && anno_paramIndexs.length > 0) {
            for(int idx : anno_paramIndexs) {
                if(args[idx] == null) {
                    return emptyResult(annotation.handlerType());
                }
            }
        } else if(anno_paramNames != null && anno_paramNames.length > 0) {
            String[] methodParameterNames = methodSignature.getParameterNames();
            for (String anno_paraName : anno_paramNames) {
                for (int idx = 0; idx < methodParameterNames.length; idx++) {
                    if (anno_paraName.equals(methodParameterNames[idx]) ) {
                        if (args[idx] == null) {
                            return emptyResult(annotation.handlerType());
                        }
                    }
                }
            }
        }
        return point.proceed();
    }

    /**
     * 空值处理
     * @param type
     * @return
     */
    public Object emptyResult(EmptyHandlerType type) {
        switch (type) {
            case NULL_VALUE:
                return null;
            case FALSE_VALUE:
                return false;
            case RUNTIME_EXCEPTION:
                throw new RuntimeException(xdoi18n.XdoI18nUtil.t("空值异常"));
            default:
                throw new RuntimeException(xdoi18n.XdoI18nUtil.t("EmptyHandlerType 参数异常"));
        }
    }
}
