package com.dcjet.cs.util.pojo;

import lombok.Data;

@Data
public class BillHeadApiReturn {

    private String tempRemark;
    private String agentCode;
    private String declareCode;
    private String trafMode;
    private String tradeMode;
    private String tradeCountry;
    private String emsListNo;
    private String listType;
    private String dclcusMark;
    private String dclcusType;
    private String entryType;
    private String mergeType;
    private String rotateApplyNo;
    private String overseasShipperName;
    private String cutMode;
    private String tradeNation;
    private String entryPort;
    private String wrapType;
    private String packNum;
    private String transMode;
    private String confirmSpecial;
    private String confirmPrice;
    private String confirmRoyalties;
    private String dutySelf;
    private String emsNo;
    private String declCustoms;
    private String headId;
    private String iemark;
    private String gmark;
    private String ieport;
}
