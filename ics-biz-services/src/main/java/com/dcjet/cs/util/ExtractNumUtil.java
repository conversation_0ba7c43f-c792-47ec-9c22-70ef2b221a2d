package com.dcjet.cs.util;

import org.springframework.util.StringUtils;

import java.math.BigDecimal;

public class ExtractNumUtil {

    /**
     * 数字提取
     * @param value
     * @return
     */
    private static String extractNum(String value) {
        String result = "";
        if (StringUtils.isEmpty(value)) {
            return result;
        }

        Boolean isStart = false;
        Boolean isPoint = false;

        value = value.replace(",", "");
        for(int i=0;i<value.length();i++){
            if(value.charAt(i)>=48 && value.charAt(i)<=57){
                isStart = true;
                result += value.charAt(i);
            } else {
                if (value.charAt(i) == 46 && !isPoint) {
                    isPoint = true;
                    result += value.charAt(i);
                } else if (isStart) {
                    return result;
                }
            }
        }
        return result;
    }

    public static Integer extractInteger(String value) {
        String res = extractNum(value);
        if (StringUtils.isEmpty(res)) {
            return null;
        }
        if(res.contains(".")) {
            res = res.substring(0, res.indexOf("."));
        }
        return Integer.parseInt(res);
    }

    public static Long extractLong(String value) {
        String res = extractNum(value);
        if (StringUtils.isEmpty(res)) {
            return null;
        }
        if(res.contains(".")) {
            res = res.substring(0, res.indexOf("."));
        }
        return Long.parseLong(res);
    }

    public static BigDecimal extractBigDecimal(String value) {
        String res = extractNum(value);
        if (StringUtils.isEmpty(res)) {
            return null;
        }
        return new BigDecimal(res);
    }
}
