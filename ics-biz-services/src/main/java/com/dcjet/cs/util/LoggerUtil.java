package com.dcjet.cs.util;

import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;

/**
 * @description:
 * @author: WJ
 * @createDate: 2020/8/19 13:16
 */
public class LoggerUtil {

    public static void logInfo(Logger logger, String message) {
        logger.info(message);
        XxlJobLogger.log(message);
    }
    public static void logInfo(Logger logger, String message, int num) {
        logger.info(message, num);
        XxlJobLogger.log(message);
    }
    public static void logDebug(Logger logger, String message) {
        logger.debug(message);
        XxlJobLogger.log(message);
    }
    public static void logDebug(Logger logger, String message, int num) {
        logger.debug(message, num);
        XxlJobLogger.log(message);
    }
    public static void logError(Logger logger, String message) {
        logger.error(message);
        XxlJobLogger.log(message);
    }
    public static void logError(Logger logger, Throwable e ) {
        logger.error(e.getMessage());
        XxlJobLogger.log(e);
    }
    public static void logError(Logger logger, String message, Throwable e ) {
        logger.error(message, e);
        XxlJobLogger.log(e);
    }
}
