package com.dcjet.cs.util;

import org.springframework.stereotype.Service;
import tk.mybatis.mapper.common.Mapper;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;

/**
 * SelectSplit
 */
@Service
public class SelectSplit {

    /**
     * in查询 分批
     * @param m
     * @param method
     * @param list
     * @param args
     * @return
     * @throws InvocationTargetException
     * @throws IllegalAccessException
     */
    public <M extends Mapper, P, T > List<T> select(M m, Method method, List<P> list, Object... args) throws InvocationTargetException, IllegalAccessException {
        List<T> result = new ArrayList<>();
        int batch = 1000;
        int i = 0;
        while(i < list.size()) {
            int toIdx = i+batch > list.size() ? list.size() : i+batch;
            List<P> subList = list.subList(i, toIdx);
            List<Object> mergeArgs = new ArrayList<>();
            mergeArgs.add(subList);
            mergeArgs.addAll(Arrays.asList(args));
            List<T> res = (List)method.invoke(m, mergeArgs.toArray());
            result.addAll(res);
            i += batch;
        }
        return result;
    }

    /**
     * in查询 分批
     * @param list
     * @param function
     * @return
     * @throws InvocationTargetException
     * @throws IllegalAccessException
     */
    public <P, T> Integer update(List<P> list, Function<List<P>, Integer> function) throws InvocationTargetException, IllegalAccessException {
        int result = 0;
        int batch = 1000;
        int i = 0;
        while(i < list.size()) {
            int toIdx = i+batch > list.size() ? list.size() : i+batch;
            List<P> subList = list.subList(i, toIdx);
            int cnt = function.apply(subList);
            result += cnt;
            i += batch;
        }
        return result;
    }
}
