package com.dcjet.cs.util;

import net.lingala.zip4j.exception.ZipException;
import net.lingala.zip4j.model.FileHeader;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.filefilter.TrueFileFilter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * @version V1.0
 * @ClassName FileUtil
 * @Description
 * <AUTHOR>
 * @Date 2018/1/9 20:58
 */
public class FileUtil {
    private static Logger logger = LoggerFactory.getLogger(FileUtil.class);
    private static final int  BUFFER_SIZE = 10 * 1024;
    /**
     * 获得指定文件的byte数组
     */
    public static byte[] getBytes(String filePath){
        byte[] buffer = null;
        try {
            File file = new File(filePath);
            FileInputStream fis = new FileInputStream(file);
            ByteArrayOutputStream bos = new ByteArrayOutputStream(1000);
            byte[] b = new byte[1000];
            int n;
            while ((n = fis.read(b)) != -1) {
                bos.write(b, 0, n);
            }
            fis.close();
            bos.close();
            buffer = bos.toByteArray();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return buffer;
    }

    /**
     * 根据byte数组，生成文件
     */
    public static void getFile(byte[] bfile, String filePath,String fileName) {
        BufferedOutputStream bos = null;
        FileOutputStream fos = null;
        File file = null;
        try {
            File dir = new File(filePath);
            if(!dir.exists()){//判断文件目录是否存在
                dir.mkdirs();
            }
            file = new File(filePath+"\\"+fileName);
            fos = new FileOutputStream(file);
            bos = new BufferedOutputStream(fos);
            bos.write(bfile);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (bos != null) {
                try {
                    bos.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
    }

    /**
     * byte[] 转InputStream
     */
    public static final InputStream byte2Input(byte[] buf) {
        return new ByteArrayInputStream(buf);
    }

    /**
     * InputStream 转 byte[]
     * @param inStream
     * @return
     * @throws IOException
     */
    public static final byte[] input2byte(InputStream inStream)
            throws IOException {
        ByteArrayOutputStream swapStream = new ByteArrayOutputStream();
        byte[] buff = new byte[100];
        int rc = 0;
        while ((rc = inStream.read(buff, 0, 100)) > 0) {
            swapStream.write(buff, 0, rc);
        }
        byte[] in2b = swapStream.toByteArray();
        return in2b;
    }

    /**
     * byte[] 转 InputStreamReader
     */
    public static final InputStreamReader byte2Reader(byte[] buf) {
        InputStreamReader isr = new InputStreamReader(new ByteArrayInputStream(buf));
        return isr;
    }

    /**
     * 删除文件
     *
     * @param pathname
     *          文件名（包括路径）
     */
    public static void deleteFile(String pathname){
        File file = new File(pathname);
        if(file.isFile() && file.exists()){
            file.delete();
            logger.info("File["+ pathname +"] delete success!");
        }
    }

    /**
     * 删除文件树
     *
     * @param dirpath
     *          文件夹路径
     */
    public static void deleteFileTree(String dirpath) throws IOException {

        File dir = new File(dirpath);
        FileUtils.deleteDirectory(dir);
    }

    /**
     * 获取文件扩展名
     *
     * @param fileName
     *            文件名
     * @return
     */
    public static String getExtention(String fileName) {
        int pos = fileName.lastIndexOf(".");
        return fileName.substring(pos+1);
    }
    /**
     * 获取文件名(不包含扩展名)
     *
     * @param fileName
     *            文件名
     * @return
     */
    public static String getFileName(String fileName) {
        int pos = fileName.lastIndexOf(".");
        return fileName.substring(0,pos);
    }
    /**
     * 获取文件分隔符
     *
     * @return
     */
    public static String getFileSeparator() {
        return File.separator;
    }

    /**
     * 获取相对路径
     *
     * @param params
     *          按参数先后位置得到相对路径
     * @return
     */
    public static String getRelativePath(String... params){

        if(null != params){
            String path = "";
            for(String str : params){
                path = path + getFileSeparator() + str;
            }

            return path;
        }

        return null;
    }

    /**
     * 把一个字符串写到指定文件中
     * @param str  要写入文件中的字符串内容
     * @param path 文件夹路径
     * @param fileName 文件名称
     */
    public static void writeStringToFile(String str,String path,String fileName) throws IOException {
        File fileDir = new File(path);
        if(!fileDir.exists()){
            fileDir.mkdirs();
        }
        File file = new File(path+fileName);
        if(!file.exists()){
            file.createNewFile();
        }
        FileWriter fw = new FileWriter(file,true);
        fw.write(str);
        fw.flush();
        fw.close();
    }

    /**
     * 在某个文件中追加内容
     * @param fileName
     * @param content
     */
    public static void appendStringToFile(String fileName, String content) {
        try {
            //判断文件是否存在
            File file = new File(fileName);
            fileExists(file);
            // 打开一个随机访问文件流，按读写方式
            RandomAccessFile randomFile = new RandomAccessFile(fileName, "rw");
            // 文件长度，字节数
            long fileLength = randomFile.length();
            // 将写文件指针移到文件尾。
            randomFile.seek(fileLength);
            randomFile.write(("\r\n" + content).getBytes());
            randomFile.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     *判断文件是否存在,如果不存在则创建
     */
    public static void fileExists(String filePath) {
        File file=new File(filePath);
        fileExists(file);
    }
    /**
     *判断文件是否存在,如果不存在则创建
     */
    public static void fileExists(File file) {
        if (file.exists()) {
        } else {
            try {
                file.createNewFile();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
    /**
     *判断文件是否存在,如果不存在则创建
     */
    public static void dirExists(String filePath) {
        File file=new File(filePath);
        dirExists(file);
    }
    /**
     *判断文件是否存在,如果不存在则创建
     */
    public static void dirExists(File file) {
        if (file.exists()) {
            if (file.isDirectory()) {
                System.out.println("dir exists");
            } else {
                System.out.println("the same name file exists, can not create dir");
            }
        } else {
            System.out.println("dir not exists, create it ...");
            file.mkdirs();
        }
    }    /**
     * 遍历目录下的所有文件--方式1
     *
     * @param targetDir
     */
    public static Collection<File> localListFiles(File targetDir) {
        Collection<File> fileCollection = new ArrayList<>();
        if (targetDir != null && targetDir.exists() && targetDir.isDirectory()) {
            /**
             * targetDir：不要为 null、不要是文件、不要不存在
             * 第二个 文件过滤 参数如果为 FalseFileFilter.FALSE ，则不会查询任何文件
             * 第三个 目录过滤 参数如果为 FalseFileFilter.FALSE , 则只获取目标文件夹下的一级文件，而不会迭代获取子文件夹下的文件
             */
            fileCollection = FileUtils.listFiles(targetDir, TrueFileFilter.INSTANCE, TrueFileFilter.INSTANCE);
        }
        return fileCollection;
    }
    /**
     * 功能描述:按行读取文件
     * @auther: zhuhui
     * @version :  1.0
     * @date: 2019/3/28
     * @param:  * @param null
     * @return:返回读取数据
     */
    public static Map<Long,String> readFileByLines(String fileName, long startLine, Integer batchLine) {
        File file = new File(fileName);
        BufferedReader reader = null;
        Map<Long,String> map=new HashMap<Long, String>();
        try {
            System.out.println("以行为单位读取文件内容，一次读一行");
            reader = new BufferedReader(new FileReader(file));
            String tempString = null;
            long line = 1;
            long needMaxLine=startLine+batchLine;
            //一次读一行，读入null时文件结束 TODO:可优化
            while ((tempString = reader.readLine()) != null) {
            //把当前行号显示出来
                if( startLine<=line && line <=needMaxLine ){
                    System.out.println("line " + line + ": " + tempString);
                    map.put(line,tempString);
                }else if (line>needMaxLine){
                    break;
                }
                line++;
            }
            reader.close();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
       return map;
    }

    /**
     * 功能描述:读取文件内容
     */
    public static String readFileContent(String filePath) {
        String content = "";
        try {
            content = new String(Files.readAllBytes(Paths.get(filePath)));
        } catch (IOException e) {
            e.printStackTrace();
        }
        return content;
    }

    public static boolean MoveFile(String orginFilePath,String destFilePath){
        File orginFile = new File(orginFilePath);
        if (orginFile.renameTo(new File(destFilePath))) {
            logger.info("File is moved successful!");
            System.out.println("File is moved successful!");
            return  true;
        } else {
            return  false;
        }
    }

    /**
     * 获取文件名
     */
    public static String getRealFileName(String fileName) {
        String realFileName;
        try {
            realFileName = fileName.substring(fileName.lastIndexOf("/"));
            realFileName = realFileName.replace("/","");
        } catch (Exception e) {
            realFileName = fileName.substring(fileName.lastIndexOf("\\"));
            realFileName = realFileName.replace("\\","");
        }
        return realFileName;
    }

    /**
     * 获取某个文件夹下的所有文件
     *
     * @param fileNameList 存放文件名称的list
     * @param path 文件夹的路径
     * @return
     */
    public static void getAllFileName(String path, List<String> fileNameList) {
        boolean flag = false;
        File file = new File(path);
        File[] tempList = file.listFiles();
        for (int i = 0; i < tempList.length; i++) {
            if (tempList[i].isFile()) {
                fileNameList.add(tempList[i].getName());
            }
            if (tempList[i].isDirectory()) {
                getAllFileName(tempList[i].getAbsolutePath(),fileNameList);
            }
        }
        return;
    }

    /**
     * 将存放在sourceFilePath目录下的源文件，打包成fileName名称的zip文件，并存放到zipFilePath路径下
     * @param sourceFilePath :待压缩的文件路径
     * @param zipFilePath :压缩后存放路径
     * @param fileName :压缩后文件的名称
     * @return
     */
    public static boolean fileToZip(String sourceFilePath, String zipFilePath, String fileName){
        boolean flag = false;
        File sourceFile = new File(sourceFilePath);
        FileInputStream fis = null;
        BufferedInputStream bis = null;
        FileOutputStream fos = null;
        ZipOutputStream zos = null;

        if (!sourceFile.exists()) {
            logger.info("待压缩的文件目录：{}不存在.", sourceFilePath);
        } else {
            try {
                File zipFile = new File(zipFilePath + "/" + fileName +".zip");
                if (zipFile.exists()) {
                    logger.info("{}目录下存在名字为:{}.zip打包文件.", zipFilePath, fileName);
                } else {
                    File[] sourceFiles = sourceFile.listFiles();
                    if (null == sourceFiles || sourceFiles.length<1) {
                        logger.info("待压缩的文件目录：{}里面不存在文件，无需压缩.", sourceFilePath);
                    } else {
                        fos = new FileOutputStream(zipFile);
                        zos = new ZipOutputStream(new BufferedOutputStream(fos));
                        byte[] bufs = new byte[BUFFER_SIZE];
                        for (int i = 0;i < sourceFiles.length;i++) {
                            //创建ZIP实体，并添加进压缩包
                            ZipEntry zipEntry = new ZipEntry(sourceFiles[i].getName());
                            zos.putNextEntry(zipEntry);
                            //读取待压缩的文件并写进压缩包里
                            fis = new FileInputStream(sourceFiles[i]);
                            bis = new BufferedInputStream(fis, BUFFER_SIZE);
                            int read = 0;
                            while ((read = bis.read(bufs, 0, BUFFER_SIZE)) != -1) {
                                zos.write(bufs,0,read);
                            }
                            try {
                                if (null != fis) {
                                    fis.close();
                                }
                            } catch (IOException e) {
                                logger.error(e.getMessage());
                            } finally {
                                try {
                                    if (null != bis) {
                                        bis.close();
                                    }
                                } catch (IOException e) {
                                    logger.error(e.getMessage());
                                }
                            }
                        }
                        flag = true;
                    }
                }
            } catch (FileNotFoundException e) {
                logger.error(e.getMessage());
            } catch (IOException e) {
                logger.error(e.getMessage());
            } finally{
                // 关闭流
                try {
                    if (null != zos) {
                        zos.close();
                    }
                } catch (IOException ie) {
                    logger.error(ie.getMessage());
                } finally {
                    try {
                        if (null != fos) {
                            fos.close();
                        }
                    } catch (IOException ie) {
                        logger.error(ie.getMessage());
                    }
                }
            }
        }
        return flag;
    }

    /**
     * 压缩成ZIP
     * @param sourceFilePath :待压缩的文件路径
     * @param zipFilePath :压缩后存放路径
     * @param fileName :压缩后文件的名称
     * @param keepDirStructure  是否保留原来的目录结构,true:保留目录结构;
     *                          false:所有文件跑到压缩包根目录下(注意：不保留目录结构可能会出现同名文件,会压缩失败)
     * @throws RuntimeException 压缩失败会抛出运行时异常
     */
    public static boolean toZip (String sourceFilePath, String zipFilePath, String fileName, boolean keepDirStructure) {
        boolean flag = false;
        File sourceFile = new File(sourceFilePath);

        FileOutputStream fos = null;
        ZipOutputStream zos = null;

        if (!sourceFile.exists()) {
            logger.info("待压缩的文件目录：{}不存在.", sourceFilePath);
        } else {
            try {
                File zipFile = new File(zipFilePath + "/" + fileName +".zip");
                if (zipFile.exists()) {
                    logger.info("{}目录下存在名字为:{}.zip打包文件.", zipFilePath, fileName);
                } else {
                    File[] sourceFiles = sourceFile.listFiles();
                    if (null == sourceFiles || sourceFiles.length<1) {
                        logger.info("待压缩的文件目录：{}里面不存在文件，无需压缩.", sourceFilePath);
                    } else {
                        fos = new FileOutputStream(zipFile);
                        zos = new ZipOutputStream(new BufferedOutputStream(fos));
                        compress(sourceFile, zos, sourceFile.getName(), keepDirStructure);
                        flag = true;
                    }
                }
            } catch (FileNotFoundException e) {
                logger.error(e.getMessage());
            } finally {
                // 关闭流
                try {
                    if (null != zos) {
                        zos.close();
                    }
                } catch (IOException ie) {
                    logger.error(ie.getMessage());
                } finally {
                    try {
                        if (null != fos) {
                            fos.close();
                        }
                    } catch (IOException ie) {
                        logger.error(ie.getMessage());
                    }
                }
            }
        }
        return flag;
    }

    /**
     * 递归压缩方法
     * @param sourceFile 源文件
     * @param zos        zip输出流
     * @param name       压缩后的名称
     * @param keepDirStructure  是否保留原来的目录结构,true:保留目录结构;
     *                          false:所有文件跑到压缩包根目录下(注意：不保留目录结构可能会出现同名文件,会压缩失败)
     * @throws Exception
     */
    private static void compress (File sourceFile, ZipOutputStream zos, String name, boolean keepDirStructure) {
        byte[] buf = new byte[BUFFER_SIZE];
        try {
            if (sourceFile.isFile()) {
                // 向zip输出流中添加一个zip实体，构造器中name为zip实体的文件的名字
                zos.putNextEntry(new ZipEntry(name));
                // copy文件到zip输出流中
                int len;
                FileInputStream fis = new FileInputStream(sourceFile);
                BufferedInputStream bis = new BufferedInputStream(fis, BUFFER_SIZE);
                while ((len = bis.read(buf, 0, BUFFER_SIZE)) != -1){
                    zos.write(buf,0,len);
                }
                try {
                    if (null != fis) {
                        fis.close();
                    }
                } catch (IOException e) {
                    logger.error(e.getMessage());
                } finally {
                    try {
                        if (null != bis) {
                            bis.close();
                        }
                    } catch (IOException e) {
                        logger.error(e.getMessage());
                    }
                }
            } else {
                File[] listFiles = sourceFile.listFiles();
                if (listFiles == null || listFiles.length == 0) {
                    // 需要保留原来的文件结构时,需要对空文件夹进行处理
                    if (keepDirStructure) {
                        // 空文件夹的处理
                        zos.putNextEntry(new ZipEntry(name + "/"));
                        // 没有文件，不需要文件的copy
                        zos.closeEntry();
                    }
                } else {
                    for (File file : listFiles) {
                        // 判断是否需要保留原来的文件结构
                        if (keepDirStructure) {
                            // 注意：file.getName()前面需要带上父文件夹的名字加一斜杠,
                            // 不然最后压缩包中就不能保留原来的文件结构,即：所有文件都跑到压缩包根目录下了
                            compress(file, zos, name + "/" + file.getName(), keepDirStructure);
                        } else {
                            compress(file, zos, file.getName(), keepDirStructure);
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("生成压缩文件失败：{}", e);
        }

    }

    /**
     *
     * @param sourceFile 源文件
     * @param zipFilePath 压缩后文件目录
     * @return
     */
    public static File zipFile(File sourceFile, String zipFilePath){
        boolean flag = false;
        String fileName = sourceFile.getName().split("\\.")[0];
        FileInputStream fis = null;
        BufferedInputStream bis = null;
        FileOutputStream fos = null;
        ZipOutputStream zos = null;

        try {
            File zipFile = new File(zipFilePath + File.separator + fileName +".zip");
            if (zipFile.exists()) {
                logger.info("{}目录下存在名字为:{}.zip打包文件.", zipFilePath, fileName);
            } else {
                fos = new FileOutputStream(zipFile);
                zos = new ZipOutputStream(new BufferedOutputStream(fos));
                byte[] bufs = new byte[BUFFER_SIZE];
                // 创建ZIP实体，并添加进压缩包
                ZipEntry zipEntry = new ZipEntry(sourceFile.getName());
                zos.putNextEntry(zipEntry);
                // 读取待压缩的文件并写进压缩包里
                fis = new FileInputStream(sourceFile);
                bis = new BufferedInputStream(fis, BUFFER_SIZE);
                int read = 0;
                while ((read = bis.read(bufs, 0, BUFFER_SIZE)) != -1) {
                    zos.write(bufs,0,read);
                }
                try {
                    if (null != fis) {
                        fis.close();
                    }
                } catch (IOException e) {
                    logger.error(e.getMessage());
                } finally {
                    try {
                        if (null != bis) {
                            bis.close();
                        }
                    } catch (IOException e) {
                        logger.error(e.getMessage());
                    }
                }
                return zipFile;
            }
        } catch (FileNotFoundException e) {
            logger.error(e.getMessage());
        } catch (IOException e) {
            logger.error(e.getMessage());
        } finally{
            // 关闭流
            try {
                if (null != zos) {
                    zos.close();
                }
            } catch (IOException ie) {
                logger.error(ie.getMessage());
            } finally {
                try {
                    if (null != fos) {
                        fos.close();
                    }
                } catch (IOException ie) {
                    logger.error(ie.getMessage());
                }
            }
        }
        return null;
    }

    /**
     * 使用zip4j工具包解压缩
     * @param sourceFileUrl
     * @param targetDirectory
     * @throws IOException
     */
    public static void unzipUseZip4j(String sourceFileUrl, String targetDirectory) throws Exception {
        File sourceFile = new File(sourceFileUrl);
        net.lingala.zip4j.ZipFile zipFile = new net.lingala.zip4j.ZipFile(sourceFile);
        zipFile.setCharset(StandardCharsets.UTF_8);
        List<FileHeader> headers = zipFile.getFileHeaders();
        if (isRandomCode(headers)) {//判断文件名是否有乱码，有乱码，将编码格式设置成GBK
            zipFile.close();
            zipFile = new net.lingala.zip4j.ZipFile(sourceFileUrl);
            zipFile.setCharset(Charset.forName("GBK"));
        }
        if (!zipFile.isValidZipFile()) {
            throw new ZipException(xdoi18n.XdoI18nUtil.t("压缩文件不合法,可能被损坏."));
        }
        // 解压全部zip文件
        zipFile.extractAll(targetDirectory);
    }

    //待解压的文件名是否乱码
    private static boolean isRandomCode(List<FileHeader> fileHeaders) {
        for (FileHeader fileHeader : fileHeaders) {
            boolean canEnCode = Charset.forName("GBK").newEncoder().canEncode(fileHeader.getFileName());
            if (!canEnCode) {//canEnCode为true，表示不是乱码。false.表示乱码。是乱码则需要重新设置编码格式
                return true;
            }
        }
        return false;
    }
}
