package com.dcjet.cs.util;

import java.util.List;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * 批处理
 *
 * <AUTHOR>
 */
public class BatchUtil {

    private static final int DEFAULT_PAGE_SIZE = 500;

    /***
     *
     * @param list
     * @param <T>
     * @return
     */
    public static <T> Stream<List<T>> batchPage(List<T> list) {
      return batchPage(list, DEFAULT_PAGE_SIZE);
    }

    /***
     *
     * @param list
     * @param <T>
     * @return
     */
    public static <T> Stream<List<T>> batchPage(List<T> list, int pageSize) {
        if (list == null) {
            return Stream.empty();
        }
        return IntStream.range(0, (list.size() + pageSize - 1) / pageSize)
                .mapToObj(i -> list.subList(i * pageSize, Math.min(pageSize * (i + 1), list.size())));
    }
}
