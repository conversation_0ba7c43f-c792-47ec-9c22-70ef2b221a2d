package com.dcjet.cs.util;

public class ClassNameFormatter {

    /**
     * 获取spring上下文注册的service名
     * @param cls
     * @return
     */
    public static String getClassName(Class cls) {
        String simpleName = cls.getSimpleName();
        String realName = simpleName.substring(0,simpleName.indexOf("$$"));
        return toLowerCaseFirstOne(realName);
    }

    public static String toLowerCaseFirstOne(String s){
        if(Character.isLowerCase(s.charAt(0)))
            return s;
        else
            return (new StringBuilder()).append(Character.toLowerCase(s.charAt(0))).append(s.substring(1)).toString();
    }
}
