package com.dcjet.cs.util.emsApi;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 手册导入传入数据模型
 * @param <T>
 */
@ApiModel("手册导入传入数据模型")
@Setter
@Getter
public class EmsApiParamModel<T> implements Serializable {

    @ApiModelProperty("手册企业内部编号")
    public String copEmsNo;

    @ApiModelProperty("导入类型：1：新增导入 2:修改导入 3:删除导入")
    public String importType;

    @ApiModelProperty("用于追踪导入任务，每次请求使用不同值，建议使用GUID")
    public String taskID;

    @ApiModelProperty("业务数据")
    public List<T>  data;
}
