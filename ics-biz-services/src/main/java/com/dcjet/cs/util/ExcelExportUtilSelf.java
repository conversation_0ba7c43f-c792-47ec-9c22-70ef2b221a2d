package com.dcjet.cs.util;

import com.xdo.common.json.JsonObjectMapper;
import com.xdo.common.util.DateUtils;
import com.xdo.common.util.Parameters;
import com.xdo.domain.KeyValuePair;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.invoke.MethodHandles;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 导出 excel
 *
 * @author:
 * @date: 2018-12-26
 */
public class ExcelExportUtilSelf<T> {

    private static final Logger log = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());
    private static final int MAX_ROW = 1000;

    public ByteArrayInputStream exportDecTaxHeader(String sheetName, List<KeyValuePair<String, String>> header, List data)  throws Exception {
        Parameters.requireNonEmpty(sheetName);
        Parameters.requireNonEmpty(header);

        try (
                Workbook wb = data.size() > MAX_ROW ? new SXSSFWorkbook(MAX_ROW) : new XSSFWorkbook();
                ByteArrayOutputStream out = new ByteArrayOutputStream()
        ) {
            exportDecTaxHeader(sheetName, header, data, wb, out);
            return new ByteArrayInputStream(out.toByteArray());
        }
    }

    private void exportDecTaxHeader(String sheetName, List<KeyValuePair<String, String>> header, List<T> data, Workbook wb, ByteArrayOutputStream out) throws IOException {
        Sheet sheet = wb.createSheet(sheetName);
        int rowNum = 4;
        createHeader(wb, sheet, header, 0);
        createHeaderLine4CouldModify(wb, sheet, header,3);
        createHeaderLine4NotEmpty(wb, sheet, header,4);
        createHeaderLine4Alert(wb, sheet, header);
        int size = header.size();
        CellRangeAddress cellAddresses=new CellRangeAddress(3,3,0,size-1);
        sheet.addMergedRegion(cellAddresses);
        if (data != null) {
            for (T item : data) {
                var cla = item.getClass();
                JsonObjectMapper jsonObjectMapper = JsonObjectMapper.getInstance();
                Map map = jsonObjectMapper.convert(item, Map.class);
                Row row = sheet.createRow(rowNum);
                for (int i = 0; i < size; i++) {
                    String key = header.get(i).getKey();
                    row.createCell(i).setCellValue(getCellValue(map.get(key)));
                }
                rowNum++;
            }
        }

        wb.write(out);
    }

    public ByteArrayInputStream exportGenericMarOrg(String sheetName, List<KeyValuePair<String, String>> header, List data)  throws Exception {
        Parameters.requireNonEmpty(sheetName);
        Parameters.requireNonEmpty(header);

        try (
                Workbook wb = data.size() > MAX_ROW ? new SXSSFWorkbook(MAX_ROW) : new XSSFWorkbook();
                ByteArrayOutputStream out = new ByteArrayOutputStream()
        ) {
            exportGenericMatOrg(sheetName, header, data, wb, out);
            return new ByteArrayInputStream(out.toByteArray());
        }
    }

    private void exportGenericMatOrg(String sheetName, List<KeyValuePair<String, String>> header, List<T> data, Workbook wb, ByteArrayOutputStream out) throws IOException {
        Sheet sheet = wb.createSheet(sheetName);
        int rowNum = 4;
        createHeader(wb, sheet, header, 0);
        createHeaderLine4NotEmpty(wb, sheet, header,6);
        createHeaderLine4CouldModify(wb, sheet, header,2);
        createHeaderLine4Alert(wb, sheet, header);
        int size = header.size();
        CellRangeAddress cellAddresses=new CellRangeAddress(3,3,0,size-1);
        sheet.addMergedRegion(cellAddresses);
        if (data != null) {
            for (T item : data) {
                var cla = item.getClass();
                JsonObjectMapper jsonObjectMapper = JsonObjectMapper.getInstance();
                Map map = jsonObjectMapper.convert(item, Map.class);
                Row row = sheet.createRow(rowNum);
                for (int i = 0; i < size; i++) {
                    String key = header.get(i).getKey();
                    row.createCell(i).setCellValue(getCellValue(map.get(key)));
                }
                rowNum++;
            }
        }

        wb.write(out);
    }
    private static Row createHeader(Workbook wb, Sheet sheet, List<KeyValuePair<String, String>> header, int rowNum) {
        int size = header.size();
        Row row = sheet.createRow(rowNum);

        CellStyle cellStyle = createHeaderCellStyle(wb);
        cellStyle.setFillForegroundColor(IndexedColors.GREEN.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        for (int i = 0; i < size; i++) {
            Cell cell = row.createCell(i, CellType.STRING);
            cell.setCellValue(header.get(i).getValue());
            Font font = wb.createFont();
            font.setColor(IndexedColors.WHITE.getIndex());
            font.setBold(true);
            cellStyle.setFont(font);
            cell.setCellStyle(cellStyle);
        }
        return row;
    }
    private static Row createHeaderLine4NotEmpty(Workbook wb, Sheet sheet, List<KeyValuePair<String, String>> header, int rowNum) {
        int size = header.size();
        Row row = sheet.createRow(2);
        for (int i = 0; i < size; i++) {
            CellStyle cellStyle = wb.createCellStyle();;
            cellStyle.setFillForegroundColor(IndexedColors.PALE_BLUE.getIndex());
            cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            Cell cell = row.createCell(i, CellType.STRING);
            cell.setCellValue(i>=rowNum?xdoi18n.XdoI18nUtil.t("可空项"):xdoi18n.XdoI18nUtil.t("必填项"));
            if (rowNum > i) {
                Font font = wb.createFont();
                font.setColor(IndexedColors.RED.getIndex());
                cellStyle.setFont(font);
            }
            else
            {
                Font font = wb.createFont();
                font.setColor(IndexedColors.BLACK.getIndex());
                cellStyle.setFont(font);
            }
            cell.setCellStyle(cellStyle);
        }
        return row;
    }
    private static Row createHeaderLine4CouldModify(Workbook wb, Sheet sheet, List<KeyValuePair<String, String>> header, int rowNum) {
        int size = header.size();
        Row row = sheet.createRow(1);
        for (int i = 0; i < size; i++) {
            Cell cell = row.createCell(i, CellType.STRING);
            cell.setCellValue(i>rowNum?xdoi18n.XdoI18nUtil.t("可修改"):xdoi18n.XdoI18nUtil.t("不可修改"));
            CellStyle cellStyle = wb.createCellStyle();;
            cellStyle.setFillForegroundColor(IndexedColors.LIGHT_CORNFLOWER_BLUE.getIndex());
            cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            if(rowNum>=i) {
                Font font = wb.createFont();
                font.setColor(IndexedColors.RED.getIndex());
                cellStyle.setFont(font);
            }
            else
            {
                Font font = wb.createFont();
                font.setColor(IndexedColors.BLACK.getIndex());
                cellStyle.setFont(font);
            }
            cell.setCellStyle(cellStyle);
        }
        return row;
    }
    private static Row createHeaderLine4CouldModifyImg(Workbook wb, Sheet sheet, List<KeyValuePair<String, String>> header, int rowNum) {
        int size = header.size();
        Row row = sheet.createRow(1);
        for (int i = 0; i < size; i++) {
            Cell cell = row.createCell(i, CellType.STRING);
            cell.setCellValue(i>rowNum?xdoi18n.XdoI18nUtil.t("可修改"):xdoi18n.XdoI18nUtil.t("不可修改"));
            if (i==1){
                cell.setCellValue(xdoi18n.XdoI18nUtil.t("可修改"));
            }
            CellStyle cellStyle = wb.createCellStyle();;
            cellStyle.setFillForegroundColor(IndexedColors.LIGHT_CORNFLOWER_BLUE.getIndex());
            cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            if(rowNum>=i) {
                Font font = wb.createFont();
                if (i != 1) {
                    font.setColor(IndexedColors.RED.getIndex());
                }
                cellStyle.setFont(font);
            }
            else
            {
                Font font = wb.createFont();
                font.setColor(IndexedColors.BLACK.getIndex());
                cellStyle.setFont(font);
            }
            cell.setCellStyle(cellStyle);
        }
        return row;
    }
    private static Row createHeaderLine4Alert(Workbook wb, Sheet sheet, List<KeyValuePair<String, String>> header) {
        int size = header.size();
        Row row = sheet.createRow(3);

        CellStyle cellStyle = wb.createCellStyle();
        cellStyle.setFillForegroundColor(IndexedColors.PALE_BLUE.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Cell cell = row.createCell(0, CellType.STRING);
        Font font = wb.createFont();
        font.setColor(IndexedColors.RED.getIndex());
        cellStyle.setFont(font);
        cell.setCellValue(xdoi18n.XdoI18nUtil.t("特别说明：页面只放本次需要修改的数据，不需要修改的栏位不要更改或删除，只针对需要修改的栏位编辑修改即可。日期格式：yyyy-MM-dd"));
        cell.setCellStyle(cellStyle);
        return row;
    }


    public ByteArrayInputStream exportGenericMar(String sheetName, List<KeyValuePair<String, String>> header, List data)  throws Exception {
        Parameters.requireNonEmpty(sheetName);
        Parameters.requireNonEmpty(header);

        try (
                Workbook wb = data.size() > MAX_ROW ? new SXSSFWorkbook(MAX_ROW) : new XSSFWorkbook();
                ByteArrayOutputStream out = new ByteArrayOutputStream()
        ) {
            exportGenericMat(sheetName, header, data, wb, out);
            return new ByteArrayInputStream(out.toByteArray());
        }
    }

    private void exportGenericMat(String sheetName, List<KeyValuePair<String, String>> header, List<T> data, Workbook wb, ByteArrayOutputStream out) throws IOException {
        Sheet sheet = wb.createSheet(sheetName);
        int rowNum = 4;
        createHeader(wb, sheet, header, 0);
        createHeaderLine4NotEmpty(wb, sheet, header,3);
        createHeaderLine4CouldModifyImg(wb, sheet, header,3);
        createHeaderLine4Alert(wb, sheet, header);
        int size = header.size();
        CellRangeAddress cellAddresses=new CellRangeAddress(3,3,0,size-1);
        sheet.addMergedRegion(cellAddresses);
        if (data != null) {
            for (T item : data) {
                var cla = item.getClass();
                JsonObjectMapper jsonObjectMapper = JsonObjectMapper.getInstance();
                Map map = jsonObjectMapper.convert(item, Map.class);
                Row row = sheet.createRow(rowNum);
                for (int i = 0; i < size; i++) {
                    String key = header.get(i).getKey();
                    row.createCell(i).setCellValue(getCellValue(map.get(key)));
                }
                rowNum++;
            }
        }

        wb.write(out);
    }

    public ByteArrayInputStream exportGenericNoBond(String sheetName, List<KeyValuePair<String, String>> header, List data)  throws Exception {
        Parameters.requireNonEmpty(sheetName);
        Parameters.requireNonEmpty(header);

        try (
                Workbook wb = data.size() > MAX_ROW ? new SXSSFWorkbook(MAX_ROW) : new XSSFWorkbook();
                ByteArrayOutputStream out = new ByteArrayOutputStream()
        ) {
            exportGenericNoBond(sheetName, header, data, wb, out);
            return new ByteArrayInputStream(out.toByteArray());
        }
    }

    private void exportGenericNoBond(String sheetName, List<KeyValuePair<String, String>> header, List<T> data, Workbook wb, ByteArrayOutputStream out) throws IOException {
        Sheet sheet = wb.createSheet(sheetName);
        int rowNum = 4;
        createHeader(wb, sheet, header, 0);
        createHeaderLine4NotEmpty(wb, sheet, header,5);
        createHeaderLine4CouldModify(wb, sheet, header,1);
        createHeaderLine4Alert(wb, sheet, header);
        int size = header.size();
        CellRangeAddress cellAddresses=new CellRangeAddress(3,3,0,size-1);
        sheet.addMergedRegion(cellAddresses);
        if (data != null) {
            for (T item : data) {
                var cla = item.getClass();
                JsonObjectMapper jsonObjectMapper = JsonObjectMapper.getInstance();
                Map map = jsonObjectMapper.convert(item, Map.class);
                Row row = sheet.createRow(rowNum);
                for (int i = 0; i < size; i++) {
                    String key = header.get(i).getKey();
                    row.createCell(i).setCellValue(getCellValue(map.get(key)));
                }
                rowNum++;
            }
        }

        wb.write(out);
    }
    private static Row createHeaderNoBondLine2(Workbook wb, Sheet sheet, List<KeyValuePair<String, String>> header) {
        int size = header.size();
        Row row = sheet.createRow(2);

        CellStyle cellStyle = wb.createCellStyle();;
        cellStyle.setFillForegroundColor(IndexedColors.LIGHT_CORNFLOWER_BLUE.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        for (int i = 0; i < size; i++) {
            Cell cell = row.createCell(i, CellType.STRING);
            cell.setCellValue(i>1?xdoi18n.XdoI18nUtil.t("可修改"):xdoi18n.XdoI18nUtil.t("不可修改"));

            cell.setCellStyle(cellStyle);
        }
        return row;
    }

    /***
     *
     * @param obj
     * @return
     */
    private static String getCellValue(Object obj) {
        if (obj == null) {
            return "";
        }
        if (obj instanceof Date) {
            Date date = (Date) obj;
            return DateUtils.dateToString(date);
        } else if (obj instanceof LocalDate) {
            LocalDate date = (LocalDate) obj;
            return date.format(DateTimeFormatter.ISO_LOCAL_DATE);
        } else if (obj instanceof LocalDateTime) {
            LocalDateTime dateTime = (LocalDateTime) obj;
            return dateTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        }

        return obj.toString();
    }
    /**
     * 创建header cell style
     *
     * @param wb
     */
    private static CellStyle createHeaderCellStyle(Workbook wb) {
        Font font = wb.createFont();
        font.setBold(true);
        font.setFontName("Microsoft YaHei");

        CellStyle cellStyle = wb.createCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setFont(font);
        return cellStyle;
    }

    public ByteArrayInputStream exportGenericHeadList(String sheetName, List<KeyValuePair<String, String>> header, List data)throws Exception {
        Parameters.requireNonEmpty(sheetName);
        Parameters.requireNonEmpty(header);

        try (
                Workbook wb = data.size() > MAX_ROW ? new SXSSFWorkbook(MAX_ROW) : new XSSFWorkbook();
                ByteArrayOutputStream out = new ByteArrayOutputStream()
        ) {
            exportGenericHead(sheetName, header, data, wb, out);
            return new ByteArrayInputStream(out.toByteArray());
        }
    }

    private void exportGenericHead(String sheetName, List<KeyValuePair<String, String>> header, List<T> data, Workbook wb, ByteArrayOutputStream out) throws Exception {
        Sheet sheet = wb.createSheet(sheetName);
        int rowNum = 4;
        createHeader(wb, sheet, header, 0);//栏位名称
        createHeaderLine4NotEmpty(wb, sheet, header,9);  //必填项
        createHeaderLine4CouldModify(wb, sheet, header,1); //可修改
        createHeaderLine4Alert(wb, sheet, header);
        int size = header.size();
        CellRangeAddress cellAddresses=new CellRangeAddress(3,3,0,size-1);
        sheet.addMergedRegion(cellAddresses);
        if (data != null) {
            for (T item : data) {
                var cla = item.getClass();
                JsonObjectMapper jsonObjectMapper = JsonObjectMapper.getInstance();
                Map map = jsonObjectMapper.convert(item, Map.class);
                Row row = sheet.createRow(rowNum);
                for (int i = 0; i < size; i++) {
                    String key = header.get(i).getKey();
                    row.createCell(i).setCellValue(getCellValue(map.get(key)));
                }
                rowNum++;
            }
        }

        wb.write(out);
    }

    /**
     * 进出口保税
     * @param sheetName
     * @param header
     * @param data
     * @return
     * @throws Exception
     */
    public ByteArrayInputStream exportGenericBondList(String sheetName, List<KeyValuePair<String, String>> header, List data)throws Exception {
        Parameters.requireNonEmpty(sheetName);
        Parameters.requireNonEmpty(header);

        try (
                Workbook wb = data.size() > MAX_ROW ? new SXSSFWorkbook(MAX_ROW) : new XSSFWorkbook();
                ByteArrayOutputStream out = new ByteArrayOutputStream()
        ) {
            exportGenericBondList(sheetName, header, data, wb, out);
            return new ByteArrayInputStream(out.toByteArray());
        }
    }

    /**
     * 进出口保税
     * @param sheetName
     * @param header
     * @param data
     * @param wb
     * @param out
     * @throws Exception
     */
    private void exportGenericBondList(String sheetName, List<KeyValuePair<String, String>> header, List<T> data, Workbook wb, ByteArrayOutputStream out) throws Exception {
        Sheet sheet = wb.createSheet(sheetName);
        int rowNum = 4;
        createHeader(wb, sheet, header, 0);
        createHeaderLine4NotEmpty(wb, sheet, header,9);
        createHeaderLine4CouldModify(wb, sheet, header,2);
        createHeaderLine4Alert(wb, sheet, header);
        int size = header.size();
        CellRangeAddress cellAddresses=new CellRangeAddress(3,3,0,size-1);
        sheet.addMergedRegion(cellAddresses);
        if (data != null) {
            for (T item : data) {
                var cla = item.getClass();
                JsonObjectMapper jsonObjectMapper = JsonObjectMapper.getInstance();
                Map map = jsonObjectMapper.convert(item, Map.class);
                Row row = sheet.createRow(rowNum);
                for (int i = 0; i < size; i++) {
                    String key = header.get(i).getKey();
                    row.createCell(i).setCellValue(getCellValue(map.get(key)));
                }
                rowNum++;
            }
        }

        wb.write(out);
    }

    public ByteArrayInputStream exportGenericDecTrack(String sheetName, List<KeyValuePair<String, String>> header, List data) throws Exception {
        Parameters.requireNonEmpty(sheetName);
        Parameters.requireNonEmpty(header);

        try (
                Workbook wb = data.size() > MAX_ROW ? new SXSSFWorkbook(MAX_ROW) : new XSSFWorkbook();
                ByteArrayOutputStream out = new ByteArrayOutputStream()
        ) {
            exportGenericDecTrack(sheetName, header, data, wb, out);
            return new ByteArrayInputStream(out.toByteArray());
        }
    }

    private void exportGenericDecTrack(String sheetName, List<KeyValuePair<String, String>> header, List<T> data, Workbook wb, ByteArrayOutputStream out) throws IOException {
        Sheet sheet = wb.createSheet(sheetName);
        int rowNum = 4;
        createHeader(wb, sheet, header, 0);//栏位名称
        createHeaderLine4NotEmpty(wb, sheet, header,1);//必填项
        createHeaderLine4CouldModify(wb, sheet, header,0);//可修改
        createHeaderLineTrack3(wb, sheet, header);
        int size = header.size();
        CellRangeAddress cellAddresses=new CellRangeAddress(3,3,0,size-1);
        sheet.addMergedRegion(cellAddresses);
        if (data != null) {
            for (T item : data) {
                var cla = item.getClass();
                JsonObjectMapper jsonObjectMapper = JsonObjectMapper.getInstance();
                Map map = jsonObjectMapper.convert(item, Map.class);
                Row row = sheet.createRow(rowNum);
                for (int i = 0; i < size; i++) {
                    String key = header.get(i).getKey();
                    row.createCell(i).setCellValue(getCellValue(map.get(key)));
                }
                rowNum++;
            }
        }

        wb.write(out);
    }


    private static Row createHeaderLineTrack3(Workbook wb, Sheet sheet, List<KeyValuePair<String, String>> header) {
        int size = header.size();
        Row row = sheet.createRow(3);

        CellStyle cellStyle = wb.createCellStyle();
        cellStyle.setFillForegroundColor(IndexedColors.PALE_BLUE.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Cell cell = row.createCell(0, CellType.STRING);
        Font font = wb.createFont();
        font.setColor(IndexedColors.RED.getIndex());
        cellStyle.setFont(font);
        cell.setCellValue(xdoi18n.XdoI18nUtil.t("特别说明：页面只放本次需要修改的料号级数据，不需要修改的栏位不要更改或删除，只针对需要修改的栏位编辑修改即可,所有日期格式”yyyy-MM-dd“，例如2020-06-17"));
        cell.setCellStyle(cellStyle);
        return row;
    }


    private static Row createHeaderLogistics3(Workbook wb,String gMark, Sheet sheet, List<KeyValuePair<String, String>> header) {
        int size = header.size();
        Row row = sheet.createRow(3);

        CellStyle cellStyle = wb.createCellStyle();
        cellStyle.setFillForegroundColor(IndexedColors.PALE_BLUE.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Cell cell = row.createCell(0, CellType.STRING);
        Font font = wb.createFont();
        font.setColor(IndexedColors.RED.getIndex());
        cellStyle.setFont(font);
//        if (StringUtils.equals(gMark, "I")) {
            cell.setCellValue(xdoi18n.XdoI18nUtil.t("特别说明：页面只放本次需要修改的料号级数据，不需要修改的栏位不要更改或删除，只针对需要修改的栏位编辑修改即可,所有日期格式”yyyy-MM-dd“，例如2020-06-17"));
//        } else if (StringUtils.equals(gMark, "E")){
//            cell.setCellValue("特别说明：页面只放本次需要修改的料号级数据，不需要修改的栏位不要更改或删除!");
//        }
        cell.setCellStyle(cellStyle);
        return row;
    }


    public ByteArrayInputStream exportGenericPallet(String sheetName, List<KeyValuePair<String, String>> header, List data) throws IOException {
        Parameters.requireNonEmpty(sheetName);
        Parameters.requireNonEmpty(header);

        try (
                Workbook wb = data.size() > MAX_ROW ? new SXSSFWorkbook(MAX_ROW) : new XSSFWorkbook();
                ByteArrayOutputStream out = new ByteArrayOutputStream()
        ) {
            exportGenericPallet(sheetName, header, data, wb, out);
            return new ByteArrayInputStream(out.toByteArray());
        }
    }

    private void exportGenericPallet(String sheetName, List<KeyValuePair<String, String>> header, List<T> data, Workbook wb, ByteArrayOutputStream out) throws IOException {
        Sheet sheet = wb.createSheet(sheetName);
        int rowNum = 4;
        createHeader(wb, sheet, header, 0);
        createHeaderLine4NotEmpty(wb, sheet, header,5);
        createHeaderLine4CouldModify(wb, sheet, header,2);
        createHeaderLine4Alert(wb, sheet, header);
        int size = header.size();
        CellRangeAddress cellAddresses=new CellRangeAddress(3,3,0,size-1);
        sheet.addMergedRegion(cellAddresses);
        if (data != null) {
            for (T item : data) {
                var cla = item.getClass();
                JsonObjectMapper jsonObjectMapper = JsonObjectMapper.getInstance();
                Map map = jsonObjectMapper.convert(item, Map.class);
                Row row = sheet.createRow(rowNum);
                for (int i = 0; i < size; i++) {
                    String key = header.get(i).getKey();
                    row.createCell(i).setCellValue(getCellValue(map.get(key)));
                }
                rowNum++;
            }
        }

        wb.write(out);
    }

    public ByteArrayInputStream exportGenericBatchEdit(String sheetName, String gMark, List<KeyValuePair<String, String>> header, List data) throws Exception {
        Parameters.requireNonEmpty(sheetName);
        Parameters.requireNonEmpty(header);

        try (
                Workbook wb = data.size() > MAX_ROW ? new SXSSFWorkbook(MAX_ROW) : new XSSFWorkbook();
                ByteArrayOutputStream out = new ByteArrayOutputStream()
        ) {
            exportGenericBatchEdit(sheetName,gMark, header, data, wb, out);
            return new ByteArrayInputStream(out.toByteArray());
        }
    }

    private void exportGenericBatchEdit(String sheetName, String gMark, List<KeyValuePair<String, String>> header, List<T> data, Workbook wb, ByteArrayOutputStream out) throws IOException {
        Sheet sheet = wb.createSheet(sheetName);
        int rowNum = 4;
        createHeader(wb, sheet, header, 0);//栏位名称

        createHeaderLine4NotEmpty(wb, sheet, header, 1);//必填项
        if (StringUtils.equals(gMark, "I")) {
            createHeaderLine4CouldModify(wb, sheet, header, 2);//可修改
        } else if (StringUtils.equals(gMark, "E")){
//            createHeaderLine4NotEmpty(wb, sheet, header, 3);//必填项
            createHeaderLine4CouldModify(wb, sheet, header, 3);//可修改
        }
        createHeaderLogistics3(wb, gMark, sheet, header);
        int size = header.size();
        CellRangeAddress cellAddresses=new CellRangeAddress(3,3,0,size-1);
        sheet.addMergedRegion(cellAddresses);
        if (data != null) {
            for (T item : data) {
                var cla = item.getClass();
                JsonObjectMapper jsonObjectMapper = JsonObjectMapper.getInstance();
                Map map = jsonObjectMapper.convert(item, Map.class);
                Row row = sheet.createRow(rowNum);
                for (int i = 0; i < size; i++) {
                    String key = header.get(i).getKey();
                    row.createCell(i).setCellValue(getCellValue(map.get(key)));
                }
                rowNum++;
            }
        }

        wb.write(out);
    }

    public ByteArrayInputStream exportGenericPrice(String sheetName, List<KeyValuePair<String, String>> header, List data) throws Exception {
        Parameters.requireNonEmpty(sheetName);
        Parameters.requireNonEmpty(header);

        try (
                Workbook wb = data.size() > MAX_ROW ? new SXSSFWorkbook(MAX_ROW) : new XSSFWorkbook();
                ByteArrayOutputStream out = new ByteArrayOutputStream()
        ) {
            exportGenericPrice(sheetName, header, data, wb, out);
            return new ByteArrayInputStream(out.toByteArray());
        }
    }


    private void exportGenericPrice(String sheetName, List<KeyValuePair<String, String>> header, List<T> data, Workbook wb, ByteArrayOutputStream out) throws IOException {
        Sheet sheet = wb.createSheet(sheetName);
        int rowNum = 4;
        createHeader(wb, sheet, header, 0);//栏位名称
        createHeaderLine4NotPriceEmpty(wb, sheet, header,9);//必填项
        createHeaderLine4CouldModify(wb, sheet, header,7);//可修改
        createHeaderLinePriceTrack3(wb, sheet, header);
        int size = header.size();
        CellRangeAddress cellAddresses=new CellRangeAddress(3,3,0,size-1);
        sheet.addMergedRegion(cellAddresses);
        if (data != null) {
            for (T item : data) {
                var cla = item.getClass();
                JsonObjectMapper jsonObjectMapper = JsonObjectMapper.getInstance();
                Map map = jsonObjectMapper.convert(item, Map.class);
                Row row = sheet.createRow(rowNum);
                for (int i = 0; i < size; i++) {
                    String key = header.get(i).getKey();
                    row.createCell(i).setCellValue(getCellValue(map.get(key)));
                }
                rowNum++;
            }
        }

        wb.write(out);
    }

    private static Row createHeaderLine4NotPriceEmpty(Workbook wb, Sheet sheet, List<KeyValuePair<String, String>> header, int rowNum) {
        int size = header.size();
        Row row = sheet.createRow(2);
        for (int i = 0; i < size; i++) {
            CellStyle cellStyle = wb.createCellStyle();;
            cellStyle.setFillForegroundColor(IndexedColors.PALE_BLUE.getIndex());
            cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            Cell cell = row.createCell(i, CellType.STRING);
            if (i != 6 && i != 7) {
                cell.setCellValue(i >= rowNum ? xdoi18n.XdoI18nUtil.t("可空项") : xdoi18n.XdoI18nUtil.t("必填项"));
            }else {
                cell.setCellValue(xdoi18n.XdoI18nUtil.t("可空项"));
            }
            if(rowNum>i) {
                Font font = wb.createFont();
                font.setColor(IndexedColors.RED.getIndex());
                cellStyle.setFont(font);
            }
            else
            {
                Font font = wb.createFont();
                font.setColor(IndexedColors.BLACK.getIndex());
                cellStyle.setFont(font);
            }
            cell.setCellStyle(cellStyle);
        }
        return row;
    }


    private static Row createHeaderLinePriceTrack3(Workbook wb, Sheet sheet, List<KeyValuePair<String, String>> header) {
        int size = header.size();
        Row row = sheet.createRow(3);

        CellStyle cellStyle = wb.createCellStyle();
        cellStyle.setFillForegroundColor(IndexedColors.PALE_BLUE.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Cell cell = row.createCell(0, CellType.STRING);
        Font font = wb.createFont();
        font.setColor(IndexedColors.RED.getIndex());
        cellStyle.setFont(font);
        cell.setCellValue(xdoi18n.XdoI18nUtil.t("特别说明：页面只放本次需要修改的料号级数据，不需要修改的栏位不要更改或删除，只针对需要修改的栏位编辑修改即可!"));
        cell.setCellStyle(cellStyle);
        return row;
    }
}
