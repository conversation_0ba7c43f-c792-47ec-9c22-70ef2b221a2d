package com.dcjet.cs.util;
/**
  * <AUTHOR>
  * @date: 2019-4-17
 */
public class Constants {

    public final static int LIST_INITIAL_CAPACITY = 16;
    /***
     * redis key 前缀
     */
    public final static String REDIS_KEY_PREFIX = "gwstd-";

    public final static String JOB_STATUS_0 = "0";
    public final static String JOB_STATUS_1 = "1";
    public final static String JOB_STATUS_2 = "2";
    public final static String JOB_STATUS_3 = "3";
    /***
     *
     */
    public final static String ERROR_MESSAGE_500 = "服务调用失败，请联系开发商";
    /***
     *
     */
    public final static String ERROR_MESSAGE_CONFIG_500 = "当前企业数据配置异常，请联系开发商";
    public final static String APPNO = "bootstrap";
    public final static String SSO_APPNO = "SSO";
    public final static String ERROR_MSG = "GUID无效";
    public final static String W_NAMESPACE = "DEFAULT";


    /***
     * 开放接口中最大能传输的数据条数
     */
    public final static int OPEN_POST_MAX_DATA = 100;

    /**
     * FTP业务类型
     */
    public final static String ATTACHED = "ATTACHED";//处理附件
    public final static String ERP_FILE = "ERP_FILE";//处理ERP数据
    //库存接口 数据提取失败
    public final static String STATUS_99 = "99";
    /**
     * FTP文件类型（分不同前缀）
     */
    public final static String ERP_FTP_FILE_ITEM = "item";//物料
    public final static String ERP_FTP_FILE_PO = "po";//进口
    public final static String ERP_FTP_FILE_SHIPPING = "shipping";//出口
    public final static String ERP_FTP_FILE_IMG = "img";//料件出入库
    public final static String ERP_FTP_FILE_EXG = "exg";//成品出入库
    public final static String ERP_FTP_FILE_CUS = "cus";//第三方资料
    public final static String ERP_FTP_FILE_IMGWH = "imgwh";//料件库存
    public final static String ERP_FTP_FILE_EXGWH = "exgwh";//成品库存
    public final static String ERP_FTP_FILE_BOM = "bom";//单损耗
    public final static String ERP_FTP_FILE_RATE = "rate";//汇率
    public final static String ERP_FTP_FILE_WIPWH = "wipwh";//在制品库存
    public final static String ERP_STOCK = "STOCK";//库存
    public final static String ERP_STOCK_IMG = "IMG_STOCK";//料件库存
    public final static String ERP_STOCK_EXG = "EXG_STOCK";//成品库存
    public final static String ERP_STOCK_SEMI_EXG = "SEMI_EXG_STOCK";//半成品库存
    public final static String ERP_STOCK_WIP = "WIP_STOCK";//在制品库存
    //用于字符替换的过渡常量
    public final static String REPLACE_SPECITAL_CHAR = "ERP_FTP";
    //待接收目录
    public final static String ERP_FTP_DIR_RECEIVE = "receive";
    //处理完成目录
    public final static String ERP_FTP_DIR_SUCCESS = "success";
    //处理错误目录
    public final static String ERP_FTP_DIR_ERROR = "error";
    //临时目录
    public final static String ERP_FTP_DIR_TEMP = "temp";

    /**
     * 翻译模块常量
     */
    public final static String TRANSLATE_MAT_CLASSIFY = "matClassify";//物料待归类
    public final static String TRANSLATE_MAT_CENTER = "matCenter";//物料中心
    public final static String TRANSLATE_BILL_I = "billI";//进口提单
    public final static String TRANSLATE_BILL_E = "billE";//出口提单

    /**
     * 定义需要转码的参数类型
     */
    public final static String CONVERT_BOND_MARK = "BONDMARK";//保税完标记
    public final static String CONVERT_GMARK = "GMARK";//料件/成品标识
    public final static String CONVERT_CURR = "CURR";//币制
    public final static String CONVERT_COUNTRY = "COUNTRY";//国别
    public final static String CONVERT_UNIT = "UNIT";//计量单位
    public final static String CONVERT_IN_WAY = "IN_WAY";//进口方式
    public final static String CONVERT_OUT_WAY = "OUT_WAY";//出口方式

    /**
     * 定义代码转换常量
     */
    public final static int PAGE_COUNT = 5000;
    /**
     * 定义代码转换常量
     */
    public final static int PAGE_COUNT2 = 1000;
    /**
     * 定义税率解析分页常量
     */
    public final static int TARIFF_PAGE_COUNT = 500;
    /**
     * 定义中期核查数据提取分页常量
     */
    public final static int PAGE_COUNT_MID = 20000;
    /**
     * ERP数据转换模块常量
     */
    public final static String CONVERT_MAT = "mat";//ERP物料清单
    public final static String CONVERT_DECE = "dece";//ERP出口明细
    public final static String CONVERT_DECI = "deci";//ERP进口明细
    public final static String CONVERT_IMGIE = "imgie";//ERP料件出入库
    public final static String CONVERT_EXGIE = "exgie";//ERP成品出入库

    /**
     * erp数据回填类型
     */
    public final static String XCODE_FILL = "XCODE_FILL";//报关单号/贸易方式回填
    public final static String MAKTX_FILL = "MAKTX_FILL";//报关品名回填
    /***
     * 最小值
     */
    public final static String MRP_MIN = "min";
    /***
     * 最大值
     */
    public final static String MRP_MAX = "max";
    /***
     * 加权平均
     */
    public final static String MRP_WEI_AVG = "wei_avg";

    public static final String CURR_USD = "502";
    public static final String CURR_RMB = "142";
    public static final String GBK = "GBK";
    /**
     * 定义库存查询默认PageSize
     */
    public final static int PAGE_SIZE = 500;
    /**
     * 免表参数 审批依据-0、审批部门代码-1、产业政策审批条目-2、业务种类-3
     */
    public static final String AVOID_PARAMS_0 = "0";
    public static final String AVOID_PARAMS_1 = "1";
    public static final String AVOID_PARAMS_2 = "2";
    public static final String AVOID_PARAMS_3 = "3";
    /**
     * 中期核查任务类型 入库明细-MID_I、出库明细-MID_E
     * 进口物料核对-MID_I_CHECK、出口物料核对-MID_E_CHECK
     * 进口关联核对-MID_I_REL_CHECK、出口关联核对-MID_E_REL_CHECK
     */
    public static final String MID_I = "MID_I";
    public static final String MID_E = "MID_E";
    public static final String MID_I_CHECK = "MID_I_CHECK";
    public static final String MID_E_CHECK = "MID_E_CHECK";
    public static final String MID_I_REL_CHECK = "MID_I_REL_CHECK";
    public static final String MID_E_REL_CHECK = "MID_E_REL_CHECK";
    public static final String OPTORUN_BALANCE_TASK = "optorunBalance";
    public static final String CAL_ORDER_EXPORT_QTY = "CAL_ORDER_EXPORT_QTY";
    public static final String DOCUMENTS_REPORT = "DOCUMENTS_REPORT";//泰马克出口单证统计表
    public static final String REPORT_DEC_I_HEAD = "REPORT_DEC_I_HEAD";
    public static final String REPORT_DEC_I_HEAD_LIST = "REPORT_DEC_I_HEAD_LIST";
    public static final String REPORT_DEC_E_HEAD = "REPORT_DEC_E_HEAD";
    public static final String REPORT_DEC_E_HEAD_LIST = "REPORT_DEC_E_HEAD_LIST";
    public static final String REPORT_BILL_I_HEAD = "REPORT_BILL_I_HEAD";
    public static final String REPORT_BILL_I_HEAD_LIST = "REPORT_BILL_I_HEAD_LIST";
    public static final String REPORT_BILL_E_HEAD = "REPORT_BILL_E_HEAD";
    public static final String REPORT_BILL_E_HEAD_LIST = "REPORT_BILL_E_HEAD_LIST";
    public static final String REPORT_ENTRY_E_HEAD = "REPORT_ENTRY_E_HEAD";
    public static final String REPORT_ENTRY_E_HEAD_LIST = "REPORT_ENTRY_E_HEAD_LIST";
    public static final String REPORT_ENTRY_I_HEAD = "REPORT_ENTRY_I_HEAD";
    public static final String REPORT_ENTRY_I_HEAD_LIST = "REPORT_ENTRY_I_HEAD_LIST";
    public static final String REPORT_DEC_I_HEAD_FT = "REPORT_DEC_I_HEAD_FT";
    public static final String REPORT_DEC_I_HEAD_LIST_FT = "REPORT_DEC_I_HEAD_LIST_FT";
    public static final String REPORT_DEC_E_HEAD_FT = "REPORT_DEC_E_HEAD_FT";
    public static final String REPORT_DEC_E_HEAD_LIST_FT = "REPORT_DEC_E_HEAD_LIST_FT";
    public static final String REPORT_PACKAGING_STATISTICS_FT = "REPORT_PACKAGING_STATISTICS_FT";
    /**
     * 减免税附件
     */
    public static final String BUSINESS_TYPE_DEV = "DEV";
    /**
     * 进口提单附件
     */
    public static final String BUSINESS_TYPE_IM = "IM";
    /**
     * 出口提单附件
     */
    public static final String BUSINESS_TYPE_EM = "EM";
    /**
     * 备案料件/成品,企业料件/成品附件
     */
    public static final String BUSINESS_TYPE_2 = "2";
    /**
     * 文件管理附件
     */
    public static final String BUSINESS_TYPE_FM = "FM";
    /**
     * 基础资料
     */
    public static final String BUSINESS_TYPE_B = "B";
    /**
     * 费用管理
     */
    public static final String BUSINESS_TYPE_C = "C";
    /**
     * 预警管理
     */
    public static final String BUSINESS_TYPE_W = "WA";
    /**
     * 待归类管理
     */
    public static final String BUSINESS_TYPE_MC = "MC";
    /**
     * 外发管理
     */
    public static final String BUSINESS_TYPE_WF = "WF";
    /**
     * 退换管理
     */
    public static final String BUSINESS_TYPE_R = "R";
    /**
     * 进口结转管理
     */
    public static final String BUSINESS_TYPE_ID = "ID";
    /**
     * 出口结转管理
     */
    public static final String BUSINESS_TYPE_ED = "ED";
    /**
     * 内销管理
     */
    public static final String BUSINESS_TYPE_MRP = "MRP";
    /**
     * 税金管理
     */
    public static final String BUSINESS_TYPE_TAX = "TAX";
    /**
     * 检验管理
     */
    public static final String BUSINESS_TYPE_CH = "CH";
    /**
     * 金二
     */
    public final static String K2_ENTRY = "【金二一般贸易报关单】";
    /**
     * 报关立交桥
     */
    public final static String LJQ_ENTRY = "【报关立交桥一般贸易报关单】";
    /**
     * 金二&报关立交桥
     */
    public final static String K2_AND_LJQ_ENTRY = "【金二&报关立交桥一般贸易报关单】";
    /**
     * 备案回执
     */
    public final static String RECORD_RECEIPT = "【备案回执处理】";
    /**
     * 捷关通回执处理
     */
    public final static String JGT_RECORD_RECEIPT = "【捷关通回执处理】";
    /**
     * 邮件
     */
    public final static String WARING_EMAIL = "【预警邮件发送】";
    /**
     * 通知邮件
     */
    public final static String NOTICE_EMAIL = "【通知邮件发送】";

    /**
     * 精度
     */
    public static final Integer DEC_TOTAL_PRECISION = 2;
    public static final Integer DEC_PRICE_PRECISION = 4;
    public static final Integer QTY_PRECISION = 3;
    public static final Integer QTY_1_PRECISION = 3;
    public static final Integer QTY_2_PRECISION = 3;
    public static final Integer NET_WT_PRECISION = 3;
    public static final Integer GROSS_WT_PRECISION = 3;
    public static final Integer VOLUME_PRECISION = 3;

    /**
     * 操作类型
     */
    public static final String OPR_TYPE_INSERT = "insert";
    public static final String OPR_TYPE_UPDATE = "update";

    /** 第三方地址类型 */
    /**
     * 发送清单备案接口
     */
    public static final String SEND_EMS_BILL_LIST = "SEND_EMS_BILL_LIST";
    /**
     * 报关立交桥api、报关立交桥导入接口、报关立交桥上载接口、报关立交桥-作废-接口
     */
    public static final String LJQ_ENTRY_URL = "LJQ_ENTRY_URL";
    /**
     * 撤回清单备案接口
     */
    public static final String CANCEL_EMS_BILL_LIST = "CANCEL_EMS_BILL_LIST";
    /**
     * 报关单回执查询接口
     */
    public static final String ENTRY_RET_INFO = "ENTRY_RET_INFO";
    /**
     * 点讯通发送清单接口、点讯通物料备案状态接口
     */
    public static final String DXT = "DXT";
    /**
     * 金二非保税报关单上载接口
     */
    public static final String UPLOAD_ENTRY_URL = "UPLOAD_ENTRY_URL";
    /**
     * 金二手册料件备案接口
     */
    public static final String EML_CUS_IMG_URL = "EML_CUS_IMG_URL";
    /**
     * 金二账册料件备案接口
     */
    public static final String EMS_CUS_IMG_URL = "EMS_CUS_IMG_URL";
    /**
     * 金二手册成品备案接口
     */
    public static final String EML_CUS_EXG_URL = "EML_CUS_EXG_URL";
    /**
     * 金二账册成品备案接口
     */
    public static final String EMS_CUS_EXG_URL = "EMS_CUS_EXG_URL";
    /**
     * 账册单损耗接口
     */
    public static final String EMS_CONSUME_URL = "EMS_CONSUME_URL";
    /**
     * 手册单损耗接口
     */
    public static final String EML_CONSUME_URL = "EML_CONSUME_URL";
    /**
     * 备案号、企业内部编号地址（账册）接口
     */
    public static final String EMS_COP_EMS_NO_URL = "EMS_COP_EMS_NO_URL";
    /**
     * 备案号、企业内部编号地址（手册）接口
     */
    public static final String EML_COP_EMS_NO_URL = "EML_COP_EMS_NO_URL";
    /**
     * 根据手册号获取手册表头接口
     */
    public static final String EML_HEAD_EMS_NO_URL = "EML_HEAD_EMS_NO_URL";
    /**
     * 获取手册料件明细信息接口
     */
    public static final String EML_IMG_URL = "EML_IMG_URL";
    /**
     * 获取手册成品明细信息接口
     */
    public static final String EML_EXG_URL = "EML_EXG_URL";
    /**
     * 获取帐册料件明细信息接口
     */
    public static final String EMS_IMG_URL = "EMS_IMG_URL";
    /**
     * 获取帐册成品明细信息接口
     */
    public static final String EMS_EXG_URL = "EMS_EXG_URL";
    /**
     * 账册备案状态返回接口
     */
    public static final String EMS_PASS_STATUS_URL = "EMS_PASS_STATUS_URL";
    /**
     * 手册备案状态返回接口
     */
    public static final String EML_PASS_STATUS_URL = "EML_PASS_STATUS_URL";
    /**
     * 参数（国家）转换列表接口
     */
    public static final String COUNTRY_TRANS = "COUNTRY_TRANS";
    /**
     * 获取舱单信息接口
     */
    public static final String GET_MANIFEST_INFO = "GET_MANIFEST_INFO";
    /**
     * 爬取接口
     */
    public static final String DUTY_FORM_SYNC_URL = "DUTY_FORM_SYNC_URL";
    public static final String DUTY_FORM_POSTTASK_URL = "DUTY_FORM_POSTTASK_URL";
    public static final String DUTY_FORM_LOCAL_URL = "DUTY_FORM_LOCAL_URL";
    /**
     * 查询报关单删改单记录数、被记录差错记录数、报关单查验记录数接口
     */
    public static final String ENTRY_RECORD = "ENTRY_RECORD";
    /**
     * 回填报关单号
     */
    public static final String ENTRY_NO_BACK = "ENTRY_NO_BACK";
    /**
     * 淳华报关品名回填
     */
    public static final String G_NAME_BACK = "G_NAME_BACK";
    /**
     * 淳华报关单号回填(淳华通过发票号回填报关单号/贸易方式)
     */
    public static final String INVOICE_BACK = "INVOICE_BACK";

    /**
     * 备案号、企业内部编号地址（账册）接口
     */
    public static final String EMS_NO_LIST_URL = "EMS_NO_LIST_URL";
    /**
     * 备案号、企业内部编号地址（手册）接口
     */
    public static final String EML_NO_LIST_URL = "EML_NO_LIST_URL";

    /**
     * 报关小助手报关单打印接口
     */
    public static final String ENTRY_DATA_PRINT = "ENTRY_DATA_PRINT";
    /**
     * 报文中心报关单下载接口
     */
    public static final String ENTRY_DATA_DOWNLOAD = "ENTRY_DATA_DOWNLOAD";
    /**
     * 报文中心国际物流信息订阅接口
     */
    public static final String LOGISTICS_SUB = "LOGISTICS_SUB";
    /**
     * 报文中心国际物流信息查询接口
     */
    public static final String LOGISTICS_QUERY = "LOGISTICS_QUERY";
    /**
     * RCEP税率信息接口
     */
    public static final String PRARM_RCEP = "PRARM_RCEP";
    /**
     * 代理企业权限类型
     */
    public static final String AGENT_PRE_BILL = "PRE_BILL";
    public static final String AGENT_LOG_TRACK = "LOG_TRACK";
    public static final String AGENT_ENTRY_TRACK = "ENTRY_TRACK";
    public static final String AGENT_FORWARDER_AGENT = "FORWARDER_AGENT";

    public static final String SYSTEM = "SYSTEM";
    /**
     * 监管方式
     */
    public static final String TRADE_MODE_4400 = "4400";
    public static final String TRADE_MODE_4600 = "4600";
    public static final String TRADE_MODE_4516 = "4516";
    public static final String TRADE_MODE_4561 = "4561";
    public static final String TRADE_MODE_1300 = "1300";
    public static final String TRADE_MODE_0300 = "0300";
    public static final String TRADE_MODE_0700 = "0700";
    /**
     * 苏泊尔单据内部编号生成规则：进口前缀：TJ
     */
    public static final String PREFIX_TJ = "TJ";
    /**
     * 苏泊尔单据内部编号生成规则：出口前缀：TC
     */
    public static final String PREFIX_TC = "TC";

    /**
     * token过期
     */
    public static final int HTTP_UNAUTHORIZED = 401;

    /**
     * 清单同步请求发送
     **/
    public static final String BILL_SYNC = "BILL_SYNC";

    /**
     * 免税申请表概要清空
     **/
    public static final String SUMMARY_CLEAR_URL = "SUMMARY_CLEAR_URL";

    /**
     * 外发加工申报表申报
     */
    public static final String WF_DEC_FORM_URL = "WF_DEC_FORM_URL";
    /**
     * DHL下单暂存接口
     */
    public static final String ES_WAY_BILL = "ES_WAY_BILL";
    /**
     * 申报要素表头明细
     */
    public static final String ELEMENT_CHANGE_INFO = "ELEMENT_CHANGE_INFO";
    /**
     * 申报要素表体明细
     */
    public static final String ELEMENT_CHANGE_DETAIL = "ELEMENT_CHANGE_DETAIL";
    /**
     * 禁止类
     */
    public static final String COMPLEX_BAN = "COMPLEX_BAN";

    /** 本地NO KAFKA 模式 SFTP_DUTYFORM类型 */
    public static final String SFTP_DUTYFORM = "DUTYFORM";
    public static final String SFTP_ALL = "ALL";
    public static final String SFTP_RECEIVE = "RECEIVE";
    public static final String SFTP_SUCCESS = "SUCCESS";
    public static final String SFTP_ERROR = "ERROR";
    public static final String SFTP_JSON_SUFFIX = "json";

    /**
     * pCode接口参数
     */
    public static final String PCODE_CODE_NUM = "CODE_NUM";
    /**
     * pCode接口参数
     */
    public static final String PCODE_CREDIT_CODE = "CREDIT_CODE";
    /**
     * pCode接口参数
     */
    public static final String PCODE_NAME = "NAME";
}
