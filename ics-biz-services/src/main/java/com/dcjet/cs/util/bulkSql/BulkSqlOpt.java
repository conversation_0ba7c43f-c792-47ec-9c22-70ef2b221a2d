package com.dcjet.cs.util.bulkSql;

import com.dcjet.cs.util.ReflectUtil;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.entity.Example;

import java.lang.invoke.MethodHandles;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ForkJoinPool;
import java.util.function.Function;

/**
 * 批量更新
 *
 * <AUTHOR>
 * @date: 2019-11-7
 */
@Component
public class BulkSqlOpt {
    /**
     * 批次处理量
     */
    private static int bCnt = 100;
    private static final int BIGGER_BATCH = 500;

    private final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /**
     * 批量更新操作
     *
     * @param ls  ： 待更新的list
     * @param cls ： 指定mapper.class
     * @param <M> : 实体类型
     * @param <T> : 指定mapper类型
     * @return
     */
    public <M, T extends Mapper> int batchUpdate(List<M> ls, Class<T> cls) {
        if (ls == null || ls.size() <= 0) {
            return -1;
        }
        SqlSessionFactory ssf = (SqlSessionFactory) SpringContextUtil.getBean("sqlSessionFactory");
        SqlSession ss = null;
        try {
            ss = ssf.openSession(ExecutorType.BATCH, false);
            T mapper = ss.getMapper(cls);
            for (int i = 0; i < ls.size(); i++) {
                M m = ls.get(i);
                mapper.updateByPrimaryKeySelective(m);
                if (i != 0 && i % bCnt == 0) {
                    ss.commit();
                }
            }
            ss.commit();
            return 0;
        } catch (Exception e) {
            logger.error("updateBatch ->" + e.getMessage(), e);
            ss.rollback();
            return -2;
        } finally {
            if (ss != null) {
                ss.close();
            }
        }
    }

    /**
     * 批量插入操作
     *
     * @param ls
     * @param cls
     * @param <M>
     * @param <T>
     * @return
     */
    public <M, T extends Mapper> int batchInsert(List<M> ls, Class<T> cls) {
        if (ls == null || ls.size() <= 0) {
            return -1;
        }
        SqlSessionFactory ssf = (SqlSessionFactory) SpringContextUtil.getBean("sqlSessionFactory");
        SqlSession ss = null;
        try {
            ss = ssf.openSession(ExecutorType.BATCH, false);
            T mapper = ss.getMapper(cls);
            for (int i = 0; i < ls.size(); i++) {
                M m = ls.get(i);
                mapper.insert(m);
                if (i != 0 && i % bCnt == 0) {
                    ss.commit();
                }
            }
            ss.commit();
            return 0;
        } catch (Exception e) {
            logger.error("insertBatch ->" + e.getMessage(), e);
            ss.rollback();
            return -2;
        } finally {
            if (ss != null) {
                ss.close();
            }
        }
    }

    /**
     * 自定义处理函数 - 批处理
     *
     * @param ls       待处理源数据列表
     * @param cls      mapper处理器数组
     * @param function 业务处理函数
     * @param <M>
     * @param <T>
     * @return
     */
    public <M, T extends Mapper> int batchOperate(List<M> ls, Class<T>[] cls, Function function) {
        if (ls == null || ls.size() <= 0) {
            return -1;
        }
        SqlSessionFactory ssf = (SqlSessionFactory) SpringContextUtil.getBean("sqlSessionFactory");
        SqlSession ss = null;
        try {
            ss = ssf.openSession(ExecutorType.BATCH, false);
            List<Mapper> mappers = new ArrayList<>(cls.length);
            for (int i = 0; i < cls.length; i++) {
                mappers.add(ss.getMapper(cls[i]));
            }
            OperateDataParam<M> param = new OperateDataParam() {{
                setCurrDate(new Date());
                setMappers(mappers);
            }};
            for (int i = 0; i < ls.size(); i++) {
                M m = ls.get(i);
                param.setParam(m);
                function.apply(param);
                if (i != 0 && i % bCnt == 0) {
                    ss.commit();
                }
            }
            ss.commit();
            return 0;
        } catch (Exception e) {
            logger.error("operateBatch ->" + e.getMessage(), e);
            ss.rollback();
            return -2;
        } finally {
            if (ss != null) {
                ss.close();
            }
        }
    }

    public <M, T extends Mapper> int batchOperate(List<M> ls, Function<M, Integer> function) {
        if (ls == null || ls.size() <= 0) {
            return -1;
        }
        SqlSessionFactory ssf = (SqlSessionFactory) SpringContextUtil.getBean("sqlSessionFactory");
        SqlSession ss = null;
        try {
            ss = ssf.openSession(ExecutorType.BATCH, false);
            for (int i = 0; i < ls.size(); i++) {
                M m = ls.get(i);
                function.apply(m);
                if (i != 0 && i % bCnt == 0) {
                    ss.commit();
                }
            }
            ss.commit();
            return 0;
        } catch (Exception e) {
            logger.error("operateBatch ->" + e.getMessage(), e);
            ss.rollback();
            return -2;
        } finally {
            if (ss != null) {
                ss.close();
            }
        }
    }

    public <M, T extends Mapper> int batchOperate2(List<M> ls, Function<M, Integer> function) {
        Method method;
        try {
            method = this.getClass().getDeclaredMethod("batchOperate", List.class, Function.class);
        } catch (NoSuchMethodException e) {
            logger.error("batchOperate -> method batchOperateInternal not found!!!", e);
            return -2;
        }
        BulkSqlForkJoinTask sumTask = new BulkSqlForkJoinTask(this, method, ls, function);
        ForkJoinPool pool = new ForkJoinPool();
        pool.invoke(sumTask);
        int status = (Integer)sumTask.join();
        return status;
    }

    /**
     * 同时处理插入和更新，且抛出异常
     *
     * @param insertList
     * @param updateList
     * @param clazz      mapper
     */
    public static <I, U, M extends Mapper> void batchSave(List<I> insertList, List<U> updateList, Class<M> clazz) {
        SqlSessionFactory ssf = (SqlSessionFactory) SpringContextUtil.getBean("sqlSessionFactory");
        SqlSession ss = null;
        try {
            ss = ssf.openSession(ExecutorType.BATCH, false);
            M mapper = ss.getMapper(clazz);
            for (int i = 0; i < insertList.size(); i++) {
                mapper.insert(insertList.get(i));
                if (i != 0 && i % BIGGER_BATCH == 0) {
                    ss.commit();
                }
            }
            for (int u = 0; u < updateList.size(); u++) {
                mapper.updateByPrimaryKey(updateList.get(u));
                if (u != 0 && u % BIGGER_BATCH == 0) {
                    ss.commit();
                }
            }
            ss.commit();
        } catch (Exception e) {
            ss.rollback();
            throw new RuntimeException(e);
        } finally {
            if (ss != null) {
                ss.close();
            }
        }
    }

    /**
     * 处理插入，且抛出异常
     *
     * @param insertList
     * @param clazz      mapper
     */
    public static <I, M extends Mapper> void batchInsertForErp(List<I> insertList, Class<M> clazz) {
        SqlSessionFactory ssf = (SqlSessionFactory) SpringContextUtil.getBean("sqlSessionFactory");
        SqlSession ss = null;
        try {
            ss = ssf.openSession(ExecutorType.BATCH, false);
            M mapper = ss.getMapper(clazz);
            for (int i = 0; i < insertList.size(); i++) {
                mapper.insert(insertList.get(i));
                if (i != 0 && i % BIGGER_BATCH == 0) {
                    ss.commit();
                }
            }
            ss.commit();
        } catch (Exception e) {
            ss.rollback();
            throw new RuntimeException(e);
        } finally {
            if (ss != null) {
                ss.close();
            }
        }
    }

    /**
     * 处理更新，且抛出异常
     *
     * @param updateList
     * @param clazz      mapper
     */
    public static <U, M extends Mapper> void batchUpdateForErp(List<U> updateList, Class<M> clazz) {
        SqlSessionFactory ssf = (SqlSessionFactory) SpringContextUtil.getBean("sqlSessionFactory");
        SqlSession ss = null;
        try {
            ss = ssf.openSession(ExecutorType.BATCH, false);
            M mapper = ss.getMapper(clazz);
            for (int u = 0; u < updateList.size(); u++) {
                mapper.updateByPrimaryKey(updateList.get(u));
                if (u != 0 && u % BIGGER_BATCH == 0) {
                    ss.commit();
                }
            }
            ss.commit();
        } catch (Exception e) {
            ss.rollback();
            throw new RuntimeException(e);
        } finally {
            if (ss != null) {
                ss.close();
            }
        }
    }

    /**
     * 处理更新(更新键sid+tradeCode)，且抛出异常
     *
     * @param updateList
     * @param clazz      mapper
     */
    public static <U, M extends Mapper> void batchUpdateBySidAndTradeCodeForErp(List<U> updateList, Class<M> clazz) {
        SqlSessionFactory ssf = (SqlSessionFactory) SpringContextUtil.getBean("sqlSessionFactory");
        SqlSession ss = null;
        try {
            ss = ssf.openSession(ExecutorType.BATCH, false);
            M mapper = ss.getMapper(clazz);
            for (int u = 0; u < updateList.size(); u++) {
                //mapper.updateByPrimaryKey(updateList.get(u));
                U uc = updateList.get(u);
                Example example = new Example(uc.getClass());
                Example.Criteria criteria = example.createCriteria();
                //反射获取sid和tradeCode属性
                String sid = (String)ReflectUtil.invokeGet(uc,"sid");
                String tradeCode = (String)ReflectUtil.invokeGet(uc,"tradeCode");
                criteria.andEqualTo("sid" , sid);
                criteria.andEqualTo("tradeCode" , tradeCode);
                mapper.updateByExample(updateList.get(u),example);
                if (u != 0 && u % BIGGER_BATCH == 0) {
                    ss.commit();
                }
            }
            ss.commit();
        } catch (Exception e) {
            ss.rollback();
            throw new RuntimeException(e);
        } finally {
            if (ss != null) {
                ss.close();
            }
        }
    }

    /**
     * 处理插入 ，且抛出异常
     *
     * @param insertList
     * @param clazz      mapper
     */
    public static <I, M extends Mapper> void batchInsertAndThrowException(List<I> insertList, Class<M> clazz) {
        SqlSessionFactory ssf = (SqlSessionFactory) SpringContextUtil.getBean("sqlSessionFactory");
        SqlSession ss = null;
        try {
            ss = ssf.openSession(ExecutorType.BATCH, false);
            M mapper = ss.getMapper(clazz);
            for (int i = 0; i < insertList.size(); i++) {
                mapper.insert(insertList.get(i));
                if (i != 0 && i % BIGGER_BATCH == 0) {
                    ss.commit();
                }
            }
            ss.commit();
        } catch (Exception e) {
            ss.rollback();
            throw new RuntimeException(e);
        } finally {
            if (ss != null) {
                ss.close();
            }
        }
    }

    /**
     * 批量更新操作并抛出异常
     *
     * @param ls  ： 待更新的list
     * @param cls ： 指定mapper.class
     * @param <M> : 实体类型
     * @param <T> : 指定mapper类型
     * @return
     */
    public <M, T extends Mapper> int batchUpdateWithException(List<M> ls, Class<T> cls) {
        if (ls == null || ls.size() <= 0) {
            return -1;
        }
        SqlSessionFactory ssf = (SqlSessionFactory) SpringContextUtil.getBean("sqlSessionFactory");
        SqlSession ss = null;
        try {
            ss = ssf.openSession(ExecutorType.BATCH, false);
            T mapper = ss.getMapper(cls);
            for (int i = 0; i < ls.size(); i++) {
                M m = ls.get(i);
                mapper.updateByPrimaryKeySelective(m);
                if (i != 0 && i % bCnt == 0) {
                    ss.commit();
                }
            }
            ss.commit();
            return 0;
        } catch (Exception e) {
            ss.rollback();
            throw new RuntimeException(e);
        } finally {
            if (ss != null) {
                ss.close();
            }
        }
    }

    /**
     * 批量插入操作并抛出异常
     *
     * @param ls
     * @param cls
     * @param <M>
     * @param <T>
     * @return
     */
    public <M, T extends Mapper> int batchInsertWithException(List<M> ls, Class<T> cls) {
        if (ls == null || ls.size() <= 0) {
            return -1;
        }
        SqlSessionFactory ssf = (SqlSessionFactory) SpringContextUtil.getBean("sqlSessionFactory");
        SqlSession ss = null;
        try {
            ss = ssf.openSession(ExecutorType.BATCH, false);
            T mapper = ss.getMapper(cls);
            for (int i = 0; i < ls.size(); i++) {
                M m = ls.get(i);
                mapper.insert(m);
                if (i != 0 && i % bCnt == 0) {
                    ss.commit();
                }
            }
            ss.commit();
            return 0;
        } catch (Exception e) {
            ss.rollback();
            throw new RuntimeException(e);
        } finally {
            if (ss != null) {
                ss.close();
            }
        }
    }
}
