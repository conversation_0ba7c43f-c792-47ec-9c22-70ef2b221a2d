package com.dcjet.cs.util;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;

public class ExcelMerger {
    // 样式和字体缓存
    private  Map<String, CellStyle> styleCache = new HashMap<>();
    private  Map<String, Font> fontCache = new HashMap<>();
    private  int ROW_GAP = 1; // 文件间隔行数

    public  String appendToFirstFile(List<String> filePaths, boolean includeHeader) throws IOException {
        if (filePaths == null || filePaths.isEmpty()) {
            throw new IllegalArgumentException("文件列表不能为空");
        }

        String firstFilePath = filePaths.get(0);

        // 1. 打开目标工作簿
        Workbook targetWorkbook;
        Sheet targetSheet;
        int lastRowNum;

        try (FileInputStream fis = new FileInputStream(firstFilePath)) {
            targetWorkbook = WorkbookFactory.create(fis);
            targetSheet = targetWorkbook.getSheetAt(0);
            lastRowNum = targetSheet.getLastRowNum() + 1;
        }

        // 2. 处理每个源文件
        for (int i = 1; i < filePaths.size(); i++) {
            String sourceFilePath = filePaths.get(i);
            try (FileInputStream fis = new FileInputStream(sourceFilePath);
                 Workbook sourceWorkbook = WorkbookFactory.create(fis)) {

                Sheet sourceSheet = sourceWorkbook.getSheetAt(0);
                int startRow = includeHeader ? 0 : 1;
                int rowOffset = lastRowNum - startRow;

                // 2.1 复制合并区域（解决重叠问题）
               copyMergedRegionsSafely(sourceSheet, targetSheet, rowOffset);

                // 2.2 复制行数据
                for (int j = startRow; j <= sourceSheet.getLastRowNum(); j++) {
                    Row sourceRow = sourceSheet.getRow(j);
                    if (sourceRow != null) {
                        Row targetRow = targetSheet.createRow(lastRowNum++);
                        copyRowWithStyle(sourceRow, targetRow, targetWorkbook, sourceWorkbook);
                    }
                }

                // 2.3 添加5行间隔
                lastRowNum += ROW_GAP;
            }
        }

        // 3. 写回目标文件
        try (FileOutputStream fos = new FileOutputStream(firstFilePath)) {
            targetWorkbook.write(fos);
            fos.close();
        }

        targetWorkbook.close();

        fixPrintArea(firstFilePath);
        return firstFilePath;
    }

    /**
     * 安全复制合并区域（解决重叠问题）
     */
    private  void copyMergedRegionsSafely(Sheet sourceSheet, Sheet targetSheet, int rowOffset) {
        List<CellRangeAddress> newRegions = new ArrayList<>();

        // 收集所有要添加的新区域
        for (int i = 0; i < sourceSheet.getNumMergedRegions(); i++) {
            CellRangeAddress sourceRegion = sourceSheet.getMergedRegion(i);
            CellRangeAddress newRegion = new CellRangeAddress(
                    sourceRegion.getFirstRow() + rowOffset,
                    sourceRegion.getLastRow() + rowOffset,
                    sourceRegion.getFirstColumn(),
                    sourceRegion.getLastColumn()
            );
            newRegions.add(newRegion);
        }

        // 处理区域冲突
        for (CellRangeAddress newRegion : newRegions) {
            boolean hasConflict = false;

            // 检查与现有区域是否冲突
            for (int i = 0; i < targetSheet.getNumMergedRegions(); i++) {
                CellRangeAddress existing = targetSheet.getMergedRegion(i);
                if (existing.intersects(newRegion)) {
                    hasConflict = true;
                    break;
                }
            }

            // 无冲突则添加
            if (!hasConflict) {
                targetSheet.addMergedRegion(newRegion);
            }
        }
    }

    /**
     * 复制行并保留完整样式
     */
    private  void copyRowWithStyle(Row sourceRow, Row targetRow, Workbook targetWorkbook, Workbook sourceWorkbook) {
        targetRow.setHeight(sourceRow.getHeight());

        for (int i = 0; i < sourceRow.getLastCellNum(); i++) {
            Cell sourceCell = sourceRow.getCell(i);
            if (sourceCell != null) {
                Cell targetCell = targetRow.createCell(i);
                copyCellWithStyle(sourceCell, targetCell, targetWorkbook, sourceWorkbook);
            }
        }
    }

    /**
     * 复制单元格并保留完整样式
     */
    private  void copyCellWithStyle(Cell sourceCell, Cell targetCell, Workbook targetWorkbook, Workbook sourceWorkbook) {
        // 复制单元格值
        copyCellValue(sourceCell, targetCell);

        // 复制样式
        CellStyle sourceStyle = sourceCell.getCellStyle();
        String styleKey = generateStyleKey(sourceStyle, sourceWorkbook);

        CellStyle targetStyle = styleCache.computeIfAbsent(styleKey, k -> {
            CellStyle newStyle = targetWorkbook.createCellStyle();
            copyBaseStyleProperties(sourceStyle, newStyle);

            // 复制字体
            Font sourceFont = sourceWorkbook.getFontAt(sourceStyle.getFontIndex());
            Font targetFont = getCachedFont(targetWorkbook, sourceFont);
            newStyle.setFont(targetFont);

            return newStyle;
        });

        targetCell.setCellStyle(targetStyle);
    }

    /**
     * 生成样式唯一标识
     */
    private  String generateStyleKey(CellStyle style, Workbook workbook) {
        Font font = workbook.getFontAt(style.getFontIndex());
        return style.getAlignment() + "|" + style.getFillForegroundColor() + "|"
                + font.getFontName() + "|" + font.getFontHeightInPoints()+"|"+ UUID.randomUUID().toString();
    }

    /**
     * 获取缓存字体
     */
    private  Font getCachedFont(Workbook workbook, Font sourceFont) {
        String fontKey = sourceFont.getFontName() + "|" + sourceFont.getFontHeightInPoints()
                + "|" + sourceFont.getColor() + "|" + sourceFont.getBold();

        return fontCache.computeIfAbsent(fontKey, k -> {
            Font newFont = workbook.createFont();
            newFont.setFontName(sourceFont.getFontName());
            newFont.setFontHeightInPoints(sourceFont.getFontHeightInPoints());
            newFont.setColor(sourceFont.getColor());
            newFont.setBold(sourceFont.getBold());
            return newFont;
        });
    }

    /**
     * 复制基础样式属性
     */
    private  void copyBaseStyleProperties(CellStyle source, CellStyle target) {
        target.setAlignment(source.getAlignment());
        target.setVerticalAlignment(source.getVerticalAlignment());
        target.setFillPattern(source.getFillPattern());
        target.setFillForegroundColor(source.getFillForegroundColor());
        target.setBorderTop(source.getBorderTop());
        target.setBorderBottom(source.getBorderBottom());
        target.setBorderLeft(source.getBorderLeft());
        target.setBorderRight(source.getBorderRight());
        target.setDataFormat(source.getDataFormat());
    }

    /**
     * 复制单元格值
     */
    private  void copyCellValue(Cell sourceCell, Cell targetCell) {
        switch (sourceCell.getCellType()) {
            case STRING:
                targetCell.setCellValue(sourceCell.getStringCellValue());
                break;
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(sourceCell)) {
                    targetCell.setCellValue(sourceCell.getDateCellValue());
                } else {
                    targetCell.setCellValue(sourceCell.getNumericCellValue());
                }
                break;
            case BOOLEAN:
                targetCell.setCellValue(sourceCell.getBooleanCellValue());
                break;
            case FORMULA:
                targetCell.setCellFormula(sourceCell.getCellFormula());
                break;
            default:
                targetCell.setCellValue("");
        }
    }

    public  void fixPrintArea(String filePath) throws IOException {
        try (FileInputStream fis = new FileInputStream(filePath);
             Workbook workbook = WorkbookFactory.create(fis)) {

            Sheet sheet = workbook.getSheetAt(0); // 假设处理第一个工作表

            // 1. 重置打印区域为所有内容
            setFullPrintArea(sheet);

//            // 2. 调整页面设置
//            adjustPageSetup(sheet);
//
//            // 3. 确保所有行可见
//            ensureAllRowsPrintable(sheet);

            // 保存修改
            try (FileOutputStream fos = new FileOutputStream(filePath)) {
                workbook.write(fos);
            }
        }
    }


    private  void setFullPrintArea(Sheet sheet) {
        // 清除现有打印区域
        sheet.getWorkbook().removePrintArea(sheet.getWorkbook().getSheetIndex(sheet));

        // 计算实际内容范围
        int firstRow = sheet.getFirstRowNum();
        int lastRow = sheet.getLastRowNum() - 1;
        int firstCol = 0;
        int lastCol = 0;

        // 找到最后一列
        for (int i = firstRow; i <= lastRow; i++) {
            Row row = sheet.getRow(i);
            if (row != null && row.getLastCellNum() > lastCol) {
                lastCol = row.getLastCellNum() - 1;
            }
        }

        if (lastCol > 0) {
            // 设置新的打印区域 (A1到最后一列最后一行的单元格)
            String printArea = CellReference.convertNumToColString(firstCol) + (firstRow + 1) +
                    ":" + CellReference.convertNumToColString(lastCol - 1) + (lastRow + 1);
            sheet.getWorkbook().setPrintArea(sheet.getWorkbook().getSheetIndex(sheet), printArea);
        }
    }

    private  void adjustPageSetup(Sheet sheet) {
        PrintSetup printSetup = sheet.getPrintSetup();

        // 设置为横向打印（如果列数较多）
       // printSetup.setLandscape(true);

        // 自动调整到一页宽
        sheet.setFitToPage(true);
        printSetup.setFitWidth((short)1);
        printSetup.setFitHeight((short)0); // 0表示自动调整高度

        // 设置合适的页边距
        sheet.setMargin(Sheet.LeftMargin, 0.5);
        sheet.setMargin(Sheet.RightMargin, 0.5);
        sheet.setMargin(Sheet.TopMargin, 0.75);
        sheet.setMargin(Sheet.BottomMargin, 0.75);
    }

    private  void ensureAllRowsPrintable(Sheet sheet) {
        // 确保所有行高度合适
        for (int i = sheet.getFirstRowNum(); i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row != null) {
                // 如果行高为0（隐藏行），设置为默认高度
                if (row.getHeight() == 0) {
                    row.setHeight((short)-1); // 恢复默认高度
                }
            }
        }

        // 确保所有列宽度合适
        for (int i = 0; i < sheet.getRow(0).getLastCellNum(); i++) {
            int colWidth = sheet.getColumnWidth(i);
            if (colWidth == 0) {
                sheet.setColumnWidth(i, 256 * 8); // 设置默认宽度（8个字符）
            }
        }
    }
}