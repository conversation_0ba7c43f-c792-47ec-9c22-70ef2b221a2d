package com.dcjet.cs.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Supplier;

public class TaskSequencerUtils {

    private static final Logger log = LoggerFactory.getLogger(TaskSequencerUtils.class);

    /**
     * 顺序执行两个任务（使用默认ForkJoinPool）
     * @param firstTask 第一个任务（可返回结果）
     * @param secondTask 第二个任务（无返回值）
     */
    public static <T> void executeSequentially(Supplier<T> firstTask, Runnable secondTask) {
        CompletableFuture.supplyAsync(firstTask)
                .thenRunAsync(secondTask)
                .exceptionally(e -> {
                    throw new RuntimeException("执行任务异常！");
                });
    }

    /**
     * 顺序执行两个任务（自定义线程池）
     * @param firstTask 第一个任务（可返回结果）
     * @param secondTask 第二个任务（无返回值）
     * @param executor 自定义线程池
     */
    public static <T> void executeSequentially(Supplier<T> firstTask, 
                                              Runnable secondTask,
                                              Executor executor) {
        CompletableFuture.supplyAsync(firstTask, executor)
                .thenRunAsync(secondTask, executor)
                .exceptionally(e -> {
                    throw new RuntimeException("执行任务异常！");
                });
    }

    /**
     * 纯Runnable版本
     */
    public static void executeRunnables(Runnable firstTask,
                                       Runnable secondTask) {
        CompletableFuture.runAsync(firstTask)
                .thenRunAsync(secondTask)
                .exceptionally(e -> {
                    throw new RuntimeException("执行任务异常！");
                });
    }
}