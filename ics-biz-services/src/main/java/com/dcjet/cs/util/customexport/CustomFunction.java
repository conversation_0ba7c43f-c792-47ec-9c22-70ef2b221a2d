package com.dcjet.cs.util.customexport;

import com.aspose.cells.Cell;
import com.aspose.cells.ICustomFunction;
import com.aspose.cells.ReferredArea;
import org.hibernate.validator.internal.util.StringHelper;

import java.math.BigDecimal;
import java.util.ArrayList;

/**
 * @author: 朱辉
 * @Date: 2019/8/1 14:52
 * @Description: excel自定义方法
 */
public class CustomFunction implements ICustomFunction {
    @Override
    public Object calculateCustomFunction(String s, ArrayList arrayList, ArrayList arrayList1) {
        Object result=null;
        if(!StringHelper.isNullOrEmptyString(s)){
            switch(s.toUpperCase()){
                //数字转英文
                case "SPELLNUMBER":
                    if(((ReferredArea)arrayList.get(0)).getValues()!=null) {
                        result = NumToEnUtil.analyze(new BigDecimal(((ReferredArea) arrayList.get(0)).getValues().toString()));
                        ((Cell) arrayList1.get(2)).putValue(result.toString().toUpperCase());
                    }
                    else
                    {
                        ((Cell) arrayList1.get(2)).putValue("");
                    }
                    break;
                //数字转英文
                case "SPELLMONEY":
                    if(((ReferredArea)arrayList.get(0)).getValues()!=null) {
                        result = NumToEnUtil.analyze(new BigDecimal(((ReferredArea) arrayList.get(0)).getValues().toString()),"DOLLARS");
                        ((Cell) arrayList1.get(2)).putValue(result.toString().toUpperCase());
                    }
                    else
                    {
                        ((Cell) arrayList1.get(2)).putValue("");
                    }
                    break;
                default:
                    break;
            }
            return  result;
        }
        return null;
    }
}
