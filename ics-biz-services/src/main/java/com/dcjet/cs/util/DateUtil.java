package com.dcjet.cs.util;

import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.Charset;
import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Slf4j
public class DateUtil {
    private static final String datePattern = "yyyy-MM-dd";
    private static final String datetimePattern = "yyyy-MM-dd HH:mm:ss";
    private static final String shortDatePattern = "yyyyMMdd";
    private static final String datePattern2 = "yyyy/MM/dd";
    private static final String datetimePattern2 = "yyyy/MM/dd HH:mm:ss";

    public final static Date plusDay(Date date, int day) {
        if (date == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, day);
        return calendar.getTime();
    }

    public static DataHandler createDataHandler(final String strVal) {
        return new DataHandler(new DataSource() {
            @Override
            public InputStream getInputStream() throws IOException {
                return  new ByteArrayInputStream(strVal.getBytes(Charset.forName("utf-8")));
            }

            @Override
            public OutputStream getOutputStream() throws IOException {
                return null;
            }

            @Override
            public String getContentType() {
                return null;
            }

            @Override
            public String getName() {
                return null;
            }
        });
    }
    
    /**
     * 尝试根据字符串转换日期
     * @param strDate
     * @return
     */
    public static Date tryDateParse(String strDate) {
        if (Strings.isNullOrEmpty(strDate)) {
            return null;
        }
        try {
            return DateUtils.parseDate(strDate, shortDatePattern, datePattern, datetimePattern,datePattern2, datetimePattern2);
        } catch (ParseException e) {
            //throw new RuntimeException("解析日期出错:" + strDate,e);
            log.error("解析日期出错",e);
            return null;
        }
    }
}
