package com.dcjet.cs.util.customexport;


import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * <AUTHOR>
 * Aspose导出数据源
 */
@Setter
@Getter
public class ExportDataSource {
    /**
     * 值变量数据
     */
    private Map<String, Object> vriableMap;
    /**
     * 表变量数据(实体类集合)
     */
    private Map<String, Object> tableMap;

    /**
     * 表变量数据(map类集合)
     * 朱辉命名
     */
    private Map<String, Object> bodyHashMap;
}