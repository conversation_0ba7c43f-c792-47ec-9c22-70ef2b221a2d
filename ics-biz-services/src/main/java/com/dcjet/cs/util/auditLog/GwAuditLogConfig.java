package com.dcjet.cs.util.auditLog;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@Table(name = "T_GW_AUDIT_LOG_CONFIG")
public class GwAuditLogConfig implements Serializable {

    /**
     * 唯一键
     */
    @Id
    @Column(name = "SID")
    private  String sid;


    /**
     * 企业编码
     */
    @Column(name = "TRADE_CODE")
    private String tradeCode;

    /**
     * 创建人
     */
    @Column(name = "INSERT_USER")
    private  String insertUser;
    /**
     * 创建人
     */
    @Column(name = "INSERT_USER_NAME")
    private  String insertUserName;
    /**
     * 创建时间
     */
    @Column(name = "INSERT_TIME")
    private Date insertTime;

    /**
     * 更新人
     */
    @Column(name = "UPDATE_USER")
    private  String updateUser;
    /**
     * 更新人
     */
    @Column(name = "UPDATE_USER_NAME")
    private  String updateUserName;
    /**
     * 更新时间
     */
    @Column(name = "UPDATE_TIME")
    private  Date updateTime;
    /**
     * 对应表名
     */
    @Column(name = "TABLE_NAME")
    private  String tableName;
    /**
     * 业务主键对应栏位
     */
    @Column(name = "BUSINESS")
    private  String business;
    /**
     * 对应表名显示操作名称
     */
    @Column(name = "MODULE_NAME")
    private  String moduleName;
    /**
     * 记录业务主键(明细显示)
     */
    @Column(name = "BIZ_KEY")
    private  String bizKey;
    /**
     * 提单录入日期-开始
     */
    @Transient
    private String insertTimeFrom;
    /**
     * 提单录入日期-结束
     */
    @Transient
    private String insertTimeTo;

}
