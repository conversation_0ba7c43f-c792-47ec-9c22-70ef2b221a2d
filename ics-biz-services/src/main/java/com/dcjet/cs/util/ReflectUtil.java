package com.dcjet.cs.util;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class ReflectUtil {

    /***
     *
     * @param type
     * @return
     */
    public static List<Field> getAllDeclaredFields(Class<?> type) {
        List<Field> fields = new ArrayList<Field>();
        for (Class<?> c = type; c != null; c = c.getSuperclass()) {
            fields.addAll(Arrays.asList(c.getDeclaredFields()));
        }
        return fields;
    }

    /***
     *
     *
     * @param type
     * @return
     */
    public static Map<String, Field> getDeclaredFieldMap(Class<?> type) {
        List<Field> fieldList = getAllDeclaredFields(type);
        return fieldList.stream().collect(Collectors.toMap(Field::getName, Function.identity(), (oldVal, newVal) -> oldVal));
    }

    /***
     *
     *
     * @param type
     * @return
     */
    public static Map<String, Field> getDeclaredFieldMapKeyLower(Class<?> type) {
        List<Field> fieldList = getAllDeclaredFields(type);
        return fieldList.stream().collect(Collectors.toMap(key -> key.getName().toLowerCase(), Function.identity(), (oldVal, newVal) -> oldVal));
    }

    /***
     * 判断对象是否是某个超类的子类
     *
     * @param clazz
     * @param superClass
     * @return
     */
    public static boolean isSubclassOf(Class<?> clazz, Class<?> superClass) {
        if (superClass.equals(Object.class)) {
            // Every class is an Object.
            return true;
        }
        if (clazz.equals(superClass)) {
            return true;
        } else {
            clazz = clazz.getSuperclass();
            // every class is Object, but superClass is below Object
            if (clazz == null || clazz.equals(Object.class)) {
                // we've reached the top of the hierarchy, but superClass couldn't be found.
                return false;
            }
            // try the next level up the hierarchy.
            return isSubclassOf(clazz, superClass);
        }
    }


    public static Object invokeGet(Object o, String fieldName) {
        Method method = getGetMethod(o.getClass(), fieldName);
        try {
            return method.invoke(o, new Object[0]);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * java反射bean的get方法
     *
     * @param objectClass
     * @param fieldName
     * @return
     */
    @SuppressWarnings("unchecked")
    public static Method getGetMethod(Class objectClass, String fieldName) {
        StringBuffer sb = new StringBuffer();
        sb.append("get");
        sb.append(fieldName.substring(0, 1).toUpperCase());
        sb.append(fieldName.substring(1));
        try {
            return objectClass.getMethod(sb.toString());
        } catch (Exception e) {
        }
        return null;
    }

    /**
     * java反射bean设置单值
     */
    public static <T> void setClassValue(T t,String name) {
        try {
            String guid = UUID.randomUUID().toString();//如果找到了代理配置但是没有配置权限,则强制设置参数
            Class cla = Class.forName(t.getClass().getName());
            Field f = cla.getDeclaredField(name);
            if(f == null) {
                throw new RuntimeException(xdoi18n.XdoI18nUtil.t("尝试反射字段出错"));
            }
            f.setAccessible(true);
            if(f.getName().equals(name)) {
                f.set(t,guid);
            }
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
    }
}
