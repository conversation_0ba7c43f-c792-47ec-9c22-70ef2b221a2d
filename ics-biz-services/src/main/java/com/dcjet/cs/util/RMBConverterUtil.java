package com.dcjet.cs.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

public class RMBConverterUtil {
    private static final String[] NUMBERS = {"零", "壹", "貳", "參", "肆", "伍", "陸", "柒", "捌", "玖"};

    public static String convertToRMBTraditional(String amountStr) {
        BigDecimal amount;
        try {
            amount = new BigDecimal(amountStr).setScale(2, RoundingMode.HALF_UP);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("无效的金额格式");
        }
        if (amount.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("金额不能为负数");
        }

        String[] parts = amount.toPlainString().split("\\.");
        String integerPartStr = parts[0];
        String decimalPartStr = parts.length > 1 ? parts[1] : "00";
        decimalPartStr = decimalPartStr.length() < 2 ? decimalPartStr + "0" : decimalPartStr.substring(0, 2);

        String integerPart = convertIntegerPart(integerPartStr);
        String decimalPart = convertDecimalPart(decimalPartStr);

        String result = integerPart;
        if (decimalPart.isEmpty()) {
            result += "整";
        } else {
            result += decimalPart;
        }
        return result;
    }

    private static String convertIntegerPart(String integerStr) {
        List<String> parts = splitIntoParts(integerStr);
        StringBuilder sb = new StringBuilder();
        int size = parts.size();
        for (int i = 0; i < size; i++) {
            String part = parts.get(i);
            int partNum = Integer.parseInt(part);
            String converted = convertFourDigit(partNum);
            if (!converted.isEmpty()) {
                sb.append(converted);
                if (size - i == 3) {
                    sb.append("億");
                } else if (size - i == 2) {
                    sb.append("萬");
                } else {
                    sb.append("圓");
                }
            }
        }
        if (sb.length() == 0) {
            sb.append("零圓");
        }
        return sb.toString();
    }

    private static List<String> splitIntoParts(String integerStr) {
        List<String> parts = new ArrayList<>();
        int len = integerStr.length();
        for (int i = 0; i < len; i += 4) {
            int end = len - i;
            int start = Math.max(0, end - 4);
            String part = integerStr.substring(start, end);
            parts.add(0, part);
        }
        return parts;
    }

    private static String convertFourDigit(int num) {
        if (num == 0) {
            return "";
        }
        String numStr = String.format("%04d", num);
        StringBuilder sb = new StringBuilder();
        boolean hasNonZero = false;
        boolean pendingZero = false;

        for (int i = 0; i < 4; i++) {
            int digit = numStr.charAt(i) - '0';
            String currentNum = NUMBERS[digit];
            String unit = "";
            switch (i) {
                case 0: unit = "仟"; break;
                case 1: unit = "佰"; break;
                case 2: unit = "拾"; break;
                case 3: unit = ""; break;
            }

            if (digit != 0) {
                hasNonZero = true;
                if (pendingZero) {
                    sb.append("零");
                    pendingZero = false;
                }
                sb.append(currentNum).append(unit);
            } else {
                if (hasNonZero) {
                    pendingZero = true;
                }
            }
        }

        if (!hasNonZero) {
            return "";
        }
        return sb.toString();
    }

    private static String convertDecimalPart(String decimalStr) {
        StringBuilder sb = new StringBuilder();
        int jiao = Character.getNumericValue(decimalStr.charAt(0));
        int fen = Character.getNumericValue(decimalStr.charAt(1));

        if (jiao != 0) {
            sb.append(NUMBERS[jiao]).append("角");
        }
        if (fen != 0) {
            sb.append(NUMBERS[fen]).append("分");
        }
        return sb.toString();
    }

    public static void main(String[] args) {
        System.out.println(convertToRMBTraditional("0")); // 零圓整
        System.out.println(convertToRMBTraditional("0.50")); // 零圓伍角
        System.out.println(convertToRMBTraditional("123.45")); // 壹佰貳拾參圓肆角伍分
        System.out.println(convertToRMBTraditional("1004")); // 壹仟零肆圓整
        System.out.println(convertToRMBTraditional("100000000")); // 壹億圓整
        System.out.println(convertToRMBTraditional("1001")); // 壹仟零壹圓整
        System.out.println(convertToRMBTraditional("1000")); // 壹仟圓整
        System.out.println(convertToRMBTraditional("0.05")); // 零圓伍分
        System.out.println(convertToRMBTraditional("2010")); // 貳仟零壹拾圓整
        System.out.println(convertToRMBTraditional("100000")); // 壹拾萬圓整
    }
}
