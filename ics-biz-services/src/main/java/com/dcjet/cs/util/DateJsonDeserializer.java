package com.dcjet.cs.util;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.io.IOException;
import java.util.Date;

/**
 * 日期反序列化，兼容多种格式，适配前端多种格式的时间字符串
 *
 */
public class DateJsonDeserializer extends JsonDeserializer<Date> {
    private static String[] pattern =
            new String[]{
                    "yyyyMMdd",
                    "yyyy-MM-dd",
                    "yyyy-MM-dd HH:mm:ss",
                    "yyyy-MM-dd HH:mm:ss.SSS",
                    "yyyy/MM/dd",
                    "yyyy/MM/dd HH:mm:ss",
                    "yyyy/MM/dd HH:mm:ss.SSS"
            };

    @Override
    public Date deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
        Date targetDate = null;
        String originDate = jsonParser.getText();
        if (StringUtils.isNotEmpty(originDate)) {
            originDate = originDate.trim();
            try {
                if(StringUtils.isNumeric(originDate) & originDate.length() > 8) {
                    long longDate = Long.parseLong(originDate);
                    targetDate = new Date(longDate);
                }else {
                    targetDate = DateUtils.parseDate(originDate, DateJsonDeserializer.pattern);
                }
            } catch (Exception ex) {
                throw new IOException(String.format(
                        "'%s' can not convert to type 'java.util.Date',just support timestamp(type of long) and following date format(%s)",
                        originDate,
                        StringUtils.join(pattern, ",")));
            }
        }
        return targetDate;
    }
}
