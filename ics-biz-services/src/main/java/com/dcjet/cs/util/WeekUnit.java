package com.dcjet.cs.util;

import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class WeekUnit {
    private static final long serialVersionUID = 1L;


    public static String getWeek(Date iDate)
    {
        Calendar calendar = Calendar.getInstance();
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        calendar.setTime(iDate);
        return String.valueOf(calendar.get(Calendar.YEAR))+"-"+String.format("%02d", calendar.get(Calendar.WEEK_OF_YEAR));
    }
}
