package com.dcjet.cs.util.variable;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date: 2019-4-17
 */
public class CommonVariable {
    public static final String CURR_USD = "502";
    public static final String CURR_RMB = "142";
    public static final String COUNTRY_USD = "502";
    public static final String COUNTRY_CHN = "142";
    //CHN000-中国境内
    public static final String PORT_CHN000 = "CHN000";

    public static final String GBK = "GBK";
    public static final String UTF8 = "utf-8";
    public static final String MIN_DATE = "1900-01-01";
    public static final String MAX_DATE = "9999-12-31";
    //运维配置项
    public final static String CONFIG_0 = "0";
    //运维配置项
    public final static String CONFIG_1 = "1";
    //运维配置项
    public final static String CONFIG_2 = "2";
    //运维配置项
    public final static String CONFIG_3 = "3";
    public final static String CONFIG_4 = "4";
    public final static String CONFIG_5 = "5";
    //运维配置项
    public final static String CONFIG_12 = "12";

    public final static String DEFAULT_STR000 = "000";
    public final static String YES_CN = "是";
    public final static String NO_CN = "否";
    public final static String YES_EN = "Y";
    public final static String NO_EN = "N";
    public final static String YES_CODE = "1";
    public final static String NO_CODE = "0";

    //备案号非保税
    public final static String EMS_A = "A";
    //备案号B
    public final static String EMS_B = "B";
    //备案号C
    public final static String EMS_C = "C";
    //备案号E
    public final static String EMS_E = "E";
    //备案号Z
    public final static String EMS_Z = "Z";

    //进口
    public final static String IE_MARK_I = "I";
    //出口
    public final static String IE_MARK_E = "E";
    //出口结转
    public final static String IE_MARK_JE = "JE";

    //暂存
    public final static String APPR_STATUS_0 = "0";
    //待审核
    public final static String APPR_STATUS_2 = "2";
    //修改
    public final static String APPR_STATUS_3 = "3";
    //接受(克诺尔用)
    public final static String APPR_STATUS_4 = "4";
    //内审通过
    public final static String APPR_STATUS_8 = "8";
    //草单审查
    public final static String APPR_STATUS_9 = "9";
    //清单撤回
    public final static String APPR_STATUS_C = "C";
    //内审退回
    public final static String APPR_STATUS_B = "-1";
    //发送备案
    public final static String APPR_STATUS_JA = "JA";
    //备案通过
    public final static String APPR_STATUS_JC = "JC";
    //备案退回
    public final static String APPR_STATUS_JD = "JD";
    /**发送备案中(提交后端任务,单损耗)*/
    public final static String APPR_STATUS_J0 = "J0";
    /**发送备案中(提交后端任务,单损耗)*/
    public final static String APPR_STATUS_J1 = "J1";

    /** 基础类型 供应商 */
    public final static String PRD = "PRD";
    /** 基础类型 客户 */
    public final static String CLI = "CLI";
    /** 基础类型 货代 */
    public final static String FOD = "FOD";
    /** 基础类型 报关行 */
    public final static String CUT = "CUT";
    /** 基础类型 企业 */
    public final static String COM = "COM";

    //单耗版本号
    public final static String EXG_VERSION_0 = "0";

    /** 进口报关单 */
    public final static String I_ENTRY = "I_ENTRY";
    /** 出口报关单 */
    public final static String E_ENTRY = "E_ENTRY";
    //发票
    public final static String INV_TYPE_1 = "1";
    //箱单
    public final static String INV_TYPE_2 = "2";
    //合同
    public final static String INV_TYPE_3 = "3";
    //托书
    public final static String INV_TYPE_4 = "4";
    //订舱单
    public final static String INV_TYPE_5 = "5";

    //请求成功
    public final static int SUCCESS = 200;
    //抛出异常
    public final static int EXCEPTION  = 400;

    public final static String TRADE_CODE_9999999999 = "9999999999";

    public final static String TRADE_MODE_0214 = "0214";
    public final static String TRADE_MODE_0615 = "0615";
    public final static String TRADE_MODE_0265 = "0265";
    public final static String TRADE_MODE_0664 = "0664";
    //报关单撤回
    public final static String APPR_STATUS_R = "R";
    /**
     * 截取字符
     * @param str
     * @param subSLength
     * @return
     */
    public static String subStr(String str, int subSLength)    {
        String  subStr ="";
        try {
            if (str == null) {
                return "";
            } else{
                int tempSubLength = subSLength;//截取字节数
                subStr = str.substring(0, Math.min(str.length(), subSLength));//截取的子串
                int subStrByetsL = subStr.getBytes(CommonVariable.GBK).length;//截取子串的字节长度
                // 说明截取的字符串中包含有汉字
                while (subStrByetsL > tempSubLength){
                    int subSLengthTemp = --subSLength;
                    subStr = str.substring(0, Math.min(subSLengthTemp, str.length()));
                    subStrByetsL = subStr.getBytes(CommonVariable.GBK).length;
                }
            }
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        return subStr;
    }

    /**
     * 功能描述 获取备案号第一个字符
     * <AUTHOR>
     * @date 2020/2/21
     * @version 1.0
     * @param emsNo 1
     * @return java.lang.String
     */
    public  static  String getFirstCode(String emsNo){
        if(StringUtils.isNotBlank(emsNo)) {
            Object Intercept = emsNo.subSequence(0, 1);
            if (CommonVariable.EMS_B.equals(Intercept)) {
                return CommonVariable.EMS_B;
            } else if (CommonVariable.EMS_C.equals(Intercept)) {
                return CommonVariable.EMS_C;
            } else {
                return CommonVariable.EMS_A;
            }
        }
        else
        {
            return CommonVariable.EMS_A;
        }
    }
    public static String getTradeMode(String emsNo){
        if (StringUtils.isNotBlank(emsNo)) {
            String firstLetter = emsNo.substring(0,1);
            if (EMS_B.equals(firstLetter)) {
                return TRADE_MODE_0214;
            } else if (EMS_C.equals(firstLetter)) {
                return TRADE_MODE_0615;
            } else if (EMS_E.equals(firstLetter)) {
                return TRADE_MODE_0615;
            }
        }
        return null;
    }
    /**
     * 校验是否存在中文
     * @param name
     * @return
     */
    public static boolean checkname(String name)
    {
        int n = 0;
        for(int i = 0; i < name.length(); i++) {
            n = (int)name.charAt(i);
            if(19968 <= n && n <40869) {
                return false;
            }
        }
        return true;
    }
}
