package com.dcjet.cs.util;

import java.util.List;
import java.util.Map;

/**
 * 第三方数据库工具接口
 * 定义与第三方数据库交互的通用方法
 */
public interface ThirdPartyDbUtil {
    
    /**
     * 初始化数据库连接参数
     * 
     * @param host 主机地址
     * @param port 端口
     * @param username 用户名
     * @param password 密码
     * @param database 数据库名称（Oracle的SID或服务名）
     */
    void init(String host, String port, String username, String password, String database);
    
    /**
     * 执行插入操作
     * 
     * @param tableName 表名
     * @param data 要插入的数据，Map的key为列名，value为列值
     * @return 影响的行数
     */
    int insert(String tableName, Map<String, Object> data);
    
    /**
     * 批量执行插入操作
     * 
     * @param tableName 表名
     * @param dataList 要插入的数据列表，每个Map的key为列名，value为列值
     * @return 影响的行数
     */
    int batchInsert(String tableName, List<Map<String, Object>> dataList);
    
    /**
     * 使用泛型对象执行插入操作
     * 
     * @param tableName 表名
     * @param entity 要插入的实体对象
     * @param <T> 实体类型
     * @return 影响的行数
     */
    <T> int insertEntity(String tableName, T entity);
    
    /**
     * 使用泛型对象批量执行插入操作
     * 
     * @param tableName 表名
     * @param entities 要插入的实体对象列表
     * @param <T> 实体类型
     * @return 影响的行数
     */
    <T> int batchInsertEntities(String tableName, List<T> entities);
    
    /**
     * 执行自定义SQL
     * 
     * @param sql SQL语句
     * @param params SQL参数
     * @return 影响的行数
     */
    int executeUpdate(String sql, Object... params);
    
    /**
     * 获取数据库类型
     * 
     * @return 数据库类型
     */
    String getDbType();

    /**
     * 获取下一个序列值
     *
     * @param sequenceName 序列名称
     * @param frequency    频次
     * @return 序列值
     */
    long[] getSequenceNextValue(String sequenceName, int frequency);
}
