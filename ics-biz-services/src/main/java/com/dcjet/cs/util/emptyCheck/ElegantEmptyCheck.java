package com.dcjet.cs.util.emptyCheck;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 空值处理
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ElegantEmptyCheck {

    /**
     * 需要处理的参数序号列表
     *  与paramNames二选一，该参数优先级比paramNames高
     * @return
     */
    int[] paramIndexs() default {};

    /**
     * 需要处理的参数名称列表
     *  与paramIndexs二选一，该参数优先级比paramIndexs低
     * @return
     */
    String[] paramNames() default {};

    /**
     * 空值处理类型
     * @return
     */
    EmptyHandlerType handlerType() default EmptyHandlerType.RUNTIME_EXCEPTION;
}
