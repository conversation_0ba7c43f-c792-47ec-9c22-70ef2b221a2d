package com.dcjet.cs.util;
/**
 * <AUTHOR>
 * @date: 2019-4-17
 */
public class ConstantsMessage {
    public final static String INSERT_SUCCESS = "新增成功";
    public final static String INSERT_FAIL = "新增失败";
    public final static String INSERT_NO = "，无法新增";
    public final static String UPDATE_SUCCESS = "修改成功";
    public final static String UPDATE_FAIL = "修改失败";
    public final static String UPDATE_NO = "，无法修改";
    public final static String DELETE_SUCCESS = "删除成功";
    public final static String DELETE_FAIL = "删除失败";
    public final static String DELETE_NO = "，无法删除";
    public final static String LOGOUT_SUCCESS = "注销成功";
    public final static String LOGOUT_FAIL = "删除失败";
    public final static String SHUTDOWN_SUCCESS = "停用成功";
    public final static String SHUTDOWN_FAIL = "停用失败";
    public final static String COPY_SUCCESS = "复制成功";
    public final static String COPY_FAIL = "复制失败";
    public final static String COPY_NO = "，无法复制";
    public final static String QUERY_SUCCESS = "查询成功";
    public final static String QUERY_FAIL = "查询失败";
    public final static String BUILD_SUCCESS = "生成成功";
    public final static String BUILD_FAIL = "生成失败";
    public final static String RBACK_SUCCESS = "回填成功";
    public final static String RBACK_FAIL = "回填失败";
    public final static String EXTRACT_SUCCESS = "提取成功";
    public final static String EXTRACT_FAIL = "提取失败";
    public final static String ROLLBACK_SUCCESS = "回滚成功";
    public final static String ROLLBACK_FAIL = "回滚失败";


    public final static String CREATE_SUCCESS = "生成成功";
    public final static String CREATE_FAIL = "生成失败";
    public final static String NO_DATA = "未获取到对应单据数据";

    public final static String SEND_SUCCESS = "发送内审成功";
    public final static String SEND_FAIL = "当前单据无法发送内审";
    public final static String RETURN_SUCCESS = "单据内审退回成功";
    public final static String RETURN_FAIL = "当前单据无法内审退回";
    public final static String PASS_FAIL = "当前单据无法内审通过";
    public final static String PASS_SUCCESS = "内审通过成功";
    public final static String SEND_CHECK_ATTACHED = "未上传随附单据，不能发送内审！";
    public final static String CHECK_NET_WT_TOTAL_MSG = "表头总净重-表体总净重超过预设值";
    public final static String CHECK_GROSS_WT_TOTAL_MSG = "表头总毛重-表体总毛重超过预设值";

    public final static String SPLIT_SUCCESS = "拆分成功";
    public final static String SPLIT_FAIL = "拆分失败";

    public final static String TRANSLATE_SUCCESS = "转换成功";
    public final static String TRANSLATE_FAIL = "转换失败";

    public final static String EMS_LIST_USED = "单据内部编号已存在";
    public final static String ERP_LIST_NOUSED = "单据内部编号在ERP接口中不存在";
    public final static String ERP_LIST_NO_USEDLIST = "单据内部编号在ERP接口明细中不存在";
    public final static String ERP_TRADE_MODE = "监管方式不能为空";

    public final static String OPERATE_FAIL = "操作失败";
    public final static String OPERATE_SUCCESS = "操作成功";

    public final static String DEC_CAN_RBACK = "仅有【内审通过】的数据才能数据回填！";
    public final static String DEC_NO_NOTNULL = "新单据内部编号不能为空";
    public final static String DEC_NO_LONG = "新单据内部编号长度超过32位";
    public final static String DEC_NO_AUTO = "未配置自定义单号生成规则";
    public final static String NO_DEC_DATA = "未获取到预录入单信息";

    /**
     * 物料相关
     */
    public final static String MAT_EXIST_BOND = "该企业料号在保税已存在";
    public final static String MAT_EXIST_NOBOND = "该企业料号/零件号/品种代码在非保税预归类中已存在";
    public final static String MAT_EXIST_BOND_O = "该备案料号在保税已存在";
    public final static String MAT_EXIST_NOBOND_O = "该备案料号在非保税预归类中已存在";
    public final static String MAT_EXIST_SERIALNO = "该备案序号已存在";
    public final static String MAT_DEL_NO = "删除数据中有不存在数据";
    public final static String MAT_DEL_SEND = "删除数据中有发送中数据";
    public final static String MAT_DEL_USED = "删除数据中有已使用数据";
    public final static String MAT_DEL_APPR = "删除数据中有已内审通过数据";
    public final static String MAT_DEL_PASS= "删除数据中有已备案数据";
    public final static String MAT_STATUS_SUCCESS = "设置物料状态成功";
    public final static String MAT_STATUS_FAIL = "设置物料状态失败";
    public final static String MAT_EXIST_NOBOND_COP = "车型已存在";
    public final static String MAT_EXIST_NOBOND_COP_KD = "品种代码已存在";

    public final static String MAT_NO_EMS_NO = "备案号不能为空";
    public final static String MAT_NO_BOND = "保税标记不能为空";
    public final static String MAT_NO_GMARK = "物料类型不能为空";
    public final static String MAT_NO_SELECT = "处理状态为“修改”并且审核状态为“暂存、内审退回、备案退回”才能恢复，请重新选择";


}
