package com.dcjet.cs.util.customexport;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.invoke.MethodHandles;
import java.math.BigDecimal;

/**
 * @description: 数字转英文
 * @author: WJ
 * @createDate: 2021/9/7 15:45
 */
public class NumToEnUtil {

    private static final Logger logger = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    /** 基本数词表 */
    public static final String[] enNum = {
            "zero", "one", "two", "three", "four", "five", "six", "seven", "eight",
            "nine", "ten", "eleven", "twelve", "thirteen", "fourteen",
            "fifteen", "sixteen", "seventeen", "eighteen", "nineteen",
            "twenty", "", "", "", "", "", "", "", "", "", "thirty", "", "", "",
            "", "", "", "", "", "", "forty", "", "", "", "", "", "", "", "",
            "", "fifty", "", "", "", "", "", "", "", "", "", "sixty", "", "",
            "", "", "", "", "", "", "", "seventy", "", "", "", "", "", "", "",
            "", "", "eighty", "", "", "", "", "", "", "", "", "", "ninety" };
    /**单位表 */
    public static final String[] enUnit = { "hundred", "thousand,", "million,",
            "billion,", "trillion,", "quintillion," };

    public static String analyze(BigDecimal num) {
        // 转String
        return analyze(String.valueOf(num), "NORMAL");
    }
    public static String analyze(BigDecimal num, String type) {
        // 转String
        return analyze(String.valueOf(num), type);
    }
    public static String analyze(String num, String type) {
        if (StringUtils.isBlank(num)) {
            return "";
        }
        if (StringUtils.isBlank(type)) {
            type = "NORMAL";
        }
        // 数字字符串参数
        // 判断字符串是否为数字
        if (!num.matches("^\\d+(\\.\\d+)?$")) {
            logger.info(String.format("%s is not number", num));
            return "";
        }
        // 把字符串前面的0去掉
        num = num.replaceAll("^[0]*([1-9]*)", "$1");

        if (num.matches("\\d+")) {
            return analyzeInteger(num) + ("NORMAL".equals(type) ? "" : " and point");
        }
        // 整数部分
        String start = analyzeInteger(num.substring(0, num.indexOf(".")));
        // 小数部分
        String end = "";
        switch (type) {
            case "NORMAL" :
                end = analyzeDecimal(num.substring(num.indexOf(".") + 1));
                break;
            case "DOLLARS" :
                end = analyzeDollarsDecimal(num.substring(num.indexOf(".") + 1));
                break;
            default:
                break;
        }

        return StringUtils.isBlank(start) ? "" : start + end;
    }

    /**
     * 小数部分转化-美元
     * @param num
     * @return
     */
    private static String analyzeDollarsDecimal(String num) {
        // 判断若全为0则不转换
        if (Long.valueOf(num) == 0L) {
            return "";
        }

        // 把字符串末尾的0去掉
//        num = num.replaceAll("0*$", "");

        char[] str = num.toCharArray();
        int decimal = 0;
        if (str.length > 2) {
            // 保留两位 四舍五入
            decimal = Integer.valueOf(num.substring(0,2));
            if (Integer.valueOf(String.valueOf(str[2])) > 4) {
                decimal += 1;
            }
        } else {
            if (str.length == 1) {
                num = num + "0";
            }
            decimal = Integer.valueOf(num);
        }

        StringBuilder end = new StringBuilder();
        if (decimal < 20 || decimal % 10 == 0) {
            // 如果小于20或10的整数倍，直接取基本数词表的单词
            end.append(enNum[decimal]).append(" ");
        } else {
            // 否则取10位数词，再取个位数词
            end.append(enNum[decimal - decimal % 10]).append("-");
            end.append(enNum[decimal % 10]).append(" ");
        }

        return " and point " + end.toString();
    }

    /**
     * 小数部分转化
     * @param num
     * @return
     */
    private static String analyzeDecimal(String num) {
        // 判断若全为0则不转换
        if (Long.valueOf(num) == 0L) {
            return "";
        }

        // 把字符串末尾的0去掉
        num = num.replaceAll("0*$", "");

        char[] str = num.toCharArray();
        StringBuilder end = new StringBuilder();
        for (int i = 0; i < str.length; i++) {
            end.append(enNum[Integer.valueOf(String.valueOf(str[i]))]).append(" ");
        }
        return " point " + end.toString();
    }

    /**
     * 整数部分转化
     * @param num
     * @return
     */
    public static String analyzeInteger(String num) {

        if (num.length() == 0) {
            // 如果长度为0，则原串都是0
            return enNum[0];
        } else if (num.length() > 12) {
            // 如果大于12，即大于999,999,999,999
            logger.info("too big");
            return "";
        }
        // 按3位分割分组
        int count = (num.length() % 3 == 0) ? num.length() / 3
                : num.length() / 3 + 1;
        // 判断组单位是否超过
        if (count > enUnit.length) {
            logger.info("too big");
            return "";
        }
        // 可以根据需求适当追加enUnit
        String[] group = new String[count];
        for (int i = num.length(), j = group.length - 1; i > 0; i -= 3) {
            group[j--] = num.substring(Math.max(i - 3, 0), i);
        }
        // 结果保存
        StringBuilder buf = new StringBuilder();
        // 遍历分割的组
        for (int i = 0; i < count; i++) {
            int v = Integer.valueOf(group[i]);
            // 因为按3位分割，所以这里不会有超过999的数
            if (v >= 100) {
                buf.append(enNum[v / 100]).append(" ").append(enUnit[0]).append(" ");
                // 获取百位，并得到百位以后的数
                v = v % 100;
                if (v != 0) {
                    // 如果百位后的数不为0，则追加and
                    buf.append("and ");
                }
            }
            // 前提是v不为0才作解析
            if (v != 0) {
                if (v < 20 || v % 10 == 0) {
                    // 如果小于20或10的整数倍，直接取基本数词表的单词
                    buf.append(enNum[v]).append(" ");
                } else {
                    // 否则取10位数词，再取个位数词
                    buf.append(enNum[v - v % 10]).append("-");
                    buf.append(enNum[v % 10]).append(" ");
                }
                if (i != count - 1) {
                    // 百位以上的组追加相应的单位
                    buf.append(enUnit[count - 1 - i]).append(" ");
                }
            }
        }
        return buf.toString().trim();
    }

}
