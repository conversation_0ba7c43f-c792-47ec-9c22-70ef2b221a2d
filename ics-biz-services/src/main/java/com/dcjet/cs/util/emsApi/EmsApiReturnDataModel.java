package com.dcjet.cs.util.emsApi;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@ApiModel(value="api导入接口返回数据模型")
@Setter
@Getter
public class EmsApiReturnDataModel<T> {

    @ApiModelProperty("用于追踪导入任务，返回请求时传入的TASK_ID")
    public String taskID;

    @ApiModelProperty("正确数据条数")
    public Number rightDataCount;

    @ApiModelProperty("若此值为0则说明数据入库成功")
    public Number errorDataCount;

    @ApiModelProperty("表头用于返回清单流水号")
    public String returnInfo;

    @ApiModelProperty("验证失败的业务数据")
    public List<T> errorData;
}
