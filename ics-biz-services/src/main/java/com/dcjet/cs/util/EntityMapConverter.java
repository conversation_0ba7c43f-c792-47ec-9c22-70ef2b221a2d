package com.dcjet.cs.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import javax.persistence.Column;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 实体对象与Map转换工具类
 */
@Slf4j
public class EntityMapConverter {

    /**
     * 将对象转换为Map
     * 
     * @param entity 实体对象
     * @param <T> 实体类型
     * @return 转换后的Map，key为属性名，value为属性值
     */
    public static <T> Map<String, Object> convertToMap(T entity) {
        if (entity == null) {
            return new HashMap<>();
        }
        
        // 如果已经是Map类型，直接返回
        if (entity instanceof Map) {
            return (Map<String, Object>) entity;
        }
        
        Map<String, Object> result = new HashMap<>();
        PropertyDescriptor[] propertyDescriptors = BeanUtils.getPropertyDescriptors(entity.getClass());
        
        for (PropertyDescriptor propertyDescriptor : propertyDescriptors) {
            String propertyName = propertyDescriptor.getName();
            // 跳过class属性
            if ("class".equals(propertyName)) {
                continue;
            }

            Method readMethod = propertyDescriptor.getReadMethod();
            if (readMethod != null) {
                try {
                    Object value = readMethod.invoke(entity);
                    if (value != null) {
                        Column column = entity.getClass().getDeclaredField(propertyName).getAnnotation(Column.class);
                        result.put(column == null || StringUtils.isBlank(column.name()) ? propertyName : column.name(), value);
                    }
                } catch (Exception e) {
                    log.warn("Failed to get property value: {}", propertyName, e);
                }
            }
        }
        
        return result;
    }
    
    /**
     * 将对象列表转换为Map列表
     * 
     * @param entities 实体对象列表
     * @param <T> 实体类型
     * @return 转换后的Map列表
     */
    public static <T> List<Map<String, Object>> convertToMapList(List<T> entities) {
        if (entities == null || entities.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<Map<String, Object>> result = new ArrayList<>(entities.size());
        for (T entity : entities) {
            result.add(convertToMap(entity));
        }
        
        return result;
    }
    
    /**
     * 将Map转换为指定类型的对象
     * 
     * @param map 属性Map
     * @param clazz 目标类型
     * @param <T> 目标类型
     * @return 转换后的对象
     */
    public static <T> T convertToEntity(Map<String, Object> map, Class<T> clazz) {
        if (map == null || map.isEmpty()) {
            return null;
        }
        
        try {
            T instance = clazz.newInstance();
            
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                String propertyName = entry.getKey();
                Object value = entry.getValue();
                
                if (value != null) {
                    PropertyDescriptor propertyDescriptor = BeanUtils.getPropertyDescriptor(clazz, propertyName);
                    if (propertyDescriptor != null) {
                        Method writeMethod = propertyDescriptor.getWriteMethod();
                        if (writeMethod != null) {
                            writeMethod.invoke(instance, value);
                        }
                    }
                }
            }
            
            return instance;
        } catch (Exception e) {
            log.error("Failed to convert map to entity: {}", clazz.getName(), e);
            return null;
        }
    }
    
    /**
     * 将Map列表转换为指定类型的对象列表
     * 
     * @param maps Map列表
     * @param clazz 目标类型
     * @param <T> 目标类型
     * @return 转换后的对象列表
     */
    public static <T> List<T> convertToEntityList(List<Map<String, Object>> maps, Class<T> clazz) {
        if (maps == null || maps.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<T> result = new ArrayList<>(maps.size());
        for (Map<String, Object> map : maps) {
            T entity = convertToEntity(map, clazz);
            if (entity != null) {
                result.add(entity);
            }
        }
        
        return result;
    }
}
