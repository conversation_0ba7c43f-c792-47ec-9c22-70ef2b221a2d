<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.base.dao.HomePageMapper">

    <select id="getAuditList" resultType="com.dcjet.cs.dto.base.HomePageDto">
        select 'O' AS TYPE,'备案归类审核' AS NAME,count(1) AS COUNT,0 as ORDER_ID,
         'cs/aeoManage/filingClassifyCheck' AS URL from T_MAT_IMGEXG_ORG t
        <where>
            and TRADE_CODE = #{tradeCode}
            and t.APPR_STATUS = '2'
            <if test='_databaseId == "postgresql" '>
                AND COALESCE(t.UPDATE_USER, t.insert_user)!=#{userNo,jdbcType=VARCHAR}
            </if>
            <if test='_databaseId != "postgresql" '>
                AND NVL(UPDATE_USER,insert_user)!=#{userNo,jdbcType=VARCHAR}
            </if>
        </where>
        union all
        SELECT 'Y','企业料件审核' AS NAME, count(1),1,
        'cs/aeoManage/prodClassifyCheck' AS URL   FROM (
        select 'Y' as APPR_TYPE from V_AEO_MAT_AUDIT t
        <where>
            and TRADE_CODE = #{tradeCode}
            and t.APPR_STATUS = '2'
            <if test='_databaseId == "postgresql" '>
                AND COALESCE(t.UPDATE_USER, t.insert_user)!=#{userNo,jdbcType=VARCHAR}
            </if>
            <if test='_databaseId != "postgresql" '>
                AND NVL(UPDATE_USER,insert_user)!=#{userNo,jdbcType=VARCHAR}
            </if>
        </where>
        union
        select 'Y' from V_AEO_NOBOND_AUDIT t
        <where>
            and TRADE_CODE = #{tradeCode}
            and t.APPR_STATUS = '2'
            <if test='_databaseId == "postgresql" '>
                AND COALESCE(t.UPDATE_USER, t.insert_user)!=#{userNo,jdbcType=VARCHAR}
            </if>
            <if test='_databaseId != "postgresql" '>
                AND NVL(UPDATE_USER,insert_user)!=#{userNo,jdbcType=VARCHAR}
            </if>
        </where>
        ) T
        union all
        SELECT 'C','单损耗审核' AS NAME,count(1),2,
        'cs/internalAuditRecordQuery/singleLossCheckList' AS URL
        FROM T_GWSTD_EMS_HEAD t
        <where>
            and TRADE_CODE = #{tradeCode}
            and t.APPR_STATUS = '2'
            <if test='_databaseId == "postgresql" '>
                AND COALESCE(t.UPDATE_USER, t.insert_user)!=#{userNo,jdbcType=VARCHAR}
            </if>
            <if test='_databaseId != "postgresql" '>
                AND NVL(UPDATE_USER,insert_user)!=#{userNo,jdbcType=VARCHAR}
            </if>
        </where>
        union all
        select 'I','进口业务审核' AS NAME,count(1),3,
        'cs/aeoManage/AeoDecErpIHeadNHeadList' AS URL
        FROM T_DEC_ERP_I_HEAD_N t
        <where>
            and TRADE_CODE = #{tradeCode}
            and t.APPR_STATUS = '2'
            <if test='_databaseId == "postgresql" '>
                AND COALESCE(t.UPDATE_USER, t.insert_user)!=#{userNo,jdbcType=VARCHAR}
            </if>
            <if test='_databaseId != "postgresql" '>
                AND NVL(UPDATE_USER,insert_user)!=#{userNo,jdbcType=VARCHAR}
            </if>
        </where>
        union all
        select 'E','出口业务审核' AS NAME, count(1),4,
        'cs/aeoManage/AeoDecErpEHeadNHeadList' AS URL
        FROM T_DEC_ERP_E_HEAD_N t
        <where>
            and t.TRADE_CODE = #{tradeCode}
            and t.APPR_STATUS = '2'
            <if test='_databaseId == "postgresql" '>
                AND COALESCE(t.UPDATE_USER, t.insert_user)!=#{userNo,jdbcType=VARCHAR}
            </if>
            <if test='_databaseId != "postgresql" '>
                AND NVL(UPDATE_USER,insert_user)!=#{userNo,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
    <select id="getAuditListForType" resultType="com.dcjet.cs.dto.base.HomePageDto">
        <foreach collection="type" item="item" index="index" separator="union all">
            <if test='item=="O"'>
                select 'O' AS TYPE,'备案归类审核' AS NAME,count(1) AS COUNT,0 as ORDER_ID,
                'cs/aeoManage/filingClassifyCheck' AS URL from T_MAT_IMGEXG_ORG t
                <where>
                    and TRADE_CODE = #{tradeCode}
                    and t.APPR_STATUS = '2'
                    <if test='_databaseId == "postgresql" '>
                        AND COALESCE(t.UPDATE_USER, t.insert_user)!=#{userNo,jdbcType=VARCHAR}
                    </if>
                    <if test='_databaseId != "postgresql" '>
                        AND NVL(UPDATE_USER,insert_user)!=#{userNo,jdbcType=VARCHAR}
                    </if>
                </where>
            </if>
            <if test='item=="Y"'>
            SELECT 'Y' AS TYPE,'企业料件审核' AS NAME, count(1) AS COUNT,1 ORDER_ID,
                'cs/aeoManage/prodClassifyCheck' AS URL FROM (
            select 'Y' as APPR_TYPE from V_AEO_MAT_AUDIT t
            <where>
                and TRADE_CODE = #{tradeCode}
                and t.APPR_STATUS = '2'
                <if test='_databaseId == "postgresql" '>
                    AND COALESCE(t.UPDATE_USER, t.insert_user)!=#{userNo,jdbcType=VARCHAR}
                </if>
                <if test='_databaseId != "postgresql" '>
                    AND NVL(UPDATE_USER,insert_user)!=#{userNo,jdbcType=VARCHAR}
                </if>
            </where>
            union
            select 'Y' from V_AEO_NOBOND_AUDIT t
            <where>
                and TRADE_CODE = #{tradeCode}
                and t.APPR_STATUS = '2'
                <if test='_databaseId == "postgresql" '>
                    AND COALESCE(t.UPDATE_USER, t.insert_user)!=#{userNo,jdbcType=VARCHAR}
                </if>
                <if test='_databaseId != "postgresql" '>
                    AND NVL(UPDATE_USER,insert_user)!=#{userNo,jdbcType=VARCHAR}
                </if>
            </where>
            ) T
            </if>
            <if test='item=="C"'>
                SELECT 'C' AS TYPE,'单损耗审核' AS NAME, count(1) AS COUNT,2 ORDER_ID,
                'cs/internalAuditRecordQuery/singleLossCheckList' AS URL
                FROM T_GWSTD_EMS_HEAD t
                <where>
                    and TRADE_CODE = #{tradeCode}
                    and t.APPR_STATUS = '2'
                    <if test='_databaseId == "postgresql" '>
                        AND COALESCE(t.UPDATE_USER, t.insert_user)!=#{userNo,jdbcType=VARCHAR}
                    </if>
                    <if test='_databaseId != "postgresql" '>
                        AND NVL(UPDATE_USER,insert_user)!=#{userNo,jdbcType=VARCHAR}
                    </if>
                </where>
            </if>
            <if test='item=="I"'>
                select 'I' AS TYPE,'进口业务审核' AS NAME, count(1) AS COUNT,3 ORDER_ID,
                'cs/aeoManage/AeoDecErpIHeadNHeadList' AS URL
                FROM T_DEC_ERP_I_HEAD_N t
                <where>
                    and TRADE_CODE = #{tradeCode}
                    and t.APPR_STATUS = '2'
                    <if test='_databaseId == "postgresql" '>
                        AND COALESCE(t.UPDATE_USER, t.insert_user)!=#{userNo,jdbcType=VARCHAR}
                    </if>
                    <if test='_databaseId != "postgresql" '>
                        AND NVL(UPDATE_USER,insert_user)!=#{userNo,jdbcType=VARCHAR}
                    </if>
                </where>
            </if>
            <if test='item=="E"'>
                select 'E' AS TYPE,'出口业务审核' AS NAME, count(1) AS COUNT,4 ORDER_ID,
                'cs/aeoManage/AeoDecErpEHeadNHeadList' AS URL
                FROM T_DEC_ERP_E_HEAD_N t
                <where>
                    and t.TRADE_CODE = #{tradeCode}
                    and t.APPR_STATUS = '2'
                    <if test='_databaseId == "postgresql" '>
                        AND COALESCE(t.UPDATE_USER, t.insert_user)!=#{userNo,jdbcType=VARCHAR}
                    </if>
                    <if test='_databaseId != "postgresql" '>
                        AND NVL(UPDATE_USER,insert_user)!=#{userNo,jdbcType=VARCHAR}
                    </if>
                </where>
            </if>
        </foreach>

    </select>
    <select id="getWarringList" resultType="com.dcjet.cs.dto.base.HomePageDto">
            select 'CARD' AS TYPE,' 证件卡类预警' AS NAME,count(1) AS COUNT,5 as ORDER_ID,
        'cs/earlyWarning/idCard' AS URL  from t_warring_card t
            LEFT JOIN T_WARRING_SET s
            ON s.WARRING_TYPE = 'CARD' and t.TRADE_CODE=s.TRADE_CODE
            <where>
                and t.TRADE_CODE = #{tradeCode}
                <![CDATA[and CASE WHEN T.STATUS = '1' THEN '3' WHEN ceil( F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP()) ) < 0 THEN '2' WHEN ceil( F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP()) ) - F_XDO_NVL(s.WARRING_DAYS,'0') < 0 THEN '1' ELSE '0' END in ('1','2')]]>
            </where>
        union all
            select 'BAIL' AS TYPE,'保金保函预警' AS NAME,count(1) AS COUNT,6 as ORDER_ID,
        'cs/earlyWarning/insuranceBond' AS URL  from t_warring_bail t
            LEFT JOIN T_WARRING_SET s
            ON s.WARRING_TYPE = 'BAIL' and t.TRADE_CODE=s.TRADE_CODE
            <where>
                and t.TRADE_CODE = #{tradeCode}
                <![CDATA[and CASE WHEN T.STATUS = '1' THEN '3' WHEN ceil( F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP()) ) < 0 THEN '2' WHEN ceil( F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP()) ) - F_XDO_NVL(s.WARRING_DAYS,'0') < 0 THEN '1' ELSE '0' END in ('1','2')]]>
            </where>
        union all
            select 'EXPIRE' AS TYPE,'外发加工到期预警' AS NAME,count(1) AS COUNT,7 as ORDER_ID,
        'cs/earlyWarning/processExpire' AS URL  from t_warring_expire t
            LEFT JOIN T_WARRING_SET s
            ON s.WARRING_TYPE = 'EXPIRE' and t.TRADE_CODE=s.TRADE_CODE
            <where>
                and t.TRADE_CODE = #{tradeCode}
                <![CDATA[and CASE WHEN T.STATUS = '1' THEN '3' WHEN ceil( F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP()) ) < 0 THEN '2' WHEN ceil( F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP()) ) - F_XDO_NVL(s.WARRING_DAYS,'0') < 0 THEN '1' ELSE '0' END in ('1','2')]]>
            </where>
        union all
            select 'EQUIP' AS TYPE,'减免税设备解除监管预警' AS NAME,count(1) AS COUNT,8 as ORDER_ID,
        'cs/earlyWarning/derateEquip' AS URL  from t_warring_equip t
            LEFT JOIN T_WARRING_SET s
            ON s.WARRING_TYPE = 'EQUIP' and t.TRADE_CODE=s.TRADE_CODE
            <where>
                and t.TRADE_CODE = #{tradeCode}
                <![CDATA[and CASE WHEN T.STATUS = '1' THEN '3' WHEN ceil( F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP()) ) < 0 THEN '2' WHEN ceil( F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP()) ) - F_XDO_NVL(s.WARRING_DAYS,'0') < 0 THEN '1' ELSE '0' END in ('1','2')]]>
            </where>
        union all
            select 'EXPORT' AS TYPE,'暂时进出口预警' AS NAME,count(1) AS COUNT,9 as ORDER_ID,
        'cs/earlyWarning/importExport' AS URL  from t_warring_export t
            LEFT JOIN T_WARRING_SET s
            ON s.WARRING_TYPE = 'EXPORT' and t.TRADE_CODE=s.TRADE_CODE
            <where>
                and t.TRADE_CODE = #{tradeCode}
                <![CDATA[and CASE WHEN T.STATUS = '1' THEN '3' WHEN ceil( F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP()) ) < 0 THEN '2' WHEN ceil( F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP()) ) - F_XDO_NVL(s.WARRING_DAYS,'0') < 0 THEN '1' ELSE '0' END in ('1','2')]]>
            </where>
        union all
            select 'REPAIR' AS TYPE,'修理物品预警' AS NAME,count(1) AS COUNT,10 as ORDER_ID,
        'cs/earlyWarning/repairItems' AS URL  from t_warring_repair t
            LEFT JOIN T_WARRING_SET s
            ON s.WARRING_TYPE = 'REPAIR' and t.TRADE_CODE=s.TRADE_CODE
            <where>
                and t.TRADE_CODE = #{tradeCode}
                <![CDATA[and CASE WHEN T.STATUS = '1' THEN '3' WHEN ceil( F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP()) ) < 0 THEN '2' WHEN ceil( F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP()) ) - F_XDO_NVL(s.WARRING_DAYS,'0') < 0 THEN '1' ELSE '0' END in ('1','2')]]>
            </where>
        union all
            select 'MARGIN' AS TYPE,'手册余量预警' AS NAME,count(1) AS COUNT,11 as ORDER_ID,
        'cs/earlyWarning/manualMargin' AS URL  from V_MAT_MARGIN t
            <where>
                and t.TRADE_CODE = #{tradeCode}
                and (t.STATUS='1' or t.STATUS='2')
            </where>
    </select>
    <select id="getWarringListForType" resultType="com.dcjet.cs.dto.base.HomePageDto">
        <foreach collection="type" item="item" index="index" separator="union all">
            <if test='item=="CARD"'>
            select 'CARD' AS TYPE,' 证件卡类预警' AS NAME,count(1) AS COUNT,5 as ORDER_ID,
                'cs/earlyWarning/idCard' AS URL from t_warring_card t
            LEFT JOIN T_WARRING_SET s
            ON s.WARRING_TYPE = 'CARD' and t.TRADE_CODE=s.TRADE_CODE
            <where>
                and t.TRADE_CODE = #{tradeCode}
                <![CDATA[and CASE WHEN T.STATUS = '1' THEN '3' WHEN ceil( F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP()) ) < 0 THEN '2' WHEN ceil( F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP()) ) - F_XDO_NVL(s.WARRING_DAYS,'0') < 0 THEN '1' ELSE '0' END in ('1','2')]]>
            </where>
            </if>
            <if test='item=="BAIL"'>
            select 'BAIL' AS TYPE,'保金保函预警' AS NAME,count(1) AS COUNT,6 as ORDER_ID,
                'cs/earlyWarning/insuranceBond' AS URL from t_warring_bail t
            LEFT JOIN T_WARRING_SET s
            ON s.WARRING_TYPE = 'BAIL' and t.TRADE_CODE=s.TRADE_CODE
            <where>
                and t.TRADE_CODE = #{tradeCode}
                <![CDATA[and CASE WHEN T.STATUS = '1' THEN '3' WHEN ceil( F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP()) ) < 0 THEN '2' WHEN ceil( F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP()) ) - F_XDO_NVL(s.WARRING_DAYS,'0') < 0 THEN '1' ELSE '0' END in ('1','2')]]>
            </where>
            </if>
            <if test='item=="EXPIRE"'>
            select 'EXPIRE' AS TYPE,'外发加工到期预警' AS NAME,count(1) AS COUNT,7 as ORDER_ID,
                'cs/earlyWarning/processExpire' AS URL from t_warring_expire t
            LEFT JOIN T_WARRING_SET s
            ON s.WARRING_TYPE = 'EXPIRE' and t.TRADE_CODE=s.TRADE_CODE
            <where>
                and t.TRADE_CODE = #{tradeCode}
                <![CDATA[and CASE WHEN T.STATUS = '1' THEN '3' WHEN ceil( F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP()) ) < 0 THEN '2' WHEN ceil( F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP()) ) - F_XDO_NVL(s.WARRING_DAYS,'0') < 0 THEN '1' ELSE '0' END in ('1','2')]]>
            </where>
            </if>
            <if test='item=="EQUIP"'>
            select 'EQUIP' AS TYPE,'减免税设备解除监管预警' AS NAME,count(1) AS COUNT,8 as ORDER_ID,
                'cs/earlyWarning/derateEquip' AS URL from t_warring_equip t
            LEFT JOIN T_WARRING_SET s
            ON s.WARRING_TYPE = 'EQUIP' and t.TRADE_CODE=s.TRADE_CODE
            <where>
                and t.TRADE_CODE = #{tradeCode}
                <![CDATA[and CASE WHEN T.STATUS = '1' THEN '3' WHEN ceil( F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP()) ) < 0 THEN '2' WHEN ceil( F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP()) ) - F_XDO_NVL(s.WARRING_DAYS,'0') < 0 THEN '1' ELSE '0' END in ('1','2')]]>
            </where>
            </if>
            <if test='item=="EXPORT"'>
            select 'EXPORT' AS TYPE,'暂时进出口预警' AS NAME,count(1) AS COUNT,9 as ORDER_ID,
                'cs/earlyWarning/importExport' AS URL from t_warring_export t
            LEFT JOIN T_WARRING_SET s
            ON s.WARRING_TYPE = 'EXPORT' and t.TRADE_CODE=s.TRADE_CODE
            <where>
                and t.TRADE_CODE = #{tradeCode}
                <![CDATA[and CASE WHEN T.STATUS = '1' THEN '3' WHEN ceil( F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP()) ) < 0 THEN '2' WHEN ceil( F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP()) ) - F_XDO_NVL(s.WARRING_DAYS,'0') < 0 THEN '1' ELSE '0' END in ('1','2')]]>
            </where>
            </if>
            <if test='item=="REPAIR"'>
            select 'REPAIR' AS TYPE,'修理物品预警' AS NAME,count(1) AS COUNT,10 as ORDER_ID,
                'cs/earlyWarning/repairItems' AS URL from t_warring_repair t
            LEFT JOIN T_WARRING_SET s
            ON s.WARRING_TYPE = 'REPAIR' and t.TRADE_CODE=s.TRADE_CODE
            <where>
                and t.TRADE_CODE = #{tradeCode}
                <![CDATA[and CASE WHEN T.STATUS = '1' THEN '3' WHEN ceil( F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP()) ) < 0 THEN '2' WHEN ceil( F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP()) ) - F_XDO_NVL(s.WARRING_DAYS,'0') < 0 THEN '1' ELSE '0' END in ('1','2')]]>
            </where>
            </if>
            <if test='item=="MARGIN"'>
            select 'MARGIN' AS TYPE,'手册余量预警' AS NAME,count(1) AS COUNT,11 as ORDER_ID,
                'cs/earlyWarning/manualMargin' AS URL from V_MAT_MARGIN t
            <where>
                and t.TRADE_CODE = #{tradeCode}
                and (t.STATUS='1' or t.STATUS='2')
            </where>
            </if>
        </foreach>
    </select>
</mapper>
