package com.dcjet.cs.base.dao;

import com.dcjet.cs.dto.base.*;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
* generated by Generate 神码
* BiShipto
* <AUTHOR>
* @date: 2019-7-26
*/
public interface FirstPageMapper extends Mapper<FirstPageDto> {
    /**
     * 查询获取数据
     * @param tradeCode
     * @param userNo
     * @return
     */
    List<FirstPageDto> getAuditList(@Param("tradeCode") String tradeCode, @Param("userNo") String userNo);
    /**
     * 查询获取数据
     * @param tradeCode
     * @return
     */
    List<FirstPageDto> getWarringList(@Param("tradeCode") String tradeCode);
    /**
     * 查询获取数据
     * @param tradeCode
     * @param userNo
     * @return
     */
    List<MonthValue> getITicketList(@Param("tradeCode") String tradeCode, @Param("userNo") String userNo);
    List<MonthValue> getETicketList(@Param("tradeCode") String tradeCode, @Param("userNo") String userNo);

    List<MonthValue> getITotalList(@Param("tradeCode") String tradeCode, @Param("userNo") String userNo);
    List<MonthValue> getETotalList(@Param("tradeCode") String tradeCode, @Param("userNo") String userNo);

    List<LogisticsIDto> getLogisticsI(HomePageParam homePageParam);
    List<LogisticsEDto> getLogisticsE(HomePageParam homePageParam);
    List<AuditInfoDto> getAuditInfo(HomePageParam homePageParam);

    List<WarringDto> getWarring(HomePageParam homePageParam);
}
