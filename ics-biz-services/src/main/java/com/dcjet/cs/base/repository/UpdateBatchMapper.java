package com.dcjet.cs.base.repository;

import org.apache.ibatis.annotations.UpdateProvider;

import java.util.List;

/**
 * <AUTHOR>
 */
@tk.mybatis.mapper.annotation.RegisterMapper
public interface UpdateBatchMapper<T> {



    /**
     * 根据主键更新实体全部字段，null值会被更新
     *
     * @param recordList
     * @return
     */
    @UpdateProvider(type = UpdateBatchProvider.class, method = "dynamicSQL")
    int updateListByPrimaryKey(List<? extends T> recordList);
}
