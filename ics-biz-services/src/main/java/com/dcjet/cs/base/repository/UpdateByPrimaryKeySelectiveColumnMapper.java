package com.dcjet.cs.base.repository;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.UpdateProvider;

import java.util.List;

@tk.mybatis.mapper.annotation.RegisterMapper
public interface UpdateByPrimaryKeySelectiveColumnMapper<T> {


    /**
     * 根据主键更新属性不为null的值, 指定的属性(null值)会被强制更新
     * @param record
     * @param updateProperties
     * @return
     */
    @UpdateProvider(type = UpdateByPrimaryKeySelectiveColumnProvider.class, method = "dynamicSQL")
    int updateByPrimaryKeySelectiveColumns(@Param("record") T record, @Param("updateProperties") List<String> updateProperties);
}
