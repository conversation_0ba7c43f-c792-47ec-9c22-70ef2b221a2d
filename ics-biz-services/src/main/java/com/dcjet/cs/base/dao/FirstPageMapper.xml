<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.base.dao.FirstPageMapper">

    <select id="getAuditList" resultType="com.dcjet.cs.dto.base.FirstPageDto">
        select 'O' AUDIT_TYPE,count(1) AUDIT_COUNT,0 as ORDER_ID from T_MAT_IMGEXG_ORG t
        <where>
            and TRADE_CODE = #{tradeCode}
            and t.APPR_STATUS = '2'
            <if test='_databaseId == "postgresql" '>
                AND COALESCE(t.UPDATE_USER, t.insert_user)!=#{userNo,jdbcType=VARCHAR}
            </if>
            <if test='_databaseId != "postgresql" '>
                AND NVL(UPDATE_USER,insert_user)!=#{userNo,jdbcType=VARCHAR}
            </if>
        </where>
        union all
        SELECT 'Y', count(1),1  FROM (
        select 'Y' as APPR_TYPE from V_AEO_MAT_AUDIT t
        <where>
            and TRADE_CODE = #{tradeCode}
            and t.APPR_STATUS = '2'
            <if test='_databaseId == "postgresql" '>
                AND COALESCE(t.UPDATE_USER, t.insert_user)!=#{userNo,jdbcType=VARCHAR}
            </if>
            <if test='_databaseId != "postgresql" '>
                AND NVL(UPDATE_USER,insert_user)!=#{userNo,jdbcType=VARCHAR}
            </if>
        </where>
        union
        select 'Y' from V_AEO_NOBOND_AUDIT t
        <where>
            and TRADE_CODE = #{tradeCode}
            and t.APPR_STATUS = '2'
            <if test='_databaseId == "postgresql" '>
                AND COALESCE(t.UPDATE_USER, t.insert_user)!=#{userNo,jdbcType=VARCHAR}
            </if>
            <if test='_databaseId != "postgresql" '>
                AND NVL(UPDATE_USER,insert_user)!=#{userNo,jdbcType=VARCHAR}
            </if>
        </where>
        ) T
        union all
        select 'E', count(1),4
        FROM T_DEC_ERP_E_HEAD_N t
        <where>
            and t.TRADE_CODE = #{tradeCode}
            and t.APPR_STATUS = '2'
            <if test='_databaseId == "postgresql" '>
                AND COALESCE(t.UPDATE_USER, t.insert_user)!=#{userNo,jdbcType=VARCHAR}
            </if>
            <if test='_databaseId != "postgresql" '>
                AND NVL(UPDATE_USER,insert_user)!=#{userNo,jdbcType=VARCHAR}
            </if>
        </where>
        union all
        select 'I',count(1),3
        FROM T_DEC_ERP_I_HEAD_N t
        <where>
            and TRADE_CODE = #{tradeCode}
            and t.APPR_STATUS = '2'
            <if test='_databaseId == "postgresql" '>
                AND COALESCE(t.UPDATE_USER, t.insert_user)!=#{userNo,jdbcType=VARCHAR}
            </if>
            <if test='_databaseId != "postgresql" '>
                AND NVL(UPDATE_USER,insert_user)!=#{userNo,jdbcType=VARCHAR}
            </if>
        </where>
        union all
        SELECT 'C',count(1),2
        FROM T_GWSTD_EMS_HEAD t
        <where>
            and TRADE_CODE = #{tradeCode}
            and t.APPR_STATUS = '2'
            <if test='_databaseId == "postgresql" '>
                AND COALESCE(t.UPDATE_USER, t.insert_user)!=#{userNo,jdbcType=VARCHAR}
            </if>
            <if test='_databaseId != "postgresql" '>
                AND NVL(UPDATE_USER,insert_user)!=#{userNo,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="getWarringList" resultType="com.dcjet.cs.dto.base.FirstPageDto">
            select 'CARD' AUDIT_TYPE,count(1) AUDIT_COUNT,0 as ORDER_ID from t_warring_card t
            LEFT JOIN T_WARRING_SET s
            ON s.WARRING_TYPE = 'CARD' and t.TRADE_CODE=s.TRADE_CODE
            <where>
                and t.TRADE_CODE = #{tradeCode}
                <![CDATA[and CASE WHEN T.STATUS = '1' THEN '3' WHEN ceil( F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP()) ) < 0 THEN '2' WHEN ceil( F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP()) ) - F_XDO_NVL(s.WARRING_DAYS,'0') < 0 THEN '1' ELSE '0' END in ('1','2')]]>
            </where>
        union all
            select 'BAIL' AUDIT_TYPE,count(1) AUDIT_COUNT,0 as ORDER_ID from t_warring_bail t
            LEFT JOIN T_WARRING_SET s
            ON s.WARRING_TYPE = 'BAIL' and t.TRADE_CODE=s.TRADE_CODE
            <where>
                and t.TRADE_CODE = #{tradeCode}
                <![CDATA[and CASE WHEN T.STATUS = '1' THEN '3' WHEN ceil( F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP()) ) < 0 THEN '2' WHEN ceil( F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP()) ) - F_XDO_NVL(s.WARRING_DAYS,'0') < 0 THEN '1' ELSE '0' END in ('1','2')]]>
            </where>
        union all
            select 'SPILL' AUDIT_TYPE,count(1) AUDIT_COUNT,0 as ORDER_ID from t_warring_spill t
            LEFT JOIN T_WARRING_SET s
            ON s.WARRING_TYPE = 'SPILL' and t.TRADE_CODE=s.TRADE_CODE
            <where>
                and t.TRADE_CODE = #{tradeCode}
                <![CDATA[and CASE WHEN T.STATUS = '1' THEN '3' WHEN ceil( F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP()) ) < 0 THEN '2' WHEN ceil( F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP()) ) - F_XDO_NVL(s.WARRING_DAYS,'0') < 0 THEN '1' ELSE '0' END in ('1','2')]]>
            </where>
        union all
            select 'EXPIRE' AUDIT_TYPE,count(1) AUDIT_COUNT,0 as ORDER_ID from t_warring_expire t
            LEFT JOIN T_WARRING_SET s
            ON s.WARRING_TYPE = 'EXPIRE' and t.TRADE_CODE=s.TRADE_CODE
            <where>
                and t.TRADE_CODE = #{tradeCode}
                <![CDATA[and CASE WHEN T.STATUS = '1' THEN '3' WHEN ceil( F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP()) ) < 0 THEN '2' WHEN ceil( F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP()) ) - F_XDO_NVL(s.WARRING_DAYS,'0') < 0 THEN '1' ELSE '0' END in ('1','2')]]>
            </where>
        union all
            select 'EQUIP' AUDIT_TYPE,count(1) AUDIT_COUNT,0 as ORDER_ID from t_warring_equip t
            LEFT JOIN T_WARRING_SET s
            ON s.WARRING_TYPE = 'EQUIP' and t.TRADE_CODE=s.TRADE_CODE
            <where>
                and t.TRADE_CODE = #{tradeCode}
                <![CDATA[and CASE WHEN T.STATUS = '1' THEN '3' WHEN ceil( F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP()) ) < 0 THEN '2' WHEN ceil( F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP()) ) - F_XDO_NVL(s.WARRING_DAYS,'0') < 0 THEN '1' ELSE '0' END in ('1','2')]]>
            </where>
            union all
            select 'EXPORT' AUDIT_TYPE,count(1) AUDIT_COUNT,0 as ORDER_ID from t_warring_export t
            LEFT JOIN T_WARRING_SET s
            ON s.WARRING_TYPE = 'EXPORT' and t.TRADE_CODE=s.TRADE_CODE
            <where>
                and t.TRADE_CODE = #{tradeCode}
                <![CDATA[and CASE WHEN T.STATUS = '1' THEN '3' WHEN ceil( F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP()) ) < 0 THEN '2' WHEN ceil( F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP()) ) - F_XDO_NVL(s.WARRING_DAYS,'0') < 0 THEN '1' ELSE '0' END in ('1','2')]]>
            </where>
            union all
            select 'REPAIR' AUDIT_TYPE,count(1) AUDIT_COUNT,0 as ORDER_ID from t_warring_repair t
            LEFT JOIN T_WARRING_SET s
            ON s.WARRING_TYPE = 'REPAIR' and t.TRADE_CODE=s.TRADE_CODE
            <where>
                and t.TRADE_CODE = #{tradeCode}
                <![CDATA[and CASE WHEN T.STATUS = '1' THEN '3' WHEN ceil( F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP()) ) < 0 THEN '2' WHEN ceil( F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP()) ) - F_XDO_NVL(s.WARRING_DAYS,'0') < 0 THEN '1' ELSE '0' END in ('1','2')]]>
            </where>
            union all
            select 'MARGIN' AUDIT_TYPE,count(1) AUDIT_COUNT,0 as ORDER_ID from V_MAT_MARGIN t
            <where>
                and t.TRADE_CODE = #{tradeCode}
                and (t.STATUS='1' or t.STATUS='2')
            </where>
    </select>

    <select id="getITicketList" resultType="com.dcjet.cs.dto.base.MonthValue">
        select to_char(insert_time,'yyyy-mm') as MONTH,count(1) as VALUE from t_dec_erp_i_head_n
        <where>
            and TRADE_CODE = #{tradeCode}
        </where>
        group by to_char(insert_time,'yyyy-mm') order by to_char(insert_time,'yyyy-mm')
    </select>
    <select id="getETicketList" resultType="com.dcjet.cs.dto.base.MonthValue">
        select to_char(insert_time,'yyyy-mm') as MONTH,count(1) as VALUE from t_dec_erp_e_head_n
        <where>
            and TRADE_CODE = #{tradeCode}
        </where>
        group by to_char(insert_time,'yyyy-mm') order by to_char(insert_time,'yyyy-mm')
    </select>

    <select id="getITotalList" resultType="com.dcjet.cs.dto.base.MonthValue">
        select to_char(insert_time, 'yyyy-mm') as MONTH, sum(DEC_TOTAL) as VALUE
        from (select t.insert_time, t.trade_code, round(sum(nvl(t.dec_total,1) * nvl(r.curr_price_usd,1)), 5) as DEC_TOTAL
        from t_dec_erp_i_list_n t
        left join (select *
        from t_exchange_rate
        where  curr_date = (select max(curr_date) from t_exchange_rate te left join t_dec_erp_i_list_n n on te.curr_code_num = n.curr AND n.TRADE_CODE = #{tradeCode})) r
        on t.curr = r.curr_code_num
        group by t.insert_time, t.trade_code) t
        <where>
            and t.TRADE_CODE = #{tradeCode}
        </where>
        group by to_char(t.insert_time,'yyyy-mm') order by to_char(t.insert_time,'yyyy-mm')
    </select>
    <select id="getITotalList" resultType="com.dcjet.cs.dto.base.MonthValue" databaseId="postgresql">
        select to_char(insert_time, 'yyyy-mm') as MONTH, sum(DEC_TOTAL) as VALUE
        from (select t.insert_time, t.trade_code, round(sum(coalesce(t.dec_total,1) * coalesce(r.curr_price_usd,1)), 5) as DEC_TOTAL
        from t_dec_erp_i_list_n t
        left join (select *
        from t_exchange_rate
        where  curr_date = (select max(curr_date) from t_exchange_rate te left join t_dec_erp_i_list_n n on te.curr_code_num = n.curr AND n.TRADE_CODE = #{tradeCode})) r
        on t.curr = r.curr_code_num
        group by t.insert_time, t.trade_code) t
        <where>
            and t.TRADE_CODE = #{tradeCode}
        </where>
        group by to_char(t.insert_time,'yyyy-mm') order by to_char(t.insert_time,'yyyy-mm')
    </select>
    <select id="getETotalList" resultType="com.dcjet.cs.dto.base.MonthValue">
        select to_char(insert_time, 'yyyy-mm') as MONTH, sum(DEC_TOTAL) as VALUE
        from (select t.insert_time, t.trade_code, round(sum(nvl(t.dec_total,1) * nvl(r.curr_price_usd,1)), 5) as DEC_TOTAL
        from t_dec_erp_e_list_n t
        left join (select *
        from t_exchange_rate
        where  curr_date = (select max(curr_date) from t_exchange_rate te left join t_dec_erp_e_list_n n on te.curr_code_num = n.curr AND n.TRADE_CODE = #{tradeCode})) r
        on t.curr = r.curr_code_num
        group by t.insert_time, t.trade_code) t
        <where>
            and t.TRADE_CODE = #{tradeCode}
        </where>
        group by to_char(t.insert_time,'yyyy-mm') order by to_char(t.insert_time,'yyyy-mm')
    </select>
    <select id="getETotalList" resultType="com.dcjet.cs.dto.base.MonthValue" databaseId="postgresql">
        select to_char(insert_time, 'yyyy-mm') as MONTH, sum(DEC_TOTAL) as VALUE
        from (select t.insert_time, t.trade_code, round(sum(coalesce(t.dec_total,1) * coalesce(r.curr_price_usd,1)), 5) as DEC_TOTAL
        from t_dec_erp_e_list_n t
        left join (select *
        from t_exchange_rate
        where  curr_date = (select max(curr_date) from t_exchange_rate te left join t_dec_erp_e_list_n n on te.curr_code_num = n.curr AND n.TRADE_CODE = #{tradeCode})) r
        on t.curr = r.curr_code_num
        group by t.insert_time, t.trade_code) t
        <where>
            and t.TRADE_CODE = #{tradeCode}
        </where>
        group by to_char(t.insert_time,'yyyy-mm') order by to_char(t.insert_time,'yyyy-mm')
    </select>

    <select id="getLogisticsI" resultType="com.dcjet.cs.dto.base.LogisticsIDto">
        select *
        from (select distinct track.trade_code,
                              coalesce(head.decIHeadNum,0) as decIHeadNum,
                              coalesce(d.unshipped,0) as unshipped,
                              coalesce(l.shipNum,0) as shipNum,
                              coalesce(r.reachNum,0) as reachNum,
                              coalesce(dec.transferCustomsNum,0) as transferCustomsNum,
                              coalesce(a.arrivalNum,0) as arrivalNum
              from t_dec_i_logistics_track track
                       left join (select trade_code, count(*) as decIHeadNum
                                  from t_dec_erp_i_head_n
                                  where trade_code = #{tradeCode}
                                    and appr_status = '8'
                                    <if test='_databaseId == "postgresql" '>
                                        <if test="apprDateFrom != null and apprDateFrom != ''">
                                            <![CDATA[ and APPR_DATE >= to_timestamp(#{apprDateFrom},'yyyy-MM-dd hh24:mi:ss')]]>
                                        </if>
                                        <if test="apprDateTo != null and apprDateTo != ''">
                                            <![CDATA[ and APPR_DATE < to_timestamp(#{apprDateTo},'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day']]>
                                        </if>
                                    </if>
                                    <if test='_databaseId != "postgresql" '>
                                        <if test="apprDateFrom != null and apprDateFrom != ''">
                                            <![CDATA[ and APPR_DATE >= TO_DATE(#{apprDateFrom},'yyyy-MM-dd hh24:mi:ss')]]>
                                        </if>
                                        <if test="apprDateTo != null and apprDateTo != ''">
                                            <![CDATA[ and APPR_DATE < TO_DATE(#{apprDateTo},'yyyy-MM-dd hh24:mi:ss')+1]]>
                                        </if>
                                    </if>
                                  group by trade_code) head on track.trade_code = head.trade_code
                       left join (select trade_code, count(*) as unshipped
                                  from t_dec_i_logistics_track
                                  where trade_code = #{tradeCode}
                                    and ship_date is null
                                  group by trade_code) d on track.trade_code = d.trade_code
                       left join (select trade_code, count(*) as shipNum
                                  from t_dec_i_logistics_track
                                  where trade_code = #{tradeCode}
                                    and ship_date is not null
                                    and arrival_Port_Date is null
                                  group by trade_code) l on track.trade_code = l.trade_code
                       left join (select trade_code, count(*) as reachNum
                                  from t_dec_i_logistics_track
                                  where trade_code = #{tradeCode}
                                    and arrival_Port_Date is not null
                                    and reach_date is null
                                  group by trade_code) r on track.trade_code = r.trade_code
                       left join (select trade_code, count(*) as transferCustomsNum
                                  from t_dec_i_logistics_track
                                  where trade_code = #{tradeCode}
                                    and reach_date is not null
                                    and arrival_date is null
                                  group by trade_code) dec on track.trade_code = dec.trade_code
                  left join (select trade_code, count(*) as arrivalNum
                  from t_dec_i_logistics_track
                  where trade_code = #{tradeCode}
                  and arrival_date is not null
                  group by trade_code) a on track.trade_code = track.trade_code
              where track.trade_code = #{tradeCode}) t
    </select>

    <select id="getLogisticsE" resultType="com.dcjet.cs.dto.base.LogisticsEDto">
        select *
        from (select distinct track.trade_code,
                              coalesce(head.decEHeadNum,0) as decEHeadNum,
                              coalesce(d.unassigned,0) as unassigned,
                              coalesce(l.declaredNum,0) as declaredNum,
                              coalesce(r.dutyNum,0) as dutyNum,
                              coalesce(dec.passNum,0) as passNum,
                              coalesce(a.releasedNum,0) as releasedNum
              from V_DEC_E_CUSTOMS_TRACK track
                       left join (select trade_code, count(*) as decEHeadNum
                                  from t_dec_erp_e_head_n
                                  where trade_code = #{tradeCode}
                                    and appr_status = '8'
                                    <if test='_databaseId == "postgresql" '>
                                        <if test="apprDateFrom != null and apprDateFrom != ''">
                                            <![CDATA[ and APPR_DATE >= to_timestamp(#{apprDateFrom},'yyyy-MM-dd hh24:mi:ss')]]>
                                        </if>
                                        <if test="apprDateTo != null and apprDateTo != ''">
                                            <![CDATA[ and APPR_DATE < to_timestamp(#{apprDateTo},'yyyy-MM-dd hh24:mi:ss') + INTERVAL '1 day']]>
                                        </if>
                                    </if>
                                    <if test='_databaseId != "postgresql" '>
                                        <if test="apprDateFrom != null and apprDateFrom != ''">
                                            <![CDATA[ and APPR_DATE >= TO_DATE(#{apprDateFrom},'yyyy-MM-dd hh24:mi:ss')]]>
                                        </if>
                                        <if test="apprDateTo != null and apprDateTo != ''">
                                            <![CDATA[ and APPR_DATE < TO_DATE(#{apprDateTo},'yyyy-MM-dd hh24:mi:ss')+1]]>
                                        </if>
                                    </if>
                                  group by trade_code) head on track.trade_code = head.trade_code
                       left join (select trade_code, count(*) as unassigned
                                  from V_DEC_E_CUSTOMS_TRACK
                                  where trade_code = #{tradeCode}
                                    and entrust_date is null
                                  group by trade_code) d on track.trade_code = track.trade_code
                       left join (select trade_code, count(*) as declaredNum
                                  from V_DEC_E_CUSTOMS_TRACK
                                  where trade_code = #{tradeCode}
                                    and entrust_date is not null
                                    and declare_date is null
                                  group by trade_code) l on track.trade_code = l.trade_code
                       left join (select trade_code, count(*) as dutyNum
                                  from V_DEC_E_CUSTOMS_TRACK
                                  where trade_code = #{tradeCode}
                                    and declare_date is not null
                                    and duty_date is null
                                  group by trade_code) r on track.trade_code = r.trade_code
                       left join (select trade_code, count(*) as passNum
                                  from V_DEC_E_CUSTOMS_TRACK
                                  where trade_code = #{tradeCode}
                                    and duty_date is not null
                                    and pass_date is null
                                  group by trade_code) dec on track.trade_code = dec.trade_code
                  left join (select trade_code, count(*) as releasedNum
                  from V_DEC_E_CUSTOMS_TRACK
                  where trade_code = #{tradeCode}
                  and pass_date is not null
                  group by trade_code) a on track.trade_code = a.trade_code
              where track.trade_code = #{tradeCode}) t
    </select>

    <select id="getAuditInfo" resultType="com.dcjet.cs.dto.base.AuditInfoDto">
        select distinct A.trade_code,
                        coalesce(fac.facGNoNum,0) as facGNoNum,
                        coalesce(cop.copGNoNum,0) as copGNoNum,
                        coalesce(EMS.emsNum,0) as emsNum,
                        coalesce(I.erpINum,0) as erpINum,
                        coalesce(E.erpENum,0) as erpENum,
                        coalesce(dev.devENum,0) as devENum
        from T_AEO_AUDIT_INFO A
                 left join (select trade_code, count(*) as facGNoNum from t_mat_imgexg where insert_user != #{insertUser} and appr_status = '2' and trade_code = #{tradeCode} group by trade_code) fac on A.trade_code = fac.trade_code
                 left join (select trade_code, count(*) as copGNoNum from t_mat_imgexg_org where insert_user != #{insertUser} and appr_status = '2' and trade_code = #{tradeCode} group by trade_code) cop on A.trade_code = cop.trade_code
                 left join (select trade_code, count(*) as emsNum from T_GWSTD_EMS_HEAD where insert_user != #{insertUser} and appr_status = '2' and trade_code = #{tradeCode} group by trade_code) ems on A.trade_code = EMS.trade_code
                 left join (SELECT trade_code,count(*) as erpINum FROM T_DEC_ERP_I_HEAD_N WHERE insert_user != #{insertUser} and appr_status= '2' and TRADE_CODE = #{tradeCode} group by trade_code) I on A.trade_code = I.trade_code
                 left join (SELECT trade_code,count(*) as erpENum FROM T_DEC_ERP_E_HEAD_N WHERE insert_user != #{insertUser} and appr_status= '2' and TRADE_CODE = #{tradeCode} group by trade_code) E on A.trade_code = E.trade_code
                 left join (SELECT trade_code,count(*) as devENum FROM T_DEV_FREE_APPLY_HEAD WHERE insert_user != #{insertUser} and appr_status= '2' and TRADE_CODE = #{tradeCode} group by trade_code) dev on A.trade_code = dev.trade_code
        where A.trade_code = #{tradeCode} and A.insert_user = #{insertUser}
    </select>
    <select id="getWarring" resultType="com.dcjet.cs.dto.base.WarringDto">
        select *
        from (select distinct w.trade_code,
                              coalesce(wr.recoreNum, 0)     as recoreNum,
                              coalesce(tt.cardNum, 0)       as cardNum,
                              coalesce(wb.bailNum, 0)       as bailNum,
                              coalesce(we.expireNum, 0)     as expireNum,
                              coalesce(ws.equipNum, 0)      as equipNum,
                              coalesce(wer.exportNum, 0)    as exportNum,
                              coalesce(wrr.rerairtNum, 0)   as rerairtNum,
                              coalesce(h.recordNum, 0)      as recordNum,
                              coalesce(hr.recordHeadNum, 0) as recordHeadNum
              from T_WARRING_RECORD w
                       left join (SELECT distinct rr.trade_code, count(*) as recoreNum
                                  FROM T_WARRING_RECORD rr
                                           JOIN T_DEC_ERP_I_HEAD_N t ON rr.business_id = t.sid
                                  WHERE rr.WARRING_TYPE = 'OVERDUE' and t.arrival_port_date is not null and t.appr_status != '8' AND rr.trade_code = #{tradeCode}
                                  group by rr.trade_code) wr on w.trade_code = wr.trade_code
                       left join (SELECT distinct r.trade_code, count(*) as cardNum
                                  FROM (SELECT t.trade_code,
                                               CASE
                                                   WHEN T.STATUS = '1' THEN '3'
                                                   WHEN ceil(F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP())) &lt; 0 THEN '2'
                                                   WHEN ceil(F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP())) - F_XDO_NVL(F_XDO_NVL(t.WARRING_DAYS, s.WARRING_DAYS), '0') &lt; 0 THEN '1'
                                                   ELSE '0' END AS DATA_STATUS
                                        FROM T_WARRING_CARD t
                                                 LEFT JOIN T_WARRING_SET s ON s.WARRING_TYPE = 'CARD' AND t.TRADE_CODE = s.TRADE_CODE
                                        WHERE t.TRADE_CODE = #{tradeCode}) R
                                  WHERE R.DATA_STATUS in ('1', '2')
                                  group by r.trade_code) tt on w.trade_code = tt.trade_code
                       left join (SELECT distinct r.trade_code, count(*) as bailNum
                                  FROM (SELECT t.trade_code,
                                               CASE
                                                   WHEN T.STATUS = '1' THEN '3'
                                                   WHEN ceil(F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP())) &lt; 0 THEN '2'
                                                   ELSE '0' END AS DATA_STATUS
                                        FROM t_warring_bail t
                                        WHERE t.TRADE_CODE = #{tradeCode}) R
                                  WHERE R.DATA_STATUS = '2'
                                  group by r.trade_code) wb on w.trade_code = wb.trade_code
                       left join (SELECT distinct r.trade_code, count(*) as expireNum
                                  FROM (SELECT t.trade_code,
                                               CASE
                                                   WHEN T.STATUS = '1' THEN '3'
                                                   WHEN ceil(F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP())) &lt; 0 THEN '2'
                                                   ELSE '0' END AS DATA_STATUS
                                        FROM T_WARRING_EXPIRE t
                                        WHERE t.TRADE_CODE = #{tradeCode}) R
                                  WHERE R.DATA_STATUS = '2'
                                  group by r.trade_code) we on w.trade_code = we.trade_code
                       left join (SELECT distinct r.trade_code, count(*) as equipNum
                                  FROM (SELECT t.trade_code,
                                               CASE
                                                   WHEN T.STATUS = '1' THEN '3'
                                                   WHEN ceil(F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP())) &lt; 0 THEN '2'
                                                   ELSE '0' END AS DATA_STATUS
                                        FROM T_WARRING_EQUIP t
                                        WHERE t.TRADE_CODE = #{tradeCode}) R
                                  WHERE R.DATA_STATUS = '2'
                                  group by r.trade_code) ws on w.trade_code = ws.trade_code
                       left join (SELECT distinct r.trade_code, count(*) as exportNum
                                  FROM (SELECT t.trade_code,
                                               CASE
                                                   WHEN T.STATUS = '1' THEN '3'
                                                   WHEN ceil(F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP())) &lt; 0 THEN '2'
                                                   ELSE '0' END AS DATA_STATUS
                                        FROM T_WARRING_EXPORT t
                                        WHERE t.TRADE_CODE = #{tradeCode}) R
                                  WHERE R.DATA_STATUS = '2'
                                  group by r.trade_code) wer on w.trade_code = wer.trade_code
                       left join (SELECT distinct r.trade_code, count(*) as rerairtNum
                                  FROM (SELECT t.trade_code,
                                               CASE
                                                   WHEN T.STATUS = '1' THEN '3'
                                                   WHEN ceil(F_XDO_DATE_DIFF(t.DATE_END, F_XDO_CURRENT_TIMESTAMP())) &lt; 0 THEN '2'
                                                   ELSE '0' END AS DATA_STATUS
                                        FROM T_WARRING_REPAIR t
                                        WHERE t.TRADE_CODE = #{tradeCode}) R
                                  WHERE R.DATA_STATUS = '2'
                                  group by r.trade_code) wrr on w.trade_code = wrr.trade_code
                       left join (select distinct t.trade_code, count(*) as recordNum
                                  from (select r.TRADE_CODE
                                        from T_WARRING_RECORD r
                                                 join T_DEC_ERP_I_HEAD_N l on l.SID = r.BUSINESS_ID
                                        where r.IE_MARK = 'I' and r.TRADE_CODE = #{tradeCode} and r.WARRING_TYPE = 'DECLARE_CUSTOMS'
                                        union all
                                        select r.TRADE_CODE
                                        from T_WARRING_RECORD r
                                                 join T_DEC_ERP_E_HEAD_N l on l.sid = r.BUSINESS_ID
                                        where r.IE_MARK = 'E' and r.TRADE_CODE = #{tradeCode} and r.WARRING_TYPE = 'DECLARE_CUSTOMS') t
                                  group by t.trade_code) h on w.trade_code = h.trade_code
                       left join (SELECT distinct t.trade_code, count(*) as recordHeadNum
                                  FROM (SELECT r.trade_code
                                        FROM T_WARRING_RECORD r
                                                 JOIN T_DEC_ERP_I_HEAD_N h ON r.business_id = h.sid
                                                 JOIN (SELECT b.HEAD_ID, max(c.ARRIVAL_PORT_DATE) ARRIVAL_PORT_DATE
                                                       FROM T_DEC_I_BILL_HEAD b
                                                                JOIN T_DEC_I_CUSTOMS_TRACK c ON b.SID = c.HEAD_ID
                                                       WHERE c.ARRIVAL_PORT_DATE IS NOT NULL AND c.DELIVERY_DATE IS NULL
                                                       GROUP BY b.HEAD_ID) s ON s.HEAD_ID = h.SID
                                        WHERE r.WARRING_TYPE = 'ARRIVAL_OVERDUE' AND r.TRADE_CODE = #{tradeCode}) t
                                  group by t.trade_code) hr on w.trade_code = hr.trade_code) tw
        where tw.TRADE_CODE = #{tradeCode}
    </select>
</mapper>
