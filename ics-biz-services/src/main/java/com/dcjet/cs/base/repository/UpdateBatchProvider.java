package com.dcjet.cs.base.repository;

import org.apache.ibatis.mapping.MappedStatement;
import tk.mybatis.mapper.entity.EntityColumn;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;
import tk.mybatis.mapper.mapperhelper.MapperTemplate;
import tk.mybatis.mapper.mapperhelper.SqlHelper;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class UpdateBatchProvider extends MapperTemplate {
    private static final String POSTGRE_SQL = "postgresql";

    public UpdateBatchProvider(Class<?> mapperClass, MapperHelper mapperHelper) {
        super(mapperClass, mapperHelper);
    }


    /**
     * 通过主键更新全部字段
     *
     * @param ms
     */
    public String updateListByPrimaryKey(MappedStatement ms) {
        final Class<?> entityClass = getEntityClass(ms);
        //开始拼sql
        StringBuilder sql = new StringBuilder();
        sql.append(SqlHelper.updateTable(entityClass, tableName(entityClass)));
        sql.append("<trim prefix=\"set\" suffixOverrides=\",\">");

        //获取全部列
        Set<EntityColumn> columnList = EntityHelper.getColumns(entityClass);
        for (EntityColumn column : columnList) {
            if (!column.isId() && column.isUpdatable()) {
                sql.append("  <trim prefix=\""+column.getColumn()+" =case\" suffix=\"end,\">");
                sql.append("    <foreach collection=\"list\" item=\"record\" index=\"index\">");

                if (Objects.equals(column.getJavaType(), Date.class) && POSTGRE_SQL.equalsIgnoreCase(ms.getConfiguration().getDatabaseId())) {
                    sql.append("       when SID=#{record.sid} then #{record."+column.getEntityField().getName()+"}::timestamp(0)");
                } else {
                    sql.append("       when SID=#{record.sid} then #{record."+column.getEntityField().getName()+"}");
                }

                if (Objects.equals(column.getJavaType(), BigDecimal.class) && POSTGRE_SQL.equalsIgnoreCase(ms.getConfiguration().getDatabaseId())) {
                    sql.append("       when SID=#{record.sid} then #{record."+column.getEntityField().getName()+"}::numeric");
                } else {
                    sql.append("       when SID=#{record.sid} then #{record."+column.getEntityField().getName()+"}");
                }

                if (Objects.equals(column.getJavaType(), Integer.class) && POSTGRE_SQL.equalsIgnoreCase(ms.getConfiguration().getDatabaseId())) {
                    sql.append("       when SID=#{record.sid} then #{record."+column.getEntityField().getName()+"}::numeric");
                } else {
                    sql.append("       when SID=#{record.sid} then #{record."+column.getEntityField().getName()+"}");
                }

                if (Objects.equals(column.getJavaType(), Long.class) && POSTGRE_SQL.equalsIgnoreCase(ms.getConfiguration().getDatabaseId())) {
                    sql.append("       when SID=#{record.sid} then #{record."+column.getEntityField().getName()+"}::numeric");
                } else {
                    sql.append("       when SID=#{record.sid} then #{record."+column.getEntityField().getName()+"}");
                }

                sql.append("    </foreach>");
                sql.append("  </trim>");
            }
        }

        sql.append("</trim>");
        sql.append("WHERE");
        sql.append(" SID IN ");
        sql.append("<trim prefix=\"(\" suffix=\")\">");
        sql.append("<foreach collection=\"list\" separator=\", \" item=\"record\" index=\"index\" >");
        sql.append("#{record.sid}");
        sql.append("</foreach>");
        sql.append("</trim>");

        return sql.toString();
    }
}
