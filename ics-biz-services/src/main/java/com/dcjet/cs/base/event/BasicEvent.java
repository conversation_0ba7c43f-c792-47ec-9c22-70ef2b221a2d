package com.dcjet.cs.base.event;

import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 */
@Setter
@Getter
public class BasicEvent extends ApplicationEvent {

    private UserInfoToken token;

    private CRUDEnum crud;

    public BasicEvent(Object source) {
        super(source);
    }

    public BasicEvent(Object source, UserInfoToken token) {
        super(source);
        this.token = token;
    }

    public BasicEvent(CRUDEnum curd, UserInfoToken token) {
        super(token);
        this.crud = curd;
        this.token = token;
    }

    public void validation() {
        if (this.token == null) {
            throw new ErrorException(400, xdoi18n.XdoI18nUtil.t("调用的用户信息为空"));
        }
    }

}
