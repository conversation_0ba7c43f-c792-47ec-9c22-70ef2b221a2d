package com.dcjet.cs.base.mapper;

import com.dcjet.cs.base.model.BasicModel;
import com.dcjet.cs.dto.base.BasicDto;
import com.dcjet.cs.dto.base.BasicParam;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueMappingStrategy;

/***
 *
 * <AUTHOR>
 * @param <E>   model
 * @param <D>   dto
 * @param <P>   param
 */
public interface BasicConverter<E extends BasicModel, D extends BasicDto, P extends BasicParam, S extends BasicParam> {

    /***
     *
     * @param param
     * @return
     */
    @BeanMapping(nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
    E fromSearchParam(S param);

    /***
     * dto to entity
     * @param param
     * @return
     */
    E toPo(P param);

    /***
     * entity to dto
     * @param po
     * @return
     */
    D toDto(E po);

    /***
     * 更新PO
     * @param param
     * @param po
     */
    @Mapping(target = "tradeCode", ignore = true)
    void updatePo(P param, @MappingTarget E po);
}
