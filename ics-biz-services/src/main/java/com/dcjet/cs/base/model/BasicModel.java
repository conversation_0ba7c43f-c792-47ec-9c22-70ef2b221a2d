package com.dcjet.cs.base.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xdo.common.token.UserInfoToken;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.*;

/**
 * <AUTHOR>
 */
@Setter @Getter
public class BasicModel implements Serializable {

    /**
     * 唯一键
     */
    @Id
    @Column(name = "SID")
    private  String sid;


    /**
     * 企业编码
     */
    @Column(name = "TRADE_CODE")
    private String tradeCode;

    /**
     * 创建人
     */
    @Column(name = "INSERT_USER")
    private  String insertUser;
    /**
     * 创建人
     */
    @Column(name = "INSERT_USER_NAME")
    private  String insertUserName;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @Column(name = "INSERT_TIME")
    private Date insertTime;

    /**
     * 更新人
     */
    @Column(name = "UPDATE_USER")
    private  String updateUser;
    /**
     * 更新人
     */
    @Column(name = "UPDATE_USER_NAME")
    private  String updateUserName;
    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @Column(name = "UPDATE_TIME")
    private  Date updateTime;

    /**
     * 提单录入日期-开始
     */
    @Transient
    private String insertTimeFrom;
    /**
     * 提单录入日期-结束
     */
    @Transient
    private String insertTimeTo;

    /**
     * 排序
     */
    @Transient
    private String sort;


    /***
     * 设置通用的新增字段
     *
     * @param token
     */
    public void preInsert(UserInfoToken token) {
        this.setSid(UUID.randomUUID().toString());
        this.setInsertTime(new Date());
        if (token != null) {
            this.setTradeCode(token.getCompany());
            this.setInsertUser(token.getUserNo());
            this.setInsertUserName(token.getUserName());
        }

    }

    /***
     * 设置通用的新增字段
     *
     * @param token
     */
    public void preInsert(UserInfoToken token,String newTradeCode) {
        this.setSid(UUID.randomUUID().toString());
        this.setInsertTime(new Date());
        if (token != null) {
            this.setTradeCode(token.getCompany());
            this.setInsertUser(token.getUserNo());
            this.setInsertUserName(token.getUserName());
        }
        if(StringUtils.isNotBlank(newTradeCode))
        {
            this.setTradeCode(newTradeCode);
        }
    }

    /***
     * 设置通用的更新字段
     *
     * @param token
     */
    public void preUpdate(UserInfoToken token) {
        this.setUpdateTime(new Date());
        if (token != null) {
            this.setUpdateUser(token.getUserNo());
            this.setUpdateUserName(token.getUserName());
        }

    }

    public List<String> preUpdateProperties() {
        return new ArrayList<>(Arrays.asList("updateTime", "updateUser", "updateUserName"));
    }

    /**
     * 代理权限类型(Y：同时拥有[预录入制单]与[提单货运代理]权限)
     */
    @Transient
    private String agentAuthType = "N";

    /**
     * 代理申报单位编码
     */
    @Transient
    private String agentDeclareCode;
    /**
     * 代理货代编码
     */
    @Transient
    private  String agentForwardCode;

    @Transient
    private String operateUser;
    public String getOperateUser() {
        return (this.insertUser==null?"":this.insertUser)+ (this.insertUserName==null?"":"("+this.insertUserName+")");
    }
}
