package com.dcjet.cs.base.dao;

import com.dcjet.cs.dto.base.HomePageDto;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
* generated by Generate 神码
* BiShipto
* <AUTHOR>
* @date: 2019-7-26
*/
public interface HomePageMapper extends Mapper<HomePageDto> {
    /**
     * 查询获取数据
     * @param tradeCode
     * @param userNo
     * @return
     */
    List<HomePageDto> getAuditList(@Param("tradeCode") String tradeCode, @Param("userNo") String userNo);
    /**
     * 查询获取数据
     * @param type
     * @param tradeCode
     * @param userNo
     * @return
     */
    List<HomePageDto> getAuditListForType(@Param("type") List<String> type,@Param("tradeCode") String tradeCode, @Param("userNo") String userNo);
    /**
     * 查询获取数据
     * @param tradeCode
     * @return
     */
    List<HomePageDto> getWarringList(@Param("tradeCode") String tradeCode);

    /**
     *
     * @param type
     * @param tradeCode
     * @return
     */
    List<HomePageDto> getWarringListForType(@Param("type") List<String> type,@Param("tradeCode") String tradeCode);
}
