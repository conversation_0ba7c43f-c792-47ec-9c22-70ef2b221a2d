package com.dcjet.cs.base.service;


import com.dcjet.cs.common.model.ImportData;
import com.dcjet.cs.common.model.ImportValidation;
import com.dcjet.cs.dto.base.BasicImportParam;
import com.dcjet.cs.dto.common.ExcelImportParam;
import com.xdo.common.token.UserInfoToken;

/***
 *
 *
 * <AUTHOR>
 * @param <T>
 */
public interface ImportService<T extends BasicImportParam> {

    /***
     * 导入验证
     *
     * @param importData
     * @param param
     * @param token
     * @return
     */
    default ImportValidation<T> importValidation(ImportData<T> importData, ExcelImportParam param, UserInfoToken token) {
        return new ImportValidation<>(null, null);
    }

    /***
     * 保存正确的数据
     *
     * @param token
     * @return
     */
    default int importSave(ImportData<T> data, UserInfoToken token) {
        return 0;
    }
}
