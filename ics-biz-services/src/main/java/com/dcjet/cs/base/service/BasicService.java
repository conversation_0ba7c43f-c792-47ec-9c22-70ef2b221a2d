package com.dcjet.cs.base.service;

import com.dcjet.cs.base.model.BasicModel;
import com.dcjet.cs.dto.base.BasicDto;
import com.dcjet.cs.dto.base.BasicParam;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface BasicService<E extends BasicModel, D extends BasicDto, P extends BasicParam, S extends BasicParam> {

    /***
     *
     * @param searchParam
     * @param pageParam
     * @param token
     * @return
     */
    ResultObject<List<D>> findByPage(S searchParam, PageParam pageParam, UserInfoToken token);


    /***
     *
     * @param searchParam
     * @param token
     * @return
     */
    List<D> getAll(S searchParam, PageParam pageParam, UserInfoToken token);

    /***
     * 获取单个参数
     * @param sid
     * @return
     */
    D get(String sid, UserInfoToken token);


    /***
     * 新增
     * @param param
     * @param userInfo
     * @return
     */
    D insert(P param, UserInfoToken userInfo);


    /***
     *
     * @param insertList
     * @param token  null not preInsert
     * @return
     */
    int batchInsert(List<E> insertList, UserInfoToken token);


    /***
     *
     * @param sid
     * @param param
     * @param userInfo
     * @return
     */
    D update(String sid, P param, UserInfoToken userInfo);

    /***
     *
     * @param sid
     * @param columnList
     * @param param
     * @param token
     * @return
     */
    D update(String sid, P param, List<String> columnList, UserInfoToken token);


    /***
     *
     * @param updateList
     * @param token null not preUpdate
     * @return
     */
    int batchUpdate(List<E> updateList, UserInfoToken token);


    /***
     *
     * @param sid
     * @param userInfo
     */
    void deleteById(String sid, UserInfoToken userInfo);

    /**
     *
     * @param idList
     * @param userInfo
     */
    void deleteBatchIds(List<String> idList, UserInfoToken userInfo);

    /***
     * 根据查询条件删除
     *
     * @param searchParam
     * @param userInfoToken
     */
    void deleteBySearchParam(S searchParam, UserInfoToken userInfoToken);

}
