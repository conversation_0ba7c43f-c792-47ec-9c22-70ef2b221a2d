package com.dcjet.cs.baseInfoCustomerParams.model;

import com.dcjet.cs.base.model.BasicModel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2019-4-18
 */
@Setter
@Getter
@Table(name = "T_BIZ_CUSTOMS_PARAMS")
public class BaseInfoCustomerParams extends BasicModel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 常用标志（业务类型
     */
    @Column(name = "BUSINESS_TYPE")
    private  String businessType;

    @Transient
    private List<String> businessTypeList;

    /**
     * 自定义参数类型
     */
    @Column(name = "PARAMS_TYPE")
    private  String paramsType;
    /**
     * 自定义参数编码
     */
    @Column(name = "PARAMS_CODE")
    private  String paramsCode;
    /**
     * 自定义参数名称
     */
    @Column(name = "PARAMS_NAME")
    private  String paramsName;

    /**
     * 对应海关参数编码
     */
    @Column(name = "CUSTOM_PARAM_CODE")
    private  String customParamCode;
    /**
     * 对应海关参数名称
     */
    @Column(name = "CUSTOM_PARAM_NAME")
    private  String customParamName;

    /**
     * 拓展备用字段
     */
    @Column(name = "EXTEND1")
    private  String extend1;


    /**
     * 拓展备用字段
     */
    @Column(name = "EXTEND2")
    private  String extend2;

    /**
     * 拓展备用字段
     */
    @Column(name = "EXTEND3")
    private  String extend3;

    /**
     * 拓展备用字段
     */
    @Column(name = "EXTEND4")
    private  String extend4;

    /**
     * 拓展备用字段
     */
    @Column(name = "EXTEND5")
    private  String extend5;

    /**
     * 拓展备用字段
     */
    @Column(name = "EXTEND6")
    private  String extend6;

    /**
     * 拓展备用字段
     */
    @Column(name = "EXTEND7")
    private  String extend7;

    /**
     * 拓展备用字段
     */
    @Column(name = "EXTEND8")
    private  String extend8;

    /**
     * 拓展备用字段
     */
    @Column(name = "EXTEND9")
    private  String extend9;


    /**
     * 拓展备用字段
     */
    @Column(name = "EXTEND10")
    private  String extend10;

    @Column(name = "unit_yonyou")
    private String unitYonyou;

    //用友代码
    @Column(name = "yy_code")
    private String yyCode;

}
