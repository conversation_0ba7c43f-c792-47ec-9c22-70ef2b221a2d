package com.dcjet.cs.baseInfoCustomerParams.mapper;

import com.dcjet.cs.baseInfoCustomerParams.model.BaseInfoCustomerParams;
import com.dcjet.cs.bi.model.BiCustomerParams;
import com.dcjet.cs.dto.baseInfoCustomerParams.BaseInfoCustomerParamsDto;
import com.dcjet.cs.dto.baseInfoCustomerParams.BaseInfoCustomerParamsParam;
import com.dcjet.cs.dto.bi.BiCustomerParamsDto;
import com.dcjet.cs.dto.bi.BiCustomerParamsParam;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * generated by Generate 神码
 *
 * <AUTHOR>
 * @date: 2019-6-6
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BaseInfoCustomerParamsDtoMapper {
    /***
     * 转换数据库对象到DTO
     * @param po
     * @return
     */
    BaseInfoCustomerParamsDto toDto(BaseInfoCustomerParams po);
    /***
     * 转换DTO到数据库对象
     * @param param
     * @return
     */
    BaseInfoCustomerParams toPo(BaseInfoCustomerParamsParam param);
    /**
     * 数据库原始数据更新
     * @param biCustomerParamsParam
     * @param biCustomerParams
     */
    default void patchPo(BaseInfoCustomerParamsParam biCustomerParamsParam, BaseInfoCustomerParams biCustomerParams) {
        // TODO 自行实现局部更新
    }

    /**
     * 数据库原始数据更新
     * @param baseInfoCustomerParamsParamParam
     * @param baseInfoCustomerParams
     */
    void updatePo(BaseInfoCustomerParamsParam baseInfoCustomerParamsParamParam, @MappingTarget BaseInfoCustomerParams baseInfoCustomerParams);

}
