package com.dcjet.cs.baseInfoCustomerParams.dao;

import com.dcjet.cs.baseInfoCustomerParams.model.BaseInfoCustomerParams;
import com.dcjet.cs.bi.model.BizMerchant;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

/**
 * generated by Generate 神码
 * BiBillto
 *
 * <AUTHOR>
 * @date: 2021-4-21
 */
public interface BaseInfoCustomerParamsMapper extends Mapper<BaseInfoCustomerParams> {
    /**
     * 查询获取数据
     *
     * @param params
     * @return
     */
    List<BaseInfoCustomerParams> getList(BaseInfoCustomerParams params);

    /**
     * 批量删除
     *
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);

    /**
     *
     * @param param
     * @return
     */

    Integer getParamsCode(Map<String,Object> param);

    List<BaseInfoCustomerParams> getParamsSelectByType(@Param("paramsType") String paramsType,@Param("businessType") String businessType, @Param("tradeCode") String tradeCode);
}
