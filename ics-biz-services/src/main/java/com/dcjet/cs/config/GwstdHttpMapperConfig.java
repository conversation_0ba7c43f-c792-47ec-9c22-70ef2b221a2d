package com.dcjet.cs.config;//package com.dcjet.cs.config;
//
//import com.dcjet.cs.common.model.GwstdHttpConfig;
//import com.dcjet.cs.common.service.CommonService;
//import com.github.benmanes.caffeine.cache.Cache;
//import com.github.benmanes.caffeine.cache.Caffeine;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.concurrent.TimeUnit;
//
///**
// * 启动时配置表中的地址信息缓存到本地cache中
// */
//@Component
//public class GwstdHttpMapperConfig {
//
//    @Resource
//    private CommonService commonService;
//
////    private Cache<String, GwstdHttpConfig> httpConfigCache;
//    private Cache<String, GwstdHttpConfig> httpConfigCache =
//            Caffeine.newBuilder().expireAfterWrite(10, TimeUnit.DAYS).build();
//
//    public Cache<String, GwstdHttpConfig> getHttpConfigCache() {
//        return httpConfigCache;
//    }
//
//    public void setHttpConfigCache(Cache<String, GwstdHttpConfig> httpConfigCache) {
//        this.httpConfigCache = httpConfigCache;
//    }
//
////    @PostConstruct
////    public void init() {
////        if (httpConfigCache == null) {
////            // 缓存
////            httpConfigCache = Caffeine.newBuilder()
////                    .expireAfterWrite(10, TimeUnit.DAYS)
////                    .build();
////        }
////
////        commonService.cacheHttpConfig(httpConfigCache);
////    }
//}
