package com.dcjet.cs.config;

import com.dcjet.cs.util.CustomDateDeserializer;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.xdo.common.json.JacksonObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Date;

@Component
public class JacksonObjectMapperConfig {

    @Autowired
    private JacksonObjectMapper jacksonObjectMapper;

    @PostConstruct
    public void init() {
        SimpleModule dateModule = new SimpleModule("dateModule");
        dateModule.addDeserializer(Date.class, new CustomDateDeserializer(Date.class));
        jacksonObjectMapper.registerModule(dateModule);
    }
}