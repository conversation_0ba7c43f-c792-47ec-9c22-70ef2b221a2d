package com.dcjet.cs.config;
/**
 * @program: mavenEasyUIDemo
 * @description:
 * <AUTHOR>
 * @date: 2019-4-17
 **/

import com.dcjet.cs.base.repository.BasicMapper;
import com.github.pagehelper.PageInterceptor;
import com.xdo.audit.sql.SqlStatementInterceptor;
import com.xdo.common.mybatis.util.SqlMapper;
import com.xdo.mybatis.XdoMyBatisConfig;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import tk.mybatis.spring.mapper.MapperScannerConfigurer;

import javax.sql.DataSource;
import java.util.Properties;
@Configuration
public class MyBatisMapperScannerConfig {
    /***
     *
     *
     *             sql审计 新增，修改，删除
     *             SqlAuditLogInterceptor sqlAuditLogInterceptor = new SqlAuditLogInterceptor();
     *             Properties properties = new Properties();
     *             path 配置记录的数据存放在磁盘的位置
     *             properties.setProperty("path", "audit/sql");
     *             sqlAuditLogInterceptor.setProperties(properties);
     *
     * @param dataSource
     * @return
     */
    @Bean(name = "sqlSessionFactory")
    public SqlSessionFactory sqlSessionFactoryBean(DataSource dataSource, SqlStatementInterceptor sqlStatementInterceptor) {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setTypeAliasesPackage("com.dcjet.cs.**.domain");
        // 设置databaseIdProvider
        bean.setDatabaseIdProvider(new XdoMyBatisConfig().getDatabaseIdProvider());
        try {
            bean.setPlugins(new Interceptor[]{getPageInterceptor(), sqlStatementInterceptor});
            bean.getObject().getConfiguration().setJdbcTypeForNull(JdbcType.NULL);
            bean.getObject().getConfiguration().setCallSettersOnNulls(true);
            bean.getObject().getConfiguration().setMapUnderscoreToCamelCase(true);
            return bean.getObject();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    @Bean
    public PageInterceptor getPageInterceptor() {
        PageInterceptor pageInterceptor = new PageInterceptor();
        Properties properties = new Properties();
        // helperDialect：
        // 分页插件会自动检测当前的数据库链接，自动选择合适的分页方式。 你可以配置helperDialect属性来指定分页插件使用哪种方言。配置时，可以使用下面的缩写值：
        // oracle,mysql,mariadb,sqlite,hsqldb,postgresql,db2,sqlserver,informix,h2,sqlserver2012,derby
        // false 默认值，该参数对使用 RowBounds 作为分页参数时有效。
        // true 将 RowBounds 中的 offset 参数当成 pageNum 使用，可以用页码和页面大小两个参数进行分页
        properties.setProperty("offsetAsPageNum", "true");
        // false 默认值，该参数对使用 RowBounds 作为分页参数时有效。
        // true 使用 RowBounds 分页会进行 count 查询
        properties.setProperty("rowBoundsWithCount", "true");
        // false 默认值，
        // true如果 pageSize=0 或者 RowBounds.limit = 0 就会查询出全部的结果（相当于没有执行分页查询，但是返回结果仍然是 Page 类型）
        properties.setProperty("pageSizeZero", "true");
        //分页合理化参数
        // false 默认值
        // true，pageNum<=0 时会查询第一页， pageNum>pages（超过总数时），会查询最后一页。默认false 时，直接根据参数进行查询。
        properties.setProperty("reasonable", "false");
        // 为了支持startPage(Object params)方法，增加了该参数来配置参数映射，用于从对象中根据属性名取值，
        // 可以配置 pageNum,pageSize,count,pageSizeZero,reasonable，不配置映射的用默认值，
        // 默认值为pageNum=pageNum;pageSize=pageSize;count=countSql;reasonable=reasonable;pageSizeZero=pageSizeZero。
        properties.setProperty("params", "pageNum=pageHelperStart;pageSize=pageHelperRows");
        // 支持通过Mapper接口参数来传递分页参数
        properties.setProperty("supportMethodsArguments", "false");
        // always总是返回PageInfo类型,check检查返回类型是否为PageInfo,none返回Page
        properties.setProperty("returnPageInfo", "none");
        pageInterceptor.setProperties(properties);
        return pageInterceptor;
    }
    @Bean
    public SqlMapper getSQLMapper(SqlSessionTemplate sqlSessionTemplate) {
        //org.mybatis.spring.SqlSessionTemplate
        SqlMapper sqlMapper = new SqlMapper(sqlSessionTemplate);
        return sqlMapper;
    }
    @Bean
    public MapperScannerConfigurer mapperScannerConfigurer() {
        MapperScannerConfigurer mapperScannerConfigurer = new MapperScannerConfigurer();
        mapperScannerConfigurer.setSqlSessionFactoryBeanName("sqlSessionFactory");
        mapperScannerConfigurer.setBasePackage("com.dcjet.cs*.**.dao,cn.dces.msg.**.dao");
        Properties properties = new Properties();
        properties.setProperty("mappers", BasicMapper.class.getName());
        properties.setProperty("notEmpty", "false");
        properties.setProperty("style", "normal");
        mapperScannerConfigurer.setProperties(properties);
        return mapperScannerConfigurer;
    }
}
