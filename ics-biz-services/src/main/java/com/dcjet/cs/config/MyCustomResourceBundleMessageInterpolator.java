package com.dcjet.cs.config;

import org.hibernate.validator.messageinterpolation.ResourceBundleMessageInterpolator;
import org.springframework.context.MessageSource;
import org.springframework.validation.beanvalidation.MessageSourceResourceBundleLocator;

public class MyCustomResourceBundleMessageInterpolator extends
        ResourceBundleMessageInterpolator {
    public MyCustomResourceBundleMessageInterpolator(MessageSource messageSource)
    {
        // Passing false for the second argument
        // in the super() constructor avoids the messages being cached.
        super(new MessageSourceResourceBundleLocator(messageSource), false);
    }
}