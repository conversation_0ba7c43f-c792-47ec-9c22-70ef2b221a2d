package com.dcjet.cs.config;//package com.dcjet.cs.config;
//
//import com.dcjet.cs.gwstd.dao.GwstdJobAllotConfigMapper;
//import com.dcjet.cs.gwstd.model.GwstdJobAllotConfig;
//import com.github.benmanes.caffeine.cache.Cache;
//import com.github.benmanes.caffeine.cache.Caffeine;
//import org.apache.commons.collections4.CollectionUtils;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.List;
//import java.util.concurrent.TimeUnit;
//
///**
// * @description:
// * @author: WJ
// * @createDate: 2021/1/4 15:24
// */
//@Component
//public class GwstdJobAllotMapperConfig {
//
//    @Resource
//    private GwstdJobAllotConfigMapper gwstdJobAllotConfigMapper;
//
////    private Cache<String, GwstdJobAllotConfig> gwstdJobAllotCache;
//    private Cache<String, GwstdJobAllotConfig> gwstdJobAllotCache =
//        Caffeine.newBuilder().expireAfterWrite(10, TimeUnit.DAYS).build();
//
//    public Cache<String, GwstdJobAllotConfig> getGwstdJobAllotCache() {
//        return gwstdJobAllotCache;
//    }
//
//    public void setGwstdJobAllotCache(Cache<String, GwstdJobAllotConfig> gwstdJobAllotCache) {
//        this.gwstdJobAllotCache = gwstdJobAllotCache;
//    }
//
////    @PostConstruct
////    public void init() {
////        if (gwstdJobAllotCache == null) {
////            // 缓存
////            gwstdJobAllotCache = Caffeine.newBuilder()
////                    .expireAfterWrite(10, TimeUnit.DAYS)
////                    .build();
////        }
////
////        refreshCache();
////
////    }
//
//    public Cache<String, GwstdJobAllotConfig> refreshCache() {
//        List<GwstdJobAllotConfig> list = gwstdJobAllotConfigMapper.selectAll();
//        if (CollectionUtils.isNotEmpty(list)) {
//            list.stream().forEach(x -> {
//                gwstdJobAllotCache.put(x.getJobType(), x);
//            });
//        }
//
//        return gwstdJobAllotCache;
//    }
//
//}
