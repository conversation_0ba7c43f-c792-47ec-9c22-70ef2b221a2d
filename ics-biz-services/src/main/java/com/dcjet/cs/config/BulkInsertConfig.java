package com.dcjet.cs.config;

import com.xdo.i.IBulkInsertSetting;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * OracleBulkConfig升级为BulkInsertConfig
 * 支持oracle和postgresql
 * <AUTHOR> 2019-03-19
 */
@Configuration
public class BulkInsertConfig implements IBulkInsertSetting {
    @Value("${spring.datasource.url}")
    private String url;

    @Value("${spring.datasource.username}")
    private String username;

    @Value("${spring.datasource.password}")
    private String password;

    @Override
    public String getUrl(){
        return this.url;
    }
    @Override
    public String getUsername(){
        return this.username;
    }
    @Override
    public String getPassword(){
        return this.password;
    }

    @Value("${xdo.db.switch:false}")
    private boolean dbSwitch;

    @Override
    public boolean getDbSwitch() {
        return this.dbSwitch;
    }
}
