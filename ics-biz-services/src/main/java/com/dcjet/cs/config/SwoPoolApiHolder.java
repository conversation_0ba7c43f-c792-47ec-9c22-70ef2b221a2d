package com.dcjet.cs.config;

import com.google.common.base.Strings;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.json.JsonObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;

import javax.annotation.PostConstruct;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class SwoPoolApiHolder {
    private Retrofit retrofit;

    @Value("${dc.swoPool.url:}")
    private String swoPoolUrl;

    @PostConstruct
    public void init() {
        if (Strings.isNullOrEmpty(swoPoolUrl)) {
            log.warn("未配置dc.swoPool.url");
            return;
        }
        OkHttpClient client = new OkHttpClient.Builder()
                .callTimeout(30, TimeUnit.SECONDS).build();
        retrofit = new Retrofit.Builder()
                .addConverterFactory(JacksonConverterFactory.create(JsonObjectMapper.getInstance().getMapper()))
                .client(client)
                .baseUrl(swoPoolUrl)
                .build();
    }


    /***
     * 创建需要调用的接口
     *
     * @param service
     * @param <T>
     * @return
     */
    public <T> T create(final Class<T> service) {
        if (retrofit == null) {
            throw new ErrorException(500, xdoi18n.XdoI18nUtil.t("当前服务配置【dc.swoPool.url】"));
        }
        return retrofit.create(service);
    }
}
