/**
 * SendMessageCallbackHandler.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis2 version: 1.7.9  Built on : Nov 16, 2018 (12:05:37 GMT)
 */
package com.dcjet.cs.email.wcf;


/**
 *  SendMessageCallbackHandler Callback class, Users can extend this class and implement
 *  their own receiveResult and receiveError methods.
 */
public abstract class SendMessageCallbackHandler {
    protected Object clientData;

    /**
     * User can pass in any object that needs to be accessed once the NonBlocking
     * Web service call is finished and appropriate method of this CallBack is called.
     * @param clientData Object mechanism by which the user can pass in user data
     * that will be avilable at the time this callback is called.
     */
    public SendMessageCallbackHandler(Object clientData) {
        this.clientData = clientData;
    }

    /**
     * Please use this constructor if you don't want to set any clientData
     */
    public SendMessageCallbackHandler() {
        this.clientData = null;
    }

    /**
     * Get the client data
     */
    public Object getClientData() {
        return clientData;
    }

    /**
     * auto generated Axis2 call back method for sendErrEmail method
     * override this method for handling normal response from sendErrEmail operation
     */
    public void receiveResultsendErrEmail(
        SendMessageStub.SendErrEmailResponse result) {
    }

    /**
     * auto generated Axis2 Error handler
     * override this method for handling error response from sendErrEmail operation
     */
    public void receiveErrorsendErrEmail(Exception e) {
    }

    /**
     * auto generated Axis2 call back method for sendAutoEmail method
     * override this method for handling normal response from sendAutoEmail operation
     */
    public void receiveResultsendAutoEmail(
        SendMessageStub.SendAutoEmailResponse result) {
    }

    /**
     * auto generated Axis2 Error handler
     * override this method for handling error response from sendAutoEmail operation
     */
    public void receiveErrorsendAutoEmail(Exception e) {
    }

    /**
     * auto generated Axis2 call back method for sendEmail method
     * override this method for handling normal response from sendEmail operation
     */
    public void receiveResultsendEmail(
        SendMessageStub.SendEmailResponse result) {
    }

    /**
     * auto generated Axis2 Error handler
     * override this method for handling error response from sendEmail operation
     */
    public void receiveErrorsendEmail(Exception e) {
    }
}
