package com.dcjet.cs.Utils;

import com.dcjet.cs.email.wcf.SendMessageStub;
import com.dcjet.cs.metaData.wcf.ServiceStub;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 实现
 */

@Component
public class SingleInstants {
    private static ServiceStub serviceStub;
    private static SendMessageStub sendMessageStub;
    //email请求地址
    private static String emailWcf;
     //wcf请求地址
    private static String WcfUrl;
    //同步对象锁
    private static Object wcfObject = new Object();
    private static Object emailObject = new Object();


    /**
     * 从yaml中获取值，使用这种方法是因为@Value写对static无效
     * @param WcfUrl
     */
    @Value("${postUrl.metaDataWcf:}")
    public void setPegsusUserName(String WcfUrl) throws org.apache.axis2.AxisFault{
        SingleInstants.WcfUrl = WcfUrl;
        if (serviceStub == null) {
            serviceStub = new ServiceStub(WcfUrl);
        }
    }

    @Value("${gw.email.wcf: }")
    public void setEmailWcf(String emailWcf) throws org.apache.axis2.AxisFault{
        SingleInstants.emailWcf = emailWcf;
        if(sendMessageStub == null){
            sendMessageStub = new SendMessageStub(emailWcf);
        }
    }

    public static ServiceStub getServiceStub() throws org.apache.axis2.AxisFault{
        if(serviceStub == null){
            synchronized (wcfObject) {
                serviceStub = new ServiceStub(WcfUrl);
            }
        }

        return serviceStub;
    }

    public static SendMessageStub getSendMessageStub() throws org.apache.axis2.AxisFault{
        if(sendMessageStub == null){
            synchronized (emailObject) {
                sendMessageStub = new SendMessageStub(emailWcf);
            }
        }
        return sendMessageStub;
    }

}
