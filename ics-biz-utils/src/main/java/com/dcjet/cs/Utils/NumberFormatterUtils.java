package com.dcjet.cs.Utils;

import java.math.BigDecimal;
import java.text.DecimalFormat;

public class NumberFormatterUtils {


    public static String formatNumber(BigDecimal value) {
        if (value == null) {
            return "";
        }
        // 判断小数部分是否为0
        if (value.stripTrailingZeros().scale() <= 0) {
            // 是整数，补两位小数，带千分位
            DecimalFormat df = new DecimalFormat("#,##0.00");
            return df.format(value);
        } else {
            // 不是整数，带千分位，原小数位保留
            // 先用 stripTrailingZeros() 保持真实小数位
            BigDecimal stripped = value.stripTrailingZeros();
            int scale = stripped.scale();
            StringBuilder pattern = new StringBuilder("#,##0");
            if (scale > 0) {
                pattern.append(".");
                for (int i = 0; i < scale; i++) {
                    pattern.append("0");
                }
            }
            DecimalFormat df = new DecimalFormat(pattern.toString());
            return df.format(stripped);
        }
    }
}
