package com.dcjet.cs.metaData.main;

import com.dcjet.cs.Utils.SingleInstants;
import com.dcjet.cs.metaData.wcf.ServiceStub;
import com.fasterxml.jackson.core.type.TypeReference;
import com.xdo.common.json.JsonObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

/**
 * @Auther: Administrator
 * @Date: 2019/4/17 19:13
 * @Description:元数据服务调用
 */
@Component
public class CallMetaDataUtil {
    private final Logger logger = LoggerFactory.getLogger("MetaDataWcf");

    /**
     * 功能描述: 请求wcf
     *
     * @auther: zhuhui
     * @version :  1.0
     * @date: 2019/4/18
     * @param: funcName 方法名称
     * @param: operateInfo 操作信息
     * @param: tClass 类类型
     * @return:
     */
    public <T> List<T>  CallWcf(String funcName, ServiceStub.OperateInfo operateInfo, Class<T> tClass) {
        JsonObjectMapper jsonObjectMapper = JsonObjectMapper.getInstance();
        logger.info(String.format("%s**********元数据服务请求开始：方法名  %s;请求参数 %s **********", "【备案回执】", funcName, jsonObjectMapper.toJson(operateInfo)));
        ServiceStub.RequestServiceJson service;
        ServiceStub.ResultInfo resultInfo;
        try {
            ServiceStub serviceStub = SingleInstants.getServiceStub();
            service = new ServiceStub.RequestServiceJson();
            operateInfo.setQueryTimeStamp("0");
            resultInfo = new ServiceStub.ResultInfo();
            service.setStrFunctionName(funcName);
            service.setOperateInfo(operateInfo);
            service.setResultInfo(resultInfo);

            ServiceStub.RequestServiceJsonResponse rtResult = serviceStub.requestServiceJson(service);
            logger.info(String.format("%s**********请求返回结果：%s**********", "【备案回执】", JsonObjectMapper.getInstance().toJson(rtResult)));
            //执行成功
            if ("0".equals(rtResult.getRequestServiceJsonResult())) {
                if (!StringUtils.isEmpty(rtResult.getResultInfo().getReturnDataJson())) {
                    List<T> rtRetusltEntitys = jsonObjectMapper.fromJsons(rtResult.getResultInfo().getReturnDataJson(),tClass);
                    return  rtRetusltEntitys;
                }
            } else {
                logger.info(String.format("%s**********元数据服务请求返回错误信息：%s**********", "【备案回执】",
                        resultInfo.getErrorMessage()));
            }
        } catch (Exception ex) {
            logger.error(String.format("%s**********元数据服务请求异常**********", "【备案回执】"), ex);
        }
        finally {
            logger.info(String.format("%s**********元数据服务请求成功！**********", "【备案回执】"));
            service = null;
            resultInfo = null;
            operateInfo = null;
        }
        return null;
    }
    /**
     * 功能描述: 页面请求wcf
     *
     * @auther: zhuhui
     * @version :  1.0
     * @date: 2019/4/18
     * @param: funcName 方法名称
     * @param: operateInfo 操作信息
     * @param: tClass 类类型
     * @return:
     */
    public <T>  T CallWcf(String funcName, ServiceStub.OperateInfo operateInfo, TypeReference<T> typeReference) throws Exception {
        JsonObjectMapper jsonObjectMapper = JsonObjectMapper.getInstance();
//        logger.info(String.format("元数据服务请求开始：方法名  %s;请求参数 %s ", funcName, jsonObjectMapper.toJson(operateInfo)));
        ServiceStub.RequestServiceJson service;
        ServiceStub.ResultInfo resultInfo;
        try {
            ServiceStub serviceStub = SingleInstants.getServiceStub();
            service = new ServiceStub.RequestServiceJson();
            operateInfo.setQueryTimeStamp("0");
            resultInfo = new ServiceStub.ResultInfo();
            service.setStrFunctionName(funcName);
            service.setOperateInfo(operateInfo);
            service.setResultInfo(resultInfo);

            ServiceStub.RequestServiceJsonResponse rtResult = serviceStub.requestServiceJson(service);
            logger.info(String.format("请求返回结果：%s", JsonObjectMapper.getInstance().toJson(rtResult)));
            //需要解析json的状态
            //1：失败  0：成功
            List<String> listParseJsonStates=new ArrayList<String>() {{add("1");add("0");}};
            //执行成功
            if (listParseJsonStates.contains(rtResult.getRequestServiceJsonResult())) {
                if (!StringUtils.isEmpty(rtResult.getResultInfo().getReturnDataJson())) {
                    T rtRetusltEntity = jsonObjectMapper.fromJson(rtResult.getResultInfo().getReturnDataJson(),typeReference);
                    return  rtRetusltEntity;
                }
            } else {
                logger.error(String.format("元数据服务请求返回错误信息：%s", resultInfo.getErrorMessage()));
                throw new Exception(rtResult.getRequestServiceJsonResult());
            }


        } catch (Exception ex) {
            logger.error("元数据服务请求异常", ex);
            throw new Exception(ex.getMessage());
        }
        finally {
            logger.info("元数据服务请求成功！");
            service = null;
            resultInfo = null;
            operateInfo = null;
        }
        return null;
    }
    /**
     * 功能描述: 请求wcf
     *
     * @auther: zhuhui
     * @version :  1.0
     * @date: 2019/4/18
     * @param: * @param null
     * @return:
     */
    public <T> String  CallWcf(String funcName, ServiceStub.OperateInfo operateInfo, Function<List<T>,String> dealResponseData, Class<T> tClass) throws Exception {
        JsonObjectMapper jsonObjectMapper = JsonObjectMapper.getInstance();
        logger.info(String.format("元数据服务请求开始：方法名  %s;请求参数 %s ", funcName, jsonObjectMapper.toJson(operateInfo)));
        String callWcfResult="";
        ServiceStub.RequestService service;
        ServiceStub.ResultInfo resultInfo;

        try {
            ServiceStub serviceStub = SingleInstants.getServiceStub();
            service = new ServiceStub.RequestService();
            operateInfo.setQueryTimeStamp("0");
            resultInfo = new ServiceStub.ResultInfo();
            service.setStrFunctionName(funcName);
            service.setOperateInfo(operateInfo);
            service.setResultInfo(resultInfo);

            ServiceStub.RequestServiceResponse rtResult = serviceStub.requestService(service);
            //执行成功
            if ("0".equals(rtResult.getRequestServiceResult())) {
                if (!StringUtils.isEmpty(rtResult.getResultInfo().getReturnDataJson())) {
                    List<T> rtRetusltEntitys = jsonObjectMapper.fromJsons(rtResult.getResultInfo().getReturnDataJson(),tClass);
                    callWcfResult=dealResponseData.apply(rtRetusltEntitys);
                }
            } else {
                logger.error(String.format("元数据服务请求返回错误信息：%s", resultInfo.getErrorMessage()));
            }
            return "";
        } catch (Exception ex) {
            logger.error("元数据服务请求异常", ex);
        }
        finally {
            logger.info("元数据服务请求成功！");
            service = null;
            resultInfo = null;
            operateInfo = null;
        }
        return callWcfResult;
    }
}
