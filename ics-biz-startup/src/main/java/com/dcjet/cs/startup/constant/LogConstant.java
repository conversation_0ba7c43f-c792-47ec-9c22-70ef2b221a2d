package com.dcjet.cs.startup.constant;

public class LogConstant {
    //日志文件MDC的key
    public static final String LOGBACK_KEY = "logType";

    //需日志分离的AppId
    public static final String LOGBACK_VALUE_XXL_ASYNC = "gw-async-pg";
    public static final String LOGBACK_VALUE_XXL_EXCHANGE = "gw-exchange-pg";
    public static final String LOGBACK_VALUE_XXL_EXECUTOR = "gw-import-pg";
    public static final String LOGBACK_VALUE_XXL_EXPORT = "gw-export-pg";
    public static final String LOGBACK_VALUE_KAFKA = "gw-kafka-pg";
    public static final String LOGBACK_VALUE_API = "gw-api-pg";
    public static final String LOGBACK_VALUE_PUBLIC_API = "gw-public-api-pg";

    //XXL执行器对应的包名
    public static final String LOGBACK_VALUE_XXL_ASYNC_PACKAGE = "com.dcjet.cs.async";
    public static final String LOGBACK_VALUE_XXL_EXECUTOR_PACKAGE = "com.dcjet.cs.executor";
    public static final String LOGBACK_VALUE_XXL_EXPORT_PACKAGE = "com.dcjet.cs.export";

    //public-Api对应的路径名
    public static final String LOGBACK_VALUE_PUBLIC_API_PACKAGE1 = "v1/inside";
    public static final String LOGBACK_VALUE_PUBLIC_API_PACKAGE2 = "v1/public";
}
