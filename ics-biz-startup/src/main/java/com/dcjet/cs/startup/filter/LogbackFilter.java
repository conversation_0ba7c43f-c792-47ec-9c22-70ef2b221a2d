package com.dcjet.cs.startup.filter;

import com.dcjet.cs.startup.constant.LogConstant;
import org.slf4j.MDC;
import org.springframework.boot.logging.LogLevel;
import org.springframework.boot.logging.LoggingSystem;
import org.springframework.context.ApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

import static org.springframework.boot.logging.LoggingSystem.ROOT_LOGGER_NAME;

public class LogbackFilter implements Filter {

    /**
     * 过滤器，用于对API进行MDC日志设置
     * @param request
     * @param response
     * @param chain
     * @throws IOException
     * @throws ServletException
     */
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {

        ServletContext context = request.getServletContext();
        ApplicationContext ctx = WebApplicationContextUtils.getWebApplicationContext(context);
        LoggingSystem loggingSystem = ctx.getBean(LoggingSystem.class);

        Environment environment = ctx.getBean(Environment.class);
        String level = environment.getProperty("logging.level.root");
        String apiLevel = environment.getProperty("logging.gw_api_pg.level")!=null?environment.getProperty("logging.gw_api_pg.level"):String.valueOf(LogLevel.DEBUG);
        String publicApiLevel = environment.getProperty("logging.gw_public_api_pg.level")!=null?environment.getProperty("logging.gw_public_api_pg.level"):String.valueOf(LogLevel.DEBUG);

        HttpServletRequest httpRequest = (HttpServletRequest) request;
        String uri = httpRequest.getRequestURI();
        //根据访问路径判断是公共API还是普通API
        if(uri.indexOf(LogConstant.LOGBACK_VALUE_PUBLIC_API_PACKAGE1)>-1||uri.indexOf(LogConstant.LOGBACK_VALUE_PUBLIC_API_PACKAGE2)>-1){
            setLevel(publicApiLevel,loggingSystem);
            MDC.put(LogConstant.LOGBACK_KEY,LogConstant.LOGBACK_VALUE_PUBLIC_API);
        }else{
            setLevel(apiLevel,loggingSystem);
            MDC.put(LogConstant.LOGBACK_KEY,LogConstant.LOGBACK_VALUE_API);
        }
        //过滤器放行
        chain.doFilter(request,response);
        //此过滤器优先级是1，会等所有过滤器执行完后进行remove操作
        setLevel(level,loggingSystem);
        MDC.remove(LogConstant.LOGBACK_KEY);
    }

    public void setLevel(String level,LoggingSystem loggingSystem){
        if (level.equalsIgnoreCase(String.valueOf(LogLevel.DEBUG))){
            loggingSystem.setLogLevel(ROOT_LOGGER_NAME, LogLevel.DEBUG);
        }else if (level.equalsIgnoreCase(String.valueOf(LogLevel.INFO))){
            loggingSystem.setLogLevel(ROOT_LOGGER_NAME, LogLevel.INFO);
        }
    }
}
