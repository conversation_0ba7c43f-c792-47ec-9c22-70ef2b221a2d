package com.dcjet.cs.startup;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.dcjet.cs.config.MyBatisMapperScannerConfig;
import com.xdo.common.support.TokenUserHandlerMethodArgumentResolver;
import com.xdo.file.XdoFileHandler;
import com.xdo.multidb.DynamicDataSourcesConfiguration;
import com.xdo.pcode.service.PCodeHolder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import tk.mybatis.spring.annotation.MapperScan;

import java.util.List;

@EnableTransactionManagement
@SpringBootApplication
@EnableCaching
@EnableScheduling
@EnableApolloConfig
@Import({MyBatisMapperScannerConfig.class, DynamicDataSourcesConfiguration.class})
@ComponentScan({"xdoi18n.**","com.dcjet.cs.**","com.xdo.springboot.**","com.xdo.mybatis.**","com.xdo.export.async.**","com.xdo.findsword.**","com.xdo.multidb", "com.xdo.audit.**","com.xdo.dataimport.**","cn.dces.msg.**"})
@MapperScan({"com.xdo.export.async.dao.**"})
@EnableAspectJAutoProxy(proxyTargetClass = true,exposeProxy = true)
public class Application extends SpringBootServletInitializer implements WebMvcConfigurer {

    @Value("${dc.pcode.url}")
    private String pcodeUrl;

    @Bean
    public PCodeHolder getPCodeHolder() {
        PCodeHolder pCodeHolder = new PCodeHolder(pcodeUrl, new String[]{"COMPANY"});
        return pCodeHolder;
    }

    @Bean(name = "otherEternalXdoFileHandler")
    public XdoFileHandler otherEternalHandler() {
        /**
         * 根据name查找配置项
         */
        String name = "otherEternal";
        return new XdoFileHandler(name);
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
        xdo.interceptor.XdoAgent.Install();
        // Application就是项目的Application名字，各个项目会有差异
        return builder.sources(Application.class);
    }

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {
        resolvers.add(new TokenUserHandlerMethodArgumentResolver());
    }

    /** 需要放置业务自己的@Bean加载器，框架的可全部去除 */
    public static void main(String[] args) {
        xdo.interceptor.XdoAgent.Install();
        SpringApplication.run(Application.class, args);
    }
}
