package com.dcjet.cs.startup.config;

import com.ctrip.framework.apollo.spring.boot.ApolloApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;

public class XdoApolloApplicationContextInitializer extends ApolloApplicationContextInitializer {
    @Override
    public void initialize(ConfigurableApplicationContext context) {
        super.initialize(context);
        System.setProperty("app.id", "");
    }
}
