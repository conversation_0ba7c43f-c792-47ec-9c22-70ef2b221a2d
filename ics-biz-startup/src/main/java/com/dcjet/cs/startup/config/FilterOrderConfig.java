package com.dcjet.cs.startup.config;

import com.dcjet.cs.startup.filter.LogbackFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FilterOrderConfig {

    @Bean
    public FilterRegistrationBean Filter01(){
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean();
        filterRegistrationBean.setFilter(new LogbackFilter());//设置过滤器名称-日志过滤器，优先级最高
        filterRegistrationBean.addUrlPatterns("/*");//配置过滤规则，拦截所有请求
        filterRegistrationBean.setOrder(1); //order的数值越小 则优先级越高
        return filterRegistrationBean;
    }
}
