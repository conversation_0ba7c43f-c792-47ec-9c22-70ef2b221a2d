package com.dcjet.cs.startup.aop;

import com.dcjet.cs.startup.constant.LogConstant;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.*;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.logging.LogLevel;
import org.springframework.boot.logging.LoggingSystem;
import org.springframework.stereotype.Component;

import static org.springframework.boot.logging.LoggingSystem.ROOT_LOGGER_NAME;

@Aspect
@Component
@Slf4j
public class LogbackAop {

    @Autowired(required = false)
    private LoggingSystem loggingSystem;

    @Value("${logging.level.root}")
    private String level;

    @Value("${logging.gw_async_pg.level:INFO}")
    private String asyncLevel;

    @Value("${logging.gw_exchange_pg.level:INFO}")
    private String exchangeLevel;

    @Value("${logging.gw_import_pg.level:INFO}")
    private String importLevel;

    @Value("${logging.gw_export_pg.level:INFO}")
    private String exportLevel;

    @Value("${logging.gw_kafka_pg.level:INFO}")
    private String kafkaLevel;

    /**
     * 设置xxl-job切入点
     */
    @Pointcut("@annotation(com.xxl.job.core.handler.annotation.XxlJob)")
    public void xxlJob() {
    }

    /**
     *  设置kafka切入点
     */
    @Pointcut("@annotation(org.springframework.kafka.annotation.KafkaListener)")
    public void kafka(){

    }

    /**
     * 对xxl-job进行MDC参数设置
     * @param joinPoint
     * @return
     */
    @Before("xxlJob()")
    public void beforeXxlJobLog(JoinPoint joinPoint) {

        if(joinPoint.getSignature().getDeclaringTypeName().indexOf(LogConstant.LOGBACK_VALUE_XXL_ASYNC_PACKAGE)>-1){
            setLevel(asyncLevel);
            MDC.put(LogConstant.LOGBACK_KEY, LogConstant.LOGBACK_VALUE_XXL_ASYNC);
        }else if(joinPoint.getSignature().getDeclaringTypeName().indexOf(LogConstant.LOGBACK_VALUE_XXL_EXECUTOR_PACKAGE)>-1){
            setLevel(importLevel);
            MDC.put(LogConstant.LOGBACK_KEY, LogConstant.LOGBACK_VALUE_XXL_EXECUTOR);
        }else if(joinPoint.getSignature().getDeclaringTypeName().indexOf(LogConstant.LOGBACK_VALUE_XXL_EXPORT_PACKAGE)>-1){
            setLevel(exportLevel);
            MDC.put(LogConstant.LOGBACK_KEY, LogConstant.LOGBACK_VALUE_XXL_EXPORT);
        }else{
            setLevel(exchangeLevel);
            MDC.put(LogConstant.LOGBACK_KEY, LogConstant.LOGBACK_VALUE_XXL_EXCHANGE);
        }
    }

    /**
     * 对xxl-job进行MDC参数设置
     * @param joinPoint
     * @return
     */
    @After("xxlJob()")
    public void afterXxlJobLog(JoinPoint joinPoint){
        setLevel(level);
        MDC.remove(LogConstant.LOGBACK_KEY);
    }

    /**
     * 对kafka进行MDC参数设置
     * @param joinPoint
     * @return
     */
    @Before("kafka()")
    public void beforeKafkaLog(JoinPoint joinPoint) {
        setLevel(kafkaLevel);
        MDC.put(LogConstant.LOGBACK_KEY, LogConstant.LOGBACK_VALUE_KAFKA);
    }

    /**
     * 对kafka进行MDC参数设置
     * @param joinPoint
     * @return
     */
    @After("kafka()")
    public void afterKafkaLog(JoinPoint joinPoint){
        setLevel(level);
        MDC.remove(LogConstant.LOGBACK_KEY);
    }

    public void setLevel(String level){
        if (level.equalsIgnoreCase(String.valueOf(LogLevel.DEBUG))){
            loggingSystem.setLogLevel(ROOT_LOGGER_NAME, LogLevel.DEBUG);
        }else if (level.equalsIgnoreCase(String.valueOf(LogLevel.INFO))){
            loggingSystem.setLogLevel(ROOT_LOGGER_NAME, LogLevel.INFO);
        }
    }
}
