<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <springProperty scope="context" name="logPath" source="logging.path" defaultValue="log"/>
    <springProperty scope="context" name="maxHistory" source="logging.file.max-history" defaultValue="30"/>
    <springProperty scope="context" name="maxFileSize" source="logging.file.max-size" defaultValue="100MB"/>
    <springProperty scope="context" name="springFrameworkLogLevel" source="logging.level.org.springframework" defaultValue="ERROR"/>
    <springProperty scope="context" name="rootLogLevel" source="logging.level.root" defaultValue="INFO"/>
    <springProperty scope="context" name="appId" source="logging.appId" defaultValue="default-app"/>

    <!--输出到控制台-->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>Debug</level>
        </filter>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %contextName [%thread] %-5level %logger{36} Token:%X{Authorization} ReqId:%X{RequestId} SourceEventId:%X{SourceEventId} TraceId:%X{TraceId} TaskId:%X{TaskId} TaskCode:%X{TaskCode} TradeCode:%X{TradeCode} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!--输出到文件-->
    <appender name="info_file" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>DENY</onMatch><!-- 如果命中ERROR就禁止这条日志 -->
            <onMismatch>ACCEPT</onMismatch><!-- 如果没有命中就使用这条规则 -->
        </filter>
        <file>${logPath}/${appId}_info_full.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${logPath}/info/${appId}_info_full.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>${maxFileSize}</maxFileSize>
            <maxHistory>${maxHistory}</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %contextName [%thread] %-5level %logger{36} Token:%X{Authorization} ReqId:%X{RequestId} SourceEventId:%X{SourceEventId} TraceId:%X{TraceId} TaskId:%X{TaskId} TaskCode:%X{TaskCode} TradeCode:%X{TradeCode} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!--输出到文件-->
    <appender name="error_file" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <file>${logPath}/${appId}_error_full.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${logPath}/error/${appId}_error_full.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>${maxFileSize}</maxFileSize>
            <maxHistory>${maxHistory}</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %contextName [%thread] %-5level %logger{36} Token:%X{Authorization} ReqId:%X{RequestId} SourceEventId:%X{SourceEventId} TraceId:%X{TraceId} TaskId:%X{TaskId} TaskCode:%X{TaskCode} TradeCode:%X{TradeCode} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="distribution_info" class="ch.qos.logback.classic.sift.SiftingAppender">
        <discriminator>
            <key>logType</key> <!-- 从MDC内获取 logType 的value，默认为appId-->
            <defaultValue>${appId}</defaultValue>
        </discriminator>
        <sift>
            <appender name="${logType}_info_file" class="ch.qos.logback.core.rolling.RollingFileAppender">
                <filter class="ch.qos.logback.classic.filter.LevelFilter">
                    <level>ERROR</level>
                    <onMatch>DENY</onMatch><!-- 如果命中ERROR就禁止这条日志 -->
                    <onMismatch>ACCEPT</onMismatch><!-- 如果没有命中就使用这条规则 -->
                </filter>
                <file>${logPath}/${logType}_info.log</file>
                <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                    <fileNamePattern>${logPath}/info/${logType}_info.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
                    <maxFileSize>${maxFileSize}</maxFileSize>
                    <maxHistory>${maxHistory}</maxHistory>
                </rollingPolicy>
                <encoder>
                    <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %contextName [%thread] %-5level %logger{36} Token:%X{Authorization} ReqId:%X{RequestId} SourceEventId:%X{SourceEventId} TraceId:%X{TraceId} TaskId:%X{TaskId} TaskCode:%X{TaskCode} TradeCode:%X{TradeCode} - %msg%n</pattern>
                    <charset>UTF-8</charset>
                </encoder>
            </appender>
        </sift>
    </appender>

    <appender name="distribution_error" class="ch.qos.logback.classic.sift.SiftingAppender">
        <discriminator>
            <key>logType</key> <!-- 从MDC内获取 logType 的value，默认为appId-->
            <defaultValue>${appId}</defaultValue>
        </discriminator>
        <sift>
            <appender name="${logType}_error_file" class="ch.qos.logback.core.rolling.RollingFileAppender">
                <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
                    <level>ERROR</level>
                </filter>
                <file>${logPath}/${logType}_error.log</file>
                <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                    <fileNamePattern>${logPath}/error/${logType}_error.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
                    <maxFileSize>${maxFileSize}</maxFileSize>
                    <maxHistory>${maxHistory}</maxHistory>
                </rollingPolicy>
                <encoder>
                    <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %contextName [%thread] %-5level %logger{36} Token:%X{Authorization} ReqId:%X{RequestId} SourceEventId:%X{SourceEventId} TraceId:%X{TraceId} TaskId:%X{TaskId} TaskCode:%X{TaskCode} TradeCode:%X{TradeCode} - %msg%n</pattern>
                    <charset>UTF-8</charset>
                </encoder>
            </appender>
        </sift>
    </appender>

    <!--输出到Sentry-->
    <appender name="Sentry" class="io.sentry.logback.SentryAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
    </appender>

    <Logger name="springfox.documentation" level="${springFrameworkLogLevel}"></Logger>
    <Logger name="org.springframework" level="${springFrameworkLogLevel}"></Logger>

    <!-- 设置kafka自带级别为INFO -->
    <Logger name="org.apache.kafka" level="INFO" ></Logger>
    <!-- 去除apollo日志输出,影响调试 -->
    <logger name="com.ctrip.framework.apollo.internals" level="off"/>

    <root level="${rootLogLevel}">
        <appender-ref ref="console" />
        <appender-ref ref="Sentry" />
        <appender-ref ref="info_file" />
        <appender-ref ref="error_file" />
        <appender-ref ref="distribution_info" />
        <appender-ref ref="distribution_error" />
    </root>

</configuration>
