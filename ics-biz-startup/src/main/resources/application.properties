#apollo\u4E2D\u9879\u76EEID
app.id=biz-api-startup-tobacoo
#\u662F\u5426\u5F00\u542F
apollo.bootstrap.enabled=${apollo:true}
apollo.bootstrap.eagerLoad.enabled=true
#\u652F\u6301\u7684namespaces, \u201C...\u201D\u4E3A\u5404\u4E2A\u7CFB\u7EDF\u81EA\u5B9A\u4E49\u7684\uFF0C\u5982\u679C\u6CA1\u6709\u8BF7\u5220\u9664
apollo.bootstrap.namespaces=application
context.initializer.classes=com.dcjet.cs.startup.config.XdoApolloApplicationContextInitializer
#mybatis.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
