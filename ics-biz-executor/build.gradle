apply plugin: 'org.springframework.boot'
apply plugin: 'java'

springBoot{
    mainClassName = 'com.dcjet.cs.executor.XxlJobExecutorApplication'
}

dependencies {
    compile "org.springframework.boot:spring-boot-starter-web"
    compile "org.springframework.boot:spring-boot-starter-validation"
    compile "com.xuxueli:xxl-job-core:2.2.0"
    compile 'org.apache.logging.log4j:log4j-core:2.17.1'
    compile 'org.apache.logging.log4j:log4j-api:2.17.1'
    compile 'org.apache.logging.log4j:log4j-to-slf4j:2.17.1'
    compile 'ch.qos.logback:logback-core:1.2.10'
    compile 'ch.qos.logback:logback-classic:1.2.10'
}

