package com.dcjet.cs.executor.mvc.controller;

import com.dcjet.cs.executor.service.jobhandler.ImportJobHandler;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;

@Controller
@EnableAutoConfiguration
@RequestMapping("import")
public class IndexController {
    @Resource
    ImportJobHandler importJobHandler;

    @PostMapping("/ceshi")
    public String index(@RequestBody String par) throws Exception {
        importJobHandler.execute(par);
        return "xxl job executor running.";
    }

}