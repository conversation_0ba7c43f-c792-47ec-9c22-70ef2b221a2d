package com.dcjet.cs.executor.service.jobhandler;

import com.xdo.dataimport.base.ImportBase;
import com.xdo.dataimport.handler.ValidateHandler;
import com.xdo.dataimport.utils.XdoImportLogger;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.Validator;

/**
 * 异步导入
 */
@Component
@Slf4j
public class ImportJobHandler {

    @Resource
    private ImportBase importBase;

    @Resource
    private Validator validator;

    @XxlJob("importJobHandler")
    public ReturnT<String> execute(String strSystemCode) throws Exception {
        ValidateHandler.setValidator(validator);
        XdoImportLogger.log("XXL-JOB调度importJobHandler成功");
        return importBase.execute(strSystemCode);
    }
}
