apply plugin: 'org.springframework.boot'
apply plugin: 'java'
apply plugin: 'org.liquibase.gradle'

ext {
    springfoxVersion = '2.9.2'
}

springBoot{
    mainClassName = 'com.dcjet.cs.async.Application'
}

dependencies {
    compile "mysql:mysql-connector-java:5.1.46"
    compile "io.springfox:springfox-swagger2:$springfoxVersion"
    compile "io.springfox:springfox-swagger-ui:$springfoxVersion"
    compile "commons-net:commons-net:3.6"
    compile "org.apache.commons:commons-lang3:3.9"
    compile 'org.javatuples:javatuples:1.2'
    compile "com.oracle:ojdbc6:********.0"
    compile 'com.squareup.okhttp3:okhttp:3.12.1'
    compile 'com.squareup.retrofit2:retrofit:2.5.0'
    compile 'com.squareup.retrofit2:converter-jackson:2.5.0'
    compile "com.xuxueli:xxl-job-core:2.2.0"
    compile 'com.ctrip.framework.apollo:apollo-client:1.4.0'
    testImplementation "org.springframework.boot:spring-boot-starter-test"
    compile "com.xdo:xdo-kafka:3.0.+"
    compile 'org.apache.logging.log4j:log4j-core:2.17.1'
    compile 'org.apache.logging.log4j:log4j-api:2.17.1'
    compile 'org.apache.logging.log4j:log4j-to-slf4j:2.17.1'
    compile 'ch.qos.logback:logback-core:1.2.10'
    compile 'ch.qos.logback:logback-classic:1.2.10'
}
configurations {
    all {
        exclude group: 'javax.servlet', module: 'servlet-api'
    }
}


