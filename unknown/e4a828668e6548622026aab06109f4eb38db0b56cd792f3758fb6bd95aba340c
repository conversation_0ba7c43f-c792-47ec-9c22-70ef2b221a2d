package com.dcjet.cs.dto.thirdparty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 第三方数据库泛型插入请求DTO
 * 
 * @param <T> 数据类型
 */
@Data
@ApiModel(value = "第三方数据库泛型插入请求")
public class ThirdPartyDbGenericInsertRequest<T> {

    @NotBlank(message = "表名不能为空")
    @ApiModelProperty(value = "表名", required = true, example = "TEST_TABLE")
    private String tableName;

    @NotEmpty(message = "数据不能为空")
    @ApiModelProperty(value = "要插入的数据对象列表", required = true)
    private List<T> data;
}
