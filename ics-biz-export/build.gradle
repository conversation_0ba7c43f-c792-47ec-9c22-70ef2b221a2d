apply plugin: 'org.springframework.boot'
apply plugin: 'java'
apply plugin: 'org.liquibase.gradle'

ext {
    springfoxVersion = '2.9.2'
}

springBoot{
    mainClassName = 'com.dcjet.cs.export.Application'
}

dependencies {
    compile "mysql:mysql-connector-java:5.1.46"
    compile "io.springfox:springfox-swagger2:$springfoxVersion"
    compile "io.springfox:springfox-swagger-ui:$springfoxVersion"
    compile "commons-net:commons-net:3.6"
    compile "org.apache.commons:commons-lang3:3.9"
    compile 'org.javatuples:javatuples:1.2'
    compile "com.oracle:ojdbc6:********.0"
    compile 'com.xuxueli:xxl-job-core:2.0.1'
    compile 'org.apache.axis2:axis2-spring:1.7.9'
    compile 'org.apache.axis2:axis2-transport-http:1.7.9'
    compile 'org.apache.axis2:axis2-transport-local:1.7.9'
    compile 'org.apache.axis2:axis2-xmlbeans:1.7.9'
    compile "com.xuxueli:xxl-job-core:2.2.0"
    compile 'com.ctrip.framework.apollo:apollo-client:1.4.0'
    testImplementation "org.springframework.boot:spring-boot-starter-test"
    compile 'org.apache.logging.log4j:log4j-core:2.17.1'
    compile 'org.apache.logging.log4j:log4j-api:2.17.1'
    compile 'org.apache.logging.log4j:log4j-to-slf4j:2.17.1'
    compile 'ch.qos.logback:logback-core:1.2.10'
    compile 'ch.qos.logback:logback-classic:1.2.10'
}
configurations {
    all {
        exclude group: 'javax.servlet', module: 'servlet-api'
    }
}


