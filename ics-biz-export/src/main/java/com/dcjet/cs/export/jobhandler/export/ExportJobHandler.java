package com.dcjet.cs.export.jobhandler.export;

import com.xdo.dataimport.utils.XdoImportLogger;
import com.xdo.export.async.core.Export;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description: 异步导出
 * @author: WJ
 * @createDate: 2020/9/7 9:59
 */
@Component
@Slf4j
public class ExportJobHandler {

    @Autowired
    private Export export;

    @XxlJob("exportJobHandler")
    public ReturnT<String> execute(String strSystemCode) throws Exception {
        XdoImportLogger.log("XXL-JOB调度exportJobHandler成功");
        return export.execute(strSystemCode);
    }
}
