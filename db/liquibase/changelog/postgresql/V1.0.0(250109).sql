--liquibase formatted sql

--changeset ycmeng:1

CREATE TABLE "BIZ_TOBACOO"."T_BIZ_I_PLAN" (
                                             "SID" VARCHAR(80) NOT NULL DEFAULT HEX(SYS_GUID()),
                                             "PARENT_ID" VARCHAR(100),
                                             "TRADE_CODE" VARCHAR(10),
                                             "BUSINESS_TYPE" VARCHAR(60) NOT NULL,
                                             "PLAN_ID" VARCHAR(100) NOT NULL,
                                             "PLAN_YEAR" TIMESTAMP(6) NOT NULL,
                                             "HALF_YEAR" VARCHAR(10) NOT NULL,
                                             "REMARK" VARCHAR(500),
                                             "STATUS" VARCHAR(10),
                                             "CONFIRM_TIME" TIMESTAMP(6),
                                             "APPR_STATUS" VARCHAR(10),
                                             "VERSION_NO" VARCHAR(20) NOT NULL,
                                             "INSERT_USER" VARCHAR(100) NOT NULL,
                                             "INSERT_USER_NAME" VARCHAR(100),
                                             "INSERT_TIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                             "UPDATE_USER" VARCHAR(100),
                                             "UPDATE_TIME" TIMESTAMP(6),
                                             "UPDATE_USER_NAME" VARCHAR(100),
                                             "EXTEND1" VARCHAR(400),
                                             "EXTEND2" VARCHAR(400),
                                             "EXTEND3" VARCHAR(400),
                                             "EXTEND4" VARCHAR(400),
                                             "EXTEND5" VARCHAR(400),
                                             "EXTEND6" VARCHAR(400),
                                             "EXTEND7" VARCHAR(400),
                                             "EXTEND8" VARCHAR(400),
                                             "EXTEND9" VARCHAR(400),
                                             "EXTEND10" VARCHAR(400),
                                             CONSTRAINT "T_BIZ_IPLAN_PKEY" PRIMARY KEY ("SID")  -- 修正约束名匹配表名
);

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN"."PARENT_ID" IS '父sid';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN"."TRADE_CODE" IS '企业编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN"."BUSINESS_TYPE" IS '业务类型，默认国营贸易进口卷烟，置灰不可修改';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN"."PLAN_ID" IS '计划编号，唯一性校验';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN"."PLAN_YEAR" IS '计划年度';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN"."HALF_YEAR" IS '上下半年，0表示上半年，1表示下半年';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN"."REMARK" IS '备注';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN"."STATUS" IS '单据状态，0编制，1确认，2作废';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN"."CONFIRM_TIME" IS '确认时间，点击确认按钮成功提交的时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN"."APPR_STATUS" IS '审批状态，0不涉及审批，1未审批，2审批中，3审批通过，4审批退回';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN"."VERSION_NO" IS '版本号，初始为1，版本复制递增';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN"."INSERT_USER" IS '制单人，自动识别最后操作人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN"."INSERT_TIME" IS '制单时间，系统自动生成';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN"."UPDATE_USER" IS '修改人，最后操作人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN"."UPDATE_TIME" IS '修改时间，最后操作时间';
COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_I_PLAN" IS '进口计划表';


CREATE TABLE "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST" (
                                                   "SID" VARCHAR(80) NOT NULL DEFAULT HEX(SYS_GUID()),
                                                   "HEAD_ID" VARCHAR(80) NOT NULL,
                                                   "TRADE_CODE" VARCHAR(50),
                                                   "PRODUCT_NAME" VARCHAR(160) NOT NULL,
                                                   "SUPPLIER" VARCHAR(400) NOT NULL,
                                                   "ENGLISH_BRAND" VARCHAR(400),
                                                   "ORIGIN" VARCHAR(200),
                                                   "PLAN_QUANTITY" NUMERIC(19,6) NOT NULL,
                                                   "UNIT" VARCHAR(40) NOT NULL,
                                                   "CURRENCY_CODE" VARCHAR(20) NOT NULL,
                                                   "UNIT_PRICE" NUMERIC(19,5) NOT NULL,
                                                   "TOTAL_AMOUNT" NUMERIC(19,5) NOT NULL,
                                                   "DISCOUNT_RATE" NUMERIC(19,4),
                                                   "DISCOUNT_AMOUNT" NUMERIC(19,5),
                                                   "INSERT_USER" VARCHAR(200) NOT NULL,
                                                   "INSERT_USER_NAME" VARCHAR(200),
                                                   "INSERT_TIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                                   "UPDATE_USER" VARCHAR(200),
                                                   "UPDATE_TIME" TIMESTAMP(6),
                                                   "UPDATE_USER_NAME" VARCHAR(200),
                                                   "EXTEND1" VARCHAR(400),
                                                   "EXTEND2" VARCHAR(400),
                                                   "EXTEND3" VARCHAR(400),
                                                   "EXTEND4" VARCHAR(400),
                                                   "EXTEND5" VARCHAR(400),
                                                   "EXTEND6" VARCHAR(400),
                                                   "EXTEND7" VARCHAR(400),
                                                   "EXTEND8" VARCHAR(400),
                                                   "EXTEND9" VARCHAR(400),
                                                   "EXTEND10" VARCHAR(400),
                                                   CONSTRAINT "T_BIZ_I_PLAN_LIST_PKEY" PRIMARY KEY ("SID")
);

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."HEAD_ID" IS '主表ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."PRODUCT_NAME" IS '商品名称（关联物料信息）';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."SUPPLIER" IS '供应商（关联物料信息，不可修改）';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."ENGLISH_BRAND" IS '英文品牌（关联物料信息，可修改）';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."ORIGIN" IS '原产地';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."PLAN_QUANTITY" IS '计划数量（数值）';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."UNIT" IS '计划数量单位（默认万支）';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."CURRENCY_CODE" IS '币种（三位字母代码）';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."UNIT_PRICE" IS '计划单价（关联物料信息，可修改）';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."TOTAL_AMOUNT" IS '计划总金额';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."DISCOUNT_RATE" IS '折扣率（单位：%）';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."INSERT_USER" IS '制单人，自动识别最后操作人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."INSERT_TIME" IS '制单时间，系统自动生成';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."UPDATE_USER" IS '修改人，最后操作人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."UPDATE_TIME" IS '修改时间，最后操作时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."TRADE_CODE" IS '企业编码';
COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST" IS '进口计划明细表';



--changeset tlhuang:1
create table if not exists BIZ_TOBACOO.T_BIZ_COST_TYPE
(
    SID         VARCHAR(50) default sys_guid() not null
        constraint T_BIZ_COST_TYPE_PK
            primary key,
    TRADE_CODE  VARCHAR(50),
    INSERT_USER VARCHAR(50) not null,
    INSERT_TIME TIMESTAMP default CURRENT_TIMESTAMP not null,
    INSERT_USER_NAME VARCHAR(50),
    UPDATE_USER VARCHAR(50),
    UPDATE_TIME TIMESTAMP,
    UPDATE_USER_NAME VARCHAR(50),
    PARAM_CODE VARCHAR(30),
    COST_NAME VARCHAR(60),
    ACCOUNT_SUBJECT VARCHAR(60),
    CUSTOMER_SUPPLIER VARCHAR(160),
    COMMON_FLAG VARCHAR(80),
    NOTE VARCHAR(400),
    EXTEND1 VARCHAR(200),
    EXTEND2 VARCHAR(200),
    EXTEND3 VARCHAR(200),
    EXTEND4 VARCHAR(200),
    EXTEND5 VARCHAR(200),
    EXTEND6 VARCHAR(200),
    EXTEND7 VARCHAR(200),
    EXTEND8 VARCHAR(200),
    EXTEND9 VARCHAR(200),
    EXTEND10 VARCHAR(200)
);

comment
    on table BIZ_TOBACOO.T_BIZ_COST_TYPE is '费用类型参数表';

comment
    on column BIZ_TOBACOO.T_BIZ_COST_TYPE.PARAM_CODE is '参数代码';

comment
    on column BIZ_TOBACOO.T_BIZ_COST_TYPE.COST_NAME is '费用名称';

comment
    on column BIZ_TOBACOO.T_BIZ_COST_TYPE.ACCOUNT_SUBJECT is '会计科目';

comment
    on column BIZ_TOBACOO.T_BIZ_COST_TYPE.CUSTOMER_SUPPLIER is '客商';

comment
    on column BIZ_TOBACOO.T_BIZ_COST_TYPE.COMMON_FLAG is '常用标志';

comment
    on column BIZ_TOBACOO.T_BIZ_COST_TYPE.NOTE is '备注';




-- auto-generated definition
create table if not exists BIZ_TOBACOO.T_BIZ_PACKAGE_INFO
(
    SID         VARCHAR(50) default sys_guid() not null
        constraint T_BIZ_PACKAGE_INFO_PK
            primary key,
    TRADE_CODE  VARCHAR(50),
    INSERT_USER VARCHAR(50) not null,
    INSERT_TIME TIMESTAMP default CURRENT_TIMESTAMP not null,
    INSERT_USER_NAME VARCHAR(50),
    UPDATE_USER VARCHAR(50),
    UPDATE_TIME TIMESTAMP,
    UPDATE_USER_NAME VARCHAR(50),
    PARAM_CODE VARCHAR(30),
    PACK_UNIT_CN_NAME VARCHAR(60),
    PACK_UNIT_EN_NAME VARCHAR(160),
    TARE_WT NUMERIC(19,5),
    NET_WT NUMERIC(19,5),
    VOLUME VARCHAR(160),
    NOTE VARCHAR(400),
    EXTEND1 VARCHAR(200),
    EXTEND2 VARCHAR(200),
    EXTEND3 VARCHAR(200),
    EXTEND4 VARCHAR(200),
    EXTEND5 VARCHAR(200),
    EXTEND6 VARCHAR(200),
    EXTEND7 VARCHAR(200),
    EXTEND8 VARCHAR(200),
    EXTEND9 VARCHAR(200),
    EXTEND10 VARCHAR(200)
);

comment
    on table BIZ_TOBACOO.T_BIZ_PACKAGE_INFO is '包装信息参数表';

comment
    on column BIZ_TOBACOO.T_BIZ_PACKAGE_INFO.PARAM_CODE is '参数代码';

comment
    on column BIZ_TOBACOO.T_BIZ_PACKAGE_INFO.PACK_UNIT_CN_NAME is '包装单位中文名称';

comment
    on column BIZ_TOBACOO.T_BIZ_PACKAGE_INFO.PACK_UNIT_EN_NAME is '包装单位英文名称';

comment
    on column BIZ_TOBACOO.T_BIZ_PACKAGE_INFO.TARE_WT is '单位皮重';

comment
    on column BIZ_TOBACOO.T_BIZ_PACKAGE_INFO.NET_WT is '单位净重';

comment
    on column BIZ_TOBACOO.T_BIZ_PACKAGE_INFO.VOLUME is '单位体积';

comment
    on column BIZ_TOBACOO.T_BIZ_PACKAGE_INFO.NOTE is '备注';



-- auto-generated definition
create table if not exists BIZ_TOBACOO.T_BIZ_PRODUCT_TYPE
(
    SID         VARCHAR(50) default sys_guid() not null
        constraint T_BIZ_PRODUCT_TYPE_PK
            primary key,
    TRADE_CODE  VARCHAR(50),
    INSERT_USER VARCHAR(50) not null,
    INSERT_TIME TIMESTAMP default CURRENT_TIMESTAMP not null,
    INSERT_USER_NAME VARCHAR(50),
    UPDATE_USER VARCHAR(50),
    UPDATE_TIME TIMESTAMP,
    UPDATE_USER_NAME VARCHAR(50),
    PARAM_CODE VARCHAR(30),
    CATEGORY_CODE VARCHAR(160),
    CATEGORY_NAME VARCHAR(160),
    NOTE VARCHAR(400),
    EXTEND1 VARCHAR(200),
    EXTEND2 VARCHAR(200),
    EXTEND3 VARCHAR(200),
    EXTEND4 VARCHAR(200),
    EXTEND5 VARCHAR(200),
    EXTEND6 VARCHAR(200),
    EXTEND7 VARCHAR(200),
    EXTEND8 VARCHAR(200),
    EXTEND9 VARCHAR(200),
    EXTEND10 VARCHAR(200)
);

comment
    on table BIZ_TOBACOO.T_BIZ_PRODUCT_TYPE is '商品类别参数表';

comment
    on column BIZ_TOBACOO.T_BIZ_PRODUCT_TYPE.PARAM_CODE is '参数代码';

comment
    on column BIZ_TOBACOO.T_BIZ_PRODUCT_TYPE.CATEGORY_CODE is '类别编号';

comment
    on column BIZ_TOBACOO.T_BIZ_PRODUCT_TYPE.CATEGORY_NAME is '类别名称';

comment
    on column BIZ_TOBACOO.T_BIZ_PRODUCT_TYPE.NOTE is '备注';




-- auto-generated definition
create table if not exists BIZ_TOBACOO.T_BIZ_RATE_TABLE
(
    SID         VARCHAR(50) default sys_guid() not null
        constraint T_BIZ_RATE_TABLE_PK
            primary key,
    TRADE_CODE  VARCHAR(50),
    INSERT_USER VARCHAR(50) not null,
    INSERT_TIME TIMESTAMP default CURRENT_TIMESTAMP not null,
    INSERT_USER_NAME VARCHAR(50),
    UPDATE_USER VARCHAR(50),
    UPDATE_TIME TIMESTAMP,
    UPDATE_USER_NAME VARCHAR(50),
    PARAM_CODE VARCHAR(30),
    CURR VARCHAR(160),
    FLOAT_RATE NUMERIC(9,4),
    NOTE VARCHAR(400),
    EXTEND1 VARCHAR(200),
    EXTEND2 VARCHAR(200),
    EXTEND3 VARCHAR(200),
    EXTEND4 VARCHAR(200),
    EXTEND5 VARCHAR(200),
    EXTEND6 VARCHAR(200),
    EXTEND7 VARCHAR(200),
    EXTEND8 VARCHAR(200),
    EXTEND9 VARCHAR(200),
    EXTEND10 VARCHAR(200)
);

comment
    on table BIZ_TOBACOO.T_BIZ_RATE_TABLE is '汇率上浮表';

comment
    on column BIZ_TOBACOO.T_BIZ_RATE_TABLE.PARAM_CODE is '参数代码';

comment
    on column BIZ_TOBACOO.T_BIZ_RATE_TABLE.CURR is '币种';

comment
    on column BIZ_TOBACOO.T_BIZ_RATE_TABLE.FLOAT_RATE is '上浮比率';

comment
    on column BIZ_TOBACOO.T_BIZ_RATE_TABLE.NOTE is '备注';



-- auto-generated definition
create table if not exists BIZ_TOBACOO.T_BIZ_STOREHOUSE
(
    SID         VARCHAR(50) default sys_guid() not null
        constraint T_BIZ_STOREHOUSE_PK
            primary key,
    TRADE_CODE  VARCHAR(50),
    INSERT_USER VARCHAR(50) not null,
    INSERT_TIME TIMESTAMP default CURRENT_TIMESTAMP not null,
    INSERT_USER_NAME VARCHAR(50),
    UPDATE_USER VARCHAR(50),
    UPDATE_TIME TIMESTAMP,
    UPDATE_USER_NAME VARCHAR(50),
    PARAM_CODE VARCHAR(30),
    STOREHOUSE_NAME VARCHAR(160),
    GROUP_NAME VARCHAR(160),
    NOTE VARCHAR(400),
    EXTEND1 VARCHAR(200),
    EXTEND2 VARCHAR(200),
    EXTEND3 VARCHAR(200),
    EXTEND4 VARCHAR(200),
    EXTEND5 VARCHAR(200),
    EXTEND6 VARCHAR(200),
    EXTEND7 VARCHAR(200),
    EXTEND8 VARCHAR(200),
    EXTEND9 VARCHAR(200),
    EXTEND10 VARCHAR(200)
);

comment
    on table BIZ_TOBACOO.T_BIZ_STOREHOUSE is '仓库参数表';

comment
    on column BIZ_TOBACOO.T_BIZ_STOREHOUSE.PARAM_CODE is '参数代码';

comment
    on column BIZ_TOBACOO.T_BIZ_STOREHOUSE.STOREHOUSE_NAME is '仓库名称';

comment
    on column BIZ_TOBACOO.T_BIZ_STOREHOUSE.GROUP_NAME is '库存组织名称';

comment
    on column BIZ_TOBACOO.T_BIZ_STOREHOUSE.NOTE is '备注';





--changeset ycmeng:2
DROP TABLE IF EXISTS BIZ_TOBACOO.T_BIZ_I_PLAN_LIST;
CREATE TABLE "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST" (
                                                   "SID" VARCHAR(80) NOT NULL DEFAULT HEX(SYS_GUID()),
                                                   "HEAD_ID" VARCHAR(80) NOT NULL,
                                                   "TRADE_CODE" VARCHAR(50),
                                                   "PRODUCT_NAME" VARCHAR(160) NOT NULL,
                                                   "SUPPLIER" VARCHAR(400) NOT NULL,
                                                   "ENGLISH_BRAND" VARCHAR(400),
                                                   "ORIGIN" VARCHAR(200),
                                                   "PLAN_QUANTITY" NUMERIC(19,6) NOT NULL,
                                                   "UNIT" VARCHAR(40) NOT NULL,
                                                   "CURR" VARCHAR(20) NOT NULL,
                                                   "UNIT_PRICE" NUMERIC(19,5) NOT NULL,
                                                   "TOTAL_AMOUNT" NUMERIC(19,5) NOT NULL,
                                                   "DISCOUNT_RATE" NUMERIC(19,4),
                                                   "DISCOUNT_AMOUNT" NUMERIC(19,5),
                                                   "INSERT_USER" VARCHAR(200) NOT NULL,
                                                   "INSERT_USER_NAME" VARCHAR(200),
                                                   "INSERT_TIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                                   "UPDATE_USER" VARCHAR(200),
                                                   "UPDATE_TIME" TIMESTAMP(6),
                                                   "UPDATE_USER_NAME" VARCHAR(200),
                                                   "EXTEND1" VARCHAR(400),
                                                   "EXTEND2" VARCHAR(400),
                                                   "EXTEND3" VARCHAR(400),
                                                   "EXTEND4" VARCHAR(400),
                                                   "EXTEND5" VARCHAR(400),
                                                   "EXTEND6" VARCHAR(400),
                                                   "EXTEND7" VARCHAR(400),
                                                   "EXTEND8" VARCHAR(400),
                                                   "EXTEND9" VARCHAR(400),
                                                   "EXTEND10" VARCHAR(400),
                                                   CONSTRAINT "T_BIZ_I_PLAN_LIST_PKEY" PRIMARY KEY ("SID")
);

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."HEAD_ID" IS '主表ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."PRODUCT_NAME" IS '商品名称（关联物料信息）';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."SUPPLIER" IS '供应商（关联物料信息，不可修改）';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."ENGLISH_BRAND" IS '英文品牌（关联物料信息，可修改）';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."ORIGIN" IS '原产地';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."PLAN_QUANTITY" IS '计划数量（数值）';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."UNIT" IS '计划数量单位（默认万支）';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."CURR" IS '币种（三位字母代码）';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."UNIT_PRICE" IS '计划单价（关联物料信息，可修改）';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."DISCOUNT_AMOUNT" IS '折扣金额';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."DISCOUNT_RATE" IS '折扣率（单位：%）';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."DISCOUNT_RATE" IS '折扣率（单位：%）';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."INSERT_USER" IS '制单人，自动识别最后操作人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."INSERT_TIME" IS '制单时间，系统自动生成';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."UPDATE_USER" IS '修改人，最后操作人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."UPDATE_TIME" IS '修改时间，最后操作时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."TRADE_CODE" IS '企业编码';
COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST" IS '进口计划明细表';



--changeset hrfan:1

create table BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST_BOX
(
    SID         VARCHAR(50),
    INSERT_USER VARCHAR(50),
    INSERT_TIME TIMESTAMP(39, 6
) ,
	INSERT_USER_NAME VARCHAR(50),
	UPDATE_USER VARCHAR(50),
	UPDATE_TIME TIMESTAMP(39,6),
	UPDATE_USER_NAME VARCHAR(50),
	TRADE_CODE VARCHAR(50),
	PRODUCT_GRADE VARCHAR(80),
	UNIT VARCHAR(9),
	QTY NUMERIC(19,6),
	CURR VARCHAR(10),
	DEC_PRICE NUMERIC(19,5),
	DEC_TOTAL NUMERIC(19,2),
	PRODUCT_TYPE VARCHAR(80),
	HEAD_ID VARCHAR(50),
	DISCOUNT_RATE NUMERIC(19,4),
	DISCOUNT_AMOUNT NUMERIC(19,2),
	PAYMENT_AMOUNT NUMERIC(19,2),
	INVOICE_NO VARCHAR(60),
	BOX_NO VARCHAR(200),
	QUANTITY NUMERIC(10),
	VERSION_NO VARCHAR(10),
	DATA_STATUS VARCHAR(10),
	EXTEND1 VARCHAR(200),
	EXTEND2 VARCHAR(200),
	EXTEND3 VARCHAR(200),
	EXTEND4 VARCHAR(200),
	EXTEND5 VARCHAR(200),
	EXTEND6 VARCHAR(200),
	EXTEND7 VARCHAR(200),
	EXTEND8 VARCHAR(200),
	EXTEND9 VARCHAR(200),
	EXTEND10 VARCHAR(200),
	LIST_HEAD_SID VARCHAR(60)
);

comment
on table BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST_BOX is '进口管理-进货信息表体-装箱子表';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST_BOX.SID is '主建SID';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST_BOX.INSERT_USER is '制单人';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST_BOX.INSERT_TIME is '订单制单时间';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST_BOX.INSERT_USER_NAME is '创建人姓名';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST_BOX.UPDATE_USER is '更新人';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST_BOX.UPDATE_TIME is '更新时间';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST_BOX.UPDATE_USER_NAME is '更新人姓名';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST_BOX.TRADE_CODE is '企业代码';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST_BOX.PRODUCT_GRADE is '商品牌号';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST_BOX.UNIT is '单位';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST_BOX.QTY is '数量';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST_BOX.CURR is '币种';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST_BOX.DEC_PRICE is '单价';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST_BOX.DEC_TOTAL is '总价';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST_BOX.PRODUCT_TYPE is '商品类别';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST_BOX.HEAD_ID is '表头HEAD_ID';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST_BOX.DISCOUNT_RATE is '折扣率%';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST_BOX.DISCOUNT_AMOUNT is '折扣金额';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST_BOX.PAYMENT_AMOUNT is '贷款金额';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST_BOX.INVOICE_NO is '进口发票号';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST_BOX.BOX_NO is '箱号';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST_BOX.QUANTITY is '件数';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST_BOX.VERSION_NO is '版本号';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST_BOX.DATA_STATUS is '数据状态';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST_BOX.EXTEND1 is '拓展字段1';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST_BOX.EXTEND2 is '拓展字段2';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST_BOX.EXTEND3 is '拓展字段3';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST_BOX.EXTEND4 is '拓展字段4';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST_BOX.EXTEND5 is '拓展字段5';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST_BOX.EXTEND6 is '拓展字段6';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST_BOX.EXTEND7 is '拓展字段7';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST_BOX.EXTEND8 is '拓展字段8';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST_BOX.EXTEND9 is '拓展字段9';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST_BOX.EXTEND10 is '拓展字段10';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST_BOX.LIST_HEAD_SID is '进货信息表体SID';




create table BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD
(
    SID         VARCHAR(50),
    INSERT_USER VARCHAR(50),
    INSERT_TIME TIMESTAMP(39, 6
) ,
	INSERT_USER_NAME VARCHAR(50),
	UPDATE_USER VARCHAR(50),
	UPDATE_TIME TIMESTAMP(39,6),
	UPDATE_USER_NAME VARCHAR(50),
	TRADE_CODE VARCHAR(50),
	BUSINESS_TYPE VARCHAR(60),
	ORDER_DATA_STATUS VARCHAR(10),
	CONTRACT_NO VARCHAR(60),
	ORDER_NO VARCHAR(60),
	PARTY_A VARCHAR(200),
	PARTY_B VARCHAR(200),
	DELIVERY_DATE VARCHAR(20),
	PAYMENT_METHOD VARCHAR(30),
	PURCHASE_ORDER_NO VARCHAR(60),
	IMPORT_INVOICE_NO VARCHAR(60),
	LICENSE_NO VARCHAR(60),
	TRANSPORT_PERMIT_NO VARCHAR(60),
	SALES_INVOICE_NO VARCHAR(60),
	SALES_CONTRACT_NO VARCHAR(60),
	PURCHASE_DATA_STATUS VARCHAR(10),
	SALES_DATA_STATUS VARCHAR(10),
	INBOUND_RECEIPT_STATUS VARCHAR(10),
	OUTBOUND_RECEIPT_STATUS VARCHAR(10),
	VERSION_NO VARCHAR(10),
	DATA_STATUS VARCHAR(10),
	EXTEND1 VARCHAR(200),
	EXTEND2 VARCHAR(200),
	EXTEND3 VARCHAR(200),
	EXTEND4 VARCHAR(200),
	EXTEND5 VARCHAR(200),
	EXTEND6 VARCHAR(200),
	EXTEND7 VARCHAR(200),
	EXTEND8 VARCHAR(200),
	EXTEND9 VARCHAR(200),
	EXTEND10 VARCHAR(200),
	DATE_OF_SIGNING TIMESTAMP(39,6),
	PLAN_NO VARCHAR(60),
	ORDER_CONFIRMATION_TIME TIMESTAMP(39,6),
	APPR_STATUS VARCHAR(10),
	NOTE VARCHAR(200),
	SERIAL_NO NUMERIC(19),
	HEAD_ID VARCHAR(50),
	IS_NEXT VARCHAR(10)
);

comment
on table BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD is '进口管理-订单信息表头';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.SID is '主建sid';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.INSERT_USER is '制单人';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.INSERT_TIME is '订单制单时间';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.INSERT_USER_NAME is '创建人姓名';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.UPDATE_USER is '更新人';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.UPDATE_TIME is '更新时间';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.UPDATE_USER_NAME is '更新人姓名';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.TRADE_CODE is '企业代码';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.BUSINESS_TYPE is '业务类型';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.ORDER_DATA_STATUS is '订单数据状态';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.CONTRACT_NO is '合同编号';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.ORDER_NO is '订单编号';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.PARTY_A is '客户';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.PARTY_B is '供应商';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.DELIVERY_DATE is '交货日期';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.PAYMENT_METHOD is '付款方式';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.PURCHASE_ORDER_NO is '进货单号';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.IMPORT_INVOICE_NO is '进口发票号码';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.LICENSE_NO is '许可证号';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.TRANSPORT_PERMIT_NO is '准运证编号';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.SALES_INVOICE_NO is '销售发票号';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.SALES_CONTRACT_NO is '销售合同号';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.PURCHASE_DATA_STATUS is '进货数据状态';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.SALES_DATA_STATUS is '销售数据状态';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.INBOUND_RECEIPT_STATUS is '入库回单状态';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.OUTBOUND_RECEIPT_STATUS is '出库回单状态';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.VERSION_NO is '版本号';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.DATA_STATUS is '数据状态';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.EXTEND1 is '拓展字段1';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.EXTEND2 is '拓展字段2';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.EXTEND3 is '拓展字段3';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.EXTEND4 is '拓展字段4';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.EXTEND5 is '拓展字段5';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.EXTEND6 is '拓展字段6';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.EXTEND7 is '拓展字段7';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.EXTEND8 is '拓展字段8';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.EXTEND9 is '拓展字段9';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.EXTEND10 is '拓展字段10';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.DATE_OF_SIGNING is '签订日期';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.PLAN_NO is '计划编号';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.ORDER_CONFIRMATION_TIME is '订单确认时间';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.APPR_STATUS is '审批状态';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.NOTE is '备注';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.SERIAL_NO is '序号';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.HEAD_ID is '进口合同表头SID';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_HEAD.IS_NEXT is '是否流入下一节点';


create table BIZ_TOBACOO.T_BIZ_I_ORDER_LIST
(
    SID         VARCHAR(50),
    INSERT_USER VARCHAR(50),
    INSERT_TIME TIMESTAMP(39, 6
) ,
	INSERT_USER_NAME VARCHAR(50),
	UPDATE_USER VARCHAR(50),
	UPDATE_TIME TIMESTAMP(39,6),
	UPDATE_USER_NAME VARCHAR(50),
	TRADE_CODE VARCHAR(50),
	PRODUCT_GRADE VARCHAR(80),
	UNIT VARCHAR(9),
	QTY NUMERIC(19,6),
	CURR VARCHAR(10),
	DEC_PRICE NUMERIC(19,5),
	DEC_TOTAL NUMERIC(19,2),
	HEAD_ID VARCHAR(50),
	PRODUCT_TYPE VARCHAR(80),
	VERSION_NO VARCHAR(10),
	DATA_STATUS VARCHAR(10),
	EXTEND1 VARCHAR(200),
	EXTEND2 VARCHAR(200),
	EXTEND3 VARCHAR(200),
	EXTEND4 VARCHAR(200),
	EXTEND5 VARCHAR(200),
	EXTEND6 VARCHAR(200),
	EXTEND7 VARCHAR(200),
	EXTEND8 VARCHAR(200),
	EXTEND9 VARCHAR(200),
	EXTEND10 VARCHAR(200)
);

comment
on table BIZ_TOBACOO.T_BIZ_I_ORDER_LIST is '进口管理-订单信息表头';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_LIST.SID is '主建SID';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_LIST.INSERT_USER is '制单人';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_LIST.INSERT_TIME is '订单制单时间';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_LIST.INSERT_USER_NAME is '创建人姓名';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_LIST.UPDATE_USER is '更新人';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_LIST.UPDATE_TIME is '更新时间';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_LIST.UPDATE_USER_NAME is '更新人姓名';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_LIST.TRADE_CODE is '企业代码';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_LIST.PRODUCT_GRADE is '商品牌号';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_LIST.UNIT is '单位';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_LIST.QTY is '数量';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_LIST.CURR is '币种';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_LIST.DEC_PRICE is '单价';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_LIST.DEC_TOTAL is '总价';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_LIST.HEAD_ID is '表头IO';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_LIST.PRODUCT_TYPE is '商品类别';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_LIST.VERSION_NO is '版本号';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_LIST.DATA_STATUS is '数据状态';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_LIST.EXTEND1 is '拓展字段1';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_LIST.EXTEND2 is '拓展字段2';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_LIST.EXTEND3 is '拓展字段3';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_LIST.EXTEND4 is '拓展字段4';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_LIST.EXTEND5 is '拓展字段5';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_LIST.EXTEND6 is '拓展字段6';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_LIST.EXTEND7 is '拓展字段7';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_LIST.EXTEND8 is '拓展字段8';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_LIST.EXTEND9 is '拓展字段9';

comment
on column BIZ_TOBACOO.T_BIZ_I_ORDER_LIST.EXTEND10 is '拓展字段10';


create table BIZ_TOBACOO.T_BIZ_I_PURCHASE_HEAD
(
    SID         VARCHAR(50),
    INSERT_USER VARCHAR(50),
    INSERT_TIME TIMESTAMP(39, 6
) ,
	INSERT_USER_NAME VARCHAR(50),
	UPDATE_USER VARCHAR(50),
	UPDATE_TIME TIMESTAMP(39,6),
	UPDATE_USER_NAME VARCHAR(50),
	TRADE_CODE VARCHAR(50),
	PURCHASE_ORDER_NO VARCHAR(60),
	HEAD_ID VARCHAR(50),
	VESSEL_VOYAGE VARCHAR(60),
	SAILING_DATE TIMESTAMP(39,6),
	EXPECTED_ARRIVAL_DATE TIMESTAMP(39,6),
	ENTRY_NO VARCHAR(18),
	ENTRY_DATE TIMESTAMP(39,6),
	PURCHASING_DATA_STATUS VARCHAR(10),
	PURCHASE_CONFIRMATION_TIME TIMESTAMP(39,6),
	VERSION_NO VARCHAR(10),
	DATA_STATUS VARCHAR(10),
	EXTEND1 VARCHAR(200),
	EXTEND2 VARCHAR(200),
	EXTEND3 VARCHAR(200),
	EXTEND4 VARCHAR(200),
	EXTEND5 VARCHAR(200),
	EXTEND6 VARCHAR(200),
	EXTEND7 VARCHAR(200),
	EXTEND8 VARCHAR(200),
	EXTEND9 VARCHAR(200),
	EXTEND10 VARCHAR(200),
	NOTE VARCHAR(200),
	PLAN_NO VARCHAR(60),
	SERIAL_NO NUMERIC(19),
	ORDER_NO VARCHAR(60),
	BUSINESS_TYPE VARCHAR(60),
	IS_NEXT VARCHAR(10)
);

comment
on table BIZ_TOBACOO.T_BIZ_I_PURCHASE_HEAD is '进口管理-进货信息表头';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_HEAD.SID is '主建SID';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_HEAD.INSERT_USER is '制单人';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_HEAD.INSERT_TIME is '订单制单时间';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_HEAD.INSERT_USER_NAME is '创建人姓名';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_HEAD.UPDATE_USER is '更新人';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_HEAD.UPDATE_TIME is '更新时间';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_HEAD.UPDATE_USER_NAME is '更新人姓名';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_HEAD.TRADE_CODE is '企业代码';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_HEAD.PURCHASE_ORDER_NO is '进货单号';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_HEAD.HEAD_ID is '表头HEAD_ID';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_HEAD.VESSEL_VOYAGE is '船名航次';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_HEAD.SAILING_DATE is '开航日期';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_HEAD.EXPECTED_ARRIVAL_DATE is '预计抵达日期';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_HEAD.ENTRY_NO is '报关单号';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_HEAD.ENTRY_DATE is '报关日期';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_HEAD.PURCHASING_DATA_STATUS is '进货数据状态';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_HEAD.PURCHASE_CONFIRMATION_TIME is '进货确认时间';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_HEAD.VERSION_NO is '版本号';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_HEAD.DATA_STATUS is '数据状态';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_HEAD.EXTEND1 is '拓展字段1';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_HEAD.EXTEND2 is '拓展字段2';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_HEAD.EXTEND3 is '拓展字段3';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_HEAD.EXTEND4 is '拓展字段4';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_HEAD.EXTEND5 is '拓展字段5';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_HEAD.EXTEND6 is '拓展字段6';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_HEAD.EXTEND7 is '拓展字段7';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_HEAD.EXTEND8 is '拓展字段8';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_HEAD.EXTEND9 is '拓展字段9';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_HEAD.EXTEND10 is '拓展字段10';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_HEAD.NOTE is '备注';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_HEAD.PLAN_NO is '计划编号';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_HEAD.SERIAL_NO is '序号';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_HEAD.ORDER_NO is '订单信息表头-订单号';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_HEAD.BUSINESS_TYPE is '业务类型';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_HEAD.IS_NEXT is '是否流入下一个节点';




create table BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST
(
    SID         VARCHAR(50),
    INSERT_USER VARCHAR(50),
    INSERT_TIME TIMESTAMP(39, 6
) ,
	INSERT_USER_NAME VARCHAR(50),
	UPDATE_USER VARCHAR(50),
	UPDATE_TIME TIMESTAMP(39,6),
	UPDATE_USER_NAME VARCHAR(50),
	TRADE_CODE VARCHAR(50),
	PRODUCT_GRADE VARCHAR(80),
	UNIT VARCHAR(9),
	QTY NUMERIC(19,6),
	CURR VARCHAR(10),
	DEC_PRICE NUMERIC(19,5),
	DEC_TOTAL NUMERIC(19,2),
	PRODUCT_TYPE VARCHAR(80),
	HEAD_ID VARCHAR(50),
	DISCOUNT_RATE NUMERIC(19,4),
	DISCOUNT_AMOUNT NUMERIC(19,2),
	PAYMENT_AMOUNT NUMERIC(19,2),
	INVOICE_NO VARCHAR(60),
	BOX_NO VARCHAR(200),
	QUANTITY NUMERIC(10),
	VERSION_NO VARCHAR(10),
	DATA_STATUS VARCHAR(10),
	EXTEND1 VARCHAR(200),
	EXTEND2 VARCHAR(200),
	EXTEND3 VARCHAR(200),
	EXTEND4 VARCHAR(200),
	EXTEND5 VARCHAR(200),
	EXTEND6 VARCHAR(200),
	EXTEND7 VARCHAR(200),
	EXTEND8 VARCHAR(200),
	EXTEND9 VARCHAR(200),
	EXTEND10 VARCHAR(200)
);

comment
on table BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST is '进口管理-进货信息表体';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST.SID is '主建SID';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST.INSERT_USER is '制单人';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST.INSERT_TIME is '订单制单时间';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST.INSERT_USER_NAME is '创建人姓名';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST.UPDATE_USER is '更新人';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST.UPDATE_TIME is '更新时间';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST.UPDATE_USER_NAME is '更新人姓名';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST.TRADE_CODE is '企业代码';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST.PRODUCT_GRADE is '商品牌号';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST.UNIT is '单位';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST.QTY is '数量';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST.CURR is '币种';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST.DEC_PRICE is '单价';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST.DEC_TOTAL is '总价';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST.PRODUCT_TYPE is '商品类别';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST.HEAD_ID is '表头HEAD_ID';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST.DISCOUNT_RATE is '折扣率%';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST.DISCOUNT_AMOUNT is '折扣金额';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST.PAYMENT_AMOUNT is '贷款金额';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST.INVOICE_NO is '进口发票号';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST.BOX_NO is '箱号';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST.QUANTITY is '件数';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST.VERSION_NO is '版本号
';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST.DATA_STATUS is '数据状态';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST.EXTEND1 is '拓展字段1';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST.EXTEND2 is '拓展字段2';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST.EXTEND3 is '拓展字段3';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST.EXTEND4 is '拓展字段4';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST.EXTEND5 is '拓展字段5';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST.EXTEND6 is '拓展字段6';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST.EXTEND7 is '拓展字段7';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST.EXTEND8 is '拓展字段8';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST.EXTEND9 is '拓展字段9';

comment
on column BIZ_TOBACOO.T_BIZ_I_PURCHASE_LIST.EXTEND10 is '拓展字段10';



--changeset xbxu1:1
drop table "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD";
CREATE TABLE if not exists  "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"
(
    "SID" VARCHAR(40) DEFAULT SYS_GUID() NOT NULL,
    "BUSINESS_TYPE" VARCHAR(60),
    "PLAN_NO" VARCHAR(120),
    "PLAN_YEAR" DATE,
    "HALF_YEAR" VARCHAR(20),
    "BUYER" VARCHAR(400),
    "SELLER" VARCHAR(400),
    "CONTRACT_NO" VARCHAR(120),
    "CONTRACT_EFFECTIVE_DATE" VARCHAR(120),
    "CONTRACT_EXPIRY_DATE" DATE,
    "SIGN_DATE" DATE,
    "LOADING_PORT" VARCHAR(100),
    "ARRIVAL_PORT" VARCHAR(100),
    "TRADE_TERMS" VARCHAR(100),
    "PRICE_TERM_PORT" VARCHAR(100),
    "EXPORT_COUNTRY" VARCHAR(80),
    "TOTAL_AMOUNT" NUMERIC(19,2),
    "TOTAL_QUANTITY" NUMERIC(9,0),
    "SHORT_OVER_PERCENT" NUMERIC(9,4),
    "NOTE"	VARCHAR(400),
    "PREPARED_BY" VARCHAR(20),
    "PREPARE_TIME" TIMESTAMP(6),
    "DATA_STATUS" VARCHAR(10),
    "CONFIRM_TIME" TIMESTAMP(6),
    "APPROVAL_STATUS" VARCHAR(10),
    "VERSION_NO" VARCHAR(10),
    "TRADE_CODE" VARCHAR(10),
    "PARENT_ID" VARCHAR(40),
    "INSERT_USER" VARCHAR(100) NOT NULL,
    "INSERT_TIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "UPDATE_USER" VARCHAR(100),
    "UPDATE_TIME" TIMESTAMP(6),
    "INSERT_USER_NAME" VARCHAR(100),
    "UPDATE_USER_NAME" VARCHAR(100),
    "EXTEND1" VARCHAR(200),
    "EXTEND2" VARCHAR(200),
    "EXTEND3" VARCHAR(200),
    "EXTEND4" VARCHAR(200),
    "EXTEND5" VARCHAR(200),
    "EXTEND6" VARCHAR(200),
    "EXTEND7" VARCHAR(200),
    "EXTEND8" VARCHAR(200),
    "EXTEND9" VARCHAR(200),
    "EXTEND10" VARCHAR(200),
    CONSTRAINT "PK_T_BIZ_I_CONTRACT_HEAD" NOT CLUSTER PRIMARY KEY("SID")) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE OR REPLACE  INDEX "BIZ_TOBACOO"."IDX_BUSINESS_CONTRACT_SELLER" ON "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"("SELLER" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE OR REPLACE  INDEX "BIZ_TOBACOO"."IDX_BUSINESS_CONTRACT_PLAN_NO" ON "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"("PLAN_NO" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE OR REPLACE  INDEX "BIZ_TOBACOO"."IDX_BUSINESS_CONTRACT_CONTRACT_NO" ON "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"("CONTRACT_NO" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD" IS '进口合同表头';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."SID" IS '主键';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."BUSINESS_TYPE" IS '业务类型';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."PLAN_NO" IS '计划编号';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."PLAN_YEAR" IS '计划年度（格式: YYYY）';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."HALF_YEAR" IS '上下半年（如：上半年/H1）';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."BUYER" IS '客户';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."SELLER" IS '供应商';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."CONTRACT_NO" IS '合同编号';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."CONTRACT_EFFECTIVE_DATE" IS '合同生效日期';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."CONTRACT_EXPIRY_DATE" IS '合同有效期';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."SIGN_DATE" IS '签约日期';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."LOADING_PORT" IS '装货港';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."ARRIVAL_PORT" IS '到货港';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."TRADE_TERMS" IS '贸易条款';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."PRICE_TERM_PORT" IS '价格条款对应的港口';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."EXPORT_COUNTRY" IS '出口国家或地区';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."TOTAL_AMOUNT" IS '合同总金额';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."TOTAL_QUANTITY" IS '合同总数量';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."SHORT_OVER_PERCENT" IS '短溢数%';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."NOTE" IS '备注';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."PREPARED_BY" IS '制单人';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."PREPARE_TIME" IS '制单时间';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."DATA_STATUS" IS '数据状态';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."CONFIRM_TIME" IS '确认时间';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."APPROVAL_STATUS" IS '审批状态';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."VERSION_NO" IS '版本号';




CREATE TABLE if not exists  "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_LIST"
(
    "SID" VARCHAR(40) DEFAULT SYS_GUID() NOT NULL,
    "BUSINESS_TYPE" VARCHAR(60),
    "DATA_STATUS" VARCHAR(10),
    "VERSION_NO" VARCHAR(10),
    "TRADE_CODE" VARCHAR(10),
    "PARENT_ID" VARCHAR(40),
    "HEAD_ID" VARCHAR(40),
    "GOODS_BRAND" VARCHAR(200),
    "UNIT" VARCHAR(20),
    "PLAN_QUANTITY" NUMERIC(19,6),
    "FORMED_QUANTITY" NUMERIC(19,6),
    "EXECUTABLE_QUANTITY" NUMERIC(19,6),
    "CONTRACT_QUANTITY" NUMERIC(19,6),
    "CURR" VARCHAR(20),
    "UNIT_PRICE" NUMERIC(19,5),
    "TOTAL_VALUE" NUMERIC(19,2),
    "GOODS_CATEGORY" VARCHAR(200),
    "INSERT_USER" VARCHAR(100) NOT NULL,
    "INSERT_TIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "UPDATE_USER" VARCHAR(100),
    "UPDATE_TIME" TIMESTAMP(6),
    "INSERT_USER_NAME" VARCHAR(100),
    "UPDATE_USER_NAME" VARCHAR(100),
    "EXTEND1" VARCHAR(200),
    "EXTEND2" VARCHAR(200),
    "EXTEND3" VARCHAR(200),
    "EXTEND4" VARCHAR(200),
    "EXTEND5" VARCHAR(200),
    "EXTEND6" VARCHAR(200),
    "EXTEND7" VARCHAR(200),
    "EXTEND8" VARCHAR(200),
    "EXTEND9" VARCHAR(200),
    "EXTEND10" VARCHAR(200),
    CONSTRAINT "PK_T_BIZ_I_CONTRACT_LIST" NOT CLUSTER PRIMARY KEY("SID")) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE OR REPLACE  INDEX "BIZ_TOBACOO"."IDX_CONTRACT_LIST_BRAND" ON "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_LIST"("GOODS_BRAND" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE OR REPLACE  INDEX "BIZ_TOBACOO"."IDX_CONTRACT_LIST_PARENT" ON "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_LIST"("HEAD_ID" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_LIST" IS '合同商品明细表（与业务合同表主从关系）';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_LIST"."HEAD_ID" IS '关联主合同表ID';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_LIST"."GOODS_BRAND" IS '商品名称';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_LIST"."UNIT" IS '单位';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_LIST"."PLAN_QUANTITY" IS '计划数量';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_LIST"."FORMED_QUANTITY" IS '已形成合同数量';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_LIST"."EXECUTABLE_QUANTITY" IS '可执行合同量';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_LIST"."CONTRACT_QUANTITY" IS '合同数量';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_LIST"."CURR" IS '币种';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_LIST"."UNIT_PRICE" IS '单价';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_LIST"."TOTAL_VALUE" IS '总值';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_LIST"."GOODS_CATEGORY" IS '商品类别';










--changeset tlhuang:2
create table if not exists BIZ_TOBACOO.T_BIZ_PAYMENT_NOTIFY_HEAD
(
    SID         VARCHAR(50) default sys_guid() not null
        constraint T_BIZ_PAYMENT_NOTIFY_HEAD_PK
            primary key,
    TRADE_CODE  VARCHAR(50),
    INSERT_USER VARCHAR(50) not null,
    INSERT_TIME TIMESTAMP default CURRENT_TIMESTAMP not null,
    INSERT_USER_NAME VARCHAR(50),
    UPDATE_USER VARCHAR(50),
    UPDATE_TIME TIMESTAMP,
    UPDATE_USER_NAME VARCHAR(50),
    NOTE VARCHAR(400),
    EXTEND1 VARCHAR(200),
    EXTEND2 VARCHAR(200),
    EXTEND3 VARCHAR(200),
    EXTEND4 VARCHAR(200),
    EXTEND5 VARCHAR(200),
    EXTEND6 VARCHAR(200),
    EXTEND7 VARCHAR(200),
    EXTEND8 VARCHAR(200),
    EXTEND9 VARCHAR(200),
    EXTEND10 VARCHAR(200),
    BIZ_TYPE VARCHAR(120),
    DOC_NO VARCHAR(120),
    DEPARTMENT VARCHAR(120),
    PAYEE VARCHAR(400),
    CONTRACT_NO VARCHAR(200),
    ORDER_NUMBER VARCHAR(200),
    PAY_AMT NUMERIC(19,2),
    PAY_AMT_RMB NUMERIC(19,2),
    RATE NUMERIC(19,2),
    CURR VARCHAR(20),
    PREPAY_FLAG VARCHAR(20),
    SEND_UFIDA VARCHAR(20),
    DOC_STATUS VARCHAR(20),
    RECV_BANK VARCHAR(20),
    RECV_ACCT VARCHAR(20),
    BIZ_DATE TIMESTAMP,
    CFM_TIME TIMESTAMP
);

comment
    on table BIZ_TOBACOO.T_BIZ_PAYMENT_NOTIFY_HEAD is '付款通知表头';

comment
    on column BIZ_TOBACOO.T_BIZ_PAYMENT_NOTIFY_HEAD.DOC_NO is '单据号';

comment
    on column BIZ_TOBACOO.T_BIZ_PAYMENT_NOTIFY_HEAD.BIZ_TYPE is '业务类型';

comment
    on column BIZ_TOBACOO.T_BIZ_PAYMENT_NOTIFY_HEAD.DEPARTMENT is '部门';

comment
    on column BIZ_TOBACOO.T_BIZ_PAYMENT_NOTIFY_HEAD.PAYEE is '收款方';

comment
    on column BIZ_TOBACOO.T_BIZ_PAYMENT_NOTIFY_HEAD.CONTRACT_NO is '合同号';

comment
    on column BIZ_TOBACOO.T_BIZ_PAYMENT_NOTIFY_HEAD.ORDER_NUMBER is '进/出货单号';

comment
    on column BIZ_TOBACOO.T_BIZ_PAYMENT_NOTIFY_HEAD.PAY_AMT is '付款金额';

comment
    on column BIZ_TOBACOO.T_BIZ_PAYMENT_NOTIFY_HEAD.PAY_AMT_RMB is 'RMB金额';

comment
    on column BIZ_TOBACOO.T_BIZ_PAYMENT_NOTIFY_HEAD.RATE is '汇率';

comment
    on column BIZ_TOBACOO.T_BIZ_PAYMENT_NOTIFY_HEAD.CURR is '币种';

comment
    on column BIZ_TOBACOO.T_BIZ_PAYMENT_NOTIFY_HEAD.PREPAY_FLAG is '预付标志';

comment
    on column BIZ_TOBACOO.T_BIZ_PAYMENT_NOTIFY_HEAD.SEND_UFIDA is '发送用友';

comment
    on column BIZ_TOBACOO.T_BIZ_PAYMENT_NOTIFY_HEAD.DOC_STATUS is '单据状态';

comment
    on column BIZ_TOBACOO.T_BIZ_PAYMENT_NOTIFY_HEAD.CFM_TIME is '确认时间';

comment
    on column BIZ_TOBACOO.T_BIZ_PAYMENT_NOTIFY_HEAD.BIZ_DATE is '业务日期';

comment
    on column BIZ_TOBACOO.T_BIZ_PAYMENT_NOTIFY_HEAD.RECV_BANK is '收款银行';

comment
    on column BIZ_TOBACOO.T_BIZ_PAYMENT_NOTIFY_HEAD.RECV_ACCT is '收款方帐号';

comment
    on column BIZ_TOBACOO.T_BIZ_PAYMENT_NOTIFY_HEAD.NOTE is '备注';

--changeset tlhuang:3
ALTER TABLE BIZ_TOBACOO.T_BIZ_PAYMENT_NOTIFY_HEAD
    MODIFY RECV_BANK VARCHAR(200);


--changeset tlhuang:4
create table if not exists BIZ_TOBACOO.T_BIZ_PAYMENT_NOTIFY_LIST
(
    SID         VARCHAR(50) default sys_guid() not null
        constraint T_BIZ_PAYMENT_NOTIFY_LIST_PK
            primary key,
    TRADE_CODE  VARCHAR(50),
    INSERT_USER VARCHAR(50) not null,
    INSERT_TIME TIMESTAMP default CURRENT_TIMESTAMP not null,
    INSERT_USER_NAME VARCHAR(50),
    UPDATE_USER VARCHAR(50),
    UPDATE_TIME TIMESTAMP,
    UPDATE_USER_NAME VARCHAR(50),
    NOTE VARCHAR(400),
    EXTEND1 VARCHAR(200),
    EXTEND2 VARCHAR(200),
    EXTEND3 VARCHAR(200),
    EXTEND4 VARCHAR(200),
    EXTEND5 VARCHAR(200),
    EXTEND6 VARCHAR(200),
    EXTEND7 VARCHAR(200),
    EXTEND8 VARCHAR(200),
    EXTEND9 VARCHAR(200),
    EXTEND10 VARCHAR(200),
    CONTRACT_NO VARCHAR(200),
    ORDER_NUMBER VARCHAR(200),
    ORDER_NO VARCHAR(200),
    GOODS_NAME VARCHAR(200),
    INVOICE_NUMBER VARCHAR(200),
    QTY NUMERIC(19,6),
    UNIT VARCHAR(20),
    PAY_AMT NUMERIC(19,2),
    HEAD_ID VARCHAR(50),
    PAY_AMT_RMB NUMERIC(19,2)

);

comment
    on table BIZ_TOBACOO.T_BIZ_PAYMENT_NOTIFY_LIST is '付款通知表体';


comment
    on column BIZ_TOBACOO.T_BIZ_PAYMENT_NOTIFY_LIST.CONTRACT_NO is '合同号';

comment
    on column BIZ_TOBACOO.T_BIZ_PAYMENT_NOTIFY_LIST.ORDER_NUMBER is '进/出货单号';

comment
    on column BIZ_TOBACOO.T_BIZ_PAYMENT_NOTIFY_LIST.ORDER_NO is '订单号';

comment
    on column BIZ_TOBACOO.T_BIZ_PAYMENT_NOTIFY_LIST.GOODS_NAME is '商品名称';

comment
    on column BIZ_TOBACOO.T_BIZ_PAYMENT_NOTIFY_LIST.INVOICE_NUMBER is '发票号';

comment
    on column BIZ_TOBACOO.T_BIZ_PAYMENT_NOTIFY_LIST.QTY is '数量';

comment
    on column BIZ_TOBACOO.T_BIZ_PAYMENT_NOTIFY_LIST.UNIT is '单位';

comment
    on column BIZ_TOBACOO.T_BIZ_PAYMENT_NOTIFY_LIST.PAY_AMT is '金额';

comment
    on column BIZ_TOBACOO.T_BIZ_PAYMENT_NOTIFY_LIST.PAY_AMT_RMB is 'RMB金额';

--changeset ycmeng:3
drop table if exists "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"
CREATE TABLE if not exists "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"
(
    "SID" VARCHAR(80) DEFAULT SYS_GUID() NOT NULL,
    "HEAD_ID" VARCHAR(100),
    "TRADE_CODE" VARCHAR(10),
    "BUSINESS_TYPE" VARCHAR(120) NOT NULL,
    "PURCHASE_ORDER_NO" VARCHAR(120) NOT NULL,
    "CONTRACT_NO" VARCHAR(120) NOT NULL,
    "CUSTOMER" VARCHAR(400) NOT NULL,
    "PAYER" VARCHAR(400) NOT NULL,
    "FOREIGN_INVOICE_NO" VARCHAR(120) NOT NULL,
    "CURR" VARCHAR(20) NOT NULL,
    "EXCHANGE_RATE" NUMERIC(19,6) NOT NULL,
    "ORIGINAL_AMOUNT" NUMERIC(19,2) NOT NULL,
    "RMB_AMOUNT" NUMERIC(19,2) NOT NULL,
    "PORT_TRANSFER_AMOUNT" NUMERIC(19,2) NOT NULL,
    "AGENCY_FEE" NUMERIC(19,2) NOT NULL,
    "AGENCY_FEE_TAX" NUMERIC(19,2) NOT NULL,
    "TOTAL_AMOUNT" NUMERIC(19,2) NOT NULL,
    "AGENCY_FEE_RATE" NUMERIC(19,2) NOT NULL,
    "AGENCY_FEE_TOTAL" NUMERIC(19,2) NOT NULL,
    "PRODUCT_CATEGORY" VARCHAR(120) NOT NULL,
    "QTY" NUMERIC(19,6) NOT NULL,
    "SEND_TO_UF" VARCHAR(20),
    "REMARK" VARCHAR(400),
    "STATUS" VARCHAR(20) NOT NULL,
    "CONFIRM_TIME" TIMESTAMP(6),
    "INSERT_USER" VARCHAR(60),
    "INSERT_USER_NAME" VARCHAR(60),
    "INSERT_TIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    "UPDATE_USER" VARCHAR(60),
    "UPDATE_USER_NAME" VARCHAR(60),
    "UPDATE_TIME" TIMESTAMP(6),
    "EXTEND1" VARCHAR(400),
    "EXTEND2" VARCHAR(400),
    "EXTEND3" VARCHAR(400),
    "EXTEND4" VARCHAR(400),
    "EXTEND5" VARCHAR(400),
    "EXTEND6" VARCHAR(400),
    "EXTEND7" VARCHAR(400),
    "EXTEND8" VARCHAR(400),
    "EXTEND9" VARCHAR(400),
    "EXTEND10" VARCHAR(400),
    CONSTRAINT "PK_T_BIZ_PAYMENT_SETTLEMENT" NOT CLUSTER PRIMARY KEY("SID")) STORAGE(ON "MAIN", CLUSTERBTR);
COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT" IS '货款结算单表';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."HEAD_ID" IS '上游SID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."TRADE_CODE" IS '企业编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."BUSINESS_TYPE" IS '业务类型';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."PURCHASE_ORDER_NO" IS '进货单号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."CONTRACT_NO" IS '合同号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."CUSTOMER" IS '客户';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."PAYER" IS '付款单位';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."FOREIGN_INVOICE_NO" IS '外商发票号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."CURR" IS '币种';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."EXCHANGE_RATE" IS '汇率';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."ORIGINAL_AMOUNT" IS '原货币价';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."RMB_AMOUNT" IS '人民币金额';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."PORT_TRANSFER_AMOUNT" IS '口岸调拨总价';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."AGENCY_FEE" IS '代理费(不含税金额)';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."AGENCY_FEE_TAX" IS '代理费税额';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."TOTAL_AMOUNT" IS '合计值';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."AGENCY_FEE_RATE" IS '代理费率';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."AGENCY_FEE_TOTAL" IS '代理费(价税合计)';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."PRODUCT_CATEGORY" IS '商品类别';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."QTY" IS '数量';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."SEND_TO_UF" IS '发送用友';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."REMARK" IS '备注';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."STATUS" IS '单据状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."CONFIRM_TIME" IS '确认时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."INSERT_USER" IS '创建人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."INSERT_USER_NAME" IS '创建人名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."INSERT_TIME" IS '创建时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."UPDATE_USER" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."UPDATE_USER_NAME" IS '修改人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."UPDATE_TIME" IS '更新时间';


--changeset tlhuang:5
ALTER TABLE BIZ_TOBACOO.T_BIZ_PAYMENT_NOTIFY_HEAD
    MODIFY RECV_ACCT VARCHAR(60);

--changeset ycmeng:4
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."SID" IS '主键SID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."EXTEND1" IS '扩展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."EXTEND2" IS '扩展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."EXTEND3" IS '扩展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."EXTEND4" IS '扩展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."EXTEND5" IS '扩展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."EXTEND6" IS '扩展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."EXTEND7" IS '扩展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."EXTEND8" IS '扩展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."EXTEND9" IS '扩展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_SETTLEMENT"."EXTEND10" IS '扩展字段10';


COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN"."SID" IS '主键SID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN"."INSERT_USER_NAME" IS '制单人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN"."UPDATE_USER_NAME" IS '修改人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN"."EXTEND1" IS '扩展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN"."EXTEND2" IS '扩展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN"."EXTEND3" IS '扩展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN"."EXTEND4" IS '扩展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN"."EXTEND5" IS '扩展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN"."EXTEND6" IS '扩展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN"."EXTEND7" IS '扩展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN"."EXTEND8" IS '扩展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN"."EXTEND9" IS '扩展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN"."EXTEND10" IS '扩展字段10';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."ID" IS '主键SID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."INSERT_USER_NAME" IS '制单人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."UPDATE_USER_NAME" IS '修改人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."EXTEND1" IS '扩展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."EXTEND2" IS '扩展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."EXTEND3" IS '扩展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."EXTEND4" IS '扩展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."EXTEND5" IS '扩展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."EXTEND6" IS '扩展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."EXTEND7" IS '扩展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."EXTEND8" IS '扩展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."EXTEND9" IS '扩展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_PLAN_LIST"."EXTEND10" IS '扩展字段10';


--changeset xbxu1:2
        COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."TRADE_CODE" IS '企业编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."PARENT_ID" IS '父节点';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."INSERT_USER" IS '插入人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."INSERT_TIME" IS '插入时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."UPDATE_USER" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."INSERT_USER_NAME" IS '插入人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."UPDATE_USER_NAME" IS '更新人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."EXTEND1" IS '备用1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."EXTEND2" IS '备用2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."EXTEND3" IS '备用3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."EXTEND4" IS '备用4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."EXTEND5" IS '备用5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."EXTEND6" IS '备用6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."EXTEND7" IS '备用7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."EXTEND8" IS '备用8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."EXTEND9" IS '备用9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_HEAD"."EXTEND10" IS '备用10';


COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_LIST"."TRADE_CODE" IS '企业编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_LIST"."PARENT_ID" IS '父节点';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_LIST"."INSERT_USER" IS '插入人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_LIST"."INSERT_TIME" IS '插入时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_LIST"."UPDATE_USER" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_LIST"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_LIST"."INSERT_USER_NAME" IS '插入人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_LIST"."UPDATE_USER_NAME" IS '更新人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_LIST"."EXTEND1" IS '备用1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_LIST"."EXTEND2" IS '备用2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_LIST"."EXTEND3" IS '备用3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_LIST"."EXTEND4" IS '备用4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_LIST"."EXTEND5" IS '备用5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_LIST"."EXTEND6" IS '备用6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_LIST"."EXTEND7" IS '备用7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_LIST"."EXTEND8" IS '备用8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_LIST"."EXTEND9" IS '备用9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_LIST"."EXTEND10" IS '备用10';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_LIST"."SID" IS '主键';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_LIST"."BUSINESS_TYPE" IS '业务类型';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_LIST"."VERSION_NO" IS '版本号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_I_CONTRACT_LIST"."DATA_STATUS" IS '数据状态';




--changeset tlhuang:6
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_COST_TYPE"."TRADE_CODE" IS '企业编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_COST_TYPE"."INSERT_USER" IS '插入人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_COST_TYPE"."INSERT_TIME" IS '插入时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_COST_TYPE"."UPDATE_USER" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_COST_TYPE"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_COST_TYPE"."INSERT_USER_NAME" IS '插入人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_COST_TYPE"."UPDATE_USER_NAME" IS '更新人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_COST_TYPE"."EXTEND1" IS '备用1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_COST_TYPE"."EXTEND2" IS '备用2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_COST_TYPE"."EXTEND3" IS '备用3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_COST_TYPE"."EXTEND4" IS '备用4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_COST_TYPE"."EXTEND5" IS '备用5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_COST_TYPE"."EXTEND6" IS '备用6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_COST_TYPE"."EXTEND7" IS '备用7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_COST_TYPE"."EXTEND8" IS '备用8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_COST_TYPE"."EXTEND9" IS '备用9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_COST_TYPE"."EXTEND10" IS '备用10';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_COST_TYPE"."SID" IS '主键';


COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PACKAGE_INFO"."TRADE_CODE" IS '企业编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PACKAGE_INFO"."INSERT_USER" IS '插入人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PACKAGE_INFO"."INSERT_TIME" IS '插入时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PACKAGE_INFO"."UPDATE_USER" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PACKAGE_INFO"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PACKAGE_INFO"."INSERT_USER_NAME" IS '插入人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PACKAGE_INFO"."UPDATE_USER_NAME" IS '更新人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PACKAGE_INFO"."EXTEND1" IS '备用1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PACKAGE_INFO"."EXTEND2" IS '备用2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PACKAGE_INFO"."EXTEND3" IS '备用3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PACKAGE_INFO"."EXTEND4" IS '备用4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PACKAGE_INFO"."EXTEND5" IS '备用5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PACKAGE_INFO"."EXTEND6" IS '备用6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PACKAGE_INFO"."EXTEND7" IS '备用7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PACKAGE_INFO"."EXTEND8" IS '备用8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PACKAGE_INFO"."EXTEND9" IS '备用9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PACKAGE_INFO"."EXTEND10" IS '备用10';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PACKAGE_INFO"."SID" IS '主键';


COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_NOTIFY_HEAD"."TRADE_CODE" IS '企业编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_NOTIFY_HEAD"."INSERT_USER" IS '插入人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_NOTIFY_HEAD"."INSERT_TIME" IS '插入时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_NOTIFY_HEAD"."UPDATE_USER" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_NOTIFY_HEAD"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_NOTIFY_HEAD"."INSERT_USER_NAME" IS '插入人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_NOTIFY_HEAD"."UPDATE_USER_NAME" IS '更新人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_NOTIFY_HEAD"."EXTEND1" IS '备用1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_NOTIFY_HEAD"."EXTEND2" IS '备用2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_NOTIFY_HEAD"."EXTEND3" IS '备用3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_NOTIFY_HEAD"."EXTEND4" IS '备用4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_NOTIFY_HEAD"."EXTEND5" IS '备用5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_NOTIFY_HEAD"."EXTEND6" IS '备用6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_NOTIFY_HEAD"."EXTEND7" IS '备用7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_NOTIFY_HEAD"."EXTEND8" IS '备用8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_NOTIFY_HEAD"."EXTEND9" IS '备用9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_NOTIFY_HEAD"."EXTEND10" IS '备用10';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_NOTIFY_HEAD"."SID" IS '主键';


COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_NOTIFY_LIST"."TRADE_CODE" IS '企业编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_NOTIFY_LIST"."INSERT_USER" IS '插入人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_NOTIFY_LIST"."INSERT_TIME" IS '插入时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_NOTIFY_LIST"."UPDATE_USER" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_NOTIFY_LIST"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_NOTIFY_LIST"."INSERT_USER_NAME" IS '插入人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_NOTIFY_LIST"."UPDATE_USER_NAME" IS '更新人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_NOTIFY_LIST"."EXTEND1" IS '备用1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_NOTIFY_LIST"."EXTEND2" IS '备用2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_NOTIFY_LIST"."EXTEND3" IS '备用3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_NOTIFY_LIST"."EXTEND4" IS '备用4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_NOTIFY_LIST"."EXTEND5" IS '备用5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_NOTIFY_LIST"."EXTEND6" IS '备用6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_NOTIFY_LIST"."EXTEND7" IS '备用7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_NOTIFY_LIST"."EXTEND8" IS '备用8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_NOTIFY_LIST"."EXTEND9" IS '备用9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_NOTIFY_LIST"."EXTEND10" IS '备用10';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PAYMENT_NOTIFY_LIST"."SID" IS '主键';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRODUCT_TYPE"."TRADE_CODE" IS '企业编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRODUCT_TYPE"."INSERT_USER" IS '插入人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRODUCT_TYPE"."INSERT_TIME" IS '插入时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRODUCT_TYPE"."UPDATE_USER" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRODUCT_TYPE"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRODUCT_TYPE"."INSERT_USER_NAME" IS '插入人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRODUCT_TYPE"."UPDATE_USER_NAME" IS '更新人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRODUCT_TYPE"."EXTEND1" IS '备用1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRODUCT_TYPE"."EXTEND2" IS '备用2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRODUCT_TYPE"."EXTEND3" IS '备用3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRODUCT_TYPE"."EXTEND4" IS '备用4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRODUCT_TYPE"."EXTEND5" IS '备用5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRODUCT_TYPE"."EXTEND6" IS '备用6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRODUCT_TYPE"."EXTEND7" IS '备用7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRODUCT_TYPE"."EXTEND8" IS '备用8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRODUCT_TYPE"."EXTEND9" IS '备用9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRODUCT_TYPE"."EXTEND10" IS '备用10';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRODUCT_TYPE"."SID" IS '主键';


COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_RATE_TABLE"."TRADE_CODE" IS '企业编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_RATE_TABLE"."INSERT_USER" IS '插入人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_RATE_TABLE"."INSERT_TIME" IS '插入时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_RATE_TABLE"."UPDATE_USER" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_RATE_TABLE"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_RATE_TABLE"."INSERT_USER_NAME" IS '插入人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_RATE_TABLE"."UPDATE_USER_NAME" IS '更新人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_RATE_TABLE"."EXTEND1" IS '备用1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_RATE_TABLE"."EXTEND2" IS '备用2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_RATE_TABLE"."EXTEND3" IS '备用3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_RATE_TABLE"."EXTEND4" IS '备用4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_RATE_TABLE"."EXTEND5" IS '备用5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_RATE_TABLE"."EXTEND6" IS '备用6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_RATE_TABLE"."EXTEND7" IS '备用7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_RATE_TABLE"."EXTEND8" IS '备用8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_RATE_TABLE"."EXTEND9" IS '备用9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_RATE_TABLE"."EXTEND10" IS '备用10';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_RATE_TABLE"."SID" IS '主键';

COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_STOREHOUSE"."TRADE_CODE" IS '企业编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_STOREHOUSE"."INSERT_USER" IS '插入人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_STOREHOUSE"."INSERT_TIME" IS '插入时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_STOREHOUSE"."UPDATE_USER" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_STOREHOUSE"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_STOREHOUSE"."INSERT_USER_NAME" IS '插入人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_STOREHOUSE"."UPDATE_USER_NAME" IS '更新人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_STOREHOUSE"."EXTEND1" IS '备用1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_STOREHOUSE"."EXTEND2" IS '备用2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_STOREHOUSE"."EXTEND3" IS '备用3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_STOREHOUSE"."EXTEND4" IS '备用4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_STOREHOUSE"."EXTEND5" IS '备用5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_STOREHOUSE"."EXTEND6" IS '备用6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_STOREHOUSE"."EXTEND7" IS '备用7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_STOREHOUSE"."EXTEND8" IS '备用8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_STOREHOUSE"."EXTEND9" IS '备用9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_STOREHOUSE"."EXTEND10" IS '备用10';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_STOREHOUSE"."SID" IS '主键';