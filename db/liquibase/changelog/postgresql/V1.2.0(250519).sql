--liquibase formatted sql

--changeset bhyue:1
DROP TABLE
    IF EXISTS "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS";

CREATE TABLE
    IF NOT EXISTS "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"
(
    "SID"              VARCHAR(50) PRIMARY KEY,
    "PARAM_CODE"       VARCHAR(30),
    "PRICE_TERM"       VARCHAR(10),
    "PRICE_TERM_DESC"  VARCHAR(80),
    "NOTE"             VARCHAR(200),
    "TRADE_CODE"       VARCHAR(50),
    "INSERT_USER"      VARCHAR(50)  NOT NULL,
    "INSERT_TIME"      TIMESTAMP(6) NOT NULL,
    "INSERT_USER_NAME" VARCHAR(50),
    "UPDATE_USER"      VARCHAR(50),
    "UPDATE_TIME"      TIMESTAMP(6),
    "UPDATE_USER_NAME" VARCHAR(50),
    "EXTEND1"          VARCHAR(200),
    "EXTEND2"          VARCHAR(200),
    "EXTEND3"          VARCHAR(200),
    "EXTEND4"          VARCHAR(200),
    "EXTEND5"          VARCHAR(200),
    "EXTEND6"          VARCHAR(200),
    "EXTEND7"          VARCHAR(200),
    "EXTEND8"          VARCHAR(200),
    "EXTEND9"          VARCHAR(200),
    "EXTEND10"         VARCHAR(200)
);

COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS" IS '价格条款表';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."SID" IS '主键';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."PARAM_CODE" IS '参数代码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."PRICE_TERM" IS '价格条款';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."PRICE_TERM_DESC" IS '价格条款描述';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."NOTE" IS '备注';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."TRADE_CODE" IS '企业编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."INSERT_USER" IS '插入人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."INSERT_TIME" IS '插入时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."INSERT_USER_NAME" IS '插入人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."UPDATE_USER" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."UPDATE_USER_NAME" IS '更新人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."EXTEND1" IS '扩展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."EXTEND2" IS '扩展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."EXTEND3" IS '扩展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."EXTEND4" IS '扩展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."EXTEND5" IS '扩展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."EXTEND6" IS '扩展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."EXTEND7" IS '扩展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."EXTEND8" IS '扩展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."EXTEND9" IS '扩展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS"."EXTEND10" IS '扩展字段10';
--changeset tlhuang:1
CREATE TABLE "BIZ_TOBACOO"."T_BIZ_TRANSCODE"
(
    "ID"                VARCHAR(40)  DEFAULT SYS_GUID()        NOT NULL,
    "TRADE_CODE"        VARCHAR(10),
    "SYS_ORG_CODE"      VARCHAR(10),
    "CREATE_BY"         VARCHAR(50)                            NOT NULL,
    "CREATE_TIME"       TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "UPDATE_BY"         VARCHAR(50),
    "UPDATE_TIME"       TIMESTAMP(6),
    "INSERT_USER_NAME"  VARCHAR(50),
    "UPDATE_USER_NAME"  VARCHAR(50),
    "BIZ_TYPE"          VARCHAR(60),
    "TARIFF_RATE"       numeric(19, 6),
    "CONSUMPTION_TAX_RATE"   numeric(19, 6),
    "VAT_RATE"               numeric(19, 6),
    "IE_AGENT_FEE_RATE" numeric(19, 4),
    "HQ_AGENT_FEE_RATE" numeric(19, 4),
    "INTL_TRANS_TYPE"   VARCHAR(20),
    "IS_CONTAINER_SHIP" VARCHAR(20),
    "CONTAINER_CAP"     VARCHAR(20),
    "CONTAINER_TYPE"    VARCHAR(20),
    "INTL_FREIGHT_AMT"  NUMERIC(19, 4),
    "PORT_CHARGES_AMT"  NUMERIC(19, 4),
    "LAND_FREIGHT_AMT"  NUMERIC(19, 4),
    "CUSTOMS_FEE_AMT"   NUMERIC(19, 4),
    "CNTR_INSP_FEE_AMT" NUMERIC(19, 4),
    "INSURANCE_RATE"    NUMERIC(19, 6),
    "OTHER_CHARGES_AMT" NUMERIC(19, 4),
    "REMARK"            VARCHAR(200),
    "EXTEND1"           VARCHAR(200),
    "EXTEND2"           VARCHAR(200),
    "EXTEND3"           VARCHAR(200),
    "EXTEND4"           VARCHAR(200),
    "EXTEND5"           VARCHAR(200),
    "EXTEND6"           VARCHAR(200),
    "EXTEND7"           VARCHAR(200),
    "EXTEND8"           VARCHAR(200),
    "EXTEND9"           VARCHAR(200),
    "EXTEND10"          VARCHAR(200),
    CONSTRAINT          "PK_T_BIZ_TRANSCODE"                   NOT CLUSTER PRIMARY KEY ("ID")
) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR);
COMMENT
    ON TABLE "BIZ_TOBACOO"."T_BIZ_TRANSCODE" IS '划款参数表';

comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.ID is '主键';

comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.TRADE_CODE is '企业编码';

comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.CREATE_BY is '插入人';

comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.CREATE_TIME is '插入时间';

comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.INSERT_USER_NAME is '插入人姓名';

comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.UPDATE_BY is '更新人';

comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.UPDATE_TIME is '更新时间';

comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.UPDATE_USER_NAME is '更新人姓名';

comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.BIZ_TYPE is '业务类型';
comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.TARIFF_RATE is '关税率%';
comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.CONSUMPTION_TAX_RATE is '消费税率%';
comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.VAT_RATE is '增值税率%';
comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.IE_AGENT_FEE_RATE is '进出口公司代理费率%';
comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.HQ_AGENT_FEE_RATE is '总公司代理费率%';
comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.INTL_TRANS_TYPE is '国际运输类型';
comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.IS_CONTAINER_SHIP is '是否集装箱装运';
comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.CONTAINER_CAP is '集装箱容量';
comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.CONTAINER_TYPE is '集装箱型号';
comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.INTL_FREIGHT_AMT is '国际运费';
comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.PORT_CHARGES_AMT is '港杂费';
comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.LAND_FREIGHT_AMT is '陆运费';
comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.CUSTOMS_FEE_AMT is '通关费';
comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.CNTR_INSP_FEE_AMT is '验柜服务费';
comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.INSURANCE_RATE is '保险费率';
comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.OTHER_CHARGES_AMT is '其他费用';
comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.REMARK is '备注';
comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.EXTEND1 is '备用1';

comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.EXTEND2 is '备用2';

comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.EXTEND3 is '备用3';

comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.EXTEND4 is '备用4';

comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.EXTEND5 is '备用5';

comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.EXTEND6 is '备用6';

comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.EXTEND7 is '备用7';

comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.EXTEND8 is '备用8';

comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.EXTEND9 is '备用9';

comment
    on column BIZ_TOBACOO.T_BIZ_TRANSCODE.EXTEND10 is '备用10';


DROP INDEX IF EXISTS IDX_BIZ_PRICE_TERMS_SEQ;
CREATE INDEX IDX_BIZ_PRICE_TERMS_SEQ ON "BIZ_TOBACOO"."T_BIZ_PRICE_TERMS" (TRADE_CODE, PARAM_CODE);

DROP TABLE
    IF EXISTS "BIZ_TOBACOO"."T_BIZ_CITY";

CREATE TABLE
    IF NOT EXISTS "BIZ_TOBACOO"."T_BIZ_CITY" (
                                                 "SID" VARCHAR(50) PRIMARY KEY,
                                                 "PARAM_CODE" VARCHAR(30),
                                                 "CITY_CN_NAME" VARCHAR(50),
                                                 "CITY_EN_NAME" VARCHAR(80),
                                                 "NOTE" VARCHAR(200),
                                                 "TRADE_CODE" VARCHAR(50),
                                                 "INSERT_USER" VARCHAR(50) NOT NULL,
                                                 "INSERT_TIME" TIMESTAMP(6) NOT NULL,
                                                 "INSERT_USER_NAME" VARCHAR(50),
                                                 "UPDATE_USER" VARCHAR(50),
                                                 "UPDATE_TIME" TIMESTAMP(6),
                                                 "UPDATE_USER_NAME" VARCHAR(50),
                                                 "EXTEND1" VARCHAR(200),
                                                 "EXTEND2" VARCHAR(200),
                                                 "EXTEND3" VARCHAR(200),
                                                 "EXTEND4" VARCHAR(200),
                                                 "EXTEND5" VARCHAR(200),
                                                 "EXTEND6" VARCHAR(200),
                                                 "EXTEND7" VARCHAR(200),
                                                 "EXTEND8" VARCHAR(200),
                                                 "EXTEND9" VARCHAR(200),
                                                 "EXTEND10" VARCHAR(200)
);

COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_CITY" IS '城市表';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."SID" IS '主键';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."PARAM_CODE" IS '参数代码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."CITY_CN_NAME" IS '城市中文名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."CITY_EN_NAME" IS '城市英文名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."NOTE" IS '备注';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."TRADE_CODE" IS '企业编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."INSERT_USER" IS '插入人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."INSERT_TIME" IS '插入时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."INSERT_USER_NAME" IS '插入人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."UPDATE_USER" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."UPDATE_USER_NAME" IS '更新人姓名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."EXTEND1" IS '扩展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."EXTEND2" IS '扩展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."EXTEND3" IS '扩展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."EXTEND4" IS '扩展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."EXTEND5" IS '扩展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."EXTEND6" IS '扩展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."EXTEND7" IS '扩展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."EXTEND8" IS '扩展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."EXTEND9" IS '扩展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_CITY"."EXTEND10" IS '扩展字段10';

DROP INDEX IF EXISTS IDX_BIZ_CITY_SEQ;
CREATE INDEX IDX_BIZ_CITY_SEQ ON "BIZ_TOBACOO"."T_BIZ_CITY" (TRADE_CODE, PARAM_CODE);