--liquibase formatted sql


--changeset ycmeng:1
ALTER TABLE BIZ_TOBACOO.T_BIZ_PAYMENT_SETTLEMENT add column if not exists business_date timestamp ;

comment on column BIZ_TOBACOO.T_BIZ_PAYMENT_SETTLEMENT.business_date is '业务日期';

ALTER TABLE BIZ_TOBACOO.t_biz_warehouse_receipt_head add column if not exists business_date timestamp ;

comment on column BIZ_TOBACOO.t_biz_warehouse_receipt_head.business_date is '业务日期';

--changeset ycmeng:2
-- 取消HALF_YEAR字段的NOT NULL约束
ALTER TABLE T_BIZ_I_PLAN MODIFY HALF_YEAR NULL;

-- 取消PLAN_YEAR字段的NOT NULL约束
ALTER TABLE T_BIZ_I_PLAN MODIFY PLAN_YEAR NULL;

-- 取消PLAN_ID字段的NOT NULL约束
ALTER TABLE T_BIZ_I_PLAN MODIFY PLAN_ID NULL;


--changeset xbxu1:1
CREATE TABLE if not exists  biz_type_dict (
                                              name VARCHAR(100),
    code VARCHAR(10)
    );

INSERT INTO biz_type_dict(name, code) VALUES
                                          ('国营贸易进口卷烟', '1'),
                                          ('国营贸易进口辅料', '2'),
                                          ('国营贸易进口卷烟设备', '3'),
                                          ('国营贸易进口丝束', '4'),
                                          ('国营贸易内购内销丝束', '5'),
                                          ('非国营贸易进口辅料', '6'),
                                          ('出料加工进口薄片', '7'),
                                          ('出口烟机设备', '8'),
                                          ('出口辅料', '9');